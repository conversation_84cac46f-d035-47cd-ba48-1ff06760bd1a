/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.utils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.codec.binary.Base64;
import static br.com.sasw.satwebservice.bradesconovo.utils.BradescoConstants.*;
import java.net.URI;
import java.util.Objects;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.utils.URIBuilder;

/**
 *
 * <AUTHOR> Silva
 */
public class BradescoUtils {

    public static String signRequestText(String requestText, PrivateKey privateKey) throws Exception {
        // Inicializar a assinatura RSA com SHA256
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(requestText.getBytes(StandardCharsets.UTF_8));

        // Gerar a assinatura e codificá-la em Base64
        byte[] signedBytes = signature.sign();
        return new String(Base64.encodeBase64(signedBytes));
    }

    public static void setDefaultBradescoHeaders(HttpEntityEnclosingRequestBase httpRequest, String token, String rawPrivateKey, String requestBody, URI uri, String ambiente) throws Exception {
        String path = "/v1".concat(httpRequest.getURI().toString().replace(getAPI_URL(ambiente), ""));
        LocalDateTime dateTimeNow = LocalDateTime.now();
        String nonce = String.valueOf(dateTimeNow.toEpochSecond(ZoneOffset.UTC));
        String timestamp = dateTimeNow.format(DateTimeFormatter.ISO_DATE_TIME);
        String rawQuery = uri.getRawQuery() == null ? "" : uri.getRawQuery();
        String singnedRequestText = buildRequestText("POST", uri.getPath(), rawQuery, requestBody, token, nonce, timestamp);
        httpRequest.setHeader("Authorization", String.format("Bearer %s", token));
        httpRequest.setHeader("X-Brad-Nonce", nonce);
        httpRequest.setHeader("X-Brad-Signature", signRequestText(singnedRequestText, formatPrivateKey(rawPrivateKey)));
        httpRequest.setHeader("X-Brad-Timestamp", timestamp);
        httpRequest.setHeader("X-Brad-Algorithm", "SHA256");
        httpRequest.setHeader("Content-Type", "application/json");
    }

    public static PrivateKey formatPrivateKey(String rawPrivateKey) throws Exception {
        // Remover headers e footers da chave privada e decodificar a chave
        String privateKeyPEM = rawPrivateKey.replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "").replaceAll("\\s", "");
        byte[] encoded = Base64.decodeBase64(privateKeyPEM);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(encoded);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(keySpec);
    }

    public static String loadPrivateKeyFromFile(String path) {
        File privateKey = new File(path);
        try {
            return new String(Files.readAllBytes(privateKey.toPath()));
        } catch (IOException ex) {
            Logger.getLogger(BradescoUtils.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    private static String buildRequestText(String method, String path, String query, String body, String authToken, String nonce, String timestamp) {
        String breakLine = "\n";
        return String.join(breakLine, method, path, query, body, authToken, nonce, timestamp, "SHA256");
    }

    public static URI buildUri(String url, String page, String size, String sort) {
        try {
            URIBuilder builder = new URIBuilder(url);
            if (Objects.nonNull(page)) {
                builder.setParameter("page", page);
            }
            if (Objects.nonNull(size)) {
                builder.setParameter("size", size);
            }
            if (Objects.nonNull(sort)) {
                builder.setParameter("sort", sort);
            }
            return builder.build();
        } catch (Exception e) {
            return null;
        }
    }

    public static String removeTokenFromQuery(String query, String token) {
        return query.replace(String.format("&token=%s", token), "");
    }

}
