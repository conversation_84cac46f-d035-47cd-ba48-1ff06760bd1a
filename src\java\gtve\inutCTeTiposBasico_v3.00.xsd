<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by <EMAIL> (PROCERGS) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/cte" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema_v1.01.xsd"/>
	<xs:include schemaLocation="tiposGeralCTe_v3.00.xsd"/>
	<xs:complexType name="TInutCTe">
		<xs:annotation>
			<xs:documentation>Tipo Pedido de Inutilização de Numeração do Conhecimento de Transporte eletrônico</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infInut">
				<xs:annotation>
					<xs:documentation>Dados do Pedido de Inutilização de Numeração do Conhecimento de Transporte eletrônico</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="tpAmb" type="TAmb">
							<xs:annotation>
								<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="xServ" type="TServ" fixed="INUTILIZAR">
							<xs:annotation>
								<xs:documentation>Serviço Solicitado</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cUF" type="TCodUfIBGE">
							<xs:annotation>
								<xs:documentation>Código da UF solicitada</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="ano">
							<xs:annotation>
								<xs:documentation>Ano de inutilização da numeração</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:short">
									<xs:pattern value="[0-9]{1,2}"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="CNPJ" type="TCnpj">
							<xs:annotation>
								<xs:documentation>CNPJ do emitente</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="mod" type="TModCT_Carga_OS">
							<xs:annotation>
								<xs:documentation>Modelo da CT-e (57 ou 67)</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="serie" type="TSerie">
							<xs:annotation>
								<xs:documentation>Série da CT-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nCTIni" type="TNF">
							<xs:annotation>
								<xs:documentation>Número da CT-e inicial</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nCTFin" type="TNF">
							<xs:annotation>
								<xs:documentation>Número da CT-e final</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="xJust" type="TJust">
							<xs:annotation>
								<xs:documentation>Justificativa do pedido de inutilização</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Id" use="required">
						<xs:simpleType>
							<xs:restriction base="xs:ID">
								<xs:pattern value="ID[0-9]{39}"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature"/>
		</xs:sequence>
		<xs:attribute name="versao" use="required">
			<xs:simpleType>
				<xs:restriction base="TVerInutCTe"/>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="TRetInutCTe">
		<xs:annotation>
			<xs:documentation>Tipo retorno do Pedido de Inutilização de Numeração do Conhecimento de Transporte eletrônico</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infInut">
				<xs:annotation>
					<xs:documentation>Dados do Retorno do Pedido de Inutilização de Numeração do Conhecimento de Transporte eletrônico</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="tpAmb" type="TAmb">
							<xs:annotation>
								<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="verAplic" type="TVerAplic">
							<xs:annotation>
								<xs:documentation>Versão do Aplicativo que processou a CT-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cStat" type="TStat">
							<xs:annotation>
								<xs:documentation>Código do status da mensagem enviada.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="xMotivo">
							<xs:annotation>
								<xs:documentation>Descrição literal do status do serviço solicitado.</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:minLength value="1"/>
									<xs:maxLength value="255"/>
									<xs:whiteSpace value="collapse"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="cUF" type="TCodUfIBGE">
							<xs:annotation>
								<xs:documentation>Código da UF solicitada</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="ano" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Ano de inutilização da numeração</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:short">
									<xs:pattern value="[0-9]{1,2}"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="CNPJ" type="TCnpj" minOccurs="0">
							<xs:annotation>
								<xs:documentation>CNPJ do emitente</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="mod" type="TModCT_Carga_OS" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Modelo da CT-e (57 ou 67)</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="serie" type="TSerie" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Série da CT-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nCTIni" type="TNF" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Número da CT-e inicial</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nCTFin" type="TNF" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Número da CT-e final</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="dhRecbto" type="TDateTimeUTC" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Data e hora de recebimento, no formato AAAA-MM-DDTHH:MM:SS TZD. Deve ser preenchida com data e hora da gravação no Banco em caso de Confirmação. Em caso de Rejeição, com data e hora do recebimento do Pedido de Inutilização.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nProt" type="TProt" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Número do Protocolo de Status do CT-e. 1 posição (1 – Secretaria de Fazenda Estadual , 3 - SEFAZ Virtual RS, 5 - SEFAZ Virtual SP); 2 - código da UF - 2 posições ano; 10 seqüencial no ano.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Id" type="xs:ID" use="optional"/>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerInutCTe" use="required"/>
	</xs:complexType>
	<xs:complexType name="TProcInutCTe">
		<xs:annotation>
			<xs:documentation>Tipo Pedido de inutilzação de númeração de CT-e processado</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="inutCTe" type="TInutCTe"/>
			<xs:element name="retInutCTe" type="TRetInutCTe"/>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerInutCTe" use="required"/>
		<xs:attribute name="ipTransmissor" type="TIPv4" use="optional">
			<xs:annotation>
				<xs:documentation>IP do transmissor do documento fiscal para o ambiente autorizador</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="nPortaCon" use="optional">
			<xs:annotation>
				<xs:documentation>Porta de origem utilizada na conexão (De 0 a 65535)</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9]{1,5}"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="dhConexao" type="TDateTimeUTC" use="optional">
			<xs:annotation>
				<xs:documentation>Data e Hora da Conexão de Origem</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<xs:simpleType name="TVerInutCTe">
		<xs:annotation>
			<xs:documentation> Tipo Versão Inutilização de numeração de CT-e</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="3\.00"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
