<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by <EMAIL> (PROCERGS) -->
<xs:schema xmlns="http://www.portalfiscal.inf.br/cte" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="tiposGeralCTe_v4.00.xsd"/>
	<xs:include schemaLocation="cteTiposBasico_v4.00.xsd"/>
	<xs:complexType name="TEnderFer">
		<xs:annotation>
			<xs:documentation>Tipo Dados do Endereço</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="xLgr">
				<xs:annotation>
					<xs:documentation>Logradouro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="255"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="nro" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Número</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xCpl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Complemento</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xBairro" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Bairro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cMun" type="TCodMunIBGE">
				<xs:annotation>
					<xs:documentation>Código do município</xs:documentation>
					<xs:documentation> Utilizar a tabela do IBGE
					Informar 9999999 para operações com o exterior.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMun">
				<xs:annotation>
					<xs:documentation>Nome do município</xs:documentation>
					<xs:documentation>Informar EXTERIOR para operações com o exterior.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="CEP">
				<xs:annotation>
					<xs:documentation>CEP</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{8}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UF" type="TUf">
				<xs:annotation>
					<xs:documentation>Sigla da UF</xs:documentation>
					<xs:documentation>Informar EX para operações com o exterior.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="ferrov">
		<xs:annotation>
			<xs:documentation>Informações do modal Ferroviário</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="tpTraf">
					<xs:annotation>
						<xs:documentation>Tipo de Tráfego</xs:documentation>
						<xs:documentation>Preencher com:
						0-Próprio;
						1-Mútuo;
						2-Rodoferroviário;
						3-Rodoviário.</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:whiteSpace value="preserve"/>
							<xs:enumeration value="0"/>
							<xs:enumeration value="1"/>
							<xs:enumeration value="2"/>
							<xs:enumeration value="3"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="trafMut" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Detalhamento de informações para o tráfego mútuo</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="respFat">
								<xs:annotation>
									<xs:documentation>Responsável pelo Faturamento</xs:documentation>
									<xs:documentation>Preencher com: 
									1-Ferrovia de origem; 
									2-Ferrovia de destino</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="preserve"/>
										<xs:enumeration value="1"/>
										<xs:enumeration value="2"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="ferrEmi">
								<xs:annotation>
									<xs:documentation>Ferrovia Emitente do CTe</xs:documentation>
									<xs:documentation>Preencher com: 
									1-Ferrovia de origem; 
									2-Ferrovia de destino</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="preserve"/>
										<xs:enumeration value="1"/>
										<xs:enumeration value="2"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="vFrete" type="TDec_1302">
								<xs:annotation>
									<xs:documentation>Valor do Frete do Tráfego Mútuo</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="chCTeFerroOrigem" type="TChDFe" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Chave de acesso do CT-e emitido pelo ferrovia de origem</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="ferroEnv" minOccurs="0" maxOccurs="unbounded">
								<xs:annotation>
									<xs:documentation>Informações das Ferrovias Envolvidas</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="CNPJ" type="TCnpj">
											<xs:annotation>
												<xs:documentation>Número do CNPJ</xs:documentation>
												<xs:documentation>Informar o CNPJ da Ferrovia Envolvida. Caso a Ferrovia envolvida não seja inscrita no CNPJ o campo deverá preenchido com zeros.
Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="cInt" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Código interno da Ferrovia envolvida</xs:documentation>
												<xs:documentation>Uso da transportadora</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TString">
													<xs:minLength value="1"/>
													<xs:maxLength value="10"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="IE" type="TIe" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Inscrição Estadual</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="xNome">
											<xs:annotation>
												<xs:documentation>Razão Social ou Nome</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TString">
													<xs:maxLength value="60"/>
													<xs:minLength value="2"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="enderFerro" type="TEnderFer">
											<xs:annotation>
												<xs:documentation>Dados do endereço da ferrovia envolvida</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="fluxo">
					<xs:annotation>
						<xs:documentation>Fluxo Ferroviário</xs:documentation>
						<xs:documentation>Trata-se de um número identificador do contrato firmado com o cliente </xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="TString">
							<xs:minLength value="1"/>
							<xs:maxLength value="10"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
