/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.integracao.creditoonlinesantender.seguranca;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.SignatureException;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import org.apache.commons.codec.binary.Base64;

/**
 *
 * <AUTHOR>
 */
public class GerarAssinatura {
    
    public static String GerarAssinatura(String systemAcronym, String nonce, String timestamp) throws Exception {  
        byte[] pemContent = lerArquivoPem(GerarAssinatura.class.getResource("../transvip.pem").getPath().replace("%20", " "));
		
        KeyFactory rsaFact = KeyFactory.getInstance("RSA");
        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) rsaFact.generatePrivate(new PKCS8EncodedKeySpec(pemContent));

        String signature = assinar(rsaPrivateKey, systemAcronym + "#" + timestamp + "#" + nonce);
//        System.out.println(systemAcronym + "#" + timestamp + "#" + nonce);
        return signature;

//        SignatureRequest hubRequest = new SignatureRequest(systemAcronym, timestamp, nonce, signature);
//        System.out.println(hubRequest);
//        return hubRequest;
    } 

    public static String assinar(PrivateKey privateKey, String message) 
            throws NoSuchAlgorithmException, InvalidKeyException, SignatureException, UnsupportedEncodingException {
        Signature sign = Signature.getInstance("SHA256withRSA");
        sign.initSign(privateKey);
        sign.update(message.getBytes("UTF-8"));
        return new String(Base64.encodeBase64(sign.sign()), "UTF-8");
    }

    public static byte[] lerArquivoPem(String pathToPem) throws IOException {
        String pemContent = "";
        BufferedReader br = new BufferedReader(new FileReader(pathToPem));
        String line;
        while ((line = br.readLine()) != null) {
            pemContent += line.trim();
        }
        br.close();
        pemContent = pemContent.replace("-----BEGIN PRIVATE KEY-----", "");
        pemContent = pemContent.replace("-----END PRIVATE KEY-----", "");
        return Base64.decodeBase64(pemContent);
    }

}
