/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.importacao.gtve;

import java.util.Set;
import javax.xml.namespace.QName;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPException;
import javax.xml.soap.SOAPMessage;
import javax.xml.ws.handler.MessageContext;
import javax.xml.ws.handler.soap.SOAPHandler;
import javax.xml.ws.handler.soap.SOAPMessageContext;

/**
 *
 * <AUTHOR>
 */
public class GTVeNamespaceMapper implements SOAPHandler<SOAPMessageContext> {

    @Override
    public Set<QName> getHeaders() {
        return null;
    }

    @Override
    public boolean handleMessage(SOAPMessageContext context) {
        final Boolean outbound = (Boolean) context.get(MessageContext.MESSAGE_OUTBOUND_PROPERTY);
        // only process outbound messages
        if (outbound) {
            try {
                final SOAPMessage soapMessage = context.getMessage();
                final SOAPEnvelope soapEnvelope = soapMessage.getSOAPPart().getEnvelope();
                final SOAPBody soapBody = soapMessage.getSOAPBody();

                // STEP 1: add new prefix/namespace entries  xmlns:="" xmlns:=""
                soapEnvelope.addNamespaceDeclaration("xsd", "http://www.w3.org/2001/XMLSchema");
                soapEnvelope.addNamespaceDeclaration("xsi", "http://www.w3.org/2001/XMLSchema-instance");
//                soapEnvelope.addNamespaceDeclaration("FOO-1", "http://foo1.bar.com/ns");

                // STEP 2: set desired namespace prefixes
                // set desired namespace prefix for the envelope, header and body
                soapEnvelope.setPrefix("soap12");
                soapBody.setPrefix("soap12");

                // STEP 3: remove prefix/namespace entries entries added by JAX-WS
                soapEnvelope.removeNamespaceDeclaration("S");
                soapEnvelope.removeNamespaceDeclaration("env");
                soapBody.removeAttribute("xmlns"); //xmlns="http://www.portalfiscal.inf.br/cte/wsdl/CTeRecepcaoGTVe"

                soapMessage.getSOAPHeader().detachNode();
//                
//                NodeList credentials = soapMessage
//                                        .getSOAPBody()
//                                        .getElementsByTagNameNS("http://www.portalfiscal.inf.br/cte/wsdl/CTeRecepcaoGTVe", "cteDadosMsg");
//
//                int len = credentials.getLength();
//                for(int i = 0; i < len; i++) {
//                    System.out.println(credentials.item(i).getTextContent());
//                    credentials.item(i).setTextContent("new credential content goes here...");
//                }

                
                // IMPORTANT! "Save" the changes
                soapMessage.saveChanges();
            } catch (SOAPException e) {
                // handle the error
            }
        }

        return true;
    }

    @Override
    public boolean handleFault(SOAPMessageContext context) {
        return true;
    }

    @Override
    public void close(MessageContext context) {
    }
}
