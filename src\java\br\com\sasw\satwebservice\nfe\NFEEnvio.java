/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.nfe;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import br.com.sasw.pacotesuteis.sasbeans.XMLNFE;
import br.com.sasw.pacotesuteis.sasdaos.XMLNFEDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.satwebservice.nfe.deserializers.NFeEnvioDeserializer;
import br.com.sasw.satwebservice.nfe.exception.NFeException;
import br.com.sasw.satwebservice.nfe.models.EnvioModel;
import br.com.sasw.satwebservice.nfe.models.ProtocoloModel;
import br.com.sasw.satwebservice.nfe.models.RejeicaoModel;
import br.com.sasw.satwebservice.nfe.models.RetornoModel;
import br.com.sasw.satwebservice.nfe.serializer.RetornoSerializer;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.Response;

/**
 *
 * <AUTHOR>
 */
public class NFEEnvio {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;

    public NFEEnvio() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\NFe\\"
                + getDataAtual("SQL") + "\\erro.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    public Response envio(String input) {

        Gson gsonRetorno = new GsonBuilder()
                .registerTypeAdapter(RetornoModel.class, new RetornoSerializer())
                .create();
        Gson gsonNFe = new GsonBuilder()
                .registerTypeAdapter(EnvioModel.class, new NFeEnvioDeserializer())
                .create();

        Map retorno = new HashMap<>();
        List processamentos = new ArrayList<>();
        try {
//             Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao("SATEXCEL");
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido");
            }

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\NFe\\"
                    + getDataAtual("SQL") + "\\envio.txt";

            this.logerro.Grava(input, this.caminho);

            JsonElement inputJson = new JsonParser().parse(input).getAsJsonObject().get("NFE");
            EnvioModel nfe;
            ProtocoloModel protocolo;
            XMLNFEDao xmlNFEDao = new XMLNFEDao();
            XMLNFE xmlNFE;
            LocalDateTime datahemi;

            if (inputJson instanceof JsonArray) {
                JsonArray nfeJsonArray = inputJson.getAsJsonArray();
                JsonObject nfeJsonObectj;
                for (int i = 0; i < nfeJsonArray.size(); i++) {

                    nfeJsonObectj = nfeJsonArray.get(i).getAsJsonObject();
                    try {

                        nfe = gsonNFe.fromJson(nfeJsonObectj, EnvioModel.class);

                        switch (nfe.getAcao()) {
                            case 1: // inclusão
                                xmlNFE = new XMLNFE();

                                xmlNFE.setTOKEN(null);
                                xmlNFE.setCNPJ(nfe.getCnpjemissor());
                                xmlNFE.setPraca("0");
                                xmlNFE.setSerie(String.valueOf(nfe.getSerie()));
                                xmlNFE.setNumero(nfe.getnRPS());
                                xmlNFE.setUF(nfe.getUfemissor());

                                datahemi = LocalDateTime.parse(nfe.getDatahemi(), DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX"));

                                xmlNFE.setDt_Nota(datahemi.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                xmlNFE.setHr_Nota(datahemi.format(DateTimeFormatter.ofPattern("HH:mm")));
                                xmlNFE.setXML_Envio(nfeJsonObectj.toString()); // JSON de envio
                                xmlNFE.setXML_Retorno(null);
                                xmlNFE.setChaveNFE(null);
                                xmlNFE.setProtocolo(null);
                                xmlNFE.setStatus("0");
                                xmlNFE.setDt_Envio(null);
                                xmlNFE.setHr_Envio(null);
                                xmlNFE.setDt_Retorno(null);
                                xmlNFE.setHr_Retorno(null);

                                if (xmlNFEDao.existeNFCeXMLNFE(nfe.getnRPS(), nfe.getSerie(), nfe.getCnpjemissor(), persistencia)) {
                                    this.logerro.Grava("RPS já cadastrado, rps: " + nfe.getnRPS() + ", "
                                            + "serie: " + nfe.getSerie() + ", "
                                            + "cnpj: " + nfe.getCnpjemissor(), this.caminho);
                                    throw new NFeException(new NFeException.NFeError(nfeJsonObectj, new RejeicaoModel("RPS já cadastrado")));
                                }

                                try {
                                    xmlNFEDao.inserirXMLNFE(xmlNFE, persistencia);
                                    xmlNFE = xmlNFEDao.buscarXMLNFE(nfe.getnRPS(), nfe.getSerie(), nfe.getCnpjemissor(), persistencia);
                                    if (xmlNFE == null) {
                                        throw new Exception("Erro insert");
                                    }
                                    if (xmlNFE.getTOKEN() == null) {
                                        throw new Exception("Token null");
                                    }
                                } catch (Exception e) {
                                    this.logerro.Grava(e.getMessage(), this.caminho);
                                    throw new NFeException(new NFeException.NFeError(nfeJsonObectj, new RejeicaoModel("Erro interno")));
                                }

                                this.logerro.Grava("RPS cadastrado, chave: " + xmlNFE.getTOKEN(), this.caminho);
                                processamentos.add(new RetornoModel(nfe, 3, xmlNFE.getTOKEN()));
                                break;
                            case 2: // cancelamento

                                protocolo = new ProtocoloModel();
                                protocolo.setNumeronfe("numeronfe");
                                protocolo.setSerie("serie");
                                protocolo.setQRCode("QRCode");
                                protocolo.setToken("token");
                                protocolo.setData("data");
                                protocolo.setLinknfe("linknfe");

//                                throw new NFeException(new NFeException.NFeError(nfeJsonObectj, 3, protocolo));
                        }

                    } catch (NFeException nfeException) {
                        processamentos.add(new RetornoModel(nfeException, nfeJsonObectj));
                    }
                }
            }

            retorno.put("NFE", processamentos);

        } catch (Exception e) {
            // Salvando em log o que foi mandado
            retorno.clear();
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);
        } finally {

            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gsonRetorno.toJson(retorno))
                    .build();
        }
    }
}
