/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.service;

import br.com.sasw.satwebservice.bradesconovo.dao.BradescoCertificateDao;
import br.com.sasw.satwebservice.bradesconovo.dao.entity.BradescoCertificateEntity;
import br.com.sasw.satwebservice.bradesconovo.utils.BradescoConstants;
import br.com.sasw.satwebservice.bradesconovo.utils.BradescoUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.nio.charset.StandardCharsets;
import java.security.Signature;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

/**
 *
 * <AUTHOR> Silva
 */
public class BradescoTokenService {
   
    final private BradescoCertificateDao bradescoCertificateDao = new BradescoCertificateDao();
    
    public String getTokenFromEmpresa(String empresa, String ambiente){
        BradescoCertificateEntity bradescoCertificateEntity = 
                bradescoCertificateDao.getCertificateByEmpresa(empresa);
        String privateKeyRaw = BradescoUtils.loadPrivateKeyFromFile(
                bradescoCertificateEntity.getPathCertificado(ambiente));  
        String chaveToken = bradescoCertificateEntity.getChaveToken(ambiente);
        return getToken(privateKeyRaw, chaveToken, ambiente);
    }
    
    public String getToken(String privateKeyRaw, String accessToken, String ambiente) {
        try {
            // Inicializando variáveis
            String privateKeyString = privateKeyRaw;

            // Configurando as variáveis temporais
            long now = System.currentTimeMillis() / 1000L;
            long exp = now + 3600; // 1 hora de expiração
            String nonce = String.valueOf(System.currentTimeMillis());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
            String timestamp = sdf.format(new Date());

            // Construção da assertion JWT
            String assertion;
            assertion = buildAssertion(BradescoConstants.getAPI_AUTENTICACAO_URL(ambiente), 
                    accessToken, now, exp, nonce, privateKeyString);

            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();
            
            HttpPost httpPost = new HttpPost(BradescoConstants.
                    getAPI_AUTENTICACAO_URL(ambiente));

            // Configuração dos headers
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");

            // Corpo do request
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("grant_type", 
                    "urn:ietf:params:oauth:grant-type:jwt-bearer"));
            params.add(new BasicNameValuePair("assertion", assertion));
            httpPost.setEntity(new UrlEncodedFormEntity(params));

            String authToken = null;
            // Executando o request e recebendo a resposta
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());

                // Extrair o token de autenticação da resposta
                authToken = responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(
                        Level.INFO, "ERRO 1:", ex);
            }
            return authToken;

        } catch (Exception ex) {
            Logger.getLogger(BradescoTokenService.class.getName()).log(
                    Level.INFO, "ERRO 2:", ex);
        }
        return null;
    }

    private String buildAssertion(String aud, String sub, long iat, long exp, String jti, String privateKeyString) throws Exception {
        // Construindo o cabeçalho e payload do JWT
        String header = Base64.encodeBase64URLSafeString(
                "{\"alg\":\"RS256\",\"typ\":\"JWT\"}".getBytes(StandardCharsets.UTF_8));

        Map<String, Object> payloadMap = new HashMap<>();
        payloadMap.put("aud", aud);
        payloadMap.put("sub", sub);
        payloadMap.put("iat", iat);
        payloadMap.put("exp", exp);
        payloadMap.put("jti", jti);
        payloadMap.put("ver", "1.1");

        ObjectMapper objectMapper = new ObjectMapper();
        String payload = Base64.encodeBase64URLSafeString(objectMapper.
                writeValueAsString(payloadMap).getBytes(StandardCharsets.UTF_8));

        // Concatenar o header e o payload
        String signingInput = header + "." + payload;

        // Assinar o JWT
        String signature = signRequestText(signingInput, privateKeyString);

        // Retornar o JWT completo
        return signingInput + "." + signature;
    }

    private String signRequestText(String requestText, String rawPrivateKey) 
            throws Exception {

        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(BradescoUtils.formatPrivateKey(rawPrivateKey));
        signature.update(requestText.getBytes(StandardCharsets.UTF_8));

        byte[] signedBytes = signature.sign();
        return Base64.encodeBase64URLSafeString(signedBytes);
    }
}
