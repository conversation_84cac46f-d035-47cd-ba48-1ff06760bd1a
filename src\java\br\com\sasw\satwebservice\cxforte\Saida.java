/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.cxforte;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.CxFGuias;
import SasBeans.CxFSaidas;
import SasBeans.OS_Vig;
import SasBeans.Rt_Perc;
import SasDaos.CxFGuiasDao;
import SasDaos.CxFGuiasVolDao;
import SasDaos.CxFSaidasDao;
import SasDaos.CxForteDao;
import SasDaos.OS_VigDao;
import SasDaos.Rt_PercDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.PreencheEsquerda;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.util.HashMap;
import java.util.Map;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;

/**
 *
 * <AUTHOR>
 */
@Path("/ws-cxforte/saida")
public class Saida {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;
    private final CxFGuiasDao cxFGuiasDao;
    private final Rt_PercDao rt_PercDao;
    private final CxForteDao cxForteDao;
    private final CxFSaidasDao cxFSaidasDao;
    private final CxFGuiasVolDao cxfGuiasVolDao;

    @Context
    private UriInfo context;

    /**
     * Creates a new instance of BuscaGTV
     */
    public Saida() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\CxForte\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        cxFGuiasDao = new CxFGuiasDao();
        rt_PercDao = new Rt_PercDao();
        cxForteDao = new CxForteDao();
        cxFSaidasDao = new CxFSaidasDao();
        cxfGuiasVolDao = new CxFGuiasVolDao();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response post(String input) {
        Gson gson = new GsonBuilder().create();
        Map retorno = new HashMap<>();
        try {
            // Convertendo o input para java
            JsonObject jsonObject = new JsonParser().parse(input).getAsJsonObject();
       
            // Buscando as informações do json
            String param;
            try{
                param = jsonObject.get("param").getAsString().replace(".0", "");
            } catch (Exception ee){
                // Salvando em log o que foi mandado
                this.logerro.Grava(input, this.caminho);
                throw new Exception("param");
            }
            
            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\CxForte\\" + param + "\\"
                    + getDataAtual("SQL") + "\\log.txt";
            
            // Salvando em log o que foi mandado
            this.logerro.Grava(input, this.caminho);
            
            String guia;
            try{
                guia = jsonObject.get("guia").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("guia");
            }
            
            String serie;
            try{
                serie = jsonObject.get("serie").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("serie");
            }
            
            String codFil;
            try{
                codFil = jsonObject.get("codfil").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("codfil");
            }
            
            String rota;
            try{
                rota = jsonObject.get("rota").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("rota");
            }
            
            String seqRota;
            try{
                seqRota = jsonObject.get("seqrota").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("seqrota");
            }
            
            String parada;
            try{
                parada = jsonObject.get("parada").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("parada");
            }
            
            String OS;
            try{
                OS = jsonObject.get("OS").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("OS");
            }
            
            String cliOri;
            try{
                cliOri = jsonObject.get("cliori").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("cliori");
            }
            
            String cliDst;
            try{
                cliDst = jsonObject.get("clidst").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("clidst");
            }
            
            String codCliCxf;
            try{
                codCliCxf = jsonObject.get("codclicxf").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("codclicxf");
            }
            
            String confirmacaoOS;
            try{
                confirmacaoOS = jsonObject.get("confirmacaoOS").getAsString().replace(".0", "");
            } catch (Exception ee){
                confirmacaoOS = null;
            }
            
            String valor;
            try{
                valor = jsonObject.get("valor").getAsString();
            } catch (Exception ee){
                throw new Exception("valor");
            }
            
            String remessa;
            try{
                remessa = jsonObject.get("remessa").getAsString();
            } catch (Exception ee){
                throw new Exception("remessa");
            }
            
            String hora1;
            try{
                hora1 = jsonObject.get("hora1").getAsString();
            } catch (Exception ee){
                throw new Exception("hora1");
            }
            
            String operador;
            try{
                operador = jsonObject.get("operador").getAsString();
            } catch (Exception ee){
                throw new Exception("operador");
            }
            
            persistencia = pool.getConexao(param);
            if(persistencia == null) throw new Exception("persistencia");
            
            String codSrv = "";
            
            
            CxFGuias cxFGuia = cxFGuiasDao.buscarGuia(guia, serie, codFil, persistencia);
            
            if(cxFGuia == null){
                throw new Exception("GuiaNaoCxForte", new Throwable("Guia não está em caixa forte"));
            }
            
            // Log
            System.out.println(cxFGuia.getCliOri().substring(0, 4));
            if (Integer.parseInt(rota) != 90 && Integer.parseInt(rota) != 91 
                    && cxFGuia.getCliOri().substring(0, 4).equals("9997")
                    && param.contains("TRANSVIP")) {
                Rt_Perc pedido = rt_PercDao.buscarPedido(seqRota, parada, persistencia);
                if(pedido == null || (pedido.getParada() <= 0 && pedido.getPedido().intValue() <= 0)){
                    throw new Exception("PedidoNaoRoteirizado", new Throwable("O pedido não está roteirizado. Operação não permitida."));
                }
            }
            
            if (Integer.parseInt(OS) <= 0) {
                OS_VigDao os_VigDao = new OS_VigDao();
                OS_Vig os_Vig = os_VigDao.obterOSTrajeto(cliOri, cliDst, codFil, persistencia);
                
                if(os_Vig == null){
                    if(confirmacaoOS == null){
                        throw new Exception("DestinoSemOS", new Throwable("Não existe OS para esse destino. Confirmar mesmo assim?"));
                    } else {
                        OS = "0";
                        codSrv = "";
                    }
                } else {
                    OS = os_Vig.getOS().replace(".0", "");
                    codSrv = os_Vig.getCodSrv();
                }
            }
            
            cxForteDao.saidaCxForte(valor, codFil, codCliCxf, persistencia);
            cxFSaidasDao.saidaCxFSaidas(codFil, seqRota, valor, remessa, persistencia);
            cxFGuiasDao.saidaCxFGuias(rota, seqRota, remessa, hora1, cliDst, operador, getDataAtual("SQL"),
                    getDataAtual("HORA"), OS, codSrv, guia, serie, codFil, persistencia);
            
            String status = "", cxFVol, valorCxFGuias;
            
            // cxForteSaiTotalizarRemessa
            if(Integer.parseInt(seqRota) > 0){
                CxFSaidas cxFSaidas = cxFSaidasDao.buscarSaida(codFil, seqRota, remessa, persistencia);
                
                if(cxFSaidas == null){
                    cxFSaidasDao.inserirCxFSaidas(codFil, seqRota, remessa, PreencheEsquerda(seqRota, 6, "0") + PreencheEsquerda(remessa, 3, "0"),
                            "0", "0", "0", operador, getDataAtual("SQL"), getDataAtual("HORA"), persistencia);
                
                    CxFGuias cxFGuias = rt_PercDao.buscarCxFGuias(seqRota, remessa, persistencia);
                    if(cxFGuias != null){
                        cxFGuiasDao.atualizarQtdeValor(cxFGuias.getQtde(), cxFGuias.getValor(), codFil, seqRota, remessa, persistencia);
                    }
                
                    String qtdeVolume = rt_PercDao.obterQtdeVolumesRmessa(seqRota, remessa, false, persistencia);
                    if(qtdeVolume != null){
                        cxFSaidasDao.atualizarQtdeVol(qtdeVolume, codFil, seqRota, remessa, persistencia);
                    } else {
                        cxFSaidasDao.atualizarQtdeVol("0", codFil, seqRota, remessa, persistencia);
                    }
                    status = "RemessaEmAberto";
                } else {
                    if(cxFSaidas.getOper_Saida() == null || cxFSaidas.getOper_Saida().equals("")){
                        status = "RemessaEmAberto";
                    } else {
                        status = "RemessaFechada"+cxFSaidas.getDt_Saida()+cxFSaidas.getHr_Saida();
                    }
                }
                
                valorCxFGuias  = rt_PercDao.obterValorCxFGuias(seqRota, remessa, persistencia);
                
                cxFSaidas = cxFSaidasDao.buscarSaida(codFil, seqRota, remessa, persistencia);
                if(cxFSaidas != null){
                    cxFVol = cxFSaidas.getQtdeVol().replace(".0", "");
                    if(Float.parseFloat(cxFSaidas.getQtde()) < 0 
                            || Float.parseFloat(cxFSaidas.getQtdeVol()) < 0){
                        cxFSaidasDao.atualizaCxFSaidas("0", "0", "0", codFil, seqRota, remessa, persistencia);
                    } else {
                        if(Float.parseFloat(cxFSaidas.getValor()) != Float.parseFloat(valorCxFGuias)){
                            cxFSaidasDao.atualizaCxFSaidas(cxFSaidas.getQtde(), valorCxFGuias, codFil, seqRota, remessa, persistencia);
                        }
                    }
                } else {
                    cxFVol = "0";
                }
                
                String qtdeVolume = rt_PercDao.obterQtdeVolumesRmessa(seqRota, remessa, true, persistencia);
                if(qtdeVolume != null){
                    if(Float.parseFloat(qtdeVolume) != Float.parseFloat(cxFVol)){
                        cxFSaidasDao.atualizarQtdeVol(qtdeVolume, codFil, seqRota, remessa, persistencia);
                    } else if(Float.parseFloat(cxFVol) <= 0){
                        cxFSaidasDao.atualizarQtdeVol("0", codFil, seqRota, remessa, persistencia);
                    }
                }
            }
            
            retorno.put("status",status);
            
        } catch (Exception e) {
            retorno = new HashMap();
            retorno.put("erro", e.getMessage());
            if(e.getCause() != null) retorno.put("msg", e.getCause().getMessage());
            this.logerro.Grava("Erro: "+e.getMessage(), this.caminho);
        } finally {
            try {
                persistencia.FechaConexao();
            } catch (Exception p) {
                this.logerro.Grava("Fecha Conexao: " + p.getMessage(), this.caminho);
            }
            
            this.logerro.Grava("Resposta: "+gson.toJson(retorno), this.caminho);
        }

        return Response
                .status(Response.Status.OK)
                .type("application/json")
                .entity(gson.toJson(retorno))
                .build();
    }
}
