/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.rotas.beans;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Volumes {

    private String lacre;
    private String qtvolumes;
    private String valoresLacres;
    private String observacaoLacres;
    private String tipoLacres;

    public String getLacre() {
        return lacre;
    }

    public void setLacre(String lacre) {
        this.lacre = lacre;
    }

    public String getQtvolumes() {
        return qtvolumes;
    }

    public void setQtvolumes(String qtvolumes) {
        this.qtvolumes = qtvolumes;
    }

    public String getValoresLacres() {
        return valoresLacres;
    }

    public void setValoresLacres(String valoresLacres) {
        this.valoresLacres = valoresLacres;
    }

    public String getObservacaoLacres() {
        return observacaoLacres;
    }

    public void setObservacaoLacres(String observacaoLacres) {
        this.observacaoLacres = observacaoLacres;
    }

    public String getTipoLacres() {
        return tipoLacres;
    }

    public void setTipoLacres(String tipoLacres) {
        this.tipoLacres = tipoLacres;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        hash = 29 * hash + Objects.hashCode(this.lacre);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Volumes other = (Volumes) obj;
        if (!Objects.equals(this.lacre, other.lacre)) {
            return false;
        }
        return true;
    }
}
