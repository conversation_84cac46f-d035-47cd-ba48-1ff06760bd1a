/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.service;

import br.com.sasw.satwebservice.bradesconovo.utils.HttpDeleteWithBody;
import br.com.sasw.satwebservice.bradesconovo.dao.BradescoCertificateDao;
import br.com.sasw.satwebservice.bradesconovo.dao.BradescoIntegraDao;
import br.com.sasw.satwebservice.bradesconovo.dao.entity.BradescoCertificateEntity;
import br.com.sasw.satwebservice.bradesconovo.dto.request.CreditosRequest;
import br.com.sasw.satwebservice.bradesconovo.dto.request.RecolhimentosRequest;
import br.com.sasw.satwebservice.bradesconovo.dto.request.SinistroRequest;
import br.com.sasw.satwebservice.bradesconovo.dto.request.TransferenciaRequest;
import static br.com.sasw.satwebservice.bradesconovo.utils.BradescoConstants.*;
import br.com.sasw.satwebservice.bradesconovo.utils.BradescoUtils;
import com.google.gson.Gson;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

/**
 *
 * <AUTHOR> Silva
 */
public class RetornoAtendimentoBancoService {

    final private BradescoTokenService bradescoTokenService = new BradescoTokenService();
    final private BradescoCertificateDao bradescoCertificateDao = new BradescoCertificateDao();
    final private BradescoIntegraDao bradescoIntegraDao = new BradescoIntegraDao();
    private final Gson gson = new Gson();

    private final static String RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_CREDITOS = "/conciliacao-integrada/retorno-atendimento-banco/clientes/cofres/creditos";
    private final static String RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_RECOLHIMENTOS = "/conciliacao-integrada/retorno-atendimento-banco/clientes/cofres/recolhimentos";
    private final static String RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_SINISTROS = "/conciliacao-integrada/retorno-atendimento-banco/clientes/cofres/sinistros";
    private final static String RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_TRANSFERENCIAS = "/conciliacao-integrada/retorno-atendimento-banco/clientes/cofres/transferencias";

    public String getRetornoAntendimentoClienteCofreCredito(String empresa, 
            String token, String cnpjEmpresa, String numeroPedido, 
            String cnpjCliente, String numeroSequencialCliente, 
            String cnpjPontoCliente, String agenciaCredito, String contaCredito, 
            String digitoContaCredito, String dataOperacao, String valorOperacao, 
            String numeroCofreInteligenteOrigem, String numeroOCT, 
            String nomeRemetenteOCT, String query, String ambiente) {
        try {
//Número do pedido
//4 - filial do CNPJ
//4 - nome da empresa
//6 - aammdd
//6 - sequencial para empresa ou filial
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_CREDITOS, null, null, null);
            HttpPost httpPost = new HttpPost(uri);

            String rawRequest = buildCreditosRequest(cnpjEmpresa,
                    bradescoCertificateEntity.getChaveCoin(ambiente),
                    numeroPedido,
                    cnpjCliente,
                    numeroSequencialCliente,
                    cnpjPontoCliente,
                    agenciaCredito,
                    contaCredito,
                    digitoContaCredito,
                    dataOperacao,
                    valorOperacao,
                    numeroCofreInteligenteOrigem,
                    numeroOCT,
                    nomeRemetenteOCT
            );
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_CREDITOS, 
                        query, bradescoCertificateEntity.getBancoDeDados(), "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String deleteRetornoAntendimentoClienteCofreCredito(String empresa, 
            String token, String cnpjEmpresa, String numeroPedido, 
            String cnpjCliente, String numeroSequencialCliente, 
            String cnpjPontoCliente, String agenciaCredito, String contaCredito, 
            String digitoContaCredito, String dataOperacao, String valorOperacao, 
            String numeroCofreInteligenteOrigem, String numeroOCT, 
            String nomeRemetenteOCT, String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_CREDITOS, null, null, null);
            HttpDeleteWithBody httpDeleteWithBody = new HttpDeleteWithBody(uri);

            String rawRequest = buildCreditosRequest(cnpjEmpresa,
                    bradescoCertificateEntity.getChaveCoin(ambiente),
                    numeroPedido,
                    cnpjCliente,
                    numeroSequencialCliente,
                    cnpjPontoCliente,
                    agenciaCredito,
                    contaCredito,
                    digitoContaCredito,
                    dataOperacao,
                    valorOperacao,
                    numeroCofreInteligenteOrigem,
                    numeroOCT,
                    nomeRemetenteOCT
            );
            httpDeleteWithBody.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpDeleteWithBody, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpDeleteWithBody)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_CREDITOS, 
                        query, bradescoCertificateEntity.getBancoDeDados(), "DELETE");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String getRetornoAntendimentoClienteCofreRecolhimento(String empresa, 
            String token, RecolhimentosRequest request, String query, 
            String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_RECOLHIMENTOS, null, null, null);
            HttpPost httpPost = new HttpPost(uri);

            String rawRequest = gson.toJson(request);
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                System.out.println(rawRequest);
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_RECOLHIMENTOS, 
                        rawRequest, bradescoCertificateEntity.getBancoDeDados(), 
                        "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String deleteRetornoAntendimentoClienteCofreRecolhimento(
            String empresa, String token, RecolhimentosRequest request, 
            String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_RECOLHIMENTOS, 
                    null, null, null);
            HttpDeleteWithBody httpDeleteWithBody = new HttpDeleteWithBody(uri);

            String rawRequest = gson.toJson(request);
            httpDeleteWithBody.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpDeleteWithBody, token, privateKey, rawRequest, uri, ambiente);
            System.out.println("AAAA");
            try ( CloseableHttpResponse response = httpClient.execute(httpDeleteWithBody)) {
                System.out.println("TRY");
                String responseBody = EntityUtils.toString(response.getEntity());
                System.out.println(responseBody);
                bradescoIntegraDao.save(responseBody, 
                        RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_RECOLHIMENTOS, 
                        query, bradescoCertificateEntity.getBancoDeDados(), 
                        "DELETE");
                return responseBody;
            } catch (Exception ex) {
                System.out.println("EXXXX");
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }
            System.out.println("RETURN");
            return null;
        } catch (UnsupportedEncodingException ex) {
            System.out.println("EXXXX1");
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            System.out.println("EXXXX1");
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        System.out.println("RETURN2");
        return null;
    }

    public String getConsultaMovimentacaoClienteCofreSinistro(String empresa, 
            String token, String cnpj, String numeroPedido, String dataOperacao, 
            String valorOperacao, String numeroCofreInteligenteOrigem, 
            String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_SINISTROS, null, 
                    null, null);
            HttpPost httpPost = new HttpPost(uri);

            String rawRequest = buildSinistroRequest(cnpj, 
                    bradescoCertificateEntity.getChaveCoin(ambiente),
                    numeroPedido, dataOperacao, valorOperacao, 
                    numeroCofreInteligenteOrigem);
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_SINISTROS, 
                        query, bradescoCertificateEntity.getBancoDeDados(), 
                        "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String deleteConsultaMovimentacaoClienteCofreSinistro(String empresa, 
            String token, String cnpj, String numeroPedido, String dataOperacao, 
            String valorOperacao, String numeroCofreInteligenteOrigem, 
            String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_SINISTROS, null, 
                    null, null);
            HttpDeleteWithBody httpDeleteWithBody = new HttpDeleteWithBody(uri);

            String rawRequest = buildSinistroRequest(cnpj, 
                    bradescoCertificateEntity.getChaveCoin(ambiente),
                    numeroPedido, dataOperacao, valorOperacao, 
                    numeroCofreInteligenteOrigem);
            httpDeleteWithBody.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpDeleteWithBody, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpDeleteWithBody)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_SINISTROS, 
                        query, bradescoCertificateEntity.getBancoDeDados(), 
                        "DELETE");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String getConsultaMovimentacaoClienteCofreTransferencias(String empresa, 
            String token, String cnpjEmpresaDestino, String numeroPedido, 
            String cnpjPontoCliente, String dataOperacao, String valorOperacao, 
            String numeroCofreInteligenteOrigem, 
            String numeroCofreInteligenteDestino, String cnpjEmpresaProcessadora, 
            String cnpjTransportadoraOrigem, String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_TRANSFERENCIAS, 
                    null, null, null);
            HttpPost httpPost = new HttpPost(uri);

            String rawRequest = buildTransferenciaRequest(cnpjEmpresaDestino, 
                    bradescoCertificateEntity.getChaveCoin(ambiente), 
                    numeroPedido, cnpjPontoCliente, dataOperacao,
                    valorOperacao, numeroCofreInteligenteOrigem, 
                    numeroCofreInteligenteDestino, cnpjEmpresaProcessadora, 
                    cnpjTransportadoraOrigem);

            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_TRANSFERENCIAS, 
                        query, bradescoCertificateEntity.getBancoDeDados(), 
                        "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String deleteConsultaMovimentacaoClienteCofreTransferencias(
            String empresa, String token, String cnpjEmpresaDestino, 
            String numeroPedido, String cnpjPontoCliente, String dataOperacao, 
            String valorOperacao, String numeroCofreInteligenteOrigem, 
            String numeroCofreInteligenteDestino, String cnpjEmpresaProcessadora, 
            String cnpjTransportadoraOrigem, String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_TRANSFERENCIAS, 
                    null, null, null);
            HttpDeleteWithBody httpDeleteWithBody = new HttpDeleteWithBody(uri);

            String rawRequest = buildTransferenciaRequest(cnpjEmpresaDestino, 
                    bradescoCertificateEntity.getChaveCoin(ambiente), 
                    numeroPedido, cnpjPontoCliente, dataOperacao,
                    valorOperacao, numeroCofreInteligenteOrigem, 
                    numeroCofreInteligenteDestino, cnpjEmpresaProcessadora, 
                    cnpjTransportadoraOrigem);
            httpDeleteWithBody.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpDeleteWithBody, token, 
                    privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpDeleteWithBody)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        RETORNO_ATERENDIMENTO_BANCO_CLIENTE_COFRES_TRANSFERENCIAS, 
                        query, bradescoCertificateEntity.getBancoDeDados(), 
                        "DELETE");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    private String buildSinistroRequest(String cnpj, String codigoConexaoEmpresa, String numeroPedido, String dataOperacao,
            String valorOperacao, String numeroCofreInteligenteOrigem) {

        SinistroRequest sinistroRequest = new SinistroRequest();
        sinistroRequest.setCnpjEmpresa(cnpj);
        sinistroRequest.setCodigoConexaoEmpresa(codigoConexaoEmpresa);
        sinistroRequest.setNumeroPedido(numeroPedido);
        sinistroRequest.setDataOperacao(dataOperacao);
        sinistroRequest.setValorOperacao(valorOperacao);
        sinistroRequest.setNumeroCofreInteligenteOrigem(valorOperacao);
        return gson.toJson(sinistroRequest);
    }

    private String buildTransferenciaRequest(String cnpjEmpresaDestino, String senhaConexao, String numeroPedido, String cnpjPontoCliente, String dataOperacao,
            String valorOperacao, String numeroCofreInteligenteOrigem, String numeroCofreInteligenteDestino, String cnpjEmpresaProcessadora, String cnpjTransportadoraOrigem) {

        TransferenciaRequest transferenciaRequest = new TransferenciaRequest();
        transferenciaRequest.setCnpjEmpresaDestino(cnpjEmpresaDestino);
        transferenciaRequest.setSenhaConexao(senhaConexao);
        transferenciaRequest.setNumeroPedido(numeroPedido);
        transferenciaRequest.setCnpjPontoCliente(cnpjPontoCliente);
        transferenciaRequest.setDataOperacao(dataOperacao);
        transferenciaRequest.setValorOperacao(valorOperacao);
        transferenciaRequest.setNumeroCofreInteligenteOrigem(numeroCofreInteligenteOrigem);
        transferenciaRequest.setNumeroCofreInteligenteDestino(numeroCofreInteligenteDestino);
        transferenciaRequest.setCnpjEmpresaProcessadora(cnpjEmpresaProcessadora);
        transferenciaRequest.setCnpjTransportadoraOrigem(cnpjTransportadoraOrigem);
        return gson.toJson(transferenciaRequest);
    }

    private String buildCreditosRequest(String cnpjEmpresa, String senhaConexao, String numeroPedido, String cnpjCliente, String numeroSequencialCliente,
            String cnpjPontoCliente, String agenciaCredito, String contaCredito, String digitoContaCredito, String dataOperacao,
            String valorOperacao, String numeroCofreInteligenteOrigem, String numeroOCT, String nomeRemetenteOCT) {

        CreditosRequest creditosRequest = new CreditosRequest();
        creditosRequest.setCnpjEmpresa(cnpjEmpresa);
        creditosRequest.setSenhaConexao(senhaConexao);
        creditosRequest.setNumeroPedido(numeroPedido);
        creditosRequest.setCnpjCliente(cnpjCliente);
        creditosRequest.setNumeroSequencialCliente(numeroSequencialCliente);
        creditosRequest.setCnpjPontoCliente(cnpjPontoCliente);
        creditosRequest.setAgenciaCredito(agenciaCredito);
        creditosRequest.setContaCredito(contaCredito);
        creditosRequest.setDigitoContaCredito(digitoContaCredito);
        creditosRequest.setDataOperacao(dataOperacao);
        creditosRequest.setValorOperacao(valorOperacao);
        creditosRequest.setNumeroCofreInteligenteOrigem(numeroCofreInteligenteOrigem);
        creditosRequest.setNumeroOCT(numeroOCT);
        creditosRequest.setNomeRemetenteOCT(nomeRemetenteOCT);

        return gson.toJson(creditosRequest);
    }

}
