package br.com.sasw.satwebservice.bcb;

import com.google.gson.annotations.SerializedName;

/**
 *
 * <AUTHOR>
 */
public class CotacaoDolarPeriodo {
    
    @SerializedName(value = "cotacaoCompra")
    private double cotacaoCompra;
    @SerializedName(value = "cotacaoVenda")
    private double cotacaoVenda;
    @SerializedName(value = "dataHoraCotacao")
    private String dataHoraCotacao;

    public double getCotacaoCompra() {
        return cotacaoCompra;
    }

    public double getCotacaoVenda() {
        return cotacaoVenda;
    }

    public String getDataHoraCotacao() {
        return dataHoraCotacao;
    }

    public void setCotacaoCompra(double cotacaoCompra) {
        this.cotacaoCompra = cotacaoCompra;
    }

    public void setCotacaoVenda(double cotacaoVenda) {
        this.cotacaoVenda = cotacaoVenda;
    }

    public void setDataHoraCotacao(String dataHoraCotacao) {
        this.dataHoraCotacao = dataHoraCotacao;
    }
    
}
