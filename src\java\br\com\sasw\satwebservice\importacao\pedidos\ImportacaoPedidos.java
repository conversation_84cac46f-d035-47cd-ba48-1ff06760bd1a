/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor
 */
package br.com.sasw.satwebservice.importacao.pedidos;

import br.com.sasw.satwebservice.importacao.clientes.ClienteExceptionSerializer;
import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasDaos.ClientesDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.satwebservice.importacao.exceptions.ClienteException;
import br.com.sasw.satwebservice.importacao.exceptions.ClienteException.ClienteErrorCode;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.internal.StringMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.Response;

/**
 *
 * <AUTHOR>
 */
public class ImportacaoPedidos {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;
    private final ClientesDao clientesdao = new ClientesDao();

    /**
     * Creates a new instance of ImportacaoPedidos
     */
    public ImportacaoPedidos() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }
    
    public Response json(String empresa, String input) {

        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(ClienteException.class, new ClienteExceptionSerializer());
        Gson gson = gsonBuilder.create();
        Map retorno = new HashMap<>(), processamento;
        List processamentos = new ArrayList<>();
        try {
            // Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao(empresa);
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido ("+empresa+")");
            }

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\" + empresa + "\\Pedidos\\"
                    + getDataAtual("SQL") + "\\log.txt";
            
            this.logerro.Grava(input, this.caminho);

            SasBeans.Clientes cliente;
            StringMap clienteJson = new Gson().fromJson(input, StringMap.class);
            Object clienteObject = clienteJson.get("clientes");
            if (clienteObject instanceof ArrayList) {
                List<StringMap> clientList = (ArrayList) clienteObject;
                retorno.put("clientesArchivo", clientList.size());
                for (StringMap clientObject : clientList) {
                    processamento = new HashMap<>();
//                    try{
//                        cliente = obterCliente(clientObject);
                      processamento.put("idpuntoservicio", "OUT");
//                        
//                        validarCliente(cliente);
//                        importarClientes(cliente, this.persistencia);
//                    } catch(ClienteException cliExc){
//                        System.out.println(cliExc.getMessage());
//                        System.out.println(cliExc.getCode().getCode());
//                        System.out.println(cliExc.getCode().getStatus());
//                        processamento.put("result", cliExc);
//                    }
                    processamentos.add(processamento);
                }
            } else if (clienteObject instanceof StringMap) {
                StringMap clientObject = (StringMap) ((StringMap) clienteObject).get("clientes");
                retorno.put("clientesArchivo", 1);
                processamento = new HashMap<>();
//                try{
//                    cliente = obterCliente(clientObject);
                    processamento.put("idpuntoservicio", "OUT");
//
//                    validarCliente(cliente);
//                    importarClientes(cliente, this.persistencia);
//                } catch(ClienteException cliExc){
//                    System.out.println(cliExc.getMessage());
//                    System.out.println(cliExc.getCode().getCode());
//                    System.out.println(cliExc.getCode().getStatus());
//                    processamento.put("result", cliExc);
//                }
                processamentos.add(processamento);
            }
            
            retorno.put("status", "Error");
            retorno.put("resp", processamentos);

        } catch (Exception e) {
            // Salvando em log o que foi mandado
            retorno.clear();
            retorno.put("status", "error");
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);
        } finally {
            this.logerro.Grava(gson.toJson(retorno), this.caminho);
//            return gson.toJson(retorno);

            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gson.toJson(retorno))
                    .build();
        }
    }
    
    public void importarClientes(SasBeans.Clientes cliente, Persistencia persistencia) throws ClienteException{
        
        boolean sucessoBusca = false, sucessoAtualizacao = false, sucessoInsercao = false;
        SasBeans.Clientes aux = null;
        try {
            aux = this.clientesdao.buscarCliente(cliente.getInterfExt(), cliente.getCodFil().toPlainString(), persistencia);
            sucessoBusca = true;
        } catch (Exception e) {
            this.logerro.Grava(e.getMessage(), this.caminho);
        } 
        
         // Ocorreu algum erro na consulta, que foi devidamente registrado em log
        if(!sucessoBusca){
            throw new ClienteException(new ClienteErrorCode(0));
        } else {
            
            // Encontrou algum cliente pelo idpuntoservico
            if(aux != null){
                
                // Se já existir cliente já cadastrado, não processar inclusão
                if(aux.getSituacao().equals("A")){
                    throw new ClienteException(new ClienteErrorCode(6));
                } else {
                    
                    // solicitação de inclusão de cliente inativo
                    try{
                        cliente.setCodigo(aux.getCodigo());
                        this.clientesdao.atualizar(cliente, persistencia);
                        sucessoAtualizacao = true;
                    } catch (Exception e){
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                    
                    if(sucessoAtualizacao){
                        throw new ClienteException(new ClienteErrorCode(3));
                    } else {
                        throw new ClienteException(new ClienteErrorCode(0));
                    }
                }
            } else {
                
                try{
                    cliente.setCodCli(this.clientesdao.getCodCliImportacaoSPM(cliente.getCodFil().toPlainString(), cliente.getBanco(), persistencia));
                    cliente.setCodigo(cliente.getBanco()+cliente.getTpCli()+cliente.getCodCli());
                    this.clientesdao.inserir(cliente, persistencia);
                    sucessoInsercao = true;
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
                
                if(sucessoInsercao){
                    throw new ClienteException(new ClienteErrorCode(1));
                } else {
                    throw new ClienteException(new ClienteErrorCode(0));
                }
            }
        }
          
    }
    
    private SasBeans.Clientes validarCliente(SasBeans.Clientes cliente) throws ClienteException{
         
        /** 
         * cliente.accion
         * Integer
         * 1 - inclusão; 3 - inativação
        */
        if(cliente.getRamoAtiv() == null
                || (!cliente.getRamoAtiv().equals("1") && !cliente.getRamoAtiv().equals("3"))){
            throw new ClienteException("accion", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.sucursal
         * Integer
         */
        try{
            if(cliente.getCodFil() == null) throw new Exception();
            Integer.parseInt(cliente.getCodFil().toBigInteger().toString());
        } catch(Exception e){
            throw new ClienteException("sucursal", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.codigo
         * String(7)
         */
        try{
            if(cliente.getCodExt() == null || cliente.getCodExt().equals("") || cliente.getCodExt().length() > 7) throw new Exception();
            Integer.parseInt(cliente.getCodExt(), 16);
        } catch(Exception e){
            throw new ClienteException("codigo", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.idpuntoservicio
         * String(80)
         */
        try{
            if(cliente.getInterfExt().length() > 80) throw new Exception();
        } catch(Exception e){
            throw new ClienteException("idpuntoservicio", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.unidadcli
         * String(6)
         */
        try{
            if(cliente.getAgencia().length() > 6) throw new Exception();
        } catch(Exception e){
            throw new ClienteException("unidadcli", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.subunidad
         * String(3)
         */
        try{
            if(cliente.getSubAgencia().length() > 3) throw new Exception();
        } catch(Exception e){
            throw new ClienteException("subunidad", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.tipo
         * String(1)
         */
        if (cliente.getTpCli() == null || cliente.getTpCli().length() != 1
                || (!cliente.getTpCli().equals("0")
                && !cliente.getTpCli().equals("3")
                && !cliente.getTpCli().equals("5")
                && !cliente.getTpCli().equals("6")
                && !cliente.getTpCli().equals("8")
                && !cliente.getTpCli().equals("9"))) {
            throw new ClienteException("tipo", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.registrogob 
         * String(1)
         */
        if (cliente.getCGC().length() > 14) {
            throw new ClienteException("registrogob", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.cp 
         * String(8)
         */
        if (cliente.getCEP().length() > 8) {
            throw new ClienteException("cp", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.direccion
         * String(60)
         */
        if (cliente.getEnde().length() > 60) {
            throw new ClienteException("direccion", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.barrio 
         * String(25)
         */
        if (cliente.getBairro().length() > 25){
            throw new ClienteException("barrio", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.ciudad 
         * String(25)
         */
        if (cliente.getCidade().length() > 25) {
            throw new ClienteException("ciudad", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.estado 
         * String(2)
         */
        if (cliente.getEstado().length() > 2) {
            throw new ClienteException("estado", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.email
         * String(80)
         */
        if (cliente.getEmail().length() > 80) {
            throw new ClienteException("email", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.registrogob 
         * String(11)
         */
        if (cliente.getFone1().length() > 11) {
            throw new ClienteException("telefono1", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.telefono2
         * String(11)
         */
        if (cliente.getFone2().length() > 11) {
            throw new ClienteException("telefono2", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.latitud 
         * String(20)
         */
        if (cliente.getLatitude().length() > 20) {
            throw new ClienteException("latitud", new ClienteErrorCode(9));
        }
        
        /**
         * cliente.longitud  
         * String(20)
         */
        if (cliente.getLongitude().length() > 20) {
            throw new ClienteException("longitud", new ClienteErrorCode(9));
        }
         
        return cliente;
    }
//    
//    private SasBeans.Pedido obterPedido(StringMap stringMap){
//        SasBeans.Pedido pedido = new SasBeans.Pedido();
//numero, codfil, regiao1, regiao2, data, tipo, codcli1, nred1,  hora1o, hora2o, codcli2, nred2
//        hora1d, hora2d, solicitante, valor, obs, classifsrv, operincl, dt_incl, hr_incl,
//        os, operador, dt_alter, hr_alter, situacao, PedidoCliente, flag_excl, HrOper,
//        ChequesQtde,ChequesValor, TicketsQtde, TicketsValor,
//        
//        
//        // dados[0].toUpperCase() // cliente.accion
//        cliente.setRamoAtiv(stringMap.get("accion").toString().replace(".0","")); // cliente.accion
//        
//        cliente.setCodFil(stringMap.get("sucursal").toString()); // cliente.sucursal
//        cliente.setBanco("777");
//        cliente.setTpCli(stringMap.get("tipo").toString()); // cliente.tipo
//        cliente.setSituacao("A");
//        cliente.setInterfExt(stringMap.getOrDefault("idpuntoservicio", "").toString()); // cliente.idpuntoservicio
//        cliente.setAgencia(stringMap.getOrDefault("unidadcli", "000001").toString()); // cliente.unidadcli
//        cliente.setNRed(RecortaString(stringMap.get("razonsocial").toString().toUpperCase(), 0, 20)); // cliente.razonsocial
//        cliente.setNome(RecortaString(stringMap.get("razonsocial").toString().toUpperCase(), 0, 60)); // client.razonsocial
//        cliente.setEnde(stringMap.getOrDefault("direccion", "").toString().toUpperCase()); // cliente.direccion
//        cliente.setBairro(stringMap.getOrDefault("barrio", "").toString().toUpperCase()); // cliente.barrio
//        cliente.setCidade(stringMap.getOrDefault("ciudad", "").toString().toUpperCase()); // cliente.ciudad
//        cliente.setCodExt(RecortaString(stringMap.getOrDefault("codigo", "").toString().toUpperCase(), 0 ,10)); // cliente.codigo
//        cliente.setCodPtoCli(RecortaString(stringMap.getOrDefault("codigo", "").toString().toUpperCase(), 0 ,10)); // cliente.codigo
//        cliente.setRisco("GR2");
//        cliente.setDtSituacao(LocalDate.now());
//        cliente.setDt_Cad(LocalDate.now());
//        cliente.setFone1(stringMap.getOrDefault("telefono1", "").toString().toUpperCase()); // cliente.telefono1
//        cliente.setFone2(stringMap.getOrDefault("telefono2", "").toString().toUpperCase()); // cliente.telefono2
//                    
//        cliente.setSubAgencia(stringMap.getOrDefault("subunidad", "").toString().toUpperCase()); // client.subunidad
//        cliente.setCGC(stringMap.getOrDefault("registrogob", "").toString().toUpperCase()); // cliente.registrogob
//        cliente.setCEP(stringMap.getOrDefault("cp", "").toString().toUpperCase()); // cliente.cp
//        cliente.setEstado(stringMap.getOrDefault("estado", "").toString().toUpperCase()); // cliente.estado
//        cliente.setEmail(stringMap.getOrDefault("email", "").toString()); // cliente.email
//        cliente.setLatitude(stringMap.getOrDefault("latitud", "").toString().toUpperCase()); // Latitud
//        cliente.setLongitude(stringMap.getOrDefault("longitud", "").toString().toUpperCase()); // Longitud
//
//        cliente.setDiaFechaFat(1);
//        cliente.setDiaVencNF(1);
//        cliente.setRegiao("999");
//        cliente.setOper_Inc(FuncoesString.RecortaAteEspaço("SatWebService", 0, 10));
//        cliente.setDt_Alter(LocalDate.now());
//        cliente.setHr_Alter(getDataAtual("HORA"));
//        
//        return cliente;
//    }

}
