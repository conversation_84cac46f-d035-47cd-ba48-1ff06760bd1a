/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.importacao.funcionarios;

import br.com.sasw.satwebservice.importacao.exceptions.FuncionarioException;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import java.lang.reflect.Type;

/**
 *
 * <AUTHOR>
 */
public class FuncionarioExceptionSerializer implements JsonSerializer<FuncionarioException>{

    @Override
    public JsonElement serialize(FuncionarioException t, Type type, JsonSerializationContext jsc) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.add("code", new JsonPrimitive(t.getCode().getCode()));
        jsonObject.add("message", new JsonPrimitive(t.getCode().getStatus()));
        if(t.getMessage() != null && !t.getMessage().equals("")) jsonObject.add("field", new JsonPrimitive(t.getMessage()));
        return jsonObject;
    }

}
