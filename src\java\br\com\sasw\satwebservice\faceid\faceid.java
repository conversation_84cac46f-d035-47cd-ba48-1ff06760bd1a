/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.faceid;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasDaos.EscalaDao;
import SasDaos.PessoaDao;
import SasLibrary.ValidarUsuario;
import br.com.sasw.pacotesuteis.sasbeans.PessoaDet;
import br.com.sasw.pacotesuteis.sasdaos.PessoaDetDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import com.facephi.sdk.licensing.LicenseActivationException;
import com.facephi.sdk.matcher.AuthenticationResult;
import com.facephi.sdk.matcher.MatcherException;
import java.util.Base64;
import java.util.List;
import javax.jws.WebParam;
import javax.jws.WebService;

/**
 *
 * <AUTHOR>
 */
@WebService(serviceName = "faceid")
public class faceid {

    private final ArquivoLog logerro;
    private SasPoolPersistencia pool;
    private String caminho;

    public faceid() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
        logerro = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\FaceID\\erros\\" + getDataAtual("SQL") + ".txt";
    }

    /**
     * Cria usuário em PessoaDet a partir de uma matrícula
     *
     * @param param
     * @param matr
     * @param pwweb
     * @param template
     * @return
     * @throws Exception
     */
    public Login criarUsuarioSatMobEW(@WebParam(name = "param") String param,
            @WebParam(name = "matr") String matr,
            @WebParam(name = "pwweb") String pwweb,
            @WebParam(name = "template") String template) throws Exception {

        Persistencia persistencia = pool.getConexao(param);

        if (persistencia == null) {
            this.logerro.Grava("criarUsuarioSatMobEW\r\nErro de parâmetro: " + param, this.caminho);
            throw new Exception("Erro de parâmetro.");
        }

        // Atualizando o caminho dos logs
        this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\FaceID\\"
                + param + "\\" + getDataAtual("SQL") + "\\" + matr + ".txt";

        this.logerro.Grava("criarUsuarioSatMobEW\r\n"
                + logParams(new String[]{
            "param:    " + param,
            "matr:     " + matr,
            "pwweb:    " + pwweb,
            "template: " + template
        }), this.caminho);
        
        if (matr == null || matr.isEmpty()) {
            this.logerro.Grava("criarUsuarioSatMobEW\r\nMatr can't be null", this.caminho);
            throw new Exception("Matr can't be null");
        }

        if (pwweb == null || pwweb.isEmpty()) {
            this.logerro.Grava("criarUsuarioSatMobEW\r\nPWWEb can't be null", this.caminho);
            throw new Exception("PWWEb can't be null");
        }

        if (template == null || template.isEmpty()) {
            this.logerro.Grava("criarUsuarioSatMobEW\r\nTemplate can't be null", this.caminho);
            throw new Exception("Template can't be null");
        }

        byte[] templateArray = null;
        try {
            templateArray = Base64.getDecoder().decode(template);
        } catch (Exception e) {
            this.logerro.Grava("criarUsuarioSatMobEW\r\nTemplate isn't a stringBase64", this.caminho);
            throw new Exception("Template isn't a stringBase64");
        }

        if (ValidarUsuario.ValidaUsuarioEW(matr, "", pwweb, persistencia)) {

            byte[] structure = null;

            try {
                structure = MatcherWrapper.CreateUser(templateArray);
            } catch (MatcherException e) {
                this.logerro.Grava("criarUsuarioSatMobEW\r\nMatcherException\r\n" + e.getExceptionType(), this.caminho);
                throw new Exception(e.getMessage());
            } catch (LicenseActivationException e) {
                this.logerro.Grava("criarUsuarioSatMobEW\r\nLicenseActivationException\r\n" + e.getExceptionType(), this.caminho);
                throw new Exception(e.getMessage());
            } catch (Exception e) {
                this.logerro.Grava("criarUsuarioSatMobEW\r\nMatcherWrapper.CreateUser\r\n" + e.getMessage(), this.caminho);
                throw new Exception(e.getMessage());
            }

            if (structure == null) {
                this.logerro.Grava("criarUsuarioSatMobEW\r\nInternal matcher error", this.caminho);
                throw new Exception("Internal matcher error");
            }

            try {
                PessoaDao pessoaDao = new PessoaDao();
                String codigo = pessoaDao.getCodigo(matr, persistencia);

                PessoaDet pessoaDet = new PessoaDet();
                pessoaDet.setCodigo(codigo);
                pessoaDet.setFaceID(new String(structure));
                pessoaDet.setOperador("SatWS");
                pessoaDet.setDt_alter(getDataAtual("SQL"));
                pessoaDet.setHr_Alter(getDataAtual("HORA"));

                PessoaDetDao pessoaDetDao = new PessoaDetDao();
                pessoaDetDao.inserirPessoaDet(pessoaDet, persistencia);

                // Validando o insert
                pessoaDet = pessoaDetDao.buscarPessoaDet(codigo, persistencia);
                if (pessoaDet == null) {
                    return null;
                }

                Login login = new Login();
                login.setNome(pessoaDet.getNome());
                login.setCodigo(pessoaDet.getCodigo().replace(".0", ""));
                login.setMatr(pessoaDet.getMatr().replace(".0", ""));
                login.setPwweb(pessoaDet.getPWWeb());
                return login;
            } catch (Exception e) {
                this.logerro.Grava(e.getMessage(), this.caminho);
                throw new Exception("criarUsuarioSatMobEW\r\nDatabase matcher error");
            }

        } else {
            this.logerro.Grava("criarUsuarioSatMobEW\r\nUsuário ou senha incorreta", this.caminho);
            throw new Exception("Usuário ou senha incorreta");
        }
    }

    /**
     * Cria usuário em PessoaDet a partir do código de pessoa
     *
     * @param param
     * @param codigo
     * @param pwweb
     * @param template
     * @return
     * @throws Exception
     */
    public Login criarUsuarioSatelliteMobile(@WebParam(name = "param") String param,
            @WebParam(name = "codigo") String codigo,
            @WebParam(name = "pwweb") String pwweb,
            @WebParam(name = "template") String template) throws Exception {
        
        Persistencia persistencia = pool.getConexao(param);

        if (persistencia == null) {
            this.logerro.Grava("Erro de parâmetro.", this.caminho);
            throw new Exception("Erro de parâmetro.");
        }

        // Atualizando o caminho dos logs
        this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\FaceID\\"
                + param + "\\" + getDataAtual("SQL") + "\\" + codigo + ".txt";

        this.logerro.Grava("criarUsuarioSatelliteMobile\r\n"
                + logParams(new String[]{
            "param:    " + param,
            "codigo:   " + codigo,
            "pwweb:    " + pwweb,
            "template: " + template
        }), this.caminho);

        if (codigo == null || codigo.isEmpty()) {
            this.logerro.Grava("criarUsuarioSatelliteMobile\r\nCodigo can't be null", this.caminho);
            throw new Exception("Codigo can't be null");
        }

        if (pwweb == null || pwweb.isEmpty()) {
            this.logerro.Grava("criarUsuarioSatelliteMobile\r\nPWWEb can't be null", this.caminho);
            throw new Exception("PWWEb can't be null");
        }

        if (template == null || template.isEmpty()) {
            this.logerro.Grava("criarUsuarioSatelliteMobile\r\nTemplate can't be null", this.caminho);
            throw new Exception("Template can't be null");
        }

        byte[] templateArray = null;
        try {
            templateArray = Base64.getDecoder().decode(template);
        } catch (Exception e) {
            this.logerro.Grava("criarUsuarioSatelliteMobile\r\nTemplate isn't a stringBase64", this.caminho);
            throw new Exception("Template isn't a stringBase64");
        }

        if (ValidarUsuario.ValidaUsuario(codigo, pwweb, persistencia)) {

            byte[] structure = null;

            try {
                structure = MatcherWrapper.CreateUser(templateArray);
            } catch (MatcherException e) {
                this.logerro.Grava("criarUsuarioSatelliteMobile\r\nMatcherException\r\n" + e.getExceptionType(), this.caminho);
                throw new Exception(e.getMessage());
            } catch (LicenseActivationException e) {
                this.logerro.Grava("criarUsuarioSatelliteMobile\r\nLicenseActivationException\r\n" + e.getExceptionType(), this.caminho);
                throw new Exception(e.getMessage());
            } catch (Exception e) {
                this.logerro.Grava("criarUsuarioSatelliteMobile\r\nMatcherWrapper.CreateUser\r\n" + e.getMessage(), this.caminho);
                throw new Exception(e.getMessage());
            }

            if (structure == null) {
                this.logerro.Grava("criarUsuarioSatelliteMobile\r\nInternal matcher error", this.caminho);
                throw new Exception("Internal matcher error");
            }

            PessoaDet pessoaDet = new PessoaDet();
            pessoaDet.setCodigo(codigo);
            pessoaDet.setFaceID(new String(structure));
            pessoaDet.setOperador("SatWS");
            pessoaDet.setDt_alter(getDataAtual("SQL"));
            pessoaDet.setHr_Alter(getDataAtual("HORA"));

            try {
                PessoaDetDao pessoaDetDao = new PessoaDetDao();
                pessoaDetDao.inserirPessoaDet(pessoaDet, persistencia);

                // Validando o insert
                pessoaDet = pessoaDetDao.buscarPessoaDet(codigo, persistencia);
                if (pessoaDet == null) {
                    return null;
                }

                Login login = new Login();
                login.setNome(pessoaDet.getNome());
                login.setCodigo(pessoaDet.getCodigo().replace(".0", ""));
                login.setMatr(pessoaDet.getMatr().replace(".0", ""));
                login.setPwweb(pessoaDet.getPWWeb());
                return login;
            } catch (Exception e) {
                this.logerro.Grava(e.getMessage(), this.caminho);
                throw new Exception("criarUsuarioSatelliteMobile\r\nDatabase matcher error");
            }

        } else {
            this.logerro.Grava("criarUsuarioSatelliteMobile\r\nUsuário ou senha incorreta", this.caminho);
            throw new Exception("Usuário ou senha incorreta");
        }
    }

    /**
     * Identifica um usuário
     *
     * @param param
     * @param template
     * @param data
     * @param hora
     * @return
     * @throws Exception
     */
    public Login identificarUsuarioSatelliteMobile(@WebParam(name = "param") String param,
            @WebParam(name = "template") String template,
            @WebParam(name = "data") String data,
            @WebParam(name = "hora") String hora) throws Exception {

        Persistencia persistencia = pool.getConexao(param);

        if (persistencia == null) {
            this.logerro.Grava("Erro de parâmetro.", this.caminho);
            throw new Exception("Erro de parâmetro.");
        }

        // Atualizando o caminho dos logs
        this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\FaceID\\"
                + param + "\\" + getDataAtual("SQL") + "\\identificarUsuarioSatelliteMobile.txt";

        this.logerro.Grava("identificarUsuarioSatelliteMobile\r\n"
                + logParams(new String[]{
            "param:    " + param,
            "data:     " + data,
            "hora:     " + hora,
            "template: " + template
        }), this.caminho);

        if (template == null || template.isEmpty()) {
            this.logerro.Grava("identificarUsuarioSatelliteMobile\r\nTemplate can't be null", this.caminho);
            throw new Exception("Template can't be null");
        }

        byte[] userTemplate = null;
        // TODO: manage content parameters errors
        try {
            userTemplate = Base64.getDecoder().decode(template);
        } catch (Exception e) {
            this.logerro.Grava("identificarUsuarioSatelliteMobile\r\nTemplate isn't a stringBase64", this.caminho);
            throw new Exception("Template isn't a stringBase64");
        }

        AuthenticationResult authenticationResult;
        PessoaDetDao pessoaDetDao = new PessoaDetDao();
        List<PessoaDet> pessoaDetList = pessoaDetDao.listarPessoaDet(persistencia);
        for (PessoaDet pessoaDet : pessoaDetList) {
            try {
                if(pessoaDet == null) throw new Exception("pessoaDet == null");
                if(pessoaDet.getFaceID() == null) throw new Exception("pessoaDet.getFaceID() == null");
                
                authenticationResult = MatcherWrapper.Authenticate(pessoaDet.getFaceID().getBytes(), userTemplate);
                
                if(authenticationResult == null) throw new Exception("authenticationResult == null");
                
                if (authenticationResult.getIsPositiveMatch()) {
                    try {

                        // Retrain the user biometric information
                        byte[] structure = MatcherWrapper.RetrainUser(pessoaDet.getFaceID().getBytes(), userTemplate);

                        // Tenta atualizar a cara da pessoa sempre que ela fizer login
                        if (structure != null) {
                            pessoaDet.setFaceID(new String(structure));
                            pessoaDet.setOperador("SatWS");
                            pessoaDet.setDt_alter(getDataAtual("SQL"));
                            pessoaDet.setHr_Alter(getDataAtual("HORA"));

                            pessoaDetDao.inserirPessoaDet(pessoaDet, persistencia);
                        }

                    } catch (Exception e2) {
                        this.logerro.Grava("identificarUsuarioSatelliteMobile\r\n" + e2.getMessage(), this.caminho);
                    }

                    String seqRota = null;
                    try {
                        seqRota = EscalaDao.obterSequenciaRota(data, pessoaDet.getMatr().replace(".0", ""), persistencia);
                    } catch (Exception eSeqRota) {
                        this.logerro.Grava("identificarUsuarioSatelliteMobile\r\n" + eSeqRota.getMessage(), this.caminho);
                    }

                    Login login = new Login();
                    login.setNome(pessoaDet.getNome());
                    login.setCodigo(pessoaDet.getCodigo().replace(".0", ""));
                    login.setMatr(pessoaDet.getMatr().replace(".0", ""));
                    login.setPwweb(pessoaDet.getPWWeb());
                    login.setSeqRota(seqRota);
                    return login;
                }
            
            } catch (MatcherException e) {
                this.logerro.Grava("identificarUsuarioSatelliteMobile\r\nMatcherException\r\n" + e.getExceptionType(), this.caminho);
            } catch (LicenseActivationException e) {
                this.logerro.Grava("identificarUsuarioSatelliteMobile\r\nLicenseActivationException\r\n" + e.getExceptionType(), this.caminho);
            } catch (Exception e) {
                this.logerro.Grava("identificarUsuarioSatelliteMobile\r\nMatcherWrapper.Authenticate\r\n" + e.getMessage(), this.caminho);
            }
        }

        this.logerro.Grava("identificarUsuarioSatelliteMobile\r\nUsuário não encontrado", this.caminho);
        return null;
    }

    /**
     * Utiliza o codfil para limitar um pouco o escopo de busca de um usuário e
     * torná-la mais rápida
     *
     * @param param
     * @param codfil
     * @param template
     * @param data
     * @param hora
     * @return
     * @throws Exception
     */
    public Login identificarUsuarioSatMobEW(@WebParam(name = "param") String param,
            @WebParam(name = "codfil") String codfil,
            @WebParam(name = "template") String template,
            @WebParam(name = "data") String data,
            @WebParam(name = "hora") String hora) throws Exception {

        Persistencia persistencia = pool.getConexao(param);

        if (persistencia == null) {
            this.logerro.Grava("Erro de parâmetro.", this.caminho);
            throw new Exception("Erro de parâmetro.");
        }

        // Atualizando o caminho dos logs
        this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\FaceID\\"
                + param + "\\" + getDataAtual("SQL") + "\\identificarUsuarioSatMobEW.txt";

        this.logerro.Grava("identificarUsuarioSatMobEW\r\n"
                + logParams(new String[]{
            "param:    " + param,
            "codfil:   " + codfil,
            "data:     " + data,
            "hora:     " + hora,
            "template: " + template
        }), this.caminho);

        if (template == null || template.isEmpty()) {
            this.logerro.Grava("identificarUsuarioSatMobEW\r\nTemplate can't be null", this.caminho);
            throw new Exception("Template can't be null");
        }

        byte[] userTemplate = null;
        // TODO: manage content parameters errors
        try {
            userTemplate = Base64.getDecoder().decode(template);
        } catch (Exception e) {
            this.logerro.Grava("identificarUsuarioSatMobEW\r\nTemplate isn't a stringBase64", this.caminho);
            throw new Exception("Template isn't a stringBase64");
        }

        AuthenticationResult authenticationResult;
        PessoaDetDao pessoaDetDao = new PessoaDetDao();
        List<PessoaDet> pessoaDetList = pessoaDetDao.listarPessoaDet(codfil, persistencia);
        for (PessoaDet pessoaDet : pessoaDetList) {
            try {
                if(pessoaDet == null) throw new Exception("pessoaDet == null");
                if(pessoaDet.getFaceID() == null) throw new Exception("pessoaDet.getFaceID() == null");
                
                authenticationResult = MatcherWrapper.Authenticate(pessoaDet.getFaceID().getBytes(), userTemplate);
                
                if(authenticationResult == null) throw new Exception("authenticationResult == null");
                
                if (authenticationResult.getIsPositiveMatch()) {
                    try {

                        // Retrain the user biometric information
                        byte[] structure = MatcherWrapper.RetrainUser(pessoaDet.getFaceID().getBytes(), userTemplate);

                        // Tenta atualizar a cara da pessoa sempre que ela fizer login
                        if (structure != null) {
                            pessoaDet.setFaceID(new String(structure));
                            pessoaDet.setOperador("SatWS");
                            pessoaDet.setDt_alter(getDataAtual("SQL"));
                            pessoaDet.setHr_Alter(getDataAtual("HORA"));

                            pessoaDetDao.inserirPessoaDet(pessoaDet, persistencia);
                        }

                    } catch (Exception e2) {
                        this.logerro.Grava("identificarUsuarioSatMobEW\r\n" + e2.getMessage(), this.caminho);
                    }

                    Login login = new Login();
                    login.setNome(pessoaDet.getNome());
                    login.setCodigo(pessoaDet.getCodigo().replace(".0", ""));
                    login.setMatr(pessoaDet.getMatr().replace(".0", ""));
                    login.setPwweb(pessoaDet.getPWWeb());
                    return login;
                }
            } catch (MatcherException e) {
                this.logerro.Grava("identificarUsuarioSatMobEW\r\nMatcherException\r\n" + e.getExceptionType(), this.caminho);
            } catch (LicenseActivationException e) {
                this.logerro.Grava("identificarUsuarioSatMobEW\r\nLicenseActivationException\r\n" + e.getExceptionType(), this.caminho);
            } catch (Exception e) {
                this.logerro.Grava("identificarUsuarioSatMobEW\r\nMatcherWrapper.Authenticate\r\n" + e.getMessage(), this.caminho);
            }
        }

        this.logerro.Grava("identificarUsuarioSatMobEW\r\nUsuário não encontrado", this.caminho);
        return null;
    }

    public String logParams(String[] params) {
        if (params == null) {
            return "";
        }
        StringBuilder retorno = new StringBuilder();
        for (String param : params) {
            retorno.append(param).append("\r\n");
        }
        return retorno.toString();
    }
}
