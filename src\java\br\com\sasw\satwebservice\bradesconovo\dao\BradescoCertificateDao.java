/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.dao;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.satwebservice.bradesconovo.dao.entity.BradescoCertificateEntity;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class BradescoCertificateDao {
    public BradescoCertificateEntity getCertificateByEmpresa(String empresa){
        Persistencia persistencia;
        try {
            String sql = "SELECT Empresa, PathCertificado, bancoDeDados, " +
                    " ChaveTokenHom, ChaveCoinHom, ChaveToken, ChaveCoin," +
                    " PathCertificadoHom" +
                    " FROM Satellite.dbo.BradCertif WHERE Empresa=':empresa'";
            sql = sql.replace(":empresa", empresa);
            persistencia = new Persistencia("******************************************************", "sasw", "s@$26bd1", "");
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();
            BradescoCertificateEntity bradescoCertificateEntity = new BradescoCertificateEntity();
            if(consulta.Proximo()) {
                bradescoCertificateEntity.setEmpresa(consulta.getString("Empresa"));
                bradescoCertificateEntity.setBancoDeDados(consulta.getString("bancoDeDados"));
                bradescoCertificateEntity.setPathCertificadoProd(consulta.getString("PathCertificado"));
                bradescoCertificateEntity.setPathCertificadoHom(consulta.getString("PathCertificadoHom"));
                bradescoCertificateEntity.setChaveCoinProd(consulta.getString("chaveCoin"));
                bradescoCertificateEntity.setChaveCoinHom(consulta.getString("chaveCoinHom"));
                bradescoCertificateEntity.setChaveTokenProd(consulta.getString("chaveToken"));
                bradescoCertificateEntity.setChaveTokenHom(consulta.getString("chaveTokenHom"));
            }
            consulta.Close();
            return bradescoCertificateEntity;
        } catch (Exception ex) {
            Logger.getLogger(BradescoCertificateDao.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
}
