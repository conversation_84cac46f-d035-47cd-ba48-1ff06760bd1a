<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://www.portalfiscal.inf.br/cte" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="tiposGeralCTe_v3.00.xsd"/>
	<xs:include schemaLocation="cteTiposBasico_v3.00.xsd"/>
	<xs:element name="rodo">
		<xs:annotation>
			<xs:documentation>Informações do modal Rodoviário</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="RNTRC">
					<xs:annotation>
						<xs:documentation>Registro Nacional de Transportadores Rodoviários de Carga</xs:documentation>
						<xs:documentation>Registro obrigatório do emitente do CT-e junto à ANTT para exercer a atividade de transportador rodoviário de cargas por conta de terceiros e mediante remuneração.
						</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="TRNTRC"/>
					</xs:simpleType>
				</xs:element>
				<xs:element name="occ" minOccurs="0" maxOccurs="10">
					<xs:annotation>
						<xs:documentation>Ordens de Coleta associados</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="serie" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Série da OCC</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="TString">
										<xs:minLength value="1"/>
										<xs:maxLength value="3"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="nOcc">
								<xs:annotation>
									<xs:documentation>Número da Ordem de coleta</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="preserve"/>
										<xs:pattern value="[1-9]{1}[0-9]{0,5}"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="dEmi" type="TData">
								<xs:annotation>
									<xs:documentation>Data de emissão da ordem de coleta</xs:documentation>
									<xs:documentation>Formato AAAA-MM-DD</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="emiOcc">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="CNPJ" type="TCnpj">
											<xs:annotation>
												<xs:documentation>Número do CNPJ</xs:documentation>
												<xs:documentation>Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="cInt" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Código interno de uso da transportadora</xs:documentation>
												<xs:documentation>Uso intermo das transportadoras.</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TString">
													<xs:minLength value="1"/>
													<xs:maxLength value="10"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="IE" type="TIe">
											<xs:annotation>
												<xs:documentation>Inscrição Estadual</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="UF" type="TUf">
											<xs:annotation>
												<xs:documentation>Sigla da UF</xs:documentation>
												<xs:documentation>Informar EX para operações com o exterior.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="fone" type="TFone" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Telefone</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
