/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.integracao.creditoonlinesantender.bean;

/**
 *
 * <AUTHOR>
 */
public class Autorizacao {
    
    private final String systemAcronym = "YV";
    private String nonce;
    private long timestamp;
    private String signature;

    public Autorizacao(String nonce, long timestamp, String signature) {
        this.nonce = nonce;
        this.timestamp = timestamp;
        this.signature = signature;
    }

    @Override
    public String toString() {
        return "{\n"
                    + "                \"systemAcronym\": \""+systemAcronym+"\",\n"
                    + "                \"signature\": \""+signature+"\",\n"
                    + "                \"nonce\": \""+nonce+"\",\n"
                    + "                \"timestamp\": \""+timestamp+"\"\n"
                    + "            }\n";
    }

    public String getSystemAcronym() {
        return systemAcronym;
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }
}
