/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.rotas;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.CxFGuias;
import SasBeans.CxFGuiasVol;
import SasBeans.MobileProcAnt;
import SasBeans.Paramet;
import SasBeans.RPV;
import SasBeans.Rt_PercDet;
import SasDaos.ClientesDao;
import SasDaos.CxFGuiasDao;
import SasDaos.CxFGuiasVolDao;
import SasDaos.EGtvDao;
import SasDaos.GTVDao;
import SasDaos.MobileProcAntDao;
import SasDaos.ParametDao;
import SasDaos.RPVDao;
import SasDaos.RotasDao;
import SasDaos.Rt_GuiasDao;
import SasDaos.Rt_PercDao;
import SasDaos.Rt_PercDetDao;
import SasDaos.Rt_PercSlaDao;
import SasDaos.SASLogDao;
import SasDaos.SemaforoDao;
import SasLibrary.GuiasMobile;
import br.com.sasw.pacotesuteis.sasbeans.Rt_GuiasFat;
import br.com.sasw.pacotesuteis.sasbeans.Rt_GuiasMoeda;
import br.com.sasw.pacotesuteis.sasdaos.Rt_GuiasFatDao;
import br.com.sasw.pacotesuteis.sasdaos.Rt_GuiasMoedaDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.pacotesuteis.utilidades.Horaminuto;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringTokenizer;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;

/**
 *
 * <AUTHOR>
 */
@Path("/ws-rotas/baixaservicobk/")
public class BaixaServicoBK {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;
    private final SemaforoDao semaforoDao;

    @Context
    private UriInfo context;

    
    /**
     * Creates a new instance of BaixaServico
     */
    public BaixaServicoBK() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Rotas\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        semaforoDao = new SemaforoDao();
    }
    
    private Boolean isTranspCacamba(String empresa) throws Exception {
        Persistencia inSatellite = pool.getConexao("SATELLITE");

        ParametDao parametDao = new ParametDao();
        Paramet parametGoogle = parametDao.getParametGoogleApi(empresa, inSatellite);

        return parametGoogle.getTranspCacamba().equals("0") ? false : true;
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response post(String input) {

        Gson gson = new GsonBuilder().create();
        Map retorno = new HashMap<>();

        String sequencia = null;
        String semaforoBaixaHorariosRota = "BaixaHorariosRota";
        String semaforoGTV = "GTV";
        String chaveSemaforoGTV = "Geração de Guias";

        try {
            // Convertendo o input para java
            JsonObject jsonObject;
            try{
                jsonObject = new JsonParser().parse(input).getAsJsonObject();
            } catch (Exception e){
                jsonObject = new JsonParser().parse(input).getAsJsonArray().get(0).getAsJsonObject();
            }

            // Buscando as informações do json
            String param;
            try {
                param = jsonObject.get("param").getAsString().replace(".0", "");
            } catch (Exception ee) {
                // Salvando em log o que foi mandado
                this.logerro.Grava(input, this.caminho);
                throw new Exception("param");
            }
            
            // Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao(param);
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + param + ")");
            }            
            
            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Rotas\\" + param + "\\"
                    + getDataAtual("SQL") + "\\log.txt";

            // Salvando em log o que foi mandado
            this.logerro.Grava(input, this.caminho);

            String processar = "";
            String processarRPV = "";

            String sCodPessoa;
            try {
                sCodPessoa = jsonObject.get("codpessoa").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("codpessoa");
            }

            String operador;
            try {
                operador =  RecortaAteEspaço(jsonObject.get("operador").getAsString().replace(".0", ""), 0, 10);
            } catch (Exception ee) {
                throw new Exception("operador");
            }

            String dataAtual;
            try {
                dataAtual = jsonObject.get("dataAtual").getAsString().replace(".0", "");
            } catch (Exception ee) {
                dataAtual = getDataAtual("SQL");
            }

            String horaAtual;
            try {
                horaAtual = jsonObject.get("horaAtual").getAsString().replace(".0", "");
            } catch (Exception ee) {
                horaAtual = getDataAtual("HORA");
            }

            int vBaixaDuplicidade = 0;

            try {
                sequencia = jsonObject.get("sequencia").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("sequencia");
            }

            String codCliAuth;
            try {
                codCliAuth = jsonObject.get("codCliAuth").getAsString().replace(".0", "");
            } catch (Exception ee) {
                codCliAuth = sCodPessoa;
            }

            String codfil;
            try {
                codfil = jsonObject.get("codfil").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("codfil");
            }

            String parada;
            try {
                parada = jsonObject.get("parada").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("parada");
            }

            String er;
            try {
                er = jsonObject.get("er").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("er");
            }

            String tiposerv;
            try {
                tiposerv = jsonObject.get("tiposerv").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("tiposerv");
            }

            String hora1;
            try {
                hora1 = jsonObject.get("hora1").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("hora1");
            }
            
            String hrcheg;
            try {
                hrcheg = jsonObject.get("hrcheg").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("hrcheg");
            }

            String hrsaida;
            try {
                hrsaida = jsonObject.get("hrsaida").getAsString().replace(".0", "");
            } catch (Exception ee) {
                hrsaida = horaAtual;
            }

            String horaSaidaVei;
            try {
                horaSaidaVei = jsonObject.get("hrsaidavei").getAsString().replace(".0", "");
            } catch (Exception ee) {
                horaSaidaVei = hrsaida;
            }

            if (!semaforoDao.existeSemaforo(semaforoBaixaHorariosRota, sequencia, dataAtual, persistencia)) {
                // Mudança de localização do semáforo
                semaforoDao.inserirRegistro(sCodPessoa, dataAtual, horaAtual, semaforoBaixaHorariosRota, sequencia, persistencia);
                this.logerro.Grava("Semaforo fechado (" + semaforoBaixaHorariosRota + ", " + sequencia + ")", this.caminho);

//                if (null == hrsaida || hrsaida.equals("")) { //  Tratamento para hrsaida nulo.
//                    try { // Adiciona 5 minutos da hora de entrada
//                        Calendar c = Calendar.getInstance();
//                        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
//                        c.setTime(sdf.parse(hrcheg));
//                        c.add(Calendar.MINUTE, 5);
//                        hrsaida = FuncoesString.PreencheEsquerda(Integer.toString(c.get(Calendar.HOUR_OF_DAY)), 2, "0") + ":"
//                                + FuncoesString.PreencheEsquerda(Integer.toString(c.get(Calendar.MINUTE)), 2, "0");
//                    } catch (Exception e) {
//                        hrsaida = horaAtual;
//                    }
//                }
//
//                if ("".equals(hrcheg)) {  //  Tratamento para hrcheg nulo. Ajuste posterior android 10-03-17
//                    try { // Diminuindo 5 minutos da hora de saida
//                        Calendar c = Calendar.getInstance();
//                        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
//                        c.setTime(sdf.parse(hrsaida));
//                        c.add(Calendar.MINUTE, -5);
//                        hrcheg = FuncoesString.PreencheEsquerda(Integer.toString(c.get(Calendar.HOUR_OF_DAY)), 2, "0") + ":"
//                                + FuncoesString.PreencheEsquerda(Integer.toString(c.get(Calendar.MINUTE)), 2, "0");
//                    } catch (Exception e) {
//                        hrcheg = hrsaida;
//                    }
//                }

                String guia;
                try {
                    guia = jsonObject.get("guia").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    guia = "";
                }

                String moeda;
                try {
                    moeda = jsonObject.get("moeda").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    moeda = "";
                }

                String serie;
                try {
                    serie = jsonObject.get("serie").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    serie = "";
                }

                String qtguias;
                try {
                    qtguias = jsonObject.get("qtguias").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    qtguias = "";
                }

                String valor;
                try {
                    valor = jsonObject.get("valor").getAsString().replace(".0", "");
                    if (valor.contains("$")) {
                        valor = valor.replaceAll("\\$", "");
                    }
                } catch (Exception ee) {
                    valor = "";
                }

                String rpv;
                try {
                    rpv = jsonObject.get("rpv").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    rpv = "";
                }

                String qtlacres;
                try {
                    qtlacres = jsonObject.get("qtlacres").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    qtlacres = "";
                }

                String lacre;
                try {
                    lacre = jsonObject.get("lacre").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    lacre = "";
                }

                String qtvolumes;
                try {
                    qtvolumes = jsonObject.get("qtvolumes").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    qtvolumes = "";
                }

                String valoresLacres;
                try {
                    valoresLacres = jsonObject.get("valoresLacres").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    valoresLacres = "";
                }

                String observacaoLacres;
                try {
                    observacaoLacres = jsonObject.get("observacaoLacres").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    observacaoLacres = "";
                }

                String tipoLacres;
                try {
                    tipoLacres = jsonObject.get("tipoLacres").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    tipoLacres = "";
                }

                if (er.equals("E") && "".equals(guia)) {  // Busca as guias de suprimento em caixa-forte caso nao estejam listadas
                    CxFGuiasDao cxfguiasDao = new CxFGuiasDao();
                    CxFGuiasVolDao cxfguiasvolDao = new CxFGuiasVolDao();

                    String hora1hhmm = hora1.replace(":", "");
                    List<CxFGuias> ListaGuiasE = cxfguiasDao.getCxfGuiasEntrega(sequencia, hora1hhmm, persistencia);
                    serie = "";
                    valor = "";
                    qtguias = "" + ListaGuiasE.size();
                    rpv = "";
                    lacre = "";
                    qtlacres = "";
                    for (CxFGuias cxFGuias : ListaGuiasE) {
                        if (!"".equals(guia)) {
                            guia += ";";
                            serie += ";";
                            valor += ";";
                            rpv += ";";
                        }
                        guia += cxFGuias.getGuia().replace(".0", "");
                        serie += cxFGuias.getSerie();
                        valor += cxFGuias.getValor();
                        rpv += "0";

                        List<CxFGuiasVol> volumes = cxfguiasvolDao.getLacres(cxFGuias.getGuia().replace(".0", ""), cxFGuias.getSerie(), persistencia);
                        qtlacres += volumes.size() + ";";
                        for (CxFGuiasVol volume : volumes) {
                            lacre += volume.getLacre() + ";";
                        }
                    }

                    if (qtlacres.contains(";")) {
                        qtlacres = qtlacres.substring(0, qtlacres.length() - 1);
                    }
                    if (lacre.contains(";")) {
                        lacre = lacre.substring(0, lacre.length() - 1);
                    }
                    this.logerro.Grava("Busca GTV entrega. Sequencia (" + sequencia + ") hora1d (" + hora1hhmm + ") Guia(s): " + guia + " Serie(s): " + serie, this.caminho);
                }

                String chaves;
                try {
                    chaves = jsonObject.get("chaves").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    chaves = "";
                }

                String data_sql = dataAtual;

                String latitude;
                try {
                    latitude = jsonObject.get("latitude").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    latitude = "";
                }

                String longitude;
                try {
                    longitude = jsonObject.get("longitude").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    longitude = "";
                }

                String KM;
                try {
                    KM = jsonObject.get("km").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    KM = "";
                }

                String OBS;
                try {
                    OBS = jsonObject.get("obs").getAsString().replace(".0", "");
                } catch (Exception ee) {
                    OBS = "";
                }

                int vRPVNAK = 0;
                int vGuiasNAK = 0;

                Horaminuto conversor = new Horaminuto();

                Rt_PercDao rt_percDao = new Rt_PercDao();
                Rt_PercSlaDao rt_percSlaDao = new Rt_PercSlaDao();
                RotasDao rotasDao = new RotasDao();
                MobileProcAntDao mobileProcAntDAO = new MobileProcAntDao();
                RPVDao rpvdao = new RPVDao();
                GuiasMobile gm = new GuiasMobile();

                String dataAnterior = rotasDao.existeRota(new BigDecimal(sequencia), dataAtual, persistencia);

                String nred = rt_percDao.obterNred(parada, sequencia, persistencia);
                if (("E".equals(er) && "".equals(guia) && rt_percDao.isCaixaForte(parada, sequencia, persistencia))
                        || nred.contains("ALMOCO")) {//Verififica se parada e caixa forte.

                    this.logerro.Grava("Parada de caixa forte >> " + parada + " | " + sequencia, this.caminho);

                    rt_percDao.updateHrSaida(sequencia, parada, hrsaida, hrcheg, dataAtual, persistencia);
                    rt_percSlaDao.inserirHorarioSaida(sequencia, parada, horaSaidaVei, horaAtual, dataAtual, operador, persistencia);
                    
                    // gravando dados em Rt_PercDet 

                    try {

                        Rt_PercDet rt_percdet = new Rt_PercDet();
                        Rt_PercDetDao rt_percdetdao = new Rt_PercDetDao();
                        rt_percdet.setCodFil(codfil);
                        rt_percdet.setSequencia(sequencia);
                        rt_percdet.setParada(Integer.valueOf(parada));
                        rt_percdet.setKM(KM);
                        if (rt_percdetdao.existeDet(rt_percdet, persistencia)) {
                            rt_percdetdao.AtualizaKMDet(rt_percdet, persistencia);
                        } else {
                            rt_percdetdao.InserirDet(rt_percdet, persistencia);
                        }
                        //Erros ao inserir - 14/12/2016
                        retorno.put("resp", 1);
                    } catch (Exception e) {
                        this.logerro.Grava("Rt_PercDet. Erro ao atualizar dados. SeqRota: " + sequencia + " Parada: " + parada + " KM: " + KM + " " + e.getMessage(), this.caminho);
                        retorno.put("resp", 0);
                    }

                    //grava log da baixa (botao X na tela operacoes TV
                    SASLogDao sasLogDao = new SASLogDao();
                    boolean repete = false;
                    int cont = 1;
                    while (!repete) {
                        String seq_log = sasLogDao.maxSasLog(persistencia);
                        repete = sasLogDao.gravaSasLogParada(seq_log, sequencia, parada, data_sql, hrsaida, sCodPessoa, persistencia);
                        if (cont == 20) {
                            repete = true;
                        }
                        cont++;
                    }
                } else if ("".equals(dataAnterior) && !("".equals(guia) && "R".equals(er))) {  // Serviço inconsistente. Recolhimento sem guia

                    String Atraso, Tempo_Espera;
                    String paradas, hrchegs, hora1s, hrsaidas, ers, tiposrvs, latitudes, longitudes;
                    //carregando strings lotes
                    paradas = parada;
                    ers = er;
                    tiposrvs = tiposerv;

                    hora1s = hora1;
                    hrchegs = hrcheg;
                    hrsaidas = hrsaida;
                    latitudes = latitude;
                    longitudes = longitude;
                    //carregando objetos para quebrar os lotes
                    StringTokenizer paradatk = new StringTokenizer(paradas, ";");
                    StringTokenizer ertk = new StringTokenizer(ers, ";");
                    StringTokenizer tiposrvtk = new StringTokenizer(tiposrvs, ";");
                    StringTokenizer hora1tk = new StringTokenizer(hora1s, ";");
                    StringTokenizer hrchegtk = new StringTokenizer(hrchegs, ";");
                    StringTokenizer hrsaidastk = new StringTokenizer(hrsaidas, ";");
                    StringTokenizer longitudetk = new StringTokenizer(longitudes, ";");
                    StringTokenizer latitudetk = new StringTokenizer(latitudes, ";");
                    StringTokenizer obstk;
                    StringTokenizer kmtk;
                    try {
                        obstk = new StringTokenizer(OBS, ";");
                    } catch (Exception e) {
                        obstk = null;
                    }
                    try {
                        kmtk = new StringTokenizer(KM, ";");
                    } catch (Exception e) {
                        kmtk = null;
                    }

                    RPV rtv = null;
                    GTVDao gtvDao = new GTVDao();
                    EGtvDao eGtvDao = new EGtvDao();
                    while (paradatk.hasMoreTokens()) {

                        StringTokenizer volumetkz = new StringTokenizer(lacre, ";");
                        this.logerro.Grava("Gerando trace RPV. Guia(s) (" + guia + ") Series: (" + serie + ")" + " RPV: (" + rpv + ")", this.caminho);
                        String[] rpvs = rpv.split(";");
                        String[] guias = guia.split(";");
                        String[] valores = valor.split(";");
                        String[] series = serie.split(";");

                        if (null == moeda || moeda.equals("")) {
                            moeda = "";
                            for (String g : guias) {
                                moeda += "BRL" + ";";
                            }
                            moeda = moeda.substring(0, moeda.length() - 1);
                        }

                        String[] moedas = moeda.split(";"); //xxx

                        /* Tratamento para quando o mobile envia "null" para qtvolumes e/ou valoreslacres 23/10/2017 */
                        if (null == qtvolumes || qtvolumes.equals("null")) {
                            qtvolumes = "";
                            for (String v : valores) {
                                qtvolumes += "1;";
                            }
                            qtvolumes = qtvolumes.substring(0, qtvolumes.length() - 1);
                        }
                        if (null == valoresLacres || valoresLacres.equals("null")) {
                            valoresLacres = "";
                            for (String v : valores) {
                                valoresLacres += v + ";";
                            }
                            valoresLacres = valoresLacres.substring(0, valoresLacres.length() - 1);
                        }
                        if (null == observacaoLacres || observacaoLacres.equals("")) {
                            observacaoLacres = "";
                            for (String v : valores) {
                                observacaoLacres += "null" + ";";
                            }
                            observacaoLacres = observacaoLacres.substring(0, observacaoLacres.length() - 1);
                        }
                        if (null == tipoLacres || tipoLacres.equals("")) {
                            tipoLacres = "";
                            for (String v : valores) {
                                tipoLacres += "null" + ";";
                            }
                            tipoLacres = tipoLacres.substring(0, tipoLacres.length() - 1);
                        }

                        String[] qtdVolumes = qtvolumes.split(";"); // Quantidade de volumes em vez de quantidade de lacres 01/10/2017

                        guia = "";
                        valor = "";

                        for (int i = 0; i < guias.length; i++) {  // Primeira fase: inserindo trace RPV                                                        

                            int qtdVolume;
                            try {
                                qtdVolume = Integer.parseInt(qtdVolumes[i]);
                            } catch (Exception e) {
                                qtdVolume = volumetkz.countTokens();
                            }
                            if ("".equals(valores[i])) {//Realiza o tratamento do valor digitado
                                valores[i] = "0.00";
                            }

                            String guiasRpv = guias[i];
                            String moedaRpv = moedas[i];
                            String serieRpv = series[i];
                            rpv = rpvs[i];

                            if (er.equals("R")
                                    //                                            && new BigDecimal(guiasRpv).compareTo(new BigDecimal("200000000000")) == 1
                                    && "53".equals(serieRpv)
                                    && "0".equals(rpv)) {
                                rpv = "1"; // Conserto erro 10-03-2017 - Aplicacao com erro ajuste no servidor
                            }

                            if (er.equals("E") || isTranspCacamba(param)) { // RPVs nao sao permitidos em entregas
                                rpv = "0";
                            }

                            if ("0".equals(rpv)) { // Processar guia convencional
                                try {
                                    if (!rpvdao.existeGuiaRPV(guias[i], serieRpv, sequencia, parada, persistencia)) {

                                        processarRPV += "1;";
                                        rtv = new RPV();
                                        rtv.setCodPessoAut(codCliAuth);
                                        rtv.setGuia(guias[i]);
                                        rtv.setSerie(serieRpv);
                                        rtv.setRpv("0");
                                        rtv.setParada(Integer.parseInt(parada));
                                        rtv.setSeqRota(Float.parseFloat(sequencia));
                                        rtv.setData(dataAtual);
                                        rtv.setHora(horaAtual);
                                        rtv.setFlag_excl("");
                                        rtv.setValor(Float.parseFloat(valores[i].replace(" ", "")));
                                        rtv.setVolumes(String.valueOf(qtdVolume)); // 01/10/2017
                                        // rtv.setVolumes(volumeRpv);
                                        rpvdao.salvarInformacoes(rtv, persistencia);

                                        try {
                                            Rt_GuiasMoeda rt_guiasMoeda = new Rt_GuiasMoeda();
                                            rt_guiasMoeda.setSequencia(sequencia);
                                            rt_guiasMoeda.setParada(parada);
                                            rt_guiasMoeda.setGuia(guias[i]);
                                            rt_guiasMoeda.setSerie(serieRpv);
                                            rt_guiasMoeda.setMoeda(moedas[i]);
                                            rt_guiasMoeda.setOperador(RecortaAteEspaço(operador, 0, 10));
                                            rt_guiasMoeda.setDt_Alter(dataAtual);
                                            rt_guiasMoeda.setHr_Alter(horaAtual);

                                            Rt_GuiasMoedaDao rt_guiasMoedaDao = new Rt_GuiasMoedaDao();
                                            rt_guiasMoedaDao.inserirRt_GuiasMoedasDao(rt_guiasMoeda, persistencia);
                                        } catch (Exception em) {
                                            this.logerro.Grava("Rt_GuiasMoeda - " + em.getMessage(), this.caminho);
                                        }
                                    } else {

                                        this.logerro.Grava("RPV - Tentativa de baixa em duplicidade: Guia: " + guias[i] + " Serie: " + series[i] + " SeqRota/Parada: " + sequencia + "/" + parada, this.caminho);
                                        processarRPV += "0;";
                                        vRPVNAK += 1;
                                        vBaixaDuplicidade += 1;
                                    }
                                } catch (Exception e) {

                                    processarRPV = "0;";
                                    vRPVNAK += 1;
                                    this.logerro.Grava("RPV - Erro de gravacao. Provavel violacao de chave: Guia: " + guias[i] + " Serie: " + series[i] + " SeqRota/Parada: " + sequencia + "/" + parada + "\r\n" + e.getMessage(), this.caminho);
                                }

                                if (isTranspCacamba(param)) {
                                    try {
                                        String formaPgto;
                                        try {
                                            formaPgto = jsonObject.get("FormaPgto").getAsString().replace(".0", "");
                                        } catch (Exception ee) {
                                            throw new Exception("Sem infoPgto.");
                                        }
                                        String obsFormaPgto;
                                        try {
                                            obsFormaPgto = jsonObject.get("ObsFormaPgto").getAsString().replace(".0", "");
                                        } catch (Exception ee) {
                                            obsFormaPgto = "";
                                        }

                                        String valorPagamento;
                                        try {
                                            valorPagamento = jsonObject.get("valorPagamento").getAsString().replace(".0", "");
                                        } catch (Exception ee) {
                                            valorPagamento = "";
                                        }

                                        if (formaPgto == null || formaPgto.equals("") || formaPgto.equals("null")) {
                                            throw new Exception("Sem infoPgto.");
                                        }

                                        Rt_GuiasFat rt_guiasFat = new Rt_GuiasFat();
                                        rt_guiasFat.setSequencia(sequencia);
                                        rt_guiasFat.setParada(parada);
                                        rt_guiasFat.setGuia(guias[i]);
                                        rt_guiasFat.setSerie(serieRpv);
                                        rt_guiasFat.setCodFil(codfil);
                                        rt_guiasFat.setOS(rt_percDao.obterOS(sequencia, parada, persistencia));
                                        rt_guiasFat.setEmbarques("1");
                                        rt_guiasFat.setValorEmb(valorPagamento);
                                        rt_guiasFat.setValorAst("");
                                        rt_guiasFat.setValorAdv("");
                                        rt_guiasFat.setValorTot(valorPagamento);
                                        rt_guiasFat.setFormaPgto(formaPgto);
                                        rt_guiasFat.setObs(obsFormaPgto);
                                        rt_guiasFat.setOperador(operador);
                                        rt_guiasFat.setDt_Alter(getDataAtual("SQL"));
                                        rt_guiasFat.setHr_Alter(getDataAtual("HORA"));

                                        Rt_GuiasFatDao rt_GuiasFatDao = new Rt_GuiasFatDao();
                                        rt_GuiasFatDao.inserirRt_GuiasFat(rt_guiasFat, persistencia);

                                    } catch (Exception e) {
                                        this.logerro.Grava("ProcessaGuiaMobile", this.caminho);
                                    }
                                }

                                valor += valores[i] + ";";
                                guia += guias[i] + ";";

                            } else if ("1".equals(rpv) && er.equals("R")) {  // Processar RPV (Gerar guia eletrônica)
                                try {
                                    String guiaExiste = rpvdao.retornaGuia(guiasRpv, persistencia);
                                    if (guiasRpv.equals(guiaExiste)) {
                                        this.logerro.Grava("RPV - Tentativa de baixa em duplicidade: Guia: " + guiaExiste, this.caminho);
                                        processarRPV += "0;"; // TAG QUE FALTAVA 13/07/2017 - Richard
                                    } else {
                                        BigDecimal guiaBD = BigDecimal.ZERO;
                                        // Nesse caso a variavel guiasRpv é um RPV

                                        rtv = new RPV();
                                        rtv.setCodPessoAut(codCliAuth);
                                        rtv.setSerie(serieRpv);
                                        rtv.setRpv(guiasRpv);
                                        rtv.setParada(Integer.parseInt(parada));
                                        rtv.setSeqRota(Float.parseFloat(sequencia));
                                        rtv.setData(dataAtual);
                                        rtv.setHora(horaAtual);
                                        rtv.setFlag_excl("");
                                        rtv.setValor(Float.parseFloat(valores[i].replace(" ", "")));
                                        rtv.setVolumes(String.valueOf(qtdVolume)); // 01/10/2017
                                        // rtv.setVolumes(volumeRpv);

                                        if (!rpvdao.existeRPV(guiasRpv, persistencia)) {
                                            // GERAÇÃO DE GUIA AQUI
                                            for (int tentativas = 1;; tentativas++) {
                                                if (!semaforoDao.existeSemaforo(semaforoGTV, chaveSemaforoGTV, dataAtual, persistencia)) {
                                                    try {
                                                        semaforoDao.inserirRegistro(operador, dataAtual, horaAtual, semaforoGTV, chaveSemaforoGTV, persistencia);
                                                        guiaBD = gtvDao.gerarGuia(serieRpv, guiasRpv, codfil, persistencia);
                                                        this.logerro.Grava("Inserindo eGTV.", this.caminho);
                                                        eGtvDao.inserirEGtv(guiaBD.toBigInteger().toString(), serieRpv, getDataAtual("SQL"), getDataAtual("HORA"), persistencia);
                                                        this.logerro.Grava("Guia " + guiaBD.toBigInteger().toString() + " gerada em " + tentativas + " tentativas.", this.caminho);
                                                        break;
                                                    } catch (Exception semaforos) {
                                                        this.logerro.Grava(semaforos.getMessage(), this.caminho);
                                                    } finally {
                                                        semaforoDao.removerBaixaTabela(semaforoGTV, chaveSemaforoGTV, persistencia);
                                                    }
                                                } else if (semaforoDao.tempoSemaforo(semaforoGTV, chaveSemaforoGTV, persistencia) > 5) {
                                                    semaforoDao.removerBaixaTabela(semaforoGTV, chaveSemaforoGTV, persistencia);
                                                    this.logerro.Grava("Semáforo GTV removido", this.caminho);
                                                }
                                                Thread.sleep(5000);
                                            }
                                            processarRPV += "1;";
                                            this.logerro.Grava("Não existe RPV ainda. RPV geracao de guia. RPV: " + rpv + " Guia: " + guiaBD.toBigInteger().toString(), this.caminho);

                                            rtv.setGuia(guiaBD.toPlainString());

                                            rpvdao.salvarInformacoes(rtv, persistencia);
                                            // Salvar RPV NA BASE CENTRAL
                                            // Persistencia satellite = pool.getConexao("SATELLITE");
                                            // rpvdao.salvarInformacoes(rtv, satellite);

                                            guia += guiaBD.toPlainString() + ";";
                                            valor += valores[i] + ";";

                                            try {
                                                Rt_GuiasMoeda rt_guiasMoeda = new Rt_GuiasMoeda();
                                                rt_guiasMoeda.setSequencia(sequencia);
                                                rt_guiasMoeda.setParada(parada);
                                                rt_guiasMoeda.setGuia(guiaBD.toBigInteger().toString());
                                                rt_guiasMoeda.setSerie(serieRpv);
                                                rt_guiasMoeda.setMoeda(moedaRpv);
                                                rt_guiasMoeda.setOperador(RecortaAteEspaço(operador, 0, 10));
                                                rt_guiasMoeda.setDt_Alter(dataAtual);
                                                rt_guiasMoeda.setHr_Alter(horaAtual);

                                                Rt_GuiasMoedaDao rt_guiasMoedaDao = new Rt_GuiasMoedaDao();
                                                rt_guiasMoedaDao.inserirRt_GuiasMoedasDao(rt_guiasMoeda, persistencia);
                                            } catch (Exception e) {
                                                this.logerro.Grava("Rt_GuiasMoeda - " + e.getMessage(), this.caminho);
                                            }

                                            try {
                                                String formaPgto;
                                                try {
                                                    formaPgto = jsonObject.get("FormaPgto").getAsString().replace(".0", "");
                                                } catch (Exception ee) {
                                                    throw new Exception("Sem infoPgto.");
                                                }
                                                String obsFormaPgto;
                                                try {
                                                    obsFormaPgto = jsonObject.get("ObsFormaPgto").getAsString().replace(".0", "");
                                                } catch (Exception ee) {
                                                    obsFormaPgto = "";
                                                }

                                                String valorPagamento;
                                                try {
                                                    valorPagamento = jsonObject.get("valorPagamento").getAsString().replace(".0", "");
                                                } catch (Exception ee) {
                                                    valorPagamento = "";
                                                }

                                                if (formaPgto == null || formaPgto.equals("") || formaPgto.equals("null")) {
                                                    throw new Exception("Sem infoPgto.");
                                                }

                                                Rt_GuiasFat rt_guiasFat = new Rt_GuiasFat();
                                                rt_guiasFat.setSequencia(sequencia);
                                                rt_guiasFat.setParada(parada);
                                                rt_guiasFat.setGuia(guiaBD.toPlainString());
                                                rt_guiasFat.setSerie(serieRpv);
                                                rt_guiasFat.setCodFil(codfil);
                                                rt_guiasFat.setOS(rt_percDao.obterOS(sequencia, parada, persistencia));
                                                rt_guiasFat.setEmbarques("1");
                                                rt_guiasFat.setValorEmb(valorPagamento);
                                                rt_guiasFat.setValorAst("");
                                                rt_guiasFat.setValorAdv("");
                                                rt_guiasFat.setValorTot(valorPagamento);
                                                rt_guiasFat.setFormaPgto(formaPgto);
                                                rt_guiasFat.setObs(obsFormaPgto);
                                                rt_guiasFat.setOperador(operador);
                                                rt_guiasFat.setDt_Alter(getDataAtual("SQL"));
                                                rt_guiasFat.setHr_Alter(getDataAtual("HORA"));

                                                Rt_GuiasFatDao rt_GuiasFatDao = new Rt_GuiasFatDao();
                                                rt_GuiasFatDao.inserirRt_GuiasFat(rt_guiasFat, persistencia);

                                            } catch (Exception e) {
                                                this.logerro.Grava("Rt_GuiasFat - " + e.getMessage(), this.caminho);
                                            }

                                            gtvDao.atualizaGTV(persistencia, dataAtual, horaAtual,
                                                    OBS, codfil, guiaBD.toPlainString(), serie);
                                        } else if (!rpvdao.existeRPVNaoExcluido(guiasRpv, persistencia)) {
                                            /*
                                                    GTVSeqDao gtvSeqDao = new GTVSeqDao();
                                                    guiaBD = gtvSeqDao.maxGuia53(codfil, sCodPessoa.contains(".0") ? sCodPessoa.replace(".0", "") : sCodPessoa, persistencia);
                                             */

                                            Rt_GuiasDao rt_guiasDao = new Rt_GuiasDao();
                                            guiaBD = rt_guiasDao.maxGuia53(persistencia);

                                            processarRPV += "1;";
                                            this.logerro.Grava("Não existe RPV não excluído. RPV geracao de guia. RPV: " + rpv + " Guia: " + guiaBD.toBigInteger().toString(), this.caminho);

                                            rtv.setGuia(guiaBD.toPlainString());

                                            rpvdao.salvarInformacoes(rtv, persistencia);

                                            guia += guiaBD.toPlainString() + ";";
                                            valor += valores[i] + ";";

                                            gtvDao.atualizaGTV(persistencia, dataAtual, horaAtual, OBS, codfil, guiaBD.toPlainString(), serie);
                                        } else {
                                            this.logerro.Grava("Guia não será processada, já deve existir entrada em RPV.", this.caminho);
                                            processarRPV += "0;";

                                            guia += "0" + ";";
                                            valor += "0" + ";";
                                        }
                                    }
                                } catch (Exception e) {
                                    this.logerro.Grava("RPV - Falha em tentativa de processar novo RPV. Numero:  " + guiasRpv + " " + e.getMessage(), this.caminho);
                                    vRPVNAK += 1;
                                }
                            }
                        }

                        if (!"".equals(valor)) {
                            try {
                                guia = guia.substring(0, guia.length() - 1);
                                valor = valor.substring(0, valor.length() - 1);
                                processarRPV = processarRPV.substring(0, processarRPV.length() - 1);
                            } catch (Exception e) {
                                this.logerro.Grava("Falha ao tratar valores. Guia: " + guia + " Valor: " + valor + "\r\n" + e.getMessage(), this.caminho);
                            }
                        }

                        this.logerro.Grava("RPV - processamento concluido. RPV: " + guia, this.caminho);

                        parada = paradatk.nextToken();
                        er = ertk.nextToken();
                        tiposerv = tiposrvtk.nextToken();
                        hora1 = hora1tk.nextToken();
                        hrcheg = hrchegtk.nextToken();
                        hrsaida = hrsaidastk.nextToken();
                        if ("".equals(hrcheg)) {  //  Tratamento para hrcheg nulo. Ajuste posterior android 10-03-17
                            hrcheg = hrsaida;
                        }

                        try {
                            latitude = latitudetk.nextToken();
                        } catch (Exception e) {
                            latitude = "";
                        }
                        try {
                            longitude = longitudetk.nextToken();
                        } catch (Exception e) {
                            longitude = "";
                        }
                        try {
                            OBS = "/" + obstk.nextToken();
                        } catch (Exception e) {
                            OBS = "";
                        }
                        try {
                            KM = kmtk.nextToken();
                        } catch (Exception e) {
                            KM = "";
                        }

                        // Inserir as guias.
                        processar = processarRPV;  // Se o processamento do RPV nao for bem sucedido nao processar guia
                        try {
                            this.logerro.Grava("Rt_Guias - Inicio processamento. Guia(s): " + guia + " Serie: " + serie, this.caminho);
                            String nome_servlet = "BaixaServico";
                            String ret = gm.ProcessaGuiaMobile(lacre, parada, qtguias, er, tiposerv, guia, serie, valor, qtlacres,
                                    qtvolumes, valoresLacres, observacaoLacres, tipoLacres,
                                    codfil, sequencia,
                                    processar, param, this.logerro, this.caminho, sCodPessoa, dataAtual, horaAtual,
                                    persistencia);
                            vGuiasNAK += Integer.parseInt(ret.split(" ")[0]);
                            this.logerro.Grava("Rt_Guias - Termino processamento. Guia(s) NAK: " + ret, this.caminho);

                            if (vGuiasNAK == 0) {
                                this.logerro.Grava("Guia(s) processadas com sucesso. Guia (" + guia + ") " + serie, this.caminho);
                            }
                        } catch (Exception e) {  // A falha geral vira formatada de ProcessaGuiaMobile
                            this.logerro.Grava(e.getMessage(), this.caminho);
                            vGuiasNAK += 1;
                        }

                        if ((!processar.contains("0")) || (vBaixaDuplicidade > 0)) {


                            rt_percDao.updateHrSaida(sequencia, parada, hrsaida, hrcheg, dataAtual, persistencia);
                            rt_percSlaDao.inserirHorarioSaida(sequencia, parada, horaSaidaVei, horaAtual, dataAtual, operador, persistencia);                            // gravando dados em Rt_PercDet 
                            try {

                                Rt_PercDet rt_percdet = new Rt_PercDet();
                                Rt_PercDetDao rt_percdetdao = new Rt_PercDetDao();
                                rt_percdet.setCodFil(codfil);
                                rt_percdet.setSequencia(sequencia);
                                rt_percdet.setParada(Integer.valueOf(parada));
                                rt_percdet.setKM(KM);
                                if (rt_percdetdao.existeDet(rt_percdet, persistencia)) {
                                    rt_percdetdao.AtualizaKMDet(rt_percdet, persistencia);
                                } else {
                                    rt_percdetdao.InserirDet(rt_percdet, persistencia);
                                }
                            } catch (Exception e) {
                                this.logerro.Grava("Rt_PercDet. Erro ao atualizar dados. SeqRota: " + sequencia + " Parada: " + parada + " KM: " + KM + " " + e.getMessage(), this.caminho);
                            }

                            //grava log da baixa (botao X na tela operacoes TV
                            SASLogDao sasLogDao = new SASLogDao();
                            boolean repete = false;
                            int cont = 1;
                            while (!repete) {
                                String seq_log = sasLogDao.maxSasLog(persistencia);
                                repete = sasLogDao.gravaSasLogParada(seq_log, sequencia, parada, data_sql, hrsaida, sCodPessoa, persistencia);
                                if (cont == 20) {
                                    repete = true;
                                }
                                cont++;
                            }

                            // Desativado em 13/03/2017 - Esse processamento foi centralizado em ProcessaGuiaMobile
                            //if ((er.equals("E")) && (!tiposerv.equals("A"))) {
                            //    Trace.gerarTrace(getServletContext(), this.getServletName(),"inciando salvamento da tes saidas", sCodPessoa, param, logerro);
                            //    TesSaidaRtGuiasCxfGuiaDao oTesSaidaRtGuiasCxfGuiaDao = new TesSaidaRtGuiasCxfGuiaDao();
                            //    List<GuiasList> listTsCxfgRtg = oTesSaidaRtGuiasCxfGuiaDao.getTesSaidasRt_GuiasCxfGuiasEntrega(codfil, hora1, sequencia, parada, persistencia);
                            //    if (!listTsCxfgRtg.isEmpty()) {
                            //        BaixaGuiaParadaE(param, listTsCxfgRtg, sequencia, parada, codfil, data_sql, hrsaida, hrcheg, persistencia);
                            //    }
                            //}
                            //grava a posição do cliente, enviada pelo celular
                            try {
                                if (!"".equals(latitude) && !"".equals(longitude)) {
                                    ClientesDao clidao = new ClientesDao();
                                    clidao.updateLglt(latitude, longitude, sequencia, parada, persistencia);
                                }
                            } catch (Exception e) {
                                this.logerro.Grava("Falha ao gravar posição do cliente\r\n" + e.getMessage(), this.caminho);
                            }

                        } else {
                            this.logerro.Grava("Rt_Perc. Horarios nao atualizados por erro em baixa de guia. SeqRota: " + sequencia + " Parada: " + parada + " hrsaida: " + hrsaida + " hrcheg: " + hrcheg + " Obs: " + OBS + " KM: " + KM, this.caminho);

                        }

                    }
                    if ((vRPVNAK + vGuiasNAK) > 0 && (vBaixaDuplicidade == 0)) {   // Se houveram rejeições
                        retorno.put("resp", 0);  //Resposta negativa
                    } else {
                        retorno.put("resp", 1); // Tudo OK  
                    }
                } else {

                    // Processamento de requisições de datas anteriores. Guarda no log para registro
                    // Realizando o salvamento do registors
                    try {
                        MobileProcAnt mobileProcAnt = new MobileProcAnt();
                        mobileProcAnt.setSequencia(mobileProcAntDAO.gerarSequencia(persistencia));
                        mobileProcAnt.setDtProcAnt(dataAnterior);
                        mobileProcAnt.setDtRecebido(dataAtual);
                        mobileProcAnt.setParam(param);
                        mobileProcAnt.setHrRecebido(horaAtual);
                        mobileProcAnt.setComando(input);
                        mobileProcAntDAO.salvarRegistros(mobileProcAnt, persistencia);

                        rt_percDao.updateHrSaida(sequencia, parada, hrsaida, hrcheg, dataAtual, persistencia);
                        rt_percSlaDao.inserirHorarioSaida(sequencia, parada, horaSaidaVei, horaAtual, dataAtual, operador, persistencia);
                        try {

                            Rt_PercDet rt_percdet = new Rt_PercDet();
                            Rt_PercDetDao rt_percdetdao = new Rt_PercDetDao();
                            rt_percdet.setCodFil(codfil);
                            rt_percdet.setSequencia(sequencia);
                            rt_percdet.setParada(Integer.valueOf(parada));
                            rt_percdet.setKM(KM);
                            if (rt_percdetdao.existeDet(rt_percdet, persistencia)) {
                                rt_percdetdao.AtualizaKMDet(rt_percdet, persistencia);
                            } else {
                                rt_percdetdao.InserirDet(rt_percdet, persistencia);
                            }
                        } catch (Exception e) {
                            this.logerro.Grava("Rt_PercDet. Erro ao atualizar dados. SeqRota: " + sequencia + " Parada: " + parada + " KM: " + KM + " " + e.getMessage(), this.caminho);
                        }
                        this.logerro.Grava("MobileProcAnt. Gravacao proc ant ou GTV zerada em recolhimento.", this.caminho);
                    } catch (Exception e) {
                        this.logerro.Grava("MobileProcAnt. Falha ao gravar processamento." + e.getMessage(), this.caminho);
                    }

                    retorno.put("resp", 1); // Tudo OK  
                }

                // Excluir do semaforo
                semaforoDao.removerBaixaTabela(semaforoBaixaHorariosRota, sequencia, persistencia);
                this.logerro.Grava("Semaforo aberto (" + semaforoBaixaHorariosRota + ", " + sequencia + ")", this.caminho);
            } else {
                retorno.put("resp", 0);
                this.logerro.Grava("Baixa existe em semaforo (" + semaforoBaixaHorariosRota + ", " + codCliAuth + ")", this.caminho);
                if (semaforoDao.tempoSemaforo(semaforoBaixaHorariosRota, sequencia, persistencia) > 2) {
                    semaforoDao.removerBaixaTabela(semaforoBaixaHorariosRota, sequencia, persistencia);
                    this.logerro.Grava("Semáforo removido".toUpperCase(), this.caminho);
                }
            }
        } catch (Exception e) {
            this.logerro.Grava("Erro geral: " + e.getMessage(), this.caminho);
            retorno.put("resp", 0);
            retorno.put("erro", e.getMessage());
            if (sequencia != null) {
                try {
                    semaforoDao.removerBaixaTabela(semaforoBaixaHorariosRota, sequencia, persistencia);
                    this.logerro.Grava("Semaforo aberto (" + semaforoBaixaHorariosRota + ", " + sequencia + ")", this.caminho);
                } catch (Exception s) {
                    this.logerro.Grava("Remover Semáforo: " + s.getMessage(), this.caminho);
                }
            }
        } finally {
            try {
                persistencia.FechaConexao();
            } catch (Exception p) {
                this.logerro.Grava("Fecha Conexao: " + p.getMessage(), this.caminho);
            }

            this.logerro.Grava("Resposta: " + gson.toJson(retorno), this.caminho);
        }

        return Response
                .status(Response.Status.OK)
                .type("application/json")
                .entity(gson.toJson(retorno))
                //                .entity(retorno)
                .build();
    }
}
