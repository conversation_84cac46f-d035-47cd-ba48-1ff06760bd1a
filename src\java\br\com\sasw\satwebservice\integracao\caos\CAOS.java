/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.integracao.caos;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Clientes;
import SasBeans.SatWebService.Caos;
import SasBeans.TesCofresMov;
import SasDaos.ClientesDao;
import SasDaos.SemaforoDao;
import SasDaos.TesCofresMovDao;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import javax.net.ssl.HttpsURLConnection;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.UriInfo;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;

/**
 * REST Web Service
 *
 * <AUTHOR>
 */
@Path("/ws-caos/")
public class CAOS {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private String caminho, caminhoLog;
    private final ClientesDao clientesDao;
    private final TesCofresMovDao tesCofresMovDao;
    private List<TesCofresMov> listTesCofresMov;
    private TesCofresMov tesCofresMov;
    private final SemaforoDao semaforoDao;
    private Caos caosModel;
    private HttpsURLConnection httpCon;

    @Context
    private UriInfo context;

    /**
     * Creates a new instance of CAOS
     */
    public CAOS() {
        logerro = new ArquivoLog();
        tesCofresMovDao = new TesCofresMovDao();
        pool = new SasPoolPersistencia();
        pool.setCaminho(this.getClass().getResource("mapconect.txt").getPath().replace("%20", " "));
        caminho = "";
        caminhoLog = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\CAOS\\" + DataAtual.getDataAtual("SQL") + ".txt";
        semaforoDao = new SemaforoDao();
        clientesDao = new ClientesDao();
        caosModel = new Caos();
        listTesCofresMov = new ArrayList<>();
    }

    /**
     * Retrieves representation of an instance of IntegracaoCAOS.CAOS
     *
     * @return an instance of java.lang.String
     */
    @GET
//    @Produces(MediaType.APPLICATION_XML)
    public String getNOP() {
        String resp;
        try {
            //Cria a persistencia
            Persistencia persistencia = pool.getConexao("SATPRESERVE");
//            Persistencia persistencia = pool.getConexao("SASW");
            String ultimaMovimentacao = tesCofresMovDao.ultimaMovimentacaoIntegracao("351", "999999", persistencia);
            resp = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + Xmls.tag("nop", null == ultimaMovimentacao ? "0" : ultimaMovimentacao);
            this.logerro.Grava(resp, caminho);
            persistencia.FechaConexao();
        } catch (Exception e) {
            resp = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + Xmls.tag("erro", e.getMessage());
            this.logerro.Grava("CAOS.getNOP - " + e.getMessage(), caminho);
        }
        return resp;
    }

    /**
     * PUT method for updating or creating an instance of Nota
     *
     * @param content representation for the resource
     */
    @PUT
    @Consumes(MediaType.APPLICATION_XML)
    public void putMov(String content) {

        TesCofresMov mov;
        JSONObject movObj;
        try {
            this.logerro.Grava(content, caminho);
            Persistencia persistencia = pool.getConexao("SATPRESERVE");
            if (!semaforoDao.existeSemaforo("TesCofresMov", "IntegracaoCAOS", getDataAtual("SQL"), persistencia)) {
                semaforoDao.inserirRegistro("SatWeb", getDataAtual("SQL"), getDataAtual("HORA"), "TesCofresMov", "IntegracaoCAOS", persistencia);
                JSONObject objMov = XML.toJSONObject(content);
                int qtd = objMov.getInt("qtd");
                if (qtd > 1) {
                    JSONArray arrayMov = objMov.getJSONArray("mov");
                    for (int i = 0; i < qtd; i++) {
                        try {
                            movObj = arrayMov.getJSONObject(i);
                            mov = new TesCofresMov();
                            mov.setCodCofre(new BigDecimal(String.valueOf(movObj.get("CodCofre"))));
                            mov.setData(LocalDate.parse(String.valueOf(movObj.get("Data")), DateTimeFormatter.ISO_DATE));
                            mov.setHora(String.valueOf(movObj.get("Hora")));
                            mov.setCodCliente(String.valueOf(movObj.get("CodCliente")));
                            mov.setIdUsuario(String.valueOf(movObj.get("idUsuario")));
                            mov.setNomeUsuario(String.valueOf(movObj.get("NomeUsuario")));
                            mov.setValorDeposito(new BigDecimal(String.valueOf(movObj.get("ValorDeposito"))));
                            mov.setTipoMoeda(String.valueOf(movObj.get("TipoMoeda")));
                            mov.setTipoDeposito(String.valueOf(movObj.get("TipoDeposito")));
                            mov.setCodigoBarras(String.valueOf(movObj.get("CodigoBarras")));
                            mov.setStatus(String.valueOf(movObj.get("Status")));
                            mov.setOperador(String.valueOf(movObj.get("Operador")));
                            mov.setDt_Incl(LocalDate.parse(String.valueOf(movObj.get("Dt_Incl")), DateTimeFormatter.ISO_DATE));
                            mov.setHr_Incl(String.valueOf(movObj.get("Hr_Incl")));
                            tesCofresMovDao.inserirMovimentacao(mov, persistencia);
                        } catch (Exception ex) {
                            this.logerro.Grava("CAOS.putMov - " + ex.getMessage(), caminho);
                        }
                    }
                } else if (qtd == 1) {
                    movObj = objMov.getJSONObject("mov");
                    mov = new TesCofresMov();
                    mov.setCodCofre(new BigDecimal(String.valueOf(movObj.get("CodCofre"))));
                    mov.setData(LocalDate.parse(String.valueOf(movObj.get("Data")), DateTimeFormatter.ISO_DATE));
                    mov.setHora(String.valueOf(movObj.get("Hora")));
                    mov.setCodCliente(String.valueOf(movObj.get("CodCliente")));
                    mov.setIdUsuario(String.valueOf(movObj.get("idUsuario")));
                    mov.setNomeUsuario(String.valueOf(movObj.get("NomeUsuario")));
                    mov.setValorDeposito(new BigDecimal(String.valueOf(movObj.get("ValorDeposito"))));
                    mov.setTipoMoeda(String.valueOf(movObj.get("TipoMoeda")));
                    mov.setTipoDeposito(String.valueOf(movObj.get("TipoDeposito")));
                    mov.setCodigoBarras(String.valueOf(movObj.get("CodigoBarras")));
                    mov.setStatus(String.valueOf(movObj.get("Status")));
                    mov.setOperador(String.valueOf(movObj.get("Operador")));
                    mov.setDt_Incl(LocalDate.parse(String.valueOf(movObj.get("Dt_Incl")), DateTimeFormatter.ISO_DATE));
                    mov.setHr_Incl(String.valueOf(movObj.get("Hr_Incl")));
                    tesCofresMovDao.inserirMovimentacao(mov, persistencia);
                }
                semaforoDao.removerBaixaTabela("TesCofresMov", "IntegracaoCAOS", persistencia);
            } else if (semaforoDao.tempoSemaforo("TesCofresMov", "IntegracaoCAOS", persistencia) > 10) {
                semaforoDao.removerBaixaTabela("TesCofresMov", "IntegracaoCAOS", persistencia);
                this.logerro.Grava("Semáforo CAOS removido", caminho);
            }
        } catch (Exception e) {
            this.logerro.Grava("CAOS.putMov - " + e.getMessage(), caminho);
        }
    }

    /**
     * POST
     *
     * @param content representation for the resource
     * @return
     */
    @POST
    @Path("/{empresa}/")
    public String movimentacoesIntegradorPost(@PathParam("empresa") String empresa, String content) {
        String resp = "";

        try {
            Persistencia persistencia = null;
            Clientes cliente = null;
            this.logerro.Grava("Acessou Integração CAOS", this.caminhoLog);
            this.logerro.Grava("Dados INPUT:\r\n" + content, this.caminhoLog);

            // Converter conteúdo em Model "Caos"
            JSONObject jSonData = XML.toJSONObject(content);

            if (!content.equals("")) {
                return "ok";
            }

            if (content.equals("")) {
                return "ok";
            }
            
            Gson gson = new GsonBuilder().create();
            this.caosModel = gson.fromJson(jSonData.getJSONObject("RemoteMessage").toString(), Caos.class);

            // Montar dados para Insert
            tesCofresMov = new TesCofresMov();
            tesCofresMov.setIdUsuario("0");
            tesCofresMov.setTipoDeposito(this.caosModel.getOperation());
            //tesCofresMov.setCodigoBarras(Double.toString(this.caosModel.getBagID()));
            try {
                tesCofresMov.setCodigoBarras(Double.toString(caosModel.getTransactionID()));
            } catch (Exception ex) {
                tesCofresMov.setCodigoBarras("0");
            }
            tesCofresMov.setCodCofre(new BigDecimal(this.caosModel.getDeviceID()));
            tesCofresMov.setNomeUsuario(this.caosModel.getUser());
            //tesCofresMov.setCodCliente("1");
            tesCofresMov.setStatus("NORMAL");
            tesCofresMov.setOperador("HOMOLOG");
            tesCofresMov.setDt_Incl(LocalDate.now());
            tesCofresMov.setHr_Incl(DataAtual.getDataAtual("HORA"));
            listTesCofresMov.add(tesCofresMov);
            DateTimeFormatter data = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate dataIN = LocalDate.parse(this.caosModel.getDate(), data);
            tesCofresMov.setData(dataIN);
            tesCofresMov.setHora(this.caosModel.getTime());
            tesCofresMov.setValorDeposito(BigDecimal.ZERO);

            Caos.CaosDestDetails caosDestDetail = this.caosModel.getDestDetails().getCount();
            
            switch (caosDestDetail.getType()) {
                case "B":
                    tesCofresMov.setValorDeposito(tesCofresMov.getValorDeposito().add(new BigDecimal(caosDestDetail.getDen()).multiply(new BigDecimal(caosDestDetail.getQty()))));
                    tesCofresMov.setTipoMoeda(caosDestDetail.getCurr());
                    break;
            }

            // Inserir se movimentacao for > que 0
            if (tesCofresMov.getValorDeposito() != BigDecimal.ZERO) {

                logerro.Grava("Recebido 1 nova movimentação.", "C:" + "\\" + "SatelliteServer" + "\\" + "log" + "\\" + "" + LocalDate.now().toString() + "" + "\\" + "log.txt");

                StringBuilder xmlMovimentacao;
                StringBuilder xmlMovimentacoes = new StringBuilder();
                xmlMovimentacoes.append(Xmls.tag("qtd", "1"));

                logerro.Grava("Montando resposta para o WebService.", "C:" + "\\" + "SatelliteServer" + "\\" + "log" + "\\" + "" + LocalDate.now().toString() + "" + "\\" + "log.txt");

                xmlMovimentacao = new StringBuilder();
                xmlMovimentacao.append(Xmls.tag("id", tesCofresMov.getId()));
                xmlMovimentacao.append(Xmls.tag("CodCofre", tesCofresMov.getCodCofre()));
                xmlMovimentacao.append(Xmls.tag("Data", tesCofresMov.getData()));
                xmlMovimentacao.append(Xmls.tag("Hora", tesCofresMov.getHora()));
                xmlMovimentacao.append(Xmls.tag("CodCliente", tesCofresMov.getCodCliente()));
                xmlMovimentacao.append(Xmls.tag("idUsuario", tesCofresMov.getIdUsuario()));
                xmlMovimentacao.append(Xmls.tag("NomeUsuario", tesCofresMov.getNomeUsuario()));
                xmlMovimentacao.append(Xmls.tag("ValorDeposito", tesCofresMov.getValorDeposito()));
                xmlMovimentacao.append(Xmls.tag("TipoMoeda", tesCofresMov.getTipoMoeda()));
                xmlMovimentacao.append(Xmls.tag("TipoDeposito", tesCofresMov.getTipoDeposito()));
                xmlMovimentacao.append(Xmls.tag("CodigoBarras", tesCofresMov.getCodigoBarras()));
                xmlMovimentacao.append(Xmls.tag("Status", tesCofresMov.getStatus()));
                xmlMovimentacao.append(Xmls.tag("Operador", tesCofresMov.getOperador()));
                xmlMovimentacao.append(Xmls.tag("Dt_Incl", tesCofresMov.getDt_Incl()));
                xmlMovimentacao.append(Xmls.tag("Hr_Incl", tesCofresMov.getHr_Incl()));
                xmlMovimentacoes.append(Xmls.tag("mov", xmlMovimentacao));

                logerro.Grava(xmlMovimentacoes.toString(), "C:" + "\\" + "SatelliteServer" + "\\" + "log" + "\\" + "" + LocalDate.now().toString() + "" + "\\" + "log.txt");
                logerro.Grava("Preparando envio da resposta.", "C:" + "\\" + "SatelliteServer" + "\\" + "log" + "\\" + "" + LocalDate.now().toString() + "" + "\\" + "log.txt");

                // Eviar Dados para WebService PUT
                URL url = new URL("https://mobile.sasw.com.br/SatWebService/api/ws-mastercoin/" + empresa + "/" + caosModel.getDeviceID() + "/");
                httpCon = (HttpsURLConnection) url.openConnection();
                httpCon.setDoOutput(true);
                httpCon.setRequestMethod("PUT");
                logerro.Grava("Conexão WebService OK, obtendo resposta.", "C:" + "\\" + "SatelliteServer" + "\\" + "log" + "\\" + "" + LocalDate.now().toString() + "" + "\\" + "log.txt");

                logerro.Grava("Enviando resposta.", "C:" + "\\" + "SatelliteServer" + "\\" + "log" + "\\" + "" + LocalDate.now().toString() + "" + "\\" + "log.txt");
                logerro.Grava("Resposta: " + xmlMovimentacoes.toString(), "C:" + "\\" + "SatelliteServer" + "\\" + "log" + "\\" + "" + LocalDate.now().toString() + "" + "\\" + "log.txt");
                OutputStreamWriter out = new OutputStreamWriter(httpCon.getOutputStream());
                out.write(xmlMovimentacoes.toString());
                out.close();

                BufferedReader in = new BufferedReader(new InputStreamReader(httpCon.getInputStream()));
                StringBuilder resposta = new StringBuilder();
                while ((resp = in.readLine()) != null) {
                    resposta.append(resp);
                }
                in.close();

                /**
                 * Convertendo a resposta recebida de XML para JSON
                 */
                JSONObject xmlResp = XML.toJSONObject(resposta.toString());

                // Verificando se tem mensagem de erro
                String erro = String.valueOf(xmlResp.get("erro"));

                if (!erro.equals("")) {
                    throw new Exception(erro);
                }

                resp = String.valueOf(xmlResp.get("msg"));
                logerro.Grava(resp, "C:" + "\\" + "SatelliteServer" + "\\" + "log" + "\\" + "" + LocalDate.now().toString() + "" + "\\" + "log.txt");
                logerro.Grava("Processo concluído com sucesso.", "C:" + "\\" + "SatelliteServer" + "\\" + "log" + "\\" + "" + LocalDate.now().toString() + "" + "\\" + "log.txt");
            } else {
                logerro.Grava("Recebido 1 movimentação vazia.", "C:" + "\\" + "SatelliteServer" + "\\" + "log" + "\\" + "" + LocalDate.now().toString() + "" + "\\" + "log.txt");
            }

            return resp;
        } catch (Exception ex) {
            this.logerro.Grava("Conteúdo\r\n" + content, this.caminhoLog);
            this.logerro.Grava("Falha\r\n" + ex.getMessage(), this.caminhoLog);
            resp = Xmls.tag("resp", ex.getMessage());
        }

        return resp;
    }

    public static TesCofresMov obterMovimentacao(TesCofresMov tesCofresMov, JSONObject depositJObject) {
        tesCofresMov.setValorDeposito(BigDecimal.ZERO);
        Object banknoteObject = depositJObject.get("Banknote");
        if (banknoteObject instanceof JSONObject) {
            JSONObject banknoteJObject = (JSONObject) banknoteObject;
            try {
                tesCofresMov.setValorDeposito(tesCofresMov.getValorDeposito()
                        .add((new BigDecimal(banknoteJObject.get("Denom").toString())
                                .multiply(new BigDecimal(banknoteJObject.get("Qty").toString())))
                        ));
            } catch (Exception e) {

            }
        } else if (banknoteObject instanceof JSONArray) {
            JSONArray banknoteJArray = (JSONArray) banknoteObject;
            JSONObject banknoteJObject;
            for (Object object : banknoteJArray) {
                banknoteJObject = (JSONObject) object;
                try {
                    tesCofresMov.setValorDeposito(tesCofresMov.getValorDeposito()
                            .add((new BigDecimal(banknoteJObject.get("Denom").toString())
                                    .multiply(new BigDecimal(banknoteJObject.get("Qty").toString())))
                            ));
                } catch (Exception e) {

                }
            }
        }
        tesCofresMov.setNomeUsuario("IMP-KISAN");
        tesCofresMov.setStatus("NORMAL");
        tesCofresMov.setOperador("IMP-KISAN");
        tesCofresMov.setDt_Incl(LocalDate.now());
        tesCofresMov.setHr_Incl(getDataAtual("HORA"));
        return tesCofresMov;
    }
}
