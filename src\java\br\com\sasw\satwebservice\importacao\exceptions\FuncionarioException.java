/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.importacao.exceptions;

/**
 *
 * <AUTHOR>
 */
public class FuncionarioException extends Exception{
    
    private static final long serialVersionUID = 7718828512143293558L;
    private final FuncionarioErrorCode code;
    
    public FuncionarioException(FuncionarioErrorCode code) {
            super();
            this.code = code;
    }

    public FuncionarioException(String message, Throwable cause, FuncionarioErrorCode code) {
            super(message, cause);
            this.code = code;
    }

    public FuncionarioException(String message, FuncionarioErrorCode code) {
            super(message);
            this.code = code;
    }

    public FuncionarioException(Throwable cause, FuncionarioErrorCode code) {
            super(cause);
            this.code = code;
    }

    public FuncionarioErrorCode getCode() {
            return this.code;
    }
        
    public static class FuncionarioErrorCode implements ErrorCode{

        public FuncionarioErrorCode() {
        }

        public FuncionarioErrorCode(int code) {
            this.code = code;
        }
        
        private int code;
        private String status;

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        @Override
        public String getStatus() {
            switch(this.code){
                case 1:
                    status = "Funcionário cadastrado com sucesso.";
                    break;
                case 2:
                    status = "Funcionário atualizado com sucesso.";
                    break;
                case 3:
                    status = "Cadastro de funcionário desativado. Funcionário ativado com sucesso.";
                    break;
                case 6:
                    status = "Funcionário já cadastrado. Cadastro não processado.";
                    break;
                case 7:
                    status = "Funcionário não encontrado. Desativação não processada.";
                    break;
                case 9:
                    status = "Dados incorretos/insuficientes. Cadastro não processado.";
                    break;
                default:
                    status = "Erro desconhecido.";
            }
            return status;
        }
    }
}
