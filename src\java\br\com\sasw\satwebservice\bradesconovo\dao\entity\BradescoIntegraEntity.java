/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.dao.entity;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 *
 * <AUTHOR>
 */
public class BradescoIntegraEntity {
    
    private Long sequencia;
    private String tipoOperacao;
    private String paramOperacao;
    private String infCtrlSolicitacao;
    private String payload;
    private String operador;
    private LocalDate dataInclusao;
    private LocalTime horaInclusao;

    public Long getSequencia() {
        return sequencia;
    }

    public void setSequencia(Long sequencia) {
        this.sequencia = sequencia;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public String getParamOperacao() {
        return paramOperacao;
    }

    public void setParamOperacao(String paramOperacao) {
        this.paramOperacao = paramOperacao;
    }

    public String getInfCtrlSolicitacao() {
        return infCtrlSolicitacao;
    }

    public void setInfCtrlSolicitacao(String infCtrlSolicitacao) {
        this.infCtrlSolicitacao = infCtrlSolicitacao;
    }

    public String getPayload() {
        return payload;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public LocalDate getDataInclusao() {
        return dataInclusao;
    }

    public void setDataInclusao(LocalDate dataInclusao) {
        this.dataInclusao = dataInclusao;
    }

    public LocalTime getHoraInclusao() {
        return horaInclusao;
    }

    public void setHoraInclusao(LocalTime horaInclusao) {
        this.horaInclusao = horaInclusao;
    }
    
}
