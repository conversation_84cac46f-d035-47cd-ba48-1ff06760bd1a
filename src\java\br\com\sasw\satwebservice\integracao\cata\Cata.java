/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.integracao.cata;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.EmailsEnviar;
import SasBeans.TesCofresMov;
import SasDaos.EmailsEnviarDao;
import SasDaos.SemaforoDao;
import SasDaos.TesCofresMovDao;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.sasbeans.CataEquip;
import br.com.sasw.pacotesuteis.sasbeans.CataMov;
import br.com.sasw.pacotesuteis.sasdaos.CataEquipDao;
import br.com.sasw.pacotesuteis.sasdaos.CataMovDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.LerArquivo;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.UriInfo;
import static javax.xml.bind.DatatypeConverter.parseDateTime;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;

/**
 *
 * <AUTHOR>
 */
@Path("ws-cata")
public class Cata {

    private final ArquivoLog logerro;
    private String caminho;
    private boolean corpvs;
    private final SasPoolPersistencia pool;
    private final CataEquipDao cataequipdao;
    private final CataMovDao catasmovdao;
    private final SemaforoDao semaforoDao;
    private final TesCofresMovDao tesCofresMovDao;

    private static final String URL_TRANSACOES = "https://clientapi.catamoeda.com.br/api/v1/client/Transaction?";

    @Context
    private UriInfo context;

    public Cata() {
        //localhost:8080/SatWebService/api/ws-cata?username=corpvs.integration&password=Cata1234&machineId=CCO000532&data=2018-11-01
        logerro = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Integracao"
                + "\\Cata\\" + DataAtual.getDataAtual("SQL") + ".txt";
        pool = new SasPoolPersistencia();
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
        catasmovdao = new CataMovDao();
        semaforoDao = new SemaforoDao();
        cataequipdao = new CataEquipDao();
        tesCofresMovDao = new TesCofresMovDao();
    }

    @GET
    @Path("/{empresa}/{cofre}/{data}/")
    @Produces(MediaType.APPLICATION_XML)
    public String getXml(@PathParam("empresa") String empresa, @PathParam("cofre") String cofre, @PathParam("data") String data,
            @QueryParam("username") String username, @QueryParam("password") String password) {
        this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ERRO\\Integracao\\Cata\\"
                + getDataAtual("SQL") + ".txt";
        String resp = "";
        try {
            if (empresa == null || empresa.equals("")) {
                throw new Exception("Empresa inválida");
            }

            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(empresa);
            Persistencia CORPVSRECIFE = null;
            if (null == persistencia) {
                throw new Exception("Empresa inválida: " + empresa);
            } else if (empresa.equals("SATCORPVS")) {
                this.corpvs = true;
                CORPVSRECIFE = pool.getConexao("SATCORPVSPE");
            }

            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Integracao\\Cata\\"
                    + empresa + "\\" + getDataAtual("SQL") + ".txt";
            this.logerro.Grava(gerarLogURL(empresa, username, password), this.caminho);

            JsonParser jsonParser = new JsonParser();
            String authorization = identificacao(jsonParser, username, password);
            if (authorization != null) {
                movimentacoesCofre(cofre, data, authorization, jsonParser, persistencia, CORPVSRECIFE);
            }

            resp = Xmls.tag("resp", "1");
        } catch (Exception e) {
            resp = Xmls.tag("resp", e.getMessage());
        }
        return resp;
    }

    private void movimentacoesCofre(String cofre, String data, String tokenId, JsonParser jsonParser, Persistencia persistencia, Persistencia CORPVSRECIFE) {
        try {
            this.logerro.Grava("movimentacoesCofre", this.caminho);
            StringBuilder url;

            CataMovDao cataMovDao = new CataMovDao();

            URL obj;
            HttpURLConnection con;
            int responseCode;
            BufferedReader in;
            String inputLine;
            StringBuilder response;
            JsonArray transactionsArray;
            JsonObject transactionsObject;
            CataMov cataMov;
            Instant dataHora;
            DateTimeFormatter formatterData = DateTimeFormatter.ofPattern("yyyyMMdd").withZone(ZoneId.of("America/Sao_Paulo"));
            DateTimeFormatter formatterHora = DateTimeFormatter.ofPattern("HH:mm:ss").withZone(ZoneId.of("America/Sao_Paulo"));

            int transacoes = 0;
            boolean inicio = true;
            for (int start = 0; inicio || transacoes == 100; start = start + transacoes) {
                inicio = false;

                url = new StringBuilder(URL_TRANSACOES);
                url.append("Filter.ByMachineSerial=").append(cofre);
                url.append("&Filter.ByDateRange.Start=").append(data).append(URLEncoder.encode("T00:00+00:00", StandardCharsets.UTF_8.toString())); // Início do período = última data do cofre
                url.append("&Filter.ByDateRange.End=").append(data).append(URLEncoder.encode("T23:59+00:00", StandardCharsets.UTF_8.toString())); // Início do período = última data do cofre
                url.append("&Filter.Start=").append(start);
                url.append("&Filter.Quantity=100");

                obj = new URL(url.toString());
                con = (HttpURLConnection) obj.openConnection();

                con.setRequestProperty("Authorization", tokenId);
                con.setRequestProperty("Method", "GET");

                responseCode = con.getResponseCode();

                if (responseCode == HttpURLConnection.HTTP_OK) { //success
                    in = new BufferedReader(new InputStreamReader(con.getInputStream()));
                    response = new StringBuilder();

                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                    in.close();

                    this.logerro.Grava(url.toString() + "\r\n" + response.toString(), this.caminho);

                    transactionsArray = (JsonArray) jsonParser.parse(response.toString()).getAsJsonObject().get("transactions");
                    transacoes = transactionsArray.size();

                    for (int i = 0; i < transactionsArray.size(); i++) {
                        transactionsObject = (JsonObject) transactionsArray.get(i);
                        cataMov = new CataMov();
                        cataMov.setID(transactionsObject.get("id").toString().replace("\"", ""));
                        cataMov.setIDEquip(transactionsObject.get("machineId").toString().replace("\"", ""));
                        dataHora = parseDateTime(transactionsObject.get("date").toString().replace("\"", "")).toInstant();
                        cataMov.setData(formatterData.format(dataHora));
                        cataMov.setHora(formatterHora.format(dataHora));
                        cataMov.setOperacao(transactionsObject.get("operation").toString());
                        cataMov.setFluxo(transactionsObject.get("flow").toString());
                        cataMov.setValor(transactionsObject.get("value").toString());
                        cataMov.setDt_incl(getDataAtual("SQL"));
                        cataMov.setHr_incl(getDataAtual("HORA"));
                        cataMov.setLido("0");

                        if (!cataMovDao.existeTransacao(cataMov.getID(), persistencia)) {
                            cataMovDao.inserirTransacao(cataMov, persistencia);
                        }
                        if (this.corpvs) {
                            if (!cataMovDao.existeTransacao(cataMov.getID(), CORPVSRECIFE)) {
                                cataMovDao.inserirTransacao(cataMov, CORPVSRECIFE);
                            }
                        }
                    }

                } else {
                    transacoes = 0;

                    in = new BufferedReader(new InputStreamReader(con.getErrorStream()));
                    response = new StringBuilder();

                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                    in.close();

                    this.logerro.Grava(url.toString() + "\r\n" + response.toString(), this.caminho);
                }
            }
        } catch (Exception e) {
            this.logerro.Grava(e.getMessage(), this.caminho);
        }
    }

    /**
     * Retrieves representation of an instance of
     * br.com.sasw.integracao.cata.Cata
     *
     * @param empresa
     * @param username
     * @param password
     * @return an instance of java.lang.String
     */
    @GET
    @Path("/{empresa}")
    @Produces(MediaType.APPLICATION_XML)
    public String getXml(@PathParam("empresa") String empresa, @QueryParam("username") String username, @QueryParam("password") String password) {
        this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ERRO\\Integracao\\Cata\\"
                + getDataAtual("SQL") + ".txt";
        String resp = "";
        try {
            if (empresa == null || empresa.equals("")) {
                throw new Exception("Empresa inválida");
            }

            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(empresa);
            Persistencia CORPVSRECIFE = null;
            if (null == persistencia) {
                throw new Exception("Empresa inválida: " + empresa);
            } else if (empresa.equals("SATCORPVS")) {
                this.corpvs = true;
                CORPVSRECIFE = pool.getConexao("SATCORPVSPE");
            }

            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Integracao\\Cata\\"
                    + empresa + "\\" + getDataAtual("SQL") + ".txt";
            this.logerro.Grava(gerarLogURL(empresa, username, password), this.caminho);

            JsonParser jsonParser = new JsonParser();
            String authorization = identificacao(jsonParser, username, password);
            if (authorization != null) {

                atualizaBaseCofres(authorization, jsonParser, persistencia, CORPVSRECIFE);
                verificaStatusCofres(authorization, jsonParser, persistencia, CORPVSRECIFE);

                movimentacoesAtrasadas(authorization, jsonParser, persistencia, CORPVSRECIFE);
                movimentacoes(authorization, jsonParser, persistencia, CORPVSRECIFE);
            }

            resp = Xmls.tag("resp", "1");
        } catch (Exception e) {
            resp = Xmls.tag("resp", e.getMessage());
        }
        return resp;
    }

    private String identificacao(JsonParser jsonParser, String username, String password) {
        String tokenId = null;
        try {
            this.logerro.Grava("identificacao", this.caminho);
            URL obj = new URL("https://identification.catamoeda.com.br/api/IdentificationService/login");
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();

            con.setRequestProperty("Content-Type", "application/json;");
            con.setRequestProperty("Accept", "application/json");
            con.setRequestProperty("Method", "POST");

            // For POST only - START
            con.setDoOutput(true);
            OutputStream os = con.getOutputStream();
            os.write(("{\"username\": \"" + username + "\",\"password\": \"" + password + "\"}").getBytes("UTF-8"));
            os.flush();
            os.close();
            // For POST only - END

            int responseCode = con.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) { //success
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                this.logerro.Grava(obj.toString() + "\r\n" + response.toString(), this.caminho);

                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject().getAsJsonObject("token");
                tokenId = jsonObject.get("id").getAsString();

            } else {
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getErrorStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

                // print result
                this.logerro.Grava(obj.toString() + "\r\n" + response.toString(), this.caminho);
            }
        } catch (Exception e) {
            this.logerro.Grava(e.getMessage(), this.caminho);
        }
        return tokenId;
    }

    private void atualizaBaseCofres(String tokenId, JsonParser jsonParser, Persistencia persistencia, Persistencia CORPVSRECIFE) {
        this.logerro.Grava("atualizaBaseCofres", this.caminho);
        try {
            URL obj = new URL("https://clientapi.catamoeda.com.br/api/v1/client/Machine/");
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();

            con.setRequestProperty("Authorization", tokenId);
            con.setRequestProperty("Method", "GET");

            int responseCode = con.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) { //success
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), "UTF-8"));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                this.logerro.Grava(obj.toString() + "\r\n" + response.toString(), this.caminho);

                JsonArray machinesArray = (JsonArray) jsonParser.parse(response.toString()).getAsJsonObject().get("machines");
                JsonObject machineObject;
                JsonObject allocationObject;

                CataEquipDao cataEquipDao = new CataEquipDao();
                CataEquip cataEquip;
                List<CataEquip> cataEquipList = cataEquipDao.listaEquipamentos(persistencia);

                for (int i = 0; i < machinesArray.size(); i++) {
                    machineObject = (JsonObject) machinesArray.get(i);
                    allocationObject = ((JsonObject) machineObject.get("allocation"));

                    cataEquip = new CataEquip();
                    cataEquip.setSerial(machineObject.get("serialNumber").toString().replace("\"", ""));
                    cataEquip.setIDEquip(machineObject.get("id").toString().replace("\"", ""));
                    cataEquip.setGeo(allocationObject.get("geo").toString().replace("\"", ""));
                    cataEquip.setAddress(allocationObject.get("address").toString().replace("\"", ""));

                    if (cataEquipList.indexOf(cataEquip) == -1) {
                        cataEquipDao.inserirEquipamento(cataEquip, persistencia);
                    }
                    if (this.corpvs) {
                        if (cataEquipList.indexOf(cataEquip) == -1) {
                            cataEquipDao.inserirEquipamento(cataEquip, CORPVSRECIFE);
                        }
                    }
                }
            } else {
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getErrorStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

                this.logerro.Grava(obj.toString() + "\r\n" + response.toString(), this.caminho);
            }
        } catch (Exception e) {
            this.logerro.Grava(e.getMessage(), this.caminho);
        }
    }

    private void verificaStatusCofres(String tokenId, JsonParser jsonParser, Persistencia persistencia, Persistencia CORPVSRECIFE) {
        this.logerro.Grava("verificaStatusCofres", this.caminho);
        try {
            URL obj = new URL("https://clientapi.catamoeda.com.br/api/v1/client/Machine/status");
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();

            con.setRequestProperty("Authorization", tokenId);
            con.setRequestProperty("Method", "GET");

            int responseCode = con.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) { //success
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), "UTF-8"));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                this.logerro.Grava(obj.toString() + "\r\n" + response.toString(), this.caminho);

                JsonArray statusArray = (JsonArray) jsonParser.parse(response.toString()).getAsJsonObject().get("status");
                JsonObject statusObject;
                JsonObject machineObject;

                CataEquipDao cataEquipDao = new CataEquipDao();
                CataEquip cataEquip, ce;
                List<CataEquip> cataEquipList = cataEquipDao.listaEquipamentos(persistencia);
                int indice;

                Persistencia satellite = null;
                EmailsEnviarDao emailsEnviarDao = new EmailsEnviarDao();
                EmailsEnviar emailsEnviar;
                String relatorio, modeloRelatorio = LerArquivo.obterConteudo(Cata.class.getResourceAsStream("email.html"));
                boolean enviarRelatorio;

                Instant dataHora, dataHoraAtual, dataHoraEnvio;
                DateTimeFormatter formatterDataRelatorio = DateTimeFormatter.ofPattern("dd/MM/yyyy").withZone(ZoneId.of("America/Sao_Paulo"));
                DateTimeFormatter formatterData = DateTimeFormatter.ofPattern("yyyyMMdd").withZone(ZoneId.of("America/Sao_Paulo"));
                DateTimeFormatter formatterHora = DateTimeFormatter.ofPattern("HH:mm:ss").withZone(ZoneId.of("America/Sao_Paulo"));

                for (int i = 0; i < statusArray.size(); i++) {
                    statusObject = (JsonObject) statusArray.get(i);
                    machineObject = ((JsonObject) statusObject.get("machine"));

                    cataEquip = new CataEquip();
                    cataEquip.setSerial(machineObject.get("serialNumber").toString().replace("\"", ""));
                    cataEquip.setIDEquip(machineObject.get("id").toString().replace("\"", ""));
                    cataEquip.setEnvio("N");
                    cataEquip.setDt_Envio(null);
                    cataEquip.setHr_Envio(null);

                    indice = cataEquipList.indexOf(cataEquip);
                    if (indice >= 0) {
                        ce = cataEquipList.get(indice);
                        enviarRelatorio = false;
                        relatorio = null;

                        dataHoraAtual = parseDateTime(LocalDateTime.now().minusHours(8).toString()).toInstant();

                        try {
                            cataEquip.setStatus(statusObject.get("status").toString().replace("\"", ""));
                            dataHora = parseDateTime(statusObject.get("lastPing").toString().replace("\"", "")).toInstant();
                            cataEquip.setData(formatterData.format(dataHora));
                            cataEquip.setHora(formatterHora.format(dataHora));

                            /**
                             * A conversão da data para Instant retorna em GMT,
                             * assim como a de LocalDateTime.now().
                             */
                            if (!getDataAtual("SQL").equals(cataEquip.getData()) // Se a data da última comunicação for diferente de hoje,
                                    && !getDataAtual("SQL").equals(ce.getDt_Envio())) { // e a data de envio for diferente de hoje também, enviar relatório.
                                enviarRelatorio = true;

                                relatorio = modeloRelatorio.replace("@endereco", ce.getAddress())
                                        .replace("@data", formatterDataRelatorio.format(dataHora))
                                        .replace("@hora", formatterHora.format(dataHora));
                            }
                        } catch (Exception e) {
                            this.logerro.Grava(cataEquip.getSerial() + "\r\n" + e.getMessage(), this.caminho);

                            if (!getDataAtual("SQL").equals(cataEquip.getData()) // Se a data da última comunicação for diferente de hoje,
                                    && !getDataAtual("SQL").equals(ce.getDt_Envio())) {
                                enviarRelatorio = true;

                                relatorio = modeloRelatorio.replace("@endereco", ce.getAddress())
                                        .replace(" desde o dia @data as @hora", "");
                            }
                        }

                        if (enviarRelatorio && relatorio != null) {

//                        System.out.println(relatorio);
                            if (satellite == null) {
                                satellite = pool.getConexao("SATELLITE");
                            }

                            if (ce.getEmail() != null && !ce.getEmail().equals("")) {
                                emailsEnviar = new EmailsEnviar();
                                emailsEnviar.setRemet_email("<EMAIL>");
                                emailsEnviar.setRemet_nome("CORPVS");
                                emailsEnviar.setDest_nome(ce.getNome());
                                emailsEnviar.setParametro("SATCORPVS");
                                emailsEnviar.setCodFil(ce.getCodFil());
                                emailsEnviar.setCodCli(ce.getCodCli());
                                emailsEnviar.setAssunto("Alerta Cofre Offline");
                                emailsEnviar.setSmtp("smtplw.com.br");
                                emailsEnviar.setMensagem(relatorio);
                                emailsEnviar.setAut_login("sasw");
                                emailsEnviar.setAut_senha("xNiadJEj9607");
                                emailsEnviar.setPorta(587);
                                emailsEnviar.setDest_email(ce.getEmail());
//                            System.out.println(relatorio);

                                emailsEnviarDao.inserirEmail(emailsEnviar, satellite);
                                this.logerro.Grava("Email enviado: " + cataEquip.getSerial(), this.caminho);
                            }

                            cataEquip.setEnvio("S");
                            cataEquip.setDt_Envio(getDataAtual("SQL"));
                            cataEquip.setHr_Envio(getDataAtual("HORA"));

                            cataEquipDao.atualizarStatusEquipamento(cataEquip, persistencia);
                            if (this.corpvs) {
                                cataEquipDao.atualizarStatusEquipamento(cataEquip, CORPVSRECIFE);
                            }
                        }
                    }
                }
            } else {
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getErrorStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

                this.logerro.Grava(obj.toString() + "\r\n" + response.toString(), this.caminho);
            }
        } catch (Exception e) {
            this.logerro.Grava(e.getMessage(), this.caminho);
        }
    }

    private void movimentacoesAtrasadas(String tokenId, JsonParser jsonParser, Persistencia persistencia, Persistencia CORPVSRECIFE) {
        try {
            this.logerro.Grava("movimentacoesAtrasadas", this.caminho);

            StringBuilder url;

            CataMovDao cataMovDao = new CataMovDao();

            // Busca maior data executada
            String dataInicio = cataMovDao.obterDataUltimaMovimentacao(persistencia);
            List<CataMov> cofresAtrasados = cataMovDao.obterCofresAntesUltimaMovimentacao(dataInicio, persistencia);

            int transacoes;
            boolean inicio;

            URL obj;
            HttpURLConnection con;
            int responseCode;
            BufferedReader in;
            String inputLine;
            StringBuilder response;
            JsonArray transactionsArray;
            JsonObject transactionsObject;
            CataMov cataMov;
            Instant dataHora;
            DateTimeFormatter formatterData = DateTimeFormatter.ofPattern("yyyyMMdd").withZone(ZoneId.of("America/Sao_Paulo"));
            DateTimeFormatter formatterHora = DateTimeFormatter.ofPattern("HH:mm:ss").withZone(ZoneId.of("America/Sao_Paulo"));

            for (CataMov cofreAtrasado : cofresAtrasados) {
                if (!cofreAtrasado.getIDEquip().equals("")) {
                    transacoes = 0;
                    inicio = true;
                    for (int start = 0; inicio || transacoes == 100; start = start + transacoes) {
                        inicio = false;

                        url = new StringBuilder(URL_TRANSACOES);

                        url.append("Filter.ByMachineSerial=").append(cofreAtrasado.getIDEquip()); // Código do cofre
                        url.append("&Filter.ByDateRange.Start=").append(cofreAtrasado.getData()).append(URLEncoder.encode("T" + cofreAtrasado.getHora() + "+03:00", StandardCharsets.UTF_8.toString())); // Início do período = última data do cofre
                        url.append("&Filter.ByDateRange.End=").append(dataInicio).append(URLEncoder.encode("T23:59+03:00", StandardCharsets.UTF_8.toString())); // Início do período = última data do cofre
                        url.append("&Filter.Start=").append(start);
                        url.append("&Filter.Quantity=100");

                        obj = new URL(url.toString());
                        con = (HttpURLConnection) obj.openConnection();

                        con.setRequestProperty("Authorization", tokenId);
                        con.setRequestProperty("Method", "GET");

                        responseCode = con.getResponseCode();

                        if (responseCode == HttpURLConnection.HTTP_OK) { //success
                            in = new BufferedReader(new InputStreamReader(con.getInputStream()));
                            response = new StringBuilder();

                            while ((inputLine = in.readLine()) != null) {
                                response.append(inputLine);
                            }
                            in.close();

                            this.logerro.Grava(url.toString() + "\r\n" + response.toString(), this.caminho);

                            transactionsArray = (JsonArray) jsonParser.parse(response.toString()).getAsJsonObject().get("transactions");
                            transacoes = transactionsArray.size();

                            for (int i = 0; i < transactionsArray.size(); i++) {
                                transactionsObject = (JsonObject) transactionsArray.get(i);
                                cataMov = new CataMov();
                                cataMov.setID(transactionsObject.get("id").toString().replace("\"", ""));
                                cataMov.setIDEquip(transactionsObject.get("machineId").toString().replace("\"", ""));
                                dataHora = parseDateTime(transactionsObject.get("date").toString().replace("\"", "")).toInstant();
                                cataMov.setData(formatterData.format(dataHora));
                                cataMov.setHora(formatterHora.format(dataHora));
                                cataMov.setOperacao(transactionsObject.get("operation").toString());
                                cataMov.setFluxo(transactionsObject.get("flow").toString());
                                cataMov.setValor(transactionsObject.get("value").toString());
                                cataMov.setDt_incl(getDataAtual("SQL"));
                                cataMov.setHr_incl(getDataAtual("HORA"));
                                cataMov.setLido("0");

                                if (!cataMovDao.existeTransacao(cataMov.getID(), persistencia)) {
                                    cataMovDao.inserirTransacao(cataMov, persistencia);
                                }
                                if (this.corpvs) {
                                    if (!cataMovDao.existeTransacao(cataMov.getID(), CORPVSRECIFE)) {
                                        cataMovDao.inserirTransacao(cataMov, CORPVSRECIFE);
                                    }
                                }
                            }

                            this.logerro.Grava(response.toString(), this.caminho);

                        } else {
                            transacoes = 0;

                            in = new BufferedReader(new InputStreamReader(con.getErrorStream()));
                            response = new StringBuilder();

                            while ((inputLine = in.readLine()) != null) {
                                response.append(inputLine);
                            }
                            in.close();

                            this.logerro.Grava(url.toString() + "\r\n" + response.toString(), this.caminho);
                        }
                    }
                }
            }
        } catch (Exception e) {
            this.logerro.Grava(e.getMessage(), this.caminho);
        }
    }

    private void movimentacoes(String tokenId, JsonParser jsonParser, Persistencia persistencia, Persistencia CORPVSRECIFE) throws Exception {
        try {
            this.logerro.Grava("movimentacoes", this.caminho);
            StringBuilder url;

            CataMovDao cataMovDao = new CataMovDao();

            // Busca maior data executada
            URL obj;
            HttpURLConnection con;
            int responseCode;
            BufferedReader in;
            String inputLine;
            StringBuilder response;
            JsonArray transactionsArray;
            JsonObject transactionsObject;
            CataMov cataMov;
            Instant dataHora;
            DateTimeFormatter formatterData = DateTimeFormatter.ofPattern("yyyyMMdd").withZone(ZoneId.of("America/Sao_Paulo"));
            DateTimeFormatter formatterHora = DateTimeFormatter.ofPattern("HH:mm:ss").withZone(ZoneId.of("America/Sao_Paulo"));

            int transacoes = 0;
            boolean inicio = true;
            for (int start = 0; inicio || transacoes == 100; start = start + transacoes) {
                inicio = false;

                url = new StringBuilder(URL_TRANSACOES);

                url.append("Filter.ByDateRange.Start=").append(URLEncoder.encode("2020-01-07", StandardCharsets.UTF_8.toString())); // Início do período = última data do cofre
                //url.append("Filter.ByDateRange.Start=").append(getDataAtual("SQL-L")).append(URLEncoder.encode("T00:00+00:00", StandardCharsets.UTF_8.toString())); // Início do período = última data do cofre
                url.append("&Filter.ByDateRange.End=").append(getDataAtual("SQL-L")).append(URLEncoder.encode("T23:59+00:00", StandardCharsets.UTF_8.toString())); // Início do período = última data do cofre
                url.append("&Filter.Start=").append(start);
                url.append("&Filter.Quantity=100");

                obj = new URL(url.toString());
                con = (HttpURLConnection) obj.openConnection();

                con.setRequestProperty("Authorization", tokenId);
                con.setRequestProperty("Method", "GET");

                responseCode = con.getResponseCode();

                if (responseCode == HttpURLConnection.HTTP_OK) { //success
                    in = new BufferedReader(new InputStreamReader(con.getInputStream()));
                    response = new StringBuilder();

                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                    in.close();

                    this.logerro.Grava(url.toString() + "\r\n" + response.toString(), this.caminho);

                    transactionsArray = (JsonArray) jsonParser.parse(response.toString()).getAsJsonObject().get("transactions");
                    transacoes = transactionsArray.size();

                    for (int i = 0; i < transactionsArray.size(); i++) {
                        transactionsObject = (JsonObject) transactionsArray.get(i);
                        cataMov = new CataMov();
                        cataMov.setID(transactionsObject.get("id").toString().replace("\"", ""));
                        cataMov.setIDEquip(transactionsObject.get("machineId").toString().replace("\"", ""));
                        dataHora = parseDateTime(transactionsObject.get("date").toString().replace("\"", "")).toInstant();
                        cataMov.setData(formatterData.format(dataHora));
                        cataMov.setHora(formatterHora.format(dataHora));
                        cataMov.setOperacao(transactionsObject.get("operation").toString());
                        cataMov.setFluxo(transactionsObject.get("flow").toString());
                        cataMov.setValor(transactionsObject.get("value").toString());
                        cataMov.setDt_incl(getDataAtual("SQL"));
                        cataMov.setHr_incl(getDataAtual("HORA"));
                        cataMov.setLido("0");

//                        if(cataMov.getIDEquip().equals("89684995-0667-4e8f-90a1-58129f6d7161")){
//                            System.out.println(cataMov.toString());
//                        }
                        if (!cataMovDao.existeTransacao(cataMov.getID(), persistencia)) {
                            cataMovDao.inserirTransacao(cataMov, persistencia);
                        }
                        if (this.corpvs) {
                            if (!cataMovDao.existeTransacao(cataMov.getID(), CORPVSRECIFE)) {
                                cataMovDao.inserirTransacao(cataMov, CORPVSRECIFE);
                            }
                        }
                    }

                } else {
                    transacoes = 0;

                    in = new BufferedReader(new InputStreamReader(con.getErrorStream()));
                    response = new StringBuilder();

                    while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                    }
                    in.close();

                    this.logerro.Grava(url.toString() + "\r\n" + response.toString(), this.caminho);
                }
            }
        } catch (Exception e) {
            this.logerro.Grava(e.getMessage(), this.caminho);
        }
    }

    private String gerarLogURL(String empresa, String username, String password) {
        StringBuilder log = new StringBuilder();
        log.append("empresa:    ").append(empresa).append("\n");
        log.append("username:   ").append(username).append("\n");
        log.append("password:   ").append(password).append("\n");
        return log.toString();
    }

    /**
     * Retrieves representation of an instance of
     * br.com.sasw.integracao.cata.Cata
     *
     * @param empresa
     * @param username
     * @param password
     * @return an instance of java.lang.String
     */
    @GET
    @Path("/{empresa}/{idcofre}/")
    @Produces(MediaType.APPLICATION_XML)
    public String getUltimaTransacao(@PathParam("empresa") String empresa, @PathParam("idcofre") String idCofre) {
        this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ERRO\\Integracao\\Cata\\"
                + getDataAtual("SQL") + ".txt";
        String resp = "";
        try {
            if (empresa == null || empresa.equals("")) {
                throw new Exception("Empresa inválida");
            }

            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(empresa);
//            Persistencia CORPVSRECIFE = null;
            if (null == persistencia) {
                throw new Exception("Empresa inválida: " + empresa);
            }
//            } else if (empresa.equals("SATCORPVS")) {
//                this.corpvs = true;
//                CORPVSRECIFE = pool.getConexao("SATCORPVSPE");
//            }
            CataMov ultimaMovimentacao = catasmovdao.obterDataHoraUltimaMovimentacao(persistencia, idCofre);
            resp = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
                    + Xmls.tag("nop", null == ultimaMovimentacao.getData() ? "0" : ultimaMovimentacao.getData())
                    + Xmls.tag("cadCataEquip", null == ultimaMovimentacao.getFluxo() ? "0" : ultimaMovimentacao.getFluxo());
            this.logerro.Grava(resp, this.caminho);
            persistencia.FechaConexao();
        } catch (Exception e) {
            resp = Xmls.tag("resp", e.getMessage());
        }

        return resp;
    }

    /**
     * PUT method for updating or creating an instance of Nota
     *
     * @param empresa
     * @param idEquip
     * @param content representation for the resource
     */
//    @PUT
//    @Consumes(MediaType.APPLICATION_JSON)
//    public void movimentacoesIntegrador(String content) {
//        JSONObject movObj;
//        try {
//            CataMovDao cataMovDao = new CataMovDao();
//            JsonArray transactionsArray = null;
//            JsonObject transactionsObject;
//            CataMov cataMov = null;
//            CataEquip cataEquip = null;
//            Instant dataHora;
//            DateTimeFormatter formatterData = DateTimeFormatter.ofPattern("yyyyMMdd").withZone(ZoneId.of("America/Sao_Paulo"));
//            DateTimeFormatter formatterHora = DateTimeFormatter.ofPattern("HH:mm:ss").withZone(ZoneId.of("America/Sao_Paulo"));
//
//            JSONObject objMov = XML.toJSONObject(content);
//            String empresa = objMov.getString("empresa");
//            String idEquip = objMov.getString("idequip");
//            String operation = objMov.getString("operation");
//
//            this.logerro.Grava(content, this.caminho);
//            Persistencia persistencia = pool.getConexao(empresa);
//            Persistencia CORPVSRECIFE = null;
//            if (null == persistencia) {
//                throw new Exception("Empresa inválida: " + empresa);
//            } else if (empresa.equals("SATCORPVS")) {
//                this.corpvs = true;
//                CORPVSRECIFE = pool.getConexao("SATCORPVSPE");
//            }
//
//            cataequipdao.registroComunicacao(idEquip, getDataAtual("SQL"), getDataAtual("HORA"), persistencia);
//
//            if (operation.equals("registerEquip")) {
//                cataEquip = new CataEquip();
//                cataEquip.setSerial(objMov.getString("serial"));
//                cataEquip.setIDEquip(objMov.getString("idequip"));
//                cataEquip.setGeo(objMov.getString("geo"));
//                cataEquip.setAddress(objMov.getString("adress"));
//                cataequipdao.inserirEquipamento(cataEquip, persistencia);
//
//            } else if (operation.equals("impTransactions")) {
//                int qtd = objMov.getInt("qtd");
//                if (qtd > 0) {
//                    if (!semaforoDao.existeSemaforo("TesCofresMov", "IntegracaoCATA", getDataAtual("SQL"), persistencia)) {
//                        semaforoDao.inserirRegistro("SatWeb", getDataAtual("SQL"), getDataAtual("HORA"), "TesCofresMov", "IntegracaoCATA", persistencia);
//                        if (qtd > 1) {
//                            JSONArray arrayMov = objMov.getJSONArray("mov");
//                            for (int i = 1; i < qtd; i++) {
//                                movObj = arrayMov.getJSONObject(i);
//                                cataMov = new CataMov();
//                                cataMov.setID(movObj.get("uniqueId").toString().replace("\"", ""));
//                                cataMov.setIDEquip(movObj.get("machineId").toString().replace("\"", ""));
//                                //dataHora = parseDateTime(movObj.get("date").toString().replace(" ", "T")).toInstant();
//                                //cataMov.setData(formatterData.format(dataHora));
//                                //cataMov.setHora(formatterHora.format(dataHora));
//                                cataMov.setData(movObj.get("date").toString().substring(0, movObj.get("date").toString().indexOf(" ")));
//                                cataMov.setHora(movObj.get("date").toString().substring(movObj.get("date").toString().indexOf(" ") + 1, movObj.get("date").toString().indexOf(" ") + 9));
//                                cataMov.setOperacao(movObj.get("operation").toString());
//                                cataMov.setFluxo(movObj.get("flow").toString());
//                                cataMov.setValor(movObj.get("value").toString());
//                                cataMov.setDt_incl(getDataAtual("SQL"));
//                                cataMov.setHr_incl(getDataAtual("HORA"));
//                                cataMov.setLido("0");
//
//                                if (!cataMovDao.existeTransacao(cataMov.getID(), persistencia)) {
//                                    cataMovDao.inserirTransacao(cataMov, persistencia);
//                                }
//                                if (this.corpvs) {
//                                    if (!cataMovDao.existeTransacao(cataMov.getID(), CORPVSRECIFE)) {
//                                        cataMovDao.inserirTransacao(cataMov, CORPVSRECIFE);
//                                    }
//                                }
//
//                                if (!cataMov.getData().replace("-", "").equals(getDataAtual("SQL"))) {
//                                    cataequipdao.atulizaDataConferenciaTransacoes(idEquip, persistencia);
//                                }
//                            }
//
//                            tesCofresMovs = cataMovDao.listaTransacoesPendentes(persistencia);
//                            for (TesCofresMov tesCofresMov : tesCofresMovs) {
//                                try {
//                                    tesCofresMovDao.inserirMovimentacao(tesCofresMov, persistencia);
//                                } catch (Exception e) {
//                                    this.logerro.Grava("Falha inserção TesCofresMov", this.caminho);
//                                }
//                            }
//                            try {
//                                if (tesCofresMovs.get(tesCofresMovs.size() - 1).getData().equals(tesCofresMovs.get(tesCofresMovs.size() - 1).getDt_Incl())) {
//                                    tesCofresMovDao.inserirResumo(tesCofresMovs.get(tesCofresMovs.size() - 1), persistencia);
//                                }
//                            } catch (Exception e) {
//                                this.logerro.Grava("Falha inserção TesCofresRes - " + e.getMessage(), this.caminho);
//                            }
//                        } else if (qtd == 1) {
//                            cataMov = new CataMov();
//                            movObj = objMov.getJSONObject("mov");
//                            cataMov.setID(movObj.get("uniqueId").toString().replace("\"", ""));
//                            cataMov.setIDEquip(movObj.get("machineId").toString().replace("\"", ""));
//                            //dataHora = parseDateTime(movObj.get("date").toString().replace(" ", "T")).toInstant();
//                            //cataMov.setData(formatterData.format(dataHora));
//                            //cataMov.setHora(formatterHora.format(dataHora));
//                            cataMov.setData(movObj.get("date").toString().substring(0, movObj.get("date").toString().indexOf(" ")));
//                            cataMov.setHora(movObj.get("date").toString().substring(movObj.get("date").toString().indexOf(" ") + 1, movObj.get("date").toString().indexOf(" ") + 9));
//                            cataMov.setOperacao(movObj.get("operation").toString());
//                            cataMov.setFluxo(movObj.get("flow").toString());
//                            cataMov.setValor(movObj.get("value").toString());
//                            cataMov.setDt_incl(getDataAtual("SQL"));
//                            cataMov.setHr_incl(getDataAtual("HORA"));
//                            cataMov.setLido("0");
//
//                            if (!cataMovDao.existeTransacao(cataMov.getID(), persistencia)) {
//                                cataMovDao.inserirTransacao(cataMov, persistencia);
//                            }
//                            if (this.corpvs) {
//                                if (!cataMovDao.existeTransacao(cataMov.getID(), CORPVSRECIFE)) {
//                                    cataMovDao.inserirTransacao(cataMov, CORPVSRECIFE);
//                                }
//                            }
//
//                            if (!(cataMov.getData().replace("-", "")).equals(getDataAtual("SQL"))) {
//                                cataequipdao.atulizaDataConferenciaTransacoes(idEquip, persistencia);
//                            }
//
//                            tesCofresMovs = cataMovDao.listaTransacoesPendentes(persistencia);
//                            for (TesCofresMov tesCofresMov : tesCofresMovs) {
//                                try {
//                                    tesCofresMovDao.inserirMovimentacao(tesCofresMov, persistencia);
//                                } catch (Exception e) {
//                                    this.logerro.Grava("Falha inserção TesCofresMov", this.caminho);
//                                    tesCofresMovDao.inserirResumo(tesCofresMovs.get(tesCofresMovs.size() - 1), persistencia);
//                                }
//                            }
//                        }
//                        //Definindo status como offline apos 15 min sem comunicação
//                        cataequipdao.verificaStatus("2", "15", persistencia);
//
//                        semaforoDao.removerBaixaTabela("TesCofresMov", "IntegracaoCATA", persistencia);
//                    }
//                    //semaforoDao.removerBaixaTabela("TesCofresMov", "IntegracaoCATA", persistencia);
//                } else if (semaforoDao.tempoSemaforo("TesCofresMov", "IntegracaoCATA", persistencia) > 10) {
//                    semaforoDao.removerBaixaTabela("TesCofresMov", "IntegracaoCATA", persistencia);
//                    this.logerro.Grava("Semáforo CATA removido.", this.caminho);
//                }
//            }
//
//        } catch (Exception e) {
//            this.logerro.Grava(e.getMessage(), this.caminho);
//        }
//    }
    /**
     * POST
     *
     * @param content representation for the resource
     * @return
     */
    @POST
    public String movimentacoesIntegradorPost(String content) {
        JSONObject movObj;
        List<TesCofresMov> tesCofresMovs, tesCofresMovsPE = new ArrayList<>();
        String resp = "";
        try {
            CataMovDao cataMovDao = new CataMovDao();
            CataMov cataMov;
            
            JSONObject objMov = XML.toJSONObject(content);
            String empresa = objMov.getString("empresa");
            String idEquip = objMov.getString("idequip");
            String operation = objMov.getString("operation");
            String establishment = objMov.getString("establishment");
            String serialNumber = objMov.getString("serialNumber");
            String allocationGEO = objMov.getString("allocationGEO");
        
            CataEquip  cataEquip= new CataEquip();
            cataEquip.setSerial(serialNumber);
            cataEquip.setIDEquip(idEquip);
            cataEquip.setGeo(allocationGEO);
            cataEquip.setAddress(establishment);

            StringBuilder xmlMovimentacaoResp = new StringBuilder();
            StringBuilder xmlMovimentacoesResp = new StringBuilder();

            Persistencia persistencia = this.pool.getConexao(empresa);
            Persistencia CORPVSRECIFE = null;
            if (null == persistencia) {
                throw new Exception("Empresa inválida: " + empresa);
            } else if (empresa.equals("SATCORPVS")) {
                this.corpvs = true;
                CORPVSRECIFE = this.pool.getConexao("SATCORPVSPE");
            }

            xmlMovimentacoesResp.append(Xmls.tag("idEquip", cataEquip.getIDEquip()));

            if (null == cataEquip.getIDEquip() || cataEquip.getIDEquip().equals("")) {
                cataEquip.setIDEquip(cataMovDao.buscarIDEquip(cataEquip.getSerial(), persistencia));
            }

            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Integracao\\Cata\\"
                    + empresa + "\\" + getDataAtual("SQL") + "\\" + cataEquip.getSerial() + ".txt";
            this.logerro.Grava("Conteúdo\r\n"+content, this.caminho);


            // Insere/Atualiza informações do cofre.
            this.cataequipdao.inserirAtualizarEquipamento(cataEquip, persistencia);
            if (this.corpvs) {
                this.cataequipdao.inserirAtualizarEquipamento(cataEquip, CORPVSRECIFE);
            }
            
            // Registra a comunicação baseado no Serial, que é chave de CataEquip
            this.cataequipdao.registroComunicacaoSerial(cataEquip.getSerial(), getDataAtual("SQL"), getDataAtual("HORA"), persistencia);
            if (this.corpvs) {
                this.cataequipdao.registroComunicacaoSerial(cataEquip.getSerial(), getDataAtual("SQL"), getDataAtual("HORA"), CORPVSRECIFE);
            }

            if (operation.equals("impTransactions")) {
                int qtd = objMov.getInt("qtd");
//                int qtdMax = 5;
                if (qtd == 0) {
                    xmlMovimentacoesResp.append( Xmls.tag("mov", ""));
                    resp = xmlMovimentacoesResp.toString();
                } else if (qtd > 0) {
                    if (!this.semaforoDao.existeSemaforo("IntegracaoCATA", cataEquip.getSerial(), getDataAtual("SQL"), persistencia)) {
                        this.semaforoDao.inserirRegistro("SatWeb", getDataAtual("SQL"), getDataAtual("HORA"), "IntegracaoCATA", cataEquip.getSerial(), persistencia);
                        
//                        if (!cataequipdao.existeEquipamento(serialNumber, persistencia)) {
//                            cataEquip = new CataEquip();
//                            cataEquip.setSerial(serialNumber);
//                            cataEquip.setIDEquip(idEquip);
//                            cataEquip.setGeo(allocationGEO);
//                            cataEquip.setAddress(establishment);
//                            cataequipdao.inserirEquipamento(cataEquip, persistencia);
//                        }
//                        if (this.corpvs) {
//                            if (!cataequipdao.existeEquipamento(serialNumber, CORPVSRECIFE)) {
//                                cataEquip = new CataEquip();
//                                cataEquip.setSerial(serialNumber);
//                                cataEquip.setIDEquip(idEquip);
//                                cataEquip.setGeo(allocationGEO);
//                                cataEquip.setAddress(establishment);
//                                cataequipdao.inserirEquipamento(cataEquip, CORPVSRECIFE);
//                            }
//                        }
                        if (qtd > 1) {
                            JSONArray arrayMov = objMov.getJSONArray("mov");
                            
                            for (int i = 1; i < qtd; i++) {
                                movObj = arrayMov.getJSONObject(i);
                                cataMov = new CataMov();
                                cataMov.setID(movObj.get("uniqueId").toString().replace("\"", ""));
                                cataMov.setIDEquip(movObj.get("machineId").toString().replace("\"", ""));
                                cataMov.setData(movObj.get("date").toString().substring(0, movObj.get("date").toString().indexOf(" ")));
                                cataMov.setHora(movObj.get("date").toString().substring(movObj.get("date").toString().indexOf(" ") + 1, movObj.get("date").toString().indexOf(" ") + 9));
                                cataMov.setOperacao(movObj.get("operation").toString());
                                cataMov.setFluxo(movObj.get("flow").toString());
                                cataMov.setValor(movObj.get("value").toString());
                                cataMov.setDt_incl(getDataAtual("SQL"));
                                cataMov.setHr_incl(getDataAtual("HORA"));
                                cataMov.setLido("0");

                                try {
                                    if (!cataMovDao.existeTransacao(cataMov.getID(), persistencia)) {
                                        cataMovDao.inserirTransacao(cataMov, cataEquip.getSerial(), persistencia);
                                    }
                                    xmlMovimentacaoResp = new StringBuilder();
                                    xmlMovimentacaoResp.append(Xmls.tag("id", i));
                                    xmlMovimentacaoResp.append(Xmls.tag("uniqueID", cataMov.getID()));
                                    xmlMovimentacaoResp.append(Xmls.tag("date", cataMov.getData()));
                                    xmlMovimentacaoResp.append(Xmls.tag("operation", cataMov.getOperacao()));
                                    xmlMovimentacoesResp.append(Xmls.tag("mov", xmlMovimentacaoResp));
                                } catch (Exception e) {
                                    this.logerro.Grava("Erro inserção catamov\r\n" + e.getMessage(), this.caminho);
                                    resp = "";
                                }

                                if (this.corpvs) {
                                    if (!cataMovDao.existeTransacao(cataMov.getID(), CORPVSRECIFE)) {
                                        cataMovDao.inserirTransacao(cataMov, cataEquip.getSerial(), CORPVSRECIFE);
                                    }
                                }

                            }

//                            if (null == idEquip || idEquip.equals("")) {
//                                idEquip = cataMovDao.buscarIDEquip(cataEquip.getSerial(), persistencia);
//                            }

//                            tesCofresMovs = cataMovDao.listaTransacoesPendentes(idEquip, persistencia);
                            tesCofresMovs = cataMovDao.listaTransacoesPendentesSerial(cataEquip.getSerial(), persistencia);
                            for (TesCofresMov tesCofresMov : tesCofresMovs) {
                                try {
                                    this.tesCofresMovDao.inserirMovimentacao(tesCofresMov, persistencia);
                                } catch (Exception e) {
                                    this.logerro.Grava("Falha inserção TesCofresMov\r\n" + e.getMessage(), this.caminho);
                                }
                            }

                            if (this.corpvs) {
//                                tesCofresMovsPE = cataMovDao.listaTransacoesPendentes(idEquip, CORPVSRECIFE);
                                tesCofresMovsPE = cataMovDao.listaTransacoesPendentesSerial(cataEquip.getSerial(), CORPVSRECIFE);
                                for (TesCofresMov tesCofresMovPE : tesCofresMovsPE) {
                                    try {
                                        this.tesCofresMovDao.inserirMovimentacao(tesCofresMovPE, CORPVSRECIFE);
                                    } catch (Exception e) {
                                        this.logerro.Grava("Falha inserção TesCofresMov CORPVSRECIFE\r\n" + e.getMessage(), this.caminho);
                                    }
                                }
                            }

                            // Insere resumo
                            try {
                                if (!tesCofresMovs.isEmpty() 
                                        && tesCofresMovs.get(tesCofresMovs.size() - 1).getData()
                                                .equals(tesCofresMovs.get(tesCofresMovs.size() - 1).getDt_Incl())) {
                                    this.tesCofresMovDao.inserirResumo(tesCofresMovs.get(tesCofresMovs.size() - 1), persistencia);
                                }
                            } catch (Exception e) {
                                this.logerro.Grava("Falha inserção TesCofresRes\r\n" + e.getMessage(), this.caminho);
                            }
                            
                            if (this.corpvs) {
                                try {
                                    if (!tesCofresMovsPE.isEmpty() 
                                        && tesCofresMovsPE.get(tesCofresMovsPE.size() - 1).getData()
                                                .equals(tesCofresMovsPE.get(tesCofresMovsPE.size() - 1).getDt_Incl())) {
                                        this.tesCofresMovDao.inserirResumo(tesCofresMovsPE.get(tesCofresMovsPE.size() - 1), CORPVSRECIFE);
                                    }
                                } catch (Exception ex) {
                                    this.logerro.Grava("Falha inserção TesCofresRes CORPVSRECIFE\r\n" + ex.getMessage(), this.caminho);
                                }
                            }
                            
                            resp = xmlMovimentacoesResp.toString();
                        } else if (qtd == 1) {
                            cataMov = new CataMov();
                            movObj = objMov.getJSONObject("mov");
                            cataMov.setID(movObj.get("uniqueId").toString().replace("\"", ""));
                            cataMov.setIDEquip(movObj.get("machineId").toString().replace("\"", ""));
                            //dataHora = parseDateTime(movObj.get("date").toString().replace(" ", "T")).toInstant();
                            //cataMov.setData(formatterData.format(dataHora));
                            //cataMov.setHora(formatterHora.format(dataHora));
                            cataMov.setData(movObj.get("date").toString().substring(0, movObj.get("date").toString().indexOf(" ")));
                            cataMov.setHora(movObj.get("date").toString().substring(movObj.get("date").toString().indexOf(" ") + 1, movObj.get("date").toString().indexOf(" ") + 9));
                            cataMov.setOperacao(movObj.get("operation").toString());
                            cataMov.setFluxo(movObj.get("flow").toString());
                            cataMov.setValor(movObj.get("value").toString());
                            cataMov.setDt_incl(getDataAtual("SQL"));
                            cataMov.setHr_incl(getDataAtual("HORA"));
                            cataMov.setLido("0");

                            try {
                                if (!cataMovDao.existeTransacao(cataMov.getID(), persistencia)) {
                                    cataMovDao.inserirTransacao(cataMov, cataEquip.getSerial(), persistencia);
                                }
                                xmlMovimentacaoResp.append(Xmls.tag("id", 1));
                                xmlMovimentacaoResp.append(Xmls.tag("uniqueID", cataMov.getID()));
                                xmlMovimentacaoResp.append(Xmls.tag("date", cataMov.getData()));
                                xmlMovimentacaoResp.append(Xmls.tag("operation", cataMov.getOperacao()));
                                xmlMovimentacoesResp.append(Xmls.tag("mov", xmlMovimentacaoResp));
                                resp = xmlMovimentacoesResp.toString();
                            } catch (Exception e) {
                                this.logerro.Grava("Erro inserção catamov\r\n" + e.getMessage(), this.caminho);
                                resp = " ";
                            }

                            if (this.corpvs) {
                                if (!cataMovDao.existeTransacao(cataMov.getID(), CORPVSRECIFE)) {
                                    cataMovDao.inserirTransacao(cataMov, cataEquip.getSerial(), CORPVSRECIFE);
                                }
                            }
//
//                            if (null == idEquip || idEquip.equals("")) {
//                                idEquip = cataMovDao.buscarIDEquip(cataEquip.getSerial(), persistencia);
//                            }

//                            tesCofresMovs = cataMovDao.listaTransacoesPendentes(idEquip, persistencia);

                            tesCofresMovs = cataMovDao.listaTransacoesPendentesSerial(cataEquip.getSerial(), persistencia);
                            for (TesCofresMov tesCofresMov : tesCofresMovs) {
                                try {
                                    this.tesCofresMovDao.inserirMovimentacao(tesCofresMov, persistencia);
                                } catch (Exception e) {
                                    this.logerro.Grava("Falha inserção TesCofresMov\r\n" + e.getMessage(), this.caminho);
                                }
                            }
                            
                            if (this.corpvs) {
//                                tesCofresMovsPE = cataMovDao.listaTransacoesPendentes(idEquip, CORPVSRECIFE);
                                tesCofresMovsPE = cataMovDao.listaTransacoesPendentesSerial(cataEquip.getSerial(), CORPVSRECIFE);
                                for (TesCofresMov tesCofresMovPE : tesCofresMovsPE) {
                                    try {
                                        this.tesCofresMovDao.inserirMovimentacao(tesCofresMovPE, CORPVSRECIFE);
                                    } catch (Exception e) {
                                        this.logerro.Grava("Falha inserção TesCofresMov CORPVSRECIFE\r\n" + e.getMessage(), this.caminho);
                                    }
                                }
                            }
                            

                            // Insere resumo
                            try {
                                if (!tesCofresMovs.isEmpty() 
                                        && tesCofresMovs.get(tesCofresMovs.size() - 1).getData()
                                                .equals(tesCofresMovs.get(tesCofresMovs.size() - 1).getDt_Incl())) {
                                    this.tesCofresMovDao.inserirResumo(tesCofresMovs.get(tesCofresMovs.size() - 1), persistencia);
                                }
                            } catch (Exception e) {
                                this.logerro.Grava("Falha inserção TesCofresRes\r\n" + e.getMessage(), this.caminho);
                            }
                            
                            if (this.corpvs) {
                                try {
                                    if (!tesCofresMovsPE.isEmpty() 
                                        && tesCofresMovsPE.get(tesCofresMovsPE.size() - 1).getData()
                                                .equals(tesCofresMovsPE.get(tesCofresMovsPE.size() - 1).getDt_Incl())) {
                                        this.tesCofresMovDao.inserirResumo(tesCofresMovsPE.get(tesCofresMovsPE.size() - 1), CORPVSRECIFE);
                                    }
                                } catch (Exception ex) {
                                    this.logerro.Grava("Falha inserção TesCofresRes CORPVSRECIFE\r\n" + ex.getMessage(), this.caminho);
                                }
                            }
                        }
                        
                        //Definindo status como offline apos 15 min sem comunicação
                        this.cataequipdao.verificaStatus("2", "15", persistencia);
                        if (this.corpvs) {
                            this.cataequipdao.verificaStatus("2", "15", CORPVSRECIFE);
                        }

                        this.semaforoDao.removerBaixaTabela("IntegracaoCATA", cataEquip.getSerial(), persistencia);
                    } else if (semaforoDao.tempoSemaforo("IntegracaoCATA", cataEquip.getSerial(), persistencia) > 10) {
                        this.semaforoDao.removerBaixaTabela("IntegracaoCATA", cataEquip.getSerial(), persistencia);
                        this.logerro.Grava("Semáforo CATA - " + cataEquip.getSerial() + " removido.", this.caminho);
                    }
                }
            }
            this.logerro.Grava("Resposta\r\n" + resp, this.caminho);
        } catch (Exception e) {
            this.logerro.Grava("Falha\r\n" + e.getMessage(), this.caminho);
            resp = Xmls.tag("resp", e.getMessage());
        }
        return resp;
    }

}
