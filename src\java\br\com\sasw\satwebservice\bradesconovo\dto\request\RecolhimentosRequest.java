/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.dto.request;

/**
 *
 * <AUTHOR>
 */
import java.util.List;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement
public class RecolhimentosRequest {

    private String cnpjEmpresa;
    private String senhaConexao;
    private String numeroPedido;
    private String dataOperacao;
    private double valorOperacao;
    private String numeroCofreInteligenteOrigem;
    private String cnpjEmpresaProcessadora;
    private List<GtveRequest> gtve;

    // Getters e Setters
    public String getCnpjEmpresa() {
        return cnpjEmpresa;
    }

    public void setCnpjEmpresa(String cnpjEmpresa) {
        this.cnpjEmpresa = cnpjEmpresa;
    }

    public String getSenhaConexao() {
        return senhaConexao;
    }

    public void setSenhaConexao(String senhaConexao) {
        this.senhaConexao = senhaConexao;
    }

    public String getNumeroPedido() {
        return numeroPedido;
    }

    public void setNumeroPedido(String numeroPedido) {
        this.numeroPedido = numeroPedido;
    }

    public String getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(String dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public double getValorOperacao() {
        return valorOperacao;
    }

    public void setValorOperacao(double valorOperacao) {
        this.valorOperacao = valorOperacao;
    }

    public String getNumeroCofreInteligenteOrigem() {
        return numeroCofreInteligenteOrigem;
    }

    public void setNumeroCofreInteligenteOrigem(String numeroCofreInteligenteOrigem) {
        this.numeroCofreInteligenteOrigem = numeroCofreInteligenteOrigem;
    }

    public String getCnpjEmpresaProcessadora() {
        return cnpjEmpresaProcessadora;
    }

    public void setCnpjEmpresaProcessadora(String cnpjEmpresaProcessadora) {
        this.cnpjEmpresaProcessadora = cnpjEmpresaProcessadora;
    }

    public List<GtveRequest> getGtve() {
        return gtve;
    }

    public void setGtve(List<GtveRequest> gtve) {
        this.gtve = gtve;
    }


}
