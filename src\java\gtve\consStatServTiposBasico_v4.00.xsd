<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by <EMAIL> (PROCERGS) -->
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns="http://www.portalfiscal.inf.br/cte" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="tiposGeralCTe_v4.00.xsd"/>
	<xs:complexType name="TConsStatServ">
		<xs:annotation>
			<xs:documentation>Tipo Pedido de Consulta do Status do Serviço CTe</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cUF" type="TCodUfIBGE">
				<xs:annotation>
					<xs:documentation>Código da UF a ser verificado o status</xs:documentation>
					<xs:documentation>Utilizar a Tabela do IBGE.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xServ" type="TServ" fixed="STATUS">
				<xs:annotation>
					<xs:documentation>Serviço Solicitado</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" use="required">
			<xs:simpleType>
				<xs:restriction base="TVerConsStat"/>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="TRetConsStatServ">
		<xs:annotation>
			<xs:documentation>Tipo Resultado da Consulta do Status do Serviço CTe</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="verAplic">
				<xs:annotation>
					<xs:documentation>Versão do Aplicativo que processou o CT-e</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TVerAplic">
						<xs:whiteSpace value="collapse"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cStat" type="TStat">
				<xs:annotation>
					<xs:documentation>Código do status da mensagem enviada.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMotivo" type="TMotivo">
				<xs:annotation>
					<xs:documentation>Descrição literal do status do serviço solicitado.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cUF" type="TCodUfIBGE">
				<xs:annotation>
					<xs:documentation>Código da UF responsável pelo serviço</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="dhRecbto" type="TDateTimeUTC">
				<xs:annotation>
					<xs:documentation>AAAA-MM-DDTHH:MM:SS TZD</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="tMed" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Tempo médio de resposta do serviço (em segundos) dos últimos 5 minutos</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:integer">
						<xs:pattern value="[0-9]{1,4}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="dhRetorno" type="TDateTimeUTC" minOccurs="0">
				<xs:annotation>
					<xs:documentation>AAAA-MM-DDTHH:MM:SSDeve ser preenchida com data e hora previstas para o retorno dos serviços prestados.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xObs" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Campo observação utilizado para incluir informações ao contribuinte</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="255"/>
						<xs:whiteSpace value="collapse"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerConsStat" use="required"/>
	</xs:complexType>
	<xs:simpleType name="TVerConsStat">
		<xs:annotation>
			<xs:documentation> Tipo Versão do Consulta do Status do Serviço CTe</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="4\.00"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
