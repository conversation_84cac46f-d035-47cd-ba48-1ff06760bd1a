/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.integracao.zenite;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasDaos.RastrearDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.io.StringWriter;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.UriInfo;
import static javax.xml.bind.DatatypeConverter.parseDateTime;
import javax.xml.namespace.QName;
import javax.xml.soap.MessageFactory;
import javax.xml.soap.SOAPBody;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPHeader;
import javax.xml.soap.SOAPMessage;
import javax.xml.soap.SOAPPart;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;

/**
 *
 * <AUTHOR>
 */
@Path("/ws-zenite/")
public class Rastreador {

    private final ArquivoLog logerro;
    private String caminho;
    private final SasPoolPersistencia pool;

    private static final String SOAP_ENV_NAMESPACE = "http://schemas.xmlsoap.org/soap/envelope/";
    private static final String PREFERRED_PREFIX = "soap";

    @Context
    private UriInfo context;

    /**
     * Creates a new instance
     */
    public Rastreador() {
        logerro = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ERRO\\"
                + "\\zenite\\" + DataAtual.getDataAtual("SQL") + "\\log.txt";
        pool = new SasPoolPersistencia();
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    @POST
    @Path("/{empresa}/updateLocalizacaoViatura")
    public String updateLocalizacaoViatura(@PathParam("empresa") String empresa, String corpo) {

        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\" + empresa + "\\"
                + "\\zenite\\" + DataAtual.getDataAtual("SQL") + "\\log.txt";
        logerro.Grava(corpo, caminho);
        String reposta = null;

        try {
            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(empresa);
            if (null == persistencia) {
                caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ERRO\\"
                        + "\\zenite\\" + DataAtual.getDataAtual("SQL") + "\\log.txt";
                throw new Exception("Empresa inválida: " + empresa);
            }

            DateTimeFormatter formatterData = DateTimeFormatter.ofPattern("yyyyMMdd").withZone(ZoneId.of("America/Sao_Paulo"));
            DateTimeFormatter formatterHora = DateTimeFormatter.ofPattern("HH:mm:ss").withZone(ZoneId.of("America/Sao_Paulo"));

            RastrearDao rastrearDao = new RastrearDao();
            Instant dataHora;
            String idRastreador, data, hora, velocidade, latitude, longitude;
            JSONObject viaturaObjetc;
            JSONArray viaturaArray;

            JSONObject jsonObj = XML.toJSONObject(corpo);
//            logerro.Grava(jsonObj.toString(), caminho);
            Object viatura = jsonObj.getJSONObject("soap:Envelope").getJSONObject("soap:Body")
                    .getJSONObject("ns1:updateLocalizacaoViatura")
                    .get("viatura");

            if (viatura instanceof JSONObject) {
                viaturaObjetc = (JSONObject) viatura;

                idRastreador = String.valueOf(viaturaObjetc.getJSONObject("idRastreador").get("content"));
                dataHora = parseDateTime(viaturaObjetc.getJSONObject("data").getString("content")).toInstant();
                data = formatterData.format(dataHora);
                hora = formatterHora.format(dataHora);
                velocidade = String.valueOf(viaturaObjetc.getJSONObject("velocidade").getFloat("content"));
                latitude = String.valueOf(viaturaObjetc.getJSONObject("latitude").getFloat("content"));
                longitude = String.valueOf(viaturaObjetc.getJSONObject("longitude").getFloat("content"));

                rastrearDao.gravarRastrearA(idRastreador, latitude, longitude, data,
                            hora, getDataAtual("SQL"), getDataAtual("HORA"), velocidade, persistencia);
            } else if (viatura instanceof JSONArray) {
                viaturaArray = (JSONArray) viatura;
                for (int i = 0; i < viaturaArray.length(); i++) {
                    viaturaObjetc = viaturaArray.getJSONObject(i);

                    idRastreador = String.valueOf(viaturaObjetc.getJSONObject("idRastreador").get("content"));
                    dataHora = parseDateTime(viaturaObjetc.getJSONObject("data").getString("content")).toInstant();
                    data = formatterData.format(dataHora);
                    hora = formatterHora.format(dataHora);
                    velocidade = String.valueOf(viaturaObjetc.getJSONObject("velocidade").getFloat("content"));
                    latitude = String.valueOf(viaturaObjetc.getJSONObject("latitude").getFloat("content"));
                    longitude = String.valueOf(viaturaObjetc.getJSONObject("longitude").getFloat("content"));

                    rastrearDao.gravarRastrearA(idRastreador, latitude, longitude, data,
                            hora, getDataAtual("SQL"), getDataAtual("HORA"), velocidade, persistencia);
                }
            }

            reposta = getResposta("ACK");
//            reposta.put("status", "success");
//            reposta.put("cause", "loaded");
        } catch (Exception e) {

            reposta = getResposta("NACK");
//            reposta.put("status", "error");
//            reposta.put("cause", e.getMessage());
        }
        logerro.Grava("Resposta: "+reposta, caminho);
        return reposta;
    }

    public String getResposta(String mensagem) {
        try {
            MessageFactory messageFactory = MessageFactory.newInstance();

            SOAPMessage soapMessage = messageFactory.createMessage();

            SOAPPart soapPart = soapMessage.getSOAPPart();
            SOAPEnvelope soapEnvelope = soapMessage.getSOAPPart().getEnvelope();
            soapEnvelope.addAttribute(new QName("soap:encodingStyle"), "http://schemas.xmlsoap.org/soap/encoding/");
            soapEnvelope.addAttribute(new QName("xmlns:ns1"), "http://webServices.sigmaWebServices.segware.com.br/");
            soapEnvelope.addAttribute(new QName("xmlns:soapenc"), "http://schemas.xmlsoap.org/soap/encoding/");
            soapEnvelope.addAttribute(new QName("xmlns:xsd"), "http://www.w3.org/2001/XMLSchema");
            soapEnvelope.addAttribute(new QName("xmlns:xsi"), "http://www.w3.org/2001/XMLSchema-instance");

            SOAPHeader soapHeader = soapEnvelope.getHeader();
            soapHeader = soapMessage.getSOAPHeader();
            soapMessage.getSOAPHeader().detachNode();

            SOAPBody soapBody = soapEnvelope.getBody();
            soapBody = soapMessage.getSOAPBody();
            soapBody.addTextNode("ACK");

            soapEnvelope.removeNamespaceDeclaration(soapEnvelope.getPrefix());
            soapEnvelope.addNamespaceDeclaration(PREFERRED_PREFIX, SOAP_ENV_NAMESPACE);
            soapEnvelope.setPrefix(PREFERRED_PREFIX);
            soapHeader.setPrefix(PREFERRED_PREFIX);
            soapBody.setPrefix(PREFERRED_PREFIX);

            DOMSource source = new DOMSource(soapEnvelope);
            StringWriter stringResult = new StringWriter();
            TransformerFactory.newInstance().newTransformer().transform(source, new StreamResult(stringResult));
            return stringResult.toString();
        } catch (Exception e) {

        }
        return null;
    }
}
