/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.importacao.gtve;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Clientes;
import SasBeans.CxForte;
import SasBeans.Pedido;
import SasBeans.ProsegurTGTVeDados;
import SasBeans.ProsegurTGTVeDadosAdic;
import SasBeans.Rotas;
import SasBeans.Rt_Guias;
import SasBeans.Rt_Perc;
import SasBeans.XMLGtve;
import SasBeans.XMLGtveCancelar;
import SasBeans.XMLGtveRet;
import SasBeans.XMLGtveRetDadosAdic;
import SasBeans.iblCTeOS;
import SasDaos.ClientesDao;
import SasDaos.CxForteDao;
import SasDaos.OS_VigDao;
import SasDaos.PedidoDao;
import SasDaos.RotasDao;
import SasDaos.Rt_GuiasDao;
import SasDaos.Rt_PercDao;
import br.com.sasw.pacotesuteis.sasbeans.XMLNFE;
import br.com.sasw.pacotesuteis.sasdaos.XMLGTVEDao;
import br.com.sasw.pacotesuteis.sasdaos.XMLNFEDao;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.GTVeDao;
import br.com.sasw.pacotesuteis.utilidades.AssinarGTVe;
import br.com.sasw.pacotesuteis.utilidades.Certificados;
import br.com.sasw.pacotesuteis.utilidades.CertificadosCorpvs;
import br.com.sasw.pacotesuteis.utilidades.CertificadosDeltaCorp;
import br.com.sasw.pacotesuteis.utilidades.CertificadosForcaAlerta;
import br.com.sasw.pacotesuteis.utilidades.CertificadosFid;
import br.com.sasw.pacotesuteis.utilidades.CertificadosInvioseg;
import br.com.sasw.pacotesuteis.utilidades.CertificadosPreserve;
import br.com.sasw.pacotesuteis.utilidades.CertificadosPreservePB;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.removeAcentoObjeto;
import br.com.sasw.pacotesuteis.utilidades.Modulo11;
import br.com.sasw.pacotesuteis.utilidades.UFUtils;
import br.com.sasw.pacotesuteis.utilidades.Validacoes;
import br.com.sasw.pacotesuteis.utilidades.XML;
import br.com.sasw.satwebservice.importacao.exceptions.GTVeException;
import br.com.sasw.satwebservice.importacao.exceptions.GTVeException.GTVeErrorCode;
import br.com.sasw.satwebservice.importacao.exceptions.RotaException;
import br.com.sasw.satwebservice.importacao.exceptions.RotaException.RotaErrorCode;
import br.com.sasw.satwebservice.importacao.rotas.MapDeserializer;
import br.com.sasw.satwebservice.importacao.rotas.models.ClientesGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.ComposicoesGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.EmissorGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.GuiasGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.RotasGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.TrajetosGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.VeiculosGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.VolumesGTVe;
import br.com.sasw.satwebservice.utilidades.SocketFactoryDinamico;
import br.com.swconsultoria.certificado.CertificadoService;
import br.inf.portalfiscal.cte.TCTeOS;
import br.inf.portalfiscal.cte.TEndeEmi;
import br.inf.portalfiscal.cte.TEndereco;
import br.inf.portalfiscal.cte.TGTVe;
import br.inf.portalfiscal.cte.TRetGTVe;
import br.inf.portalfiscal.cte.TUFSemEX;
import br.inf.portalfiscal.cte.TUf;
import br.inf.portalfiscal.www.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeStub;
import br.inf.portalfiscal.www.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeStubH;
import br.inf.portalfiscal.www.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeStubHMG;
import br.inf.portalfiscal.www.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeStubHMS;
import br.inf.portalfiscal.www.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeStubHMT;
import br.inf.portalfiscal.www.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeStubHPR;
import br.inf.portalfiscal.www.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeStubHSP;
import br.inf.portalfiscal.www.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeStubMT;
import br.inf.portalfiscal.www.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeStubPR;
import br.inf.portalfiscal.www.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeStubSP;
import br.inf.portalfiscal.www.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeStubMG;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.internal.StringMap;
import com.google.gson.reflect.TypeToken;
import com.lowagie.text.Image;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.StringReader;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.GZIPOutputStream;
import javax.net.ssl.KeyManager;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.ws.rs.core.Response;
import javax.xml.XMLConstants;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import org.apache.commons.codec.binary.Base64OutputStream;
import org.apache.commons.httpclient.protocol.Protocol;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

/**
 *
 * <AUTHOR>
 */
public class ImportacaoGTVe {

    private boolean homolog = true;

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;
    private final ClientesDao clientesDao = new ClientesDao();
    private final CxForteDao cxForteDao = new CxForteDao();
    private final GTVeDao gtvDao = new GTVeDao();
    private final OS_VigDao os_VigDao = new OS_VigDao();
    private final PedidoDao pedidoDao = new PedidoDao();
    private final RotasDao rotasDao = new RotasDao();
    private final Rt_PercDao rt_PercDao = new Rt_PercDao();
    private final XMLNFEDao xmlNfeDao = new XMLNFEDao();
    private final XMLGTVEDao xmlGtveDao = new XMLGTVEDao();

    /**
     * Creates a new instance of ImportacaoClientes
     */
    public ImportacaoGTVe(String empresa) {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\"
                + "\\DADOS\\" + getDataAtual("SQL") + "\\"+empresa+ "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    public Rt_Guias getGuiaSerieForRpv(String rpv, String empresa) throws Exception {
        try {

            this.persistencia = this.pool.getConexao(empresa);
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }

            return gtvDao.getGuiaSerieForRpv(rpv, this.persistencia);

        } catch (JAXBException | SAXException e) {
            e.printStackTrace();
            return null;
        }
    }

    private String EnviarTGTVe(TGTVe tGTVe, String empresa, String xmlAssinado) throws Exception {
        String[] DadosCertificado = null;
        if (empresa.contains("PRESERVE")){
            if(tGTVe.getInfCte().getEmit().getCNPJ().contains("08787673")){
              DadosCertificado = getNomeSenhaCertificado("PRESERVEPB").split("-");
            }else{
              DadosCertificado = getNomeSenhaCertificado(empresa).split("-");
            }
        }else{
           DadosCertificado = getNomeSenhaCertificado(empresa).split("-");
        }
        String NomeCertificado = DadosCertificado[0], SenhaCertificado = DadosCertificado[1];

        try {
            //Get JAXBContext
            JAXBContext jaxbContext = JAXBContext.newInstance(TGTVe.class);

            //Create Unmarshaller
            Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();

            //Setup schema validator
            SchemaFactory sf = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);

            //Schema employeeSchema = sf.newSchema(new File(ImportacaoGTVe.class.getResource("/gtve/GTVe_v3.00.xsd").toString().replace("file:\\", "")
            Schema employeeSchema = sf.newSchema(new File(ImportacaoGTVe.class.getResource("/gtve/GTVe_v4.00.xsd").toString().replace("file:\\", "")                    
                    .replace("file:/", "")
                    .replace("%20", " ")));
            jaxbUnmarshaller.setSchema(employeeSchema);
        } catch (JAXBException | SAXException e) {
            e.printStackTrace();
        }

        ByteArrayOutputStream obj = new ByteArrayOutputStream();
        Base64OutputStream b64os = new Base64OutputStream(obj);
        GZIPOutputStream gzipOS = new GZIPOutputStream(b64os);
        gzipOS.write(xmlAssinado.getBytes("UTF-8"));
        gzipOS.close();

        String xmlEnvio = new String(obj.toByteArray());
        obj.close();
        b64os.close();

        InputStream ce = new FileInputStream(File.separator + "Certificados" + File.separator + NomeCertificado);

        KeyStore ks = KeyStore.getInstance("PKCS12");
        ks.load(ce, SenhaCertificado.toCharArray());

        String alias = "";
        Enumeration<String> aliasesEnum = ks.aliases();
        while (aliasesEnum.hasMoreElements()) {
            alias = (String) aliasesEnum.nextElement();
            if (ks.isKeyEntry(alias)) {
                break;
            }
        }

        X509Certificate certificate = (X509Certificate) ks.getCertificate(alias);
        PrivateKey privateKey = (PrivateKey) ks.getKey(alias, SenhaCertificado.toCharArray());
        SocketFactoryDinamico socketFactoryDinamico = new SocketFactoryDinamico(certificate, privateKey);
        socketFactoryDinamico.setFileCacerts(File.separator + "Certificados" + File.separator + "cacert.jks");

        Protocol protocol = new Protocol("https", socketFactoryDinamico, 443);
        Protocol.registerProtocol("https", protocol);

        
        String RetornoString = "";
        String vTipoAmb = tGTVe.getInfCte().getIde().getTpAmb();
        String vUF = tGTVe.getInfCte().getIde().getCUF();

        this.logerro.Grava("Capturar retorno", this.caminho);

        // Capturar Retorno
        if (null != tGTVe
                && (null != tGTVe.getInfCte()
                && null != tGTVe.getInfCte().getIde().getTpEmis()
                && tGTVe.getInfCte().getIde().getTpEmis().equals("8"))) {
            // SVC-SP
            CTeRecepcaoGTVeStubSP.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStubSP.CteDadosMsg();
            cteDadosMsg.setCteDadosMsg(xmlEnvio);

            CTeRecepcaoGTVeStubSP stub = new CTeRecepcaoGTVeStubSP();
            CTeRecepcaoGTVeStubSP.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);

            try {
                JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                Unmarshaller um = context.createUnmarshaller();
                TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
            } catch (JAXBException e) {
                e.printStackTrace();
            }
            System.out.println();
        } else if (vTipoAmb.equals("2")) {
            if (vUF.equals("16") || vUF.equals("26") || vUF.equals("14") || vUF.equals("35")) {
                // SVC-SP
                CTeRecepcaoGTVeStubHSP.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStubHSP.CteDadosMsg();
                cteDadosMsg.setCteDadosMsg(xmlEnvio);
                CTeRecepcaoGTVeStubHSP stub = new CTeRecepcaoGTVeStubHSP();

                CTeRecepcaoGTVeStubHSP.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);

                try {
                    JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                    Unmarshaller um = context.createUnmarshaller();
                    TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                    System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                    RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
                } catch (JAXBException e) {
                    e.printStackTrace();

                }
            } else if (vTipoAmb.equals("2")) {
                if (vUF.equals("31")) {
                    // SVC-MG
                    // assinarConexaoMG();
                    CTeRecepcaoGTVeStubHMG.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStubHMG.CteDadosMsg();
                    cteDadosMsg.setCteDadosMsg(xmlEnvio);
                    CTeRecepcaoGTVeStubHMG stub = new CTeRecepcaoGTVeStubHMG();

                    CTeRecepcaoGTVeStubHMG.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);

                    try {
                        JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                        Unmarshaller um = context.createUnmarshaller();
                        TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                        System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                        RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
                    } catch (JAXBException e) {
                        e.printStackTrace();
                    }

                } else if (vUF.equals("41")) {
                    // SVC-PR
                    CTeRecepcaoGTVeStubHPR.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStubHPR.CteDadosMsg();
                    cteDadosMsg.setCteDadosMsg(xmlEnvio);
                    CTeRecepcaoGTVeStubHPR stub = new CTeRecepcaoGTVeStubHPR();

                    CTeRecepcaoGTVeStubHPR.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);

                    try {
                        JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                        Unmarshaller um = context.createUnmarshaller();
                        TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                        System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                        RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
                    } catch (JAXBException e) {
                        e.printStackTrace();
                    }

                } else if (vUF.equals("51") ) {
                    // SVC-MT
                    CTeRecepcaoGTVeStubHMT.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStubHMT.CteDadosMsg();
                    cteDadosMsg.setCteDadosMsg(xmlEnvio);
                    CTeRecepcaoGTVeStubHMT stub = new CTeRecepcaoGTVeStubHMT();
                    this.logerro.Grava("Enviado: "+xmlAssinado, this.caminho);
                    CTeRecepcaoGTVeStubHMT.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);

                    try {
                        JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                        Unmarshaller um = context.createUnmarshaller();
                        TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                        System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                        RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
                    } catch (JAXBException e) {
                        e.printStackTrace();
                    }
                } else if (vUF.equals("50")) {
                    // SVC-MS
                    CTeRecepcaoGTVeStubHMS.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStubHMS.CteDadosMsg();
                    cteDadosMsg.setCteDadosMsg(xmlEnvio);
                    CTeRecepcaoGTVeStubHMS stub = new CTeRecepcaoGTVeStubHMS();

                    CTeRecepcaoGTVeStubHMS.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);
//
                    try {
                        JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                        Unmarshaller um = context.createUnmarshaller();
                        TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                        System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                        RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
                    } catch (JAXBException e) {
                        e.printStackTrace();
                    }

                } else {
                    // SVC-RS
                    CTeRecepcaoGTVeStubH.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStubH.CteDadosMsg();
                    cteDadosMsg.setCteDadosMsg(xmlEnvio);
                    CTeRecepcaoGTVeStubH stub = new CTeRecepcaoGTVeStubH();

                    CTeRecepcaoGTVeStubH.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);

                    try {
                        JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                        Unmarshaller um = context.createUnmarshaller();
                        TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                        System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                        RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
                    } catch (JAXBException e) {
                        e.printStackTrace();
                    }

                }
            }
        } else {
            if (vUF.equals("41")) {
                // SVC-PR
                CTeRecepcaoGTVeStubPR.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStubPR.CteDadosMsg();
                CTeRecepcaoGTVeStubPR.CteCabecMsgE cteCabecMsg = new CTeRecepcaoGTVeStubPR.CteCabecMsgE();
                cteDadosMsg.setCteDadosMsg(xmlEnvio);
                CTeRecepcaoGTVeStubPR stub = new CTeRecepcaoGTVeStubPR();

                CTeRecepcaoGTVeStubPR.CTeRecepcaoGTVeResult result = stub.CteRecepcaoGTVe(cteDadosMsg, cteCabecMsg);
                try {
                    JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                    Unmarshaller um = context.createUnmarshaller();
                    TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                    System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                    RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
                } catch (JAXBException e) {
                    e.printStackTrace();
                }

            } else if (vUF.equals("08") || vUF.equals("16") || vUF.equals("26") || vUF.equals("14") || vUF.equals("35")) {
                // SVC-SP
                CTeRecepcaoGTVeStubSP.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStubSP.CteDadosMsg();
                cteDadosMsg.setCteDadosMsg(xmlEnvio);
                CTeRecepcaoGTVeStubSP stub = new CTeRecepcaoGTVeStubSP();

                CTeRecepcaoGTVeStubSP.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);

                try {
                    JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                    Unmarshaller um = context.createUnmarshaller();
                    TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                    System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                    RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
                } catch (JAXBException e) {
                    e.printStackTrace();
                }
            } else if (vUF.equals("31")) {
                // SVC-MG
//                    assinarConexaoMG();
                    CTeRecepcaoGTVeStubMG.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStubMG.CteDadosMsg();
                    cteDadosMsg.setCteDadosMsg(xmlEnvio);
                    CTeRecepcaoGTVeStubMG stub = new CTeRecepcaoGTVeStubMG();

                    CTeRecepcaoGTVeStubMG.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);

                    try {
                        JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                        Unmarshaller um = context.createUnmarshaller();
                        TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                        System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                        RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
                    } catch (JAXBException e) {
                        e.printStackTrace();
                    }
            } else if (vUF.equals("51")) {
                // SVC-SP
                CTeRecepcaoGTVeStubMT.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStubMT.CteDadosMsg();
                cteDadosMsg.setCteDadosMsg(xmlEnvio);
                CTeRecepcaoGTVeStubMT stub = new CTeRecepcaoGTVeStubMT();                
                CTeRecepcaoGTVeStubMT.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);

                try {
                    JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                    Unmarshaller um = context.createUnmarshaller();
                    TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                    System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                    RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
                } catch (JAXBException e) {
                    e.printStackTrace();
                }                                
            } else {
                // SVC-RS
                CTeRecepcaoGTVeStub.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStub.CteDadosMsg();
                cteDadosMsg.setCteDadosMsg(xmlEnvio);
                CTeRecepcaoGTVeStub stub = new CTeRecepcaoGTVeStub();
                CTeRecepcaoGTVeStub.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);
                try {
                    JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                    Unmarshaller um = context.createUnmarshaller();
                    TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                    System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                    RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
                } catch (JAXBException e) {
                    e.printStackTrace();
                }
                System.out.println();
            }
        }
        System.out.println();
        this.logerro.Grava("Retorno capturado", this.caminho);
        RetornoString = RetornoString.replace("RejeiÃ§Ã£o", "Rejeicao");
        RetornoString = RetornoString.replace("Rejeicao", "Rejeicao");
        RetornoString = RetornoString.replace("Ã§Ã£", "ca");
        RetornoString = RetornoString.replace("EndereÃ§o", "endereco");  
        this.logerro.Grava("Enviado: "+xmlAssinado, this.caminho);
        this.logerro.Grava("Recebido: "+RetornoString, this.caminho);                
        return RetornoString;
    }

    private String AssinarXML(TGTVe tGTVe, String empresa) throws Exception {
        String[] DadosCertificado = null;
        if (empresa.contains("PRESERVE")){
            if(tGTVe.getInfCte().getEmit().getCNPJ().contains("08787673")){
              DadosCertificado = getNomeSenhaCertificado("PRESERVEPB").split("-");
            }else{
              DadosCertificado = getNomeSenhaCertificado(empresa).split("-");
            }
        }else{
           DadosCertificado = getNomeSenhaCertificado(empresa).split("-");
        }        

        String NomeCertificado = DadosCertificado[0], SenhaCertificado = DadosCertificado[1];

        this.logerro.Grava("Assinar XML", this.caminho);
        String xml = XML.toXml(tGTVe, Boolean.FALSE);
        xml = xml.replace("Â ", "");
        xml = xml.replace("º", "");
        xml = xml.replace("°", "");
        xml = xml.replace("º", "");
        xml = xml.replace("ª", "");
        xml = xml.replace("<verProc>000</verProc>", "<verProc>0</verProc>");
        this.logerro.Grava("XML nao assinado:"+xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""), this.caminho);
//        gravarXMLGtve(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),  this.persistencia);
        String xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                        + NomeCertificado,
                        SenhaCertificado));
        this.logerro.Grava("XML assinado:"+xmlAssinado, this.caminho);

        return xmlAssinado;
    }

    private String getNomeSenhaCertificado(String empresa) throws Exception {
        String NomeCertificado = "", SenhaCertificado = "12345678";

        switch (empresa) {
            case "IBL":
            case "SATIBL":
                NomeCertificado = "IBL";
                SenhaCertificado = "Jonatas2024";
                break;

            case "CEFOR":
            case "SATCEFOR":
                NomeCertificado = "CEFOR";
                SenhaCertificado = "1234";
                break;                

            case "DELTACORP":
            case "SATDELTACORP":
                NomeCertificado = "DELTA";
                SenhaCertificado = "fenix2023";
                break;                                
            case "FORCAALERTA":
            case "SATFORCAALERTA":
                NomeCertificado = "FORCAALERTA";
                SenhaCertificado = "alerta@10";
                break;      
            case "FEDERAL":
            case "SATFEDERAL":
                NomeCertificado = "FEDERAL";
                SenhaCertificado = "1234";
                break;    
            case "BRASIFORT":
            case "SATBRASIFORT":
                NomeCertificado = "BRASIFORTNA";
                SenhaCertificado = "12345678";
                break;
            case "BRINKS":
            case "SATBRINKS":
                NomeCertificado = "BRINKS";
                SenhaCertificado = "Br1nks@SAO23";
                break;
            case "GLOBAL":
            case "SATGLOBAL":
                NomeCertificado = "GLOBAL";
                SenhaCertificado = "010113";
                break;

            case "PROSEGUR":
            case "SATPROSEGUR":
            case "PROSEGURBSB":
            case "SATPROSEGURBSB":
                NomeCertificado = "PROSEGUR";
                SenhaCertificado = "17428731";
                break;

            case "FIDELYS":
            case "SATFIDELYS1":
                NomeCertificado = "FIDELYS";
                SenhaCertificado = "1234";
                break;

            case "PRESERVE":
            case "SATPRESERVE":
                NomeCertificado = "PRESERVE";
                SenhaCertificado = "1234";
                break;

            case "PRESERVEPB":            
                NomeCertificado = "PRESERVEPB";
                SenhaCertificado = "1234";
                break;
                
                
            case "INVIOSEG":
            case "SATINVLMT":
                NomeCertificado = "INVIOSEG";
                SenhaCertificado = "1234";
                break;
                
            case "INVISEG":
            case "SATINVLRO":
                NomeCertificado = "INVISEG";
                SenhaCertificado = "Invi@5520";
                break;
                
                
            case "CORPVS":
            case "SATCORPVSPE":
            case "SATCORPVS":
            case "SATCORPVS2":
            case "SATCORPVSPE2":
                NomeCertificado = "CORPVS";
                SenhaCertificado = "123456";
                break;
                
            default:
                throw new Exception("Empresa sem Certificado Vinculado!");
        }

        return NomeCertificado + "-" + SenhaCertificado;
    }

    public Response json(String guia, String serie, String empresa) {
        this.logerro.Grava("Acessou montagem de jSon", this.caminho);
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(GTVeException.class, new GTVeExceptionSerializer());
        Gson gson = gsonBuilder.create();
        Map retorno = new HashMap<>(), processamento, processamentoTrajeto;
        List processamentos = new ArrayList<>(), trajetosProcessado;

        try {

            // Validando parâmetro de conexão
            this.logerro.Grava("Validando persistencia", this.caminho);
            this.persistencia = this.pool.getConexao(empresa);
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }
            this.logerro.Grava("Persistencia criada", this.caminho);

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\" + empresa + "\\GTVe\\"
                    + getDataAtual("SQL") + "\\log.txt";

            this.logerro.Grava("Guia:\t" + guia + "\r\nSerie:\t" + serie, this.caminho);
            /*
            String sequencia, numero;
            CxForte cxForte;
            Pedido pedido;
            Rotas rota;
            Rt_Perc trajeto;
             */
            this.logerro.Grava("Consultando dados", this.caminho);
            TGTVe gtv = gtvDao.getTGTVe(guia, serie, this.persistencia);
            this.logerro.Grava("Consulta finalizada", this.caminho);

            if (gtv == null) {
                this.logerro.Grava("Nao retornou dados", this.caminho);
                throw new GTVeException(new GTVeErrorCode(0));
            }

            /*homolog = false; Desativado o FIXO - Carlos 05/01/2022

            if (!homolog) {
                gtv.getInfCte().getIde().setTpAmb("1");
            } else {
                gtv.getInfCte().getIde().setTpAmb("2");
            }
             */
            this.logerro.Grava("Ambiente SEFAZ::\t" + gtv.getInfCte().getIde().getTpAmb(), this.caminho);

            String cuf = gtv.getInfCte().getIde().getCUF();
            String AAMM = gtv.getInfCte().getId();
            String cnpj = gtv.getInfCte().getEmit().getCNPJ();
            String mod = gtv.getInfCte().getIde().getMod();
            String s = FuncoesString.PreencheEsquerda(gtv.getInfCte().getIde().getSerie(), 3, "0");
            String nct = FuncoesString.PreencheEsquerda(gtv.getInfCte().getIde().getNCT().replace(".", ""), 9, "0");
            String tpemi = gtv.getInfCte().getIde().getTpEmis();
            String cct = FuncoesString.PreencheEsquerda(gtv.getInfCte().getIde().getCCT(), 8, "0");
            String cdv = String.valueOf(Modulo11.modulo11(cuf + AAMM + cnpj + mod + s + nct + tpemi + cct));

            gtv.getInfCte().getIde().setCCT(cct);
            gtv.getInfCte().getIde().setCDV(cdv);
            gtv.getInfCte().setVersao("4.00");
            gtv.getInfCte().setId("CTe" + cuf + AAMM + cnpj + mod + s + nct + tpemi + cct.replace(".", "") + cdv);
            gtv.getInfCte().setDetGTV(gtvDao.getDetGTV(guia, serie, persistencia));

            gtv.setVersao("4.00");

            //if (homolog) {
            if (gtv.getInfCte().getIde().getTpAmb() == "2") {
                gtv.getInfCte().getRem().setXNome("CT-E EMITIDO EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL");
                gtv.getInfCte().getDest().setXNome("CT-E EMITIDO EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL");
            }

            TGTVe.InfCTeSupl infCTeSupl = new TGTVe.InfCTeSupl();

            if (cuf.equals("31")) {
                infCTeSupl.setQrCodCTe("https://cte.fazenda.mg.gov.br/portalcte/sistema/qrcode.xhtml?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("51")) {
                infCTeSupl.setQrCodCTe("https://www.sefaz.mt.gov.br/cte/qrcode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("14")) {
                infCTeSupl.setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("16")) {
                infCTeSupl.setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("26")) {
                infCTeSupl.setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("35")) {
                infCTeSupl.setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("41")) {
                infCTeSupl.setQrCodCTe("http://www.fazenda.pr.gov.br/cte/qrcode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else {
                infCTeSupl.setQrCodCTe("https://dfe-portal.svrs.rs.gov.br/cte/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            }
            /*
            if (gtv.getInfCte().getIde().getTpEmis().equals("8")) {
                // SVC-SP
                infCTeSupl.setQrCodCTe("https://homologacao.nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else {
                // SVC-RS
                infCTeSupl.setQrCodCTe("https://dfe-portal.svrs.rs.gov.br/cte/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            }*/
            gtv.setInfCTeSupl(infCTeSupl);

            // Assina XML
            String xmlAssinado = AssinarXML(gtv, empresa);

            // Envia SEFAZ
            String RetornoString = "";
                        
            RetornoString = EnviarTGTVe(gtv, empresa, xmlAssinado);
            
            // Gravar log em xmlGTVE
            InputSource is = new InputSource(new StringReader(RetornoString));
            DocumentBuilder dBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            Document doc = dBuilder.parse(is);

            XMLGtve xmlGTVE = new XMLGtve(gtv.getInfCte().getEmit().getCNPJ(),
                    guia,
                    serie,
                    gtv.getInfCte().getIde().getCMunEnv(),
                    getDataAtual("SQL"),
                    getDataAtual("HORA"),
                    xmlAssinado,
                    RetornoString,
                    ProcuraTag(doc, "infProt", "nProt", 0, false),
                    ProcuraTag(doc, "infProt", "chCTe", 0, false),
                    getDataAtual("SQL"),
                    getDataAtual("HORA"),
                    getDataAtual("SQL"),
                    getDataAtual("HORA"),
                    gtv.getInfCTeSupl().getQrCodCTe());

            gravarXMLGtve(xmlGTVE, this.persistencia);

            retorno.put("status", "ok");
            retorno.put("resp", RetornoString);

//            ServicoEnviarLoteEventosStub.LoteEventos_type0 dadosMsgType0 = new ServicoEnviarLoteEventosStub.LoteEventos_type0();
//            System.setProperty("com.sun.xml.ws.transport.http.client.HttpTransportPipe.dump", "true");
//            System.setProperty("com.sun.xml.internal.ws.transport.http.client.HttpTransportPipe.dump", "true");
//            System.setProperty("com.sun.xml.ws.transport.http.HttpAdapter.dump", "true");
//            System.setProperty("com.sun.xml.internal.ws.transport.http.HttpAdapter.dump", "true");
//            System.setProperty("com.sun.xml.internal.ws.transport.http.HttpAdapter.dumpTreshold", "999999");
//            br.inf.portalfiscal.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVe service = new br.inf.portalfiscal.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVe();
//            service.setHandlerResolver(new HandlerResolver() {
//                @Override
//                public List<Handler> getHandlerChain(PortInfo portInfo) {
//                    return Arrays.asList(new GTVeNamespaceMapper());
//                }
//            });
//            
//            br.inf.portalfiscal.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeSoap12 port = service.getCTeRecepcaoGTVeSoap12();
//            br.inf.portalfiscal.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeResult result = port.cTeRecepcaoGTVe(xmlEnvio);
//            
//            try {
//
//                JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
//                Unmarshaller um = context.createUnmarshaller();
//                TRetGTVe retGTVe = (TRetGTVe) um.unmarshal((Node) result.getAny());
//                System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
//            } catch (JAXBException e) {
//                e.printStackTrace();
//            }
//            System.out.println(Enviar(xmlAssinado));

            /*
            List<RotasGTVe> rotaGTVeList = null;
            if (rotaGTVeList == null) {
                throw new RotaException(new RotaErrorCode(0));
            }

            for (RotasGTVe rotaGTVe : rotaGTVeList) {
                processamento = new HashMap<>();

                try {
                    rota = rotaFromRotasGTVe(rotaGTVe);
                    processamento.put("rota", rota.getRota());

                    sequencia = this.rotasDao.buscarSeqRota(rota.getCodFil().toPlainString(), rota.getRota(), rota.getData(), this.persistencia);
                    if (sequencia == null) {
                        // inserir rota

                        try {
                            sequencia = this.rotasDao.inserirRotaSequencia(rota, this.persistencia);
                        } catch (Exception insertRota) {
                            throw new RotaException(insertRota.getMessage(), new RotaErrorCode(0));
                        }
                    }

                    rota.setSequencia(sequencia);
                    trajetosProcessado = new ArrayList<>();

                    for (TrajetosGTVe trajetoGTVe : rotaGTVe.getTrajetos()) {
                        processamentoTrajeto = new HashMap<>();
                        try {
                            trajeto = trajetoFromTrajetosGTVe(trajetoGTVe, rota);
                            processamentoTrajeto.put("parada", trajeto.getParada());

                            if (this.rt_PercDao.existeRt_Perc(trajeto.getSequencia().toString(), trajeto.getParada(), this.persistencia)) {
                                throw new RotaException(new RotaErrorCode(6));
                            } else {
                                if (trajeto.getER().equals("E")) {
                                    pedido = obterPedido(trajeto);
                                    cxForte = this.cxForteDao.getCxForte(trajeto.getCodFil(), this.persistencia);
                                    pedido.setCodCli1(cxForte.getCodCli());
                                } else if (trajeto.getER().equals("R")) {
                                    pedido = obterPedido(trajeto);
                                } else {
                                    pedido = null;
                                }

                                if (pedido != null) {
                                    numero = this.pedidoDao.inserirPedidoNumero(pedido, this.persistencia);
                                    trajeto.setPedido(numero);
                                } else {
                                    trajeto.setPedido("0");
                                }

                                // insere trajeto
                                trajeto.setDt_Incl(LocalDate.now());
                                trajeto.setHr_Incl(getDataAtual("HORA"));
                                trajeto.setOperIncl(RecortaAteEspaço("SatWebService", 0, 10));
                                try {
                                    this.rt_PercDao.inserirTrajeto(trajeto, this.persistencia);

                                    processamentoTrajeto.put("result", new RotaException(new RotaErrorCode(1)));
                                } catch (Exception insertTrajeto) {
                                    throw new RotaException(insertTrajeto.getMessage(), new RotaErrorCode(-1));
                                }
                            }

                        } catch (RotaException e) {
                            processamentoTrajeto.put("result", e);
                        } catch (Exception e) {
                            processamentoTrajeto.put("result", new RotaException(e.getMessage(), new RotaErrorCode(-1)));
                        }

                        trajetosProcessado.add(processamentoTrajeto);
                    }
                    processamento.put("trajetos", trajetosProcessado);
                } catch (RotaException e) {
                    processamento.put("result", e);
                } catch (Exception e) {
                    processamento.put("result", e.getMessage());
                }

                processamentos.add(processamento);
            }

            retorno.put("status", "ok");
            retorno.put("resp", processamentos);*/
        } catch (Exception e) {            
            // Salvando em log o que foi mandado
            retorno.clear();
            retorno.put("status", "error");
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);
        } finally {
            this.logerro.Grava(gson.toJson(retorno), this.caminho);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gson.toJson(retorno))
                    .build();
        }
    }

    /* GRAVAR LOG DE GTVE */
    private void gravarXMLGtve(XMLGtve xmlGTVE, Persistencia persistencia) throws Exception {
        try {
            XMLGTVEDao xMLGTVEDao = new XMLGTVEDao();
            xMLGTVEDao.inserirXMLGtve(xmlGTVE, persistencia);
        } catch (Exception e) {
            this.logerro.Grava("Erro ao gravar XMLGTVe: " + e.getMessage(), this.caminho);
        }
    }

    private void gravarXMLGtveProsegur(XMLGtve xmlGTVE, Persistencia persistencia) throws Exception {
        try {
            XMLGTVEDao xMLGTVEDao = new XMLGTVEDao();
            xMLGTVEDao.inserirXMLGtveProsegur(xmlGTVE, persistencia);
        } catch (Exception e) {
            this.logerro.Grava("Erro ao gravar XMLGTVe: " + e.getMessage(), this.caminho);
        }
    }

    /**
     * Retorna o antecedentes de um nó.
     *
     * @param no
     * @return
     */
    private String PossuiNoPai(Node no) {
        if (null != no.getParentNode()) {
            return PossuiNoPai(no.getParentNode()) + "/" + no.getNodeName();
        }
        return "";
    }

    private String ProcuraTag(Document doc, String pai, String filho, int indice, boolean uso) throws XPathExpressionException {
        String retorno = "";
        if (pai.contains("@")) {
            pai = pai.replace("@", "");
        }
        NodeList nodeList = doc.getElementsByTagName(filho);
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node no = nodeList.item(i);
            if (null != no.getParentNode()) {
                String busca = PossuiNoPai(no.getParentNode()) + "/" + no.getNodeName();
                String param = pai + "/" + filho;
                if (busca.contains(param)) {
                    XPathFactory xPathfactory = XPathFactory.newInstance();
                    XPath xpath = xPathfactory.newXPath();
                    XPathExpression expr = xpath.compile(busca + "/text()");
                    NodeList nos = (NodeList) expr.evaluate(doc, XPathConstants.NODESET);
                    if (nos.getLength() == 1) {
                        retorno = nos.item(0).getNodeValue();
                    } else if (nos.getLength() > 1 && nos.getLength() > indice) {
                        retorno = nos.item(indice).getNodeValue();
                    } else {
                        retorno = "";
                    }
                    /**
                     * Formata a saída da data e da hora.
                     */
                    if (filho.contains("dhEmi")) {
                        String[] parts = retorno.split("T");
                        DateTimeFormatter d = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        LocalDate data = LocalDate.parse(parts[0], d);

                        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                        retorno = data.format(formatter2) + " " + parts[1].split("-")[0];
                    }
                    if (filho.contains("xObs")) {
                        retorno = uso ? "<span class=\"fonte10\">" + retorno + "</span>"
                                : "<span class=\"fonte16\">SEM VALOR FISCAL</span><br><span class=\"fonte10\">" + retorno + "</span> ";
                    }
                    return retorno;
                }
            }
        }
        if (filho.contains("xObs")) {
            retorno = uso ? "<span class=\"fonte10\">" + retorno + "</span>"
                    : "<span class=\"fonte16\">SEM VALOR FISCAL</span><br><span class=\"fonte10\">" + retorno + "</span> ";
        }
        return retorno;
    }

    private String ProcuraAtributo(Document doc, String tag) throws XPathExpressionException {
        String retorno = "";
        if (tag.contains("@")) {
            tag = tag.replace("@", "");
        }
        NodeList nodeList = doc.getElementsByTagName(tag);
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node no = nodeList.item(i);
            XPathFactory xPathfactory = XPathFactory.newInstance();
            XPath xpath = xPathfactory.newXPath();
            XPathExpression expr = xpath.compile("/" + no.getParentNode().getNodeName() + "/" + (tag) + "[@Id]");
            NodeList nos = (NodeList) expr.evaluate(doc, XPathConstants.NODESET);
            retorno = nos.item(0).getAttributes().getNamedItem("Id").getNodeValue();
            if (retorno.contains("CTe")) {
                retorno = retorno.replace("CTe", "");
            }
            return retorno;
        }
        return retorno;
    }

    public XMLNFE dadosXmlNfe(String nProt, String dtRef) throws Exception {
        return xmlNfeDao.buscarXMLNFE(nProt, dtRef, this.pool.getConexao("SATEXCEL"));
    }

    public iblCTeOS getGTVeIntegraIBL(String empresa, String guia, String serie) throws Exception {
        return gtvDao.getGTVeIntegraIBL(guia, serie, this.pool.getConexao(empresa));
    }

    public iblCTeOS getDataNfseVr(String empresa, String guia, String serie, String praca) throws Exception {
        return gtvDao.getDataNfseVr(guia, serie, praca, this.pool.getConexao(empresa));
    }

    public XMLNFE getDataNfseVrXml(String empresa, String guia, String serie, String praca) throws Exception {
        return gtvDao.getDataNfseVrXml(guia, serie, praca, this.pool.getConexao(empresa));
    }

    public XMLGtve dadosXmGtve(String empresa, String guia, String serie) throws Exception {
        this.persistencia = this.pool.getConexao(empresa);

        Rt_GuiasDao rtGuiasDao = new Rt_GuiasDao();
        Rt_Guias retorno = rtGuiasDao.consultaGuiaSerieAssina(guia, serie, "", this.persistencia);

        Image validaAssinatura;
        Image validaAssinatura2;
        String caminho = "C:/xampp/htdocs/satellite/assinaturas/";
        String caminho2 = "S:/assinaturas/";
        //String caminho = "https://mobile.sasw.com.br:9091/satellite/assinaturas/";

        try {
            // Validação de existência de arquivo com Endereçamento "Antigo"
            validaAssinatura = Image.getInstance(caminho + this.persistencia.getEmpresa()
                    + "/" + retorno.getSequencia().toPlainString().replace(".0", "") + "/" + retorno.getParada()
                    + ".png");
            validaAssinatura2 = Image.getInstance(caminho2 + this.persistencia.getEmpresa()
                    + "/" + retorno.getSequencia().toPlainString().replace(".0", "") + "/" + retorno.getParada()
                    + ".png");
            if (retorno.getAssDestinatario() != null && !retorno.getAssDestinatario().equals("")) {

                if (retorno.getAssRemetente() != null && !retorno.getAssRemetente().equals("") && retorno.getAssRemetente().equals(retorno.getAssDestinatario())) {
                    if (retorno.getEr().equals("E")) {
                        retorno.setAssDestinatarioImagem(caminho.replace("C:/xampp/htdocs/satellite/assinaturas/", "https://mobile.sasw.com.br:9091/satellite/assinaturas/") + this.persistencia.getEmpresa()
                                + "/" + retorno.getSequencia().toPlainString().replace(".0", "") + "/" + retorno.getParada()
                                + ".png");
                    }

                    if (retorno.getEr().equals("R")) {
                        retorno.setAssRemetenteImagem(caminho.replace("C:/xampp/htdocs/satellite/assinaturas/", "https://mobile.sasw.com.br:9091/satellite/assinaturas/") + this.persistencia.getEmpresa()
                                + "/" + retorno.getSequencia().toPlainString().replace(".0", "") + "/" + retorno.getParada()
                                + ".png");

                    }
                } else {
                    retorno.setAssDestinatarioImagem(caminho.replace("C:/xampp/htdocs/satellite/assinaturas/", "https://mobile.sasw.com.br:9091/satellite/assinaturas/") + this.persistencia.getEmpresa()
                            + "/" + retorno.getSequencia().toPlainString().replace(".0", "") + "/" + retorno.getParada()
                            + ".png");
                }

            } else {
                retorno.setAssRemetenteImagem(caminho.replace("C:/xampp/htdocs/satellite/assinaturas/", "https://mobile.sasw.com.br:9091/satellite/assinaturas/") + this.persistencia.getEmpresa()
                        + "/" + retorno.getSequencia().toPlainString().replace(".0", "") + "/" + retorno.getParada()
                        + ".png");
            }
        } catch (Exception ex) {
        }

        try {
            // Validação de existência de arquivo com Endereçamento "Novo" - Origem
            try {
                validaAssinatura = Image.getInstance(caminho + this.persistencia.getEmpresa()
                        + "/" + retorno.getSequencia().toPlainString().replace(".0", "") + "/" + retorno.getGuia().toPlainString().replace(".0", "") + "_" + retorno.getSerie() + "_" + retorno.getParada() + "_ori"
                        + ".png");

                validaAssinatura2 = Image.getInstance(caminho2 + this.persistencia.getEmpresa()
                        + "/" + retorno.getSequencia().toPlainString().replace(".0", "") + "/" + retorno.getGuia().toPlainString().replace(".0", "") + "_" + retorno.getSerie() + "_" + retorno.getParada() + "_ori"
                        + ".png");
                
                retorno.setAssRemetenteImagem(caminho.replace("C:/xampp/htdocs/satellite/assinaturas/", "https://mobile.sasw.com.br:9091/satellite/assinaturas/") + this.persistencia.getEmpresa()
                        + "/" + retorno.getSequencia().toPlainString().replace(".0", "") + "/" + retorno.getGuia().toPlainString().replace(".0", "") + "_" + retorno.getSerie() + "_" + retorno.getParada() + "_ori"
                        + ".png");
            } catch (Exception ex1) {

            }

            // Validação de existência de arquivo com Endereçamento "Novo" - Destino
            try {
                validaAssinatura = Image.getInstance(caminho + this.persistencia.getEmpresa()
                        + "/" + retorno.getSequencia().toPlainString().replace(".0", "") + "/" + retorno.getGuia().toPlainString().replace(".0", "") + "_" + retorno.getSerie() + "_" + retorno.getParada() + "_dst"
                        + ".png");

                validaAssinatura2 = Image.getInstance(caminho2 + this.persistencia.getEmpresa()
                        + "/" + retorno.getSequencia().toPlainString().replace(".0", "") + "/" + retorno.getGuia().toPlainString().replace(".0", "") + "_" + retorno.getSerie() + "_" + retorno.getParada() + "_dst"
                        + ".png");
                
                retorno.setAssDestinatarioImagem(caminho.replace("C:/xampp/htdocs/satellite/assinaturas/", "https://mobile.sasw.com.br:9091/satellite/assinaturas/") + this.persistencia.getEmpresa()
                        + "/" + retorno.getSequencia().toPlainString().replace(".0", "") + "/" + retorno.getGuia().toPlainString().replace(".0", "") + "_" + retorno.getSerie() + "_" + retorno.getParada() + "_dst"
                        + ".png");
            } catch (Exception ex1) {

            }
        } catch (Exception ex) {
        }

        XMLGtve retornoObj = xmlGtveDao.buscarXMLGtve(guia, serie, this.persistencia);

        retornoObj.setAssinaDestinatario(retorno.getAssDestinatario());
        retornoObj.setAssinaDestinatarioImagem(retorno.getAssDestinatarioImagem());
        retornoObj.setAssinaRemetente(retorno.getAssRemetente());
        retornoObj.setAssinaRemetenteImagem(retorno.getAssRemetenteImagem());

        return retornoObj;
    }

    public Response cancelarGTVe(String input, String empresa) {
        TCTeOS tCTeOS = new TCTeOS();
        TGTVe tGTVe = new TGTVe();
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(XMLGtveCancelar.class, new GTVeExceptionSerializer());

        GsonBuilder gsonBuilderException = new GsonBuilder();
        gsonBuilderException.registerTypeAdapter(GTVeException.class, new GTVeExceptionSerializer());
        Gson gsonException = gsonBuilderException.create();
        Map retorno = new HashMap<>();

        try {
            // Convertendo dados para Objeto
            Gson gson = gsonBuilder.create();
            XMLGtveCancelar xmlGtveCancelar = gson.fromJson(input, XMLGtveCancelar.class);

            // Pegar ID
            Boolean TrataObjeto = true;
            String Dados = "";
            int Index = input.indexOf("@Id");

            while (TrataObjeto) {
                Dados += input.substring(Index, Index + 1);

                if (Dados.contains(",")) {
                    TrataObjeto = false;
                } else {
                    Index++;
                }
            }

            Dados = Dados.replace("@Id", "");
            Dados = Dados.replace(":", "");
            Dados = Dados.replace(" ", "");
            Dados = Dados.replace("\"", "");
            Dados = Dados.replace(",", "");

            xmlGtveCancelar.infEvento.setId(Dados);

            // Validando parâmetro de conexão
            this.logerro.Grava("Validando persistencia", this.caminho);
            this.persistencia = this.pool.getConexao(empresa);

            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }
            this.logerro.Grava("Persistencia criada", this.caminho);

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\" + empresa + "\\GTVe\\" + getDataAtual("SQL") + "\\log.txt";

            if (null != xmlGtveCancelar && null != xmlGtveCancelar.infEvento) {

                String xmlSemAssinar = jaxbObjectToXML(xmlGtveCancelar);

                retorno.put("status", "ok");
                retorno.put("success", "Cancelado com Sucesso");

                return Response
                        .status(Response.Status.OK)
                        .type("application/json")
                        .entity(gsonException.toJson(retorno))
                        .build();
            } else {
                retorno.put("status", "error");
                retorno.put("error", "JSON inválido");

                return Response
                        .status(Response.Status.BAD_REQUEST)
                        .type("application/json")
                        .entity(gsonException.toJson(retorno))
                        .build();
            }
        } catch (Exception e) {
            // Salvando em log o que foi mandado
            retorno.clear();
            retorno.put("status", "error");
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);

            return Response
                    .status(Response.Status.INTERNAL_SERVER_ERROR)
                    .type("application/json")
                    .entity(gsonException.toJson(retorno))
                    .build();
        }
    }

    private static String jaxbObjectToXML(XMLGtveCancelar xmlGtveCancelar) {
        String xmlString = "";
        try {
            JAXBContext context = JAXBContext.newInstance(XMLGtveCancelar.class);
            Marshaller m = context.createMarshaller();

            m.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE); // To format XML

            StringWriter sw = new StringWriter();
            m.marshal(xmlGtveCancelar, sw);
            xmlString = sw.toString();

        } catch (JAXBException e) {
            e.printStackTrace();
        }

        return xmlString;
    }

    public Response consultaProtocolo(String empresa, String protocolo) {
        GsonBuilder gsonBuilderException = new GsonBuilder();
        gsonBuilderException.registerTypeAdapter(GTVeException.class, new GTVeExceptionSerializer());
        Gson gsonException = gsonBuilderException.create();

        Map retorno = new HashMap<>();
        Boolean isErro = false;

        try {
            // Validando parâmetro de conexão
            this.logerro.Grava("Validando persistencia", this.caminho);
            this.persistencia = this.pool.getConexao(empresa);

            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }
            this.logerro.Grava("Persistencia criada", this.caminho);

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\" + empresa + "\\GTVe\\" + getDataAtual("SQL") + "\\log.txt";

            XMLGtve xmlGtve = xmlGtveDao.buscarXMLGtvePorProcolo(protocolo, this.persistencia);

            if (null != xmlGtve && !xmlGtve.getProtocolo().equals("")) {

                XMLGtveRet xmlGtveRet = new XMLGtveRet();
                xmlGtveRet.setProtocolo(xmlGtve.getProtocolo());
                xmlGtveRet.setChaveGTVE(xmlGtve.getChaveGTVE());
                //xmlGtveRet.setXML_Envio(xmlGtve.getXML_Envio());
                xmlGtveRet.setXML_Retorno(xmlGtve.getXML_Retorno());
                xmlGtveRet.setLink(xmlGtve.getLink());

                XMLGtveRetDadosAdic Dados_adic = new XMLGtveRetDadosAdic();
                Dados_adic.setFatIdGtv(xmlGtve.getFatIdGtv());
                Dados_adic.setOpenIdGtv(xmlGtve.getOpenIdGtv());
                Dados_adic.setOpenIdFilial(xmlGtve.getOpenIdFilial());

                if (null == Dados_adic.getOpenIdGtv()) {
                    Dados_adic.setOpenIdGtv("");
                }

                xmlGtveRet.setDados_adic(Dados_adic);

                Gson gson = new Gson();

                return Response
                        .status(Response.Status.OK)
                        .type("application/json")
                        .entity(gson.toJson(xmlGtveRet))
                        .build();

            } else {
                retorno.put("status", "ok");
                retorno.put("data", "NAO ENCONTRADO");

                return Response
                        .status(Response.Status.OK)
                        .type("application/json")
                        .entity(gsonException.toJson(retorno))
                        .build();
            }

        } catch (Exception e) {
            // Salvando em log o que foi mandado
            retorno.clear();
            retorno.put("status", "error");
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);

            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gsonException.toJson(retorno))
                    .build();
        }
    }

    public Response dataCteFromJson(String input, String empresa) {
        this.logerro.Grava("XML Recebido:" + input, this.caminho);

        GsonBuilder gsonBuilder = new GsonBuilder(), gsonBuilderException = new GsonBuilder(), gsonBuilderDadosAdic = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(TGTVe.class, new GTVeExceptionSerializer());
        gsonBuilderException.registerTypeAdapter(GTVeException.class, new GTVeExceptionSerializer());
        gsonBuilderDadosAdic.registerTypeAdapter(ProsegurTGTVeDados.class, new GTVeExceptionSerializer());

        Gson gsonException = gsonBuilderException.create();
        Map retorno = new HashMap<>();
        Boolean isErro = false;

        String xmlAssinado = "", protocoloGuardado = "";

        try {
            // Validando parâmetro de conexão
            this.logerro.Grava("Validando persistencia", this.caminho);
            this.persistencia = this.pool.getConexao(empresa);

            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }
            this.logerro.Grava("Persistencia criada", this.caminho);

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\" + empresa + "\\GTVe\\" + getDataAtual("SQL") + "\\log.txt";

            // Tratamentos de conteúdo recebido
            input = input.replace("\"CNPJ\"", "\"cnpj\"").replace("\"IE\"", "\"ie\"").replace("\"CEP\"", "\"cep\"").replace("\"cUF\"", "\"cuf\"").replace("\"UF\"", "\"uf\"");
            input = input.replace("\"cCT\"", "\"cct\"").replace("\"CFOP\"", "\"cfop\"").replace("\"nCT\"", "\"nct\"").replace("\"cDV\"", "\"cdv\"");
            input = input.replace("\"UFEnv\"", "\"ufEnv\"");
            input = input.replace("\"CPF\"", "\"cpf\"").replace("\'{","{").replace("}\'","}");

            // Conversão de Dados
            Gson gson = gsonBuilder.create();
            TGTVe tGTVe;
            // Na criação das variavíes infVeiculo e infEspecie foram 
            // definidos como listas mas no objeto JSON são enviados como
            // objetos simples, foi necessário transformar o ojetivo simples
            // em um vetor para leitura do objeto da classe TGTVe sem erro
            
            // Tratamento para converter objeto "infVeiculo" em Lista
            Boolean TrataObjeto = true;
            String Dados = "";
            int Index = input.indexOf("infVeiculo");
            while (TrataObjeto) {
                Dados += input.substring(Index, Index + 1);
                if (Dados.contains("}")) {
                    TrataObjeto = false;
                } else {
                    Index++;
                }
            }
            input = input.replace(Dados, Dados.replace("{", "[{").replace("}", "}]"));

            // Tratamento para converter objeto "infEspecie" em Lista
            TrataObjeto = true;
            Dados = "";
            Index = input.indexOf("infEspecie");
            while (TrataObjeto) {
                Dados += input.substring(Index, Index + 1);
                if (Dados.contains("}")) {
                    TrataObjeto = false;
                } else {
                    Index++;
                }
            }
            input = input.replace(Dados, Dados.replace("{", "[{").replace("}", "}]"));

            tGTVe = gson.fromJson(input, TGTVe.class);                

            // Pegar ID
            TrataObjeto = true;
            Dados = "";
            Index = input.indexOf("@Id");

            while (TrataObjeto) {
                Dados += input.substring(Index, Index + 1);

                if (Dados.contains(",")) {
                    TrataObjeto = false;
                } else {
                    Index++;
                }
            }

            Dados = Dados.replace("@Id", "");
            Dados = Dados.replace(":", "");
            Dados = Dados.replace(" ", "");
            Dados = Dados.replace("\"", "");
            Dados = Dados.replace(",", "");
            Gson gsonDadosAdic = gsonBuilderDadosAdic.create();
            ProsegurTGTVeDados prosegurTGTVeDados = gsonDadosAdic.fromJson(input, ProsegurTGTVeDados.class);
            if (prosegurTGTVeDados.getDados_adic() == null) {
                prosegurTGTVeDados.setDados_adic(new ProsegurTGTVeDadosAdic());
            }

            tGTVe.getInfCte().setId(Dados);

            // Tratamento de Dados Convertidos
            try {
                if (tGTVe.getInfCte().getDest().getIE().equals("")) {
                    tGTVe.getInfCte().getDest().setIE(null);
                }
            } catch (Exception ex) {
            }

            try {
                if (tGTVe.getInfCte().getRem().getIE().equals("")) {
                    tGTVe.getInfCte().getRem().setIE(null);
                }
            } catch (Exception ex) {
            }

            try {
                if (tGTVe.getInfCte().getEmit().getIE().equals("")) {
                    tGTVe.getInfCte().getEmit().setIE(null);
                }
            } catch (Exception ex) {
            }

            if (null != tGTVe.getInfCte().getEmit()
                    && (null == tGTVe.getInfCte().getEmit().getXFant() || tGTVe.getInfCte().getEmit().getXFant().equals(""))
                    && null != tGTVe.getInfCte().getEmit().getXNome()
                    && !tGTVe.getInfCte().getEmit().getXNome().equals("")) {
                tGTVe.getInfCte().getEmit().setXFant(tGTVe.getInfCte().getEmit().getXNome());
            }

            if (null != tGTVe.getInfCte().getRem()
                    && (null == tGTVe.getInfCte().getRem().getXFant() || tGTVe.getInfCte().getRem().getXFant().equals(""))
                    && null != tGTVe.getInfCte().getRem().getXNome()
                    && !tGTVe.getInfCte().getRem().getXNome().equals("")) {
                tGTVe.getInfCte().getRem().setXFant(tGTVe.getInfCte().getRem().getXNome());
            }

            if (null != tGTVe.getInfCte().getIde().getTomaTerceiro()
                    && (null == tGTVe.getInfCte().getIde().getTomaTerceiro().getXFant() || tGTVe.getInfCte().getIde().getTomaTerceiro().getXFant().equals(""))
                    && null != tGTVe.getInfCte().getIde().getTomaTerceiro().getXNome()
                    && !tGTVe.getInfCte().getIde().getTomaTerceiro().getXNome().equals("")) {
                tGTVe.getInfCte().getIde().getTomaTerceiro().setXFant(tGTVe.getInfCte().getIde().getTomaTerceiro().getXNome());
            }

            if (tGTVe.getInfCte().getIde().getTpAmb().equals("2")) {
                this.homolog = true;
            } else {
                this.homolog = false;
            }

            // Validações
            if (null == tGTVe.getInfCte().getDest().getCNPJ() || tGTVe.getInfCte().getDest().getCNPJ().equals("")) {
                throw new Exception("CNPJ Dest Obrigatorio!");
            }
            if (null == tGTVe.getInfCte().getDest().getEnderDest() || tGTVe.getInfCte().getDest().getEnderDest().equals("")) {
                throw new Exception("Endereco Dest Obrigatorio!");
            }
            if (null == tGTVe.getInfCte().getDest().getXNome() || tGTVe.getInfCte().getDest().getXNome().equals("")) {
                throw new Exception("Nome Dest Obrigatorio!");
            }
            if ((null == tGTVe.getInfCte().getRem().getCNPJ() || tGTVe.getInfCte().getRem().getCNPJ().equals("")) && (null == tGTVe.getInfCte().getRem().getCPF() || tGTVe.getInfCte().getRem().getCPF().equals(""))) {
                //throw new Exception("CNPJ Rem Obrigatorio!");
                tGTVe.getInfCte().getRem().setCPF(tGTVe.getInfCte().getRem().getCPF());
            }
            if (null == tGTVe.getInfCte().getRem().getEnderReme() || tGTVe.getInfCte().getRem().getEnderReme().equals("")) {
                throw new Exception("Endereco Rem Obrigatorio!");
            }
            if (null == tGTVe.getInfCte().getRem().getXNome() || tGTVe.getInfCte().getRem().getXNome().equals("")) {
                throw new Exception("Nome Rem Obrigatorio!");
            }
            if (null == tGTVe.getInfCte().getEmit().getCNPJ() || tGTVe.getInfCte().getEmit().getCNPJ().equals("")) {
                throw new Exception("CNPJ Emit Obrigatorio!");
            }
            if (null == tGTVe.getInfCte().getEmit().getEnderEmit() || tGTVe.getInfCte().getEmit().getEnderEmit().equals("")) {
                throw new Exception("Endereco Emit Obrigatorio!");
            }
            if (null == tGTVe.getInfCte().getEmit().getXNome() || tGTVe.getInfCte().getEmit().getXNome().equals("")) {
                throw new Exception("Nome Emit Obrigatorio!");
            }
            if (null == tGTVe.getInfCte().getEmit().getXFant() || tGTVe.getInfCte().getEmit().getXFant().equals("")) {
                throw new Exception("Nome Fantasia Emit Obrigatorio!");
            }

            String cuf = tGTVe.getInfCte().getIde().getCUF();

            String AA = tGTVe.getInfCte().getIde().getDhEmi().replace("-", "").substring(2, 4);
            String MM = tGTVe.getInfCte().getIde().getDhEmi().replace("-", "").substring(4, 6);
            String AAMM = AA + MM;
            String cnpj = tGTVe.getInfCte().getEmit().getCNPJ();
            String mod = tGTVe.getInfCte().getIde().getMod();
            String s = FuncoesString.PreencheEsquerda(tGTVe.getInfCte().getIde().getSerie(), 3, "0");
            String nct = FuncoesString.PreencheEsquerda(tGTVe.getInfCte().getIde().getNCT(), 9, "0");
            String tpemi = tGTVe.getInfCte().getIde().getTpEmis();
            String cct = FuncoesString.PreencheEsquerda(tGTVe.getInfCte().getIde().getCCT(), 8, "0");
            String cdv = String.valueOf(Modulo11.modulo11(cuf + AAMM + cnpj + mod + s + nct + tpemi + cct));

            tGTVe.getInfCte().getIde().setCCT(cct);
            tGTVe.getInfCte().getIde().setCDV(cdv);

            tGTVe.getInfCte().setId("CTe" + cuf + AAMM + cnpj + mod + s + nct + tpemi + cct + cdv);

            if (this.homolog) {
                tGTVe.getInfCte().getRem().setXNome("CT-E EMITIDO EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL");
                tGTVe.getInfCte().getDest().setXNome("CT-E EMITIDO EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL");
            }
            
            TGTVe.InfCTeSupl infCTeSupl = new TGTVe.InfCTeSupl();
            tGTVe.setInfCTeSupl(infCTeSupl);
            if (cuf.equals("31")) {
                tGTVe.getInfCTeSupl().setQrCodCTe("https://cte.fazenda.mg.gov.br/portalcte/sistema/qrcode.xhtml?chCTe=" + tGTVe.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + tGTVe.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("51")) {
                tGTVe.getInfCTeSupl().setQrCodCTe("https://www.sefaz.mt.gov.br/cte/qrcode?chCTe=" + tGTVe.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + tGTVe.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("14")) {
                tGTVe.getInfCTeSupl().setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + tGTVe.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + tGTVe.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("16")) {
                tGTVe.getInfCTeSupl().setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + tGTVe.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + tGTVe.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("26")) {
                tGTVe.getInfCTeSupl().setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + tGTVe.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + tGTVe.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("35")) {
                tGTVe.getInfCTeSupl().setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + tGTVe.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + tGTVe.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("41")) {
                tGTVe.getInfCTeSupl().setQrCodCTe("http://www.fazenda.pr.gov.br/cte/qrcode?chCTe=" + tGTVe.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + tGTVe.getInfCte().getIde().getTpAmb());
            } else {
                tGTVe.getInfCTeSupl().setQrCodCTe("https://dfe-portal.svrs.rs.gov.br/cte/qrCode?chCTe=" + tGTVe.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + tGTVe.getInfCte().getIde().getTpAmb());
            }
            tGTVe.getInfCte().setVersao("4.00");
            tGTVe.setVersao("4.00");

            String xml = XML.toXml(tGTVe, Boolean.FALSE);
        xml = xml.replace("Â ", "");
        xml = xml.replace("º", "");
        xml = xml.replace("°", "");
        xml = xml.replace("º", "");
        xml = xml.replace("ª", "");
        xml = xml.replace("<verProc>000</verProc>", "<verProc>0</verProc>");
        xml = xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", "");
            
                XMLGtve xmlGTVE2 = new XMLGtve(tGTVe.getInfCte().getEmit().getCNPJ(),
                        tGTVe.getInfCte().getIde().getNCT(),
                        tGTVe.getInfCte().getIde().getSerie(),
                        tGTVe.getInfCte().getIde().getCMunEnv(),
                        getDataAtual("SQL"),
                        getDataAtual("HORA"),
                        xml,
                        "ERRO SEFAZ",
                        "",//ProcuraTag(doc, "infProt", "nProt", 0, false),
                        tGTVe.getInfCte().getId().toString().replace("Id=", "").replace("CTe", ""),//ProcuraTag(doc, "infProt", "chCTe", 0, false),
                        getDataAtual("SQL"),
                        getDataAtual("HORA"),
                        getDataAtual("SQL"),
                        getDataAtual("HORA"),
                        tGTVe.getInfCTeSupl().getQrCodCTe(),
                        prosegurTGTVeDados.getDados_adic().getFatidgtv(),
                        prosegurTGTVeDados.getDados_adic().getOpenidgtv(),
                        prosegurTGTVeDados.getDados_adic().getOpenidfilial());
            gravarXMLGtve(xmlGTVE2, this.persistencia);            
            
            
            // Assina XML
            xmlAssinado = AssinarXML(tGTVe, empresa);
            //    xmlAssinado = xmlAssinado.replace("&amp;", "&");

            // Envia SEFAZ
            String RetornoString = "";
            
            RetornoString = EnviarTGTVe(tGTVe, empresa, xmlAssinado);
            this.logerro.Grava("Retorno Envio:"+RetornoString, this.caminho);
            RetornoString = RetornoString.replace("RejeiÃ§Ã£o", "Rejeicao");
            RetornoString = RetornoString.replace("Rejeicao", "Rejeicao");
            RetornoString = RetornoString.replace("Ã§Ã£", "ca");
            RetornoString = RetornoString.replace("EndereÃ§o", "endereco");            
            

            InputSource is = new InputSource(new StringReader(RetornoString));
            DocumentBuilder dBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            Document doc = dBuilder.parse(is);

            if (RetornoString.contains("Rejeicao")) {
                retorno.put("status", "Erro");
                retorno.put("Rejeicao:", RetornoString);
             
                XMLGtve xmlGtve = xmlGtveDao.buscarXMLGtvePorNCT(tGTVe.getInfCte().getIde().getNCT(), this.persistencia);

                if (null != xmlGtve && null != xmlGtve.getProtocolo() && !xmlGtve.getProtocolo().equals("")) {
                    protocoloGuardado = xmlGtve.getProtocolo();
                }

                // Processou com ERRO
                XMLGtve xmlGTVE = new XMLGtve(tGTVe.getInfCte().getEmit().getCNPJ(),
                        tGTVe.getInfCte().getIde().getNCT(),
                        tGTVe.getInfCte().getIde().getSerie(),
                        tGTVe.getInfCte().getIde().getCMunEnv(),
                        getDataAtual("SQL"),
                        getDataAtual("HORA"),
                        xmlAssinado,
                        RetornoString,
                        ProcuraTag(doc, "infProt", "nProt", 0, false),
                        ProcuraTag(doc, "infProt", "chCTe", 0, false),
                        getDataAtual("SQL"),
                        getDataAtual("HORA"),
                        getDataAtual("SQL"),
                        getDataAtual("HORA"),
                        tGTVe.getInfCTeSupl().getQrCodCTe(),
                        prosegurTGTVeDados.getDados_adic().getFatidgtv(),
                        prosegurTGTVeDados.getDados_adic().getOpenidgtv(),
                        prosegurTGTVeDados.getDados_adic().getOpenidfilial());

                if (this.persistencia.getEmpresa().toUpperCase().equals("SATPROSEGUR")) {
                    xmlGTVE.setStatus("ERRO");
                    gravarXMLGtveProsegur(xmlGTVE, this.persistencia);
                } else {
                    gravarXMLGtve(xmlGTVE, this.persistencia);
                }

                throw new Exception(RetornoString);
                
            } else {
                // Processou com SUCESSO
                // Gravar log em xmlGTVE
                XMLGtve xmlGTVE = new XMLGtve(tGTVe.getInfCte().getEmit().getCNPJ(),
                        tGTVe.getInfCte().getIde().getNCT(),
                        tGTVe.getInfCte().getIde().getSerie(),
                        tGTVe.getInfCte().getIde().getCMunEnv(),
                        getDataAtual("SQL"),
                        getDataAtual("HORA"),
                        xmlAssinado,
                        RetornoString,
                        ProcuraTag(doc, "infProt", "nProt", 0, false),
                        ProcuraTag(doc, "infProt", "chCTe", 0, false),
                        getDataAtual("SQL"),
                        getDataAtual("HORA"),
                        getDataAtual("SQL"),
                        getDataAtual("HORA"),
                        tGTVe.getInfCTeSupl().getQrCodCTe(),
                        prosegurTGTVeDados.getDados_adic().getFatidgtv(),
                        prosegurTGTVeDados.getDados_adic().getOpenidgtv(),
                        prosegurTGTVeDados.getDados_adic().getOpenidfilial());

                if (this.persistencia.getEmpresa().toUpperCase().equals("SATPROSEGUR")) {
                    gravarXMLGtveProsegur(xmlGTVE, this.persistencia);
                } else {
                    gravarXMLGtve(xmlGTVE, this.persistencia);
                }

                String ProtocoloGerado = ProcuraTag(doc, "infProt", "nProt", 0, false);

                if (ProtocoloGerado.isEmpty()) {
                    ProtocoloGerado = "NAO GERADO";
                }

                retorno.put("status", "ok");
                retorno.put("nProt", ProcuraTag(doc, "infProt", "nProt", 0, false));
            }
        } catch (Exception e) {
            // Salvando em log o que foi mandado
            this.logerro.Grava("XML Erro:"+e.getMessage(), this.caminho);
            retorno.clear();
            retorno.put("status", "error");

            if (null != protocoloGuardado && !protocoloGuardado.equals("")) {
                retorno.put("nProt", protocoloGuardado);
            }

            retorno.put("error", e.getMessage());
            if (null != xmlAssinado && !xmlAssinado.equals("")) {
                retorno.put("xml_assinado", xmlAssinado);
            }
            this.logerro.Grava(e.getMessage(), this.caminho);
            isErro = true;
        } finally {
            if (!isErro) {
                // Processado sem Erro
                return Response
                        .status(Response.Status.OK)
                        .type("application/json")
                        .entity(gsonException.toJson(retorno))
                        .build();
            } else {
                // Processado com Erro
                return Response
                        .status(Response.Status.OK)
                        .type("application/json")
                        .entity(gsonException.toJson(retorno))
                        .build();
            }
        }
    }

    public Response testeComXml(String input, String empresa) {
        this.logerro.Grava("XML Recebido:" + input, this.caminho);

        GsonBuilder gsonBuilder = new GsonBuilder(), gsonBuilderException = new GsonBuilder(), gsonBuilderDadosAdic = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(TGTVe.class, new GTVeExceptionSerializer());
        gsonBuilderException.registerTypeAdapter(GTVeException.class, new GTVeExceptionSerializer());
        gsonBuilderDadosAdic.registerTypeAdapter(ProsegurTGTVeDados.class, new GTVeExceptionSerializer());

        Gson gsonException = gsonBuilderException.create();
        Map retorno = new HashMap<>();
        Boolean isErro = false;

        String AA = "2021-03-11T16:20:05-03:00".replace("-", "").substring(2, 4);
        String MM = "2021-03-11T16:20:05-03:00".replace("-", "").substring(4, 6);

        String xmlAssinado = "", protocoloGuardado = "";

        try {
            // Validando parâmetro de conexão
            this.logerro.Grava("Validando persistencia", this.caminho);
            this.persistencia = this.pool.getConexao(empresa);

            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }
            this.logerro.Grava("Persistencia criada", this.caminho);

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\" + empresa + "\\GTVe\\" + getDataAtual("SQL") + "\\log.txt";

            String[] DadosCertificado = getNomeSenhaCertificado(empresa).split("-");
            String NomeCertificado = DadosCertificado[0], SenhaCertificado = DadosCertificado[1];

            this.logerro.Grava("Assinar XML", this.caminho);
            String xml = input;
            xml = xml.replace("Â ", "");
            xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                    CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                            + NomeCertificado,
                            SenhaCertificado));

            // Envia SEFAZ
            String RetornoString = "", _Exception = "";
            //String cdv = String.valueOf(Modulo11.modulo11("42211117428731007571640011000497041000497041"));
            try {
                RetornoString = EnviarTGTVe(new TGTVe(), empresa, xmlAssinado);
            } catch (Exception e) {
                _Exception = e.getMessage();
            }
            this.logerro.Grava("Assinar XML", this.caminho);
            RetornoString = RetornoString.replace("RejeiÃ§Ã£o", "Rejeicao");
            RetornoString = RetornoString.replace("Rejeicao", "Rejeicao");
            RetornoString = RetornoString.replace("Ã§Ã£", "ca");
            RetornoString = RetornoString.replace("EndereÃ§o", "endereco");                        

            InputSource is = new InputSource(new StringReader(RetornoString));
            DocumentBuilder dBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            Document doc = dBuilder.parse(is);

            String ProtocoloGerado = ProcuraTag(doc, "infProt", "nProt", 0, false);

            if (ProtocoloGerado.isEmpty()) {
                ProtocoloGerado = "NAO GERADO";
            }

            retorno.put("status", "ok");
            retorno.put("nProt", ProcuraTag(doc, "infProt", "nProt", 0, false));
        } catch (Exception e) {
            // Salvando em log o que foi mandado
            retorno.clear();
            retorno.put("status", "error");

            if (null != protocoloGuardado && !protocoloGuardado.equals("")) {
                retorno.put("nProt", protocoloGuardado);
            }

            retorno.put("error", e.getMessage());
            if (null != xmlAssinado && !xmlAssinado.equals("")) {
                retorno.put("xml_assinado", xmlAssinado);
            }
            this.logerro.Grava(e.getMessage(), this.caminho);
            isErro = true;
        } finally {
            if (!isErro) {
                // Processado sem Erro
                return Response
                        .status(Response.Status.OK)
                        .type("application/json")
                        .entity(gsonException.toJson(retorno))
                        .build();
            } else {
                // Processado com Erro
                return Response
                        .status(Response.Status.OK)
                        .type("application/json")
                        .entity(gsonException.toJson(retorno))
                        .build();
            }
        }
    }

    public Response jsonData(String input, String empresa) {
        StringMap dataJson = new Gson().fromJson(input, StringMap.class);
        Object datasObject = dataJson.get(0);
        Map retorno = new HashMap<>(), processamento, processamentoTrajeto;
        TGTVe retornoGTVe = new TGTVe();
        TGTVe.InfCte.DetGTV retornoDet = new TGTVe.InfCte.DetGTV();

        StringMap dataObject = dataJson;

        String fusoHorario = dataObject.get("fusoHorarioSEFAZ").toString();
        TGTVe.InfCte infCte = new TGTVe.InfCte();
        TGTVe.InfCte.Ide ide = new TGTVe.InfCte.Ide();
        String UFGrava = dataObject.get("ideCUF").toString();
        ide.setCUF(UFUtils.codigo(dataObject.get("ideCUF").toString()));
        ide.setCFOP(dataObject.get("ideCFOP").toString().equals("") ? null : dataObject.get("ideCFOP").toString().trim());
        ide.setNatOp(dataObject.get("idenatOp").toString().equals("") ? null : dataObject.get("idenatOp").toString().trim());
        ide.setMod(dataObject.get("idemod").toString().equals("") ? null : dataObject.get("idemod").toString().trim());
        ide.setSerie(dataObject.get("ideserie").toString().equals("") ? null : dataObject.get("ideserie").toString().trim());
        ide.setNCT(dataObject.get("idenCT").toString().equals("") ? null : dataObject.get("idenCT").toString().trim());
        ide.setCCT(FuncoesString.PreencheEsquerda(FuncoesString.RecortaString(dataObject.get("idenCT").toString(), 0, 8), 8, "0"));
        ide.setDhEmi(dataObject.get("idedhEmi").toString() + fusoHorario);
        ide.setTpImp(dataObject.get("idetpImp").toString().equals("") ? null : dataObject.get("idetpImp").toString().trim());
        ide.setTpEmis(dataObject.get("idetpEmis").toString().equals("") ? null : dataObject.get("idetpEmis").toString().trim());
        ide.setCDV(dataObject.get("idecDV").toString().equals("") ? null : dataObject.get("idecDV").toString().trim());
        ide.setTpAmb(dataObject.get("idetpAmb").toString().equals("") ? null : dataObject.get("idetpAmb").toString().trim());
        ide.setTpCTe(dataObject.get("idetpCTe").toString().equals("") ? null : dataObject.get("idetpCTe").toString().trim());
        ide.setVerProc(dataObject.get("ideverProc").toString().equals("") ? null : dataObject.get("ideverProc").toString().trim());
        ide.setCMunEnv(dataObject.get("idecMunEnv").toString().equals("") ? null : dataObject.get("idecMunEnv").toString().trim());
        ide.setXMunEnv(dataObject.get("idexMunEnv").toString().equals("") ? null : dataObject.get("idexMunEnv").toString().trim());
        ide.setUFEnv(TUf.fromValue(dataObject.get("ideUFEnv").toString()));
        ide.setModal(dataObject.get("idemodal").toString().equals("") ? null : dataObject.get("idemodal").toString().trim());
        ide.setTpServ(dataObject.get("idetpServ").toString().equals("") ? null : dataObject.get("idetpServ").toString().trim());
        ide.setIndIEToma(dataObject.get("ideindIEToma").toString().equals("") ? null : dataObject.get("ideindIEToma").toString().trim());
        ide.setDhSaidaOrig(dataObject.get("idedhSaidaOrig").toString() + fusoHorario);
        ide.setDhChegadaDest(dataObject.get("idedhChegadaDest").toString() + fusoHorario);
        ide.setDhCont(dataObject.get("idedhCont").toString() + fusoHorario);
        ide.setXJust(dataObject.get("idexJust").toString().equals("") ? null : dataObject.get("idexJust").toString().trim());

        TGTVe.InfCte.Ide.Toma toma = new TGTVe.InfCte.Ide.Toma();
        toma.setToma(dataObject.get("tomatoma").toString().equals("") ? null : dataObject.get("tomatoma").toString().trim());
        ide.setToma(toma);

        TGTVe.InfCte.Ide.TomaTerceiro tomaTerceiro = new TGTVe.InfCte.Ide.TomaTerceiro();
        tomaTerceiro.setToma(dataObject.get("tomaTerceirotoma").toString().equals("") ? null : dataObject.get("tomaTerceirotoma").toString().trim());
        tomaTerceiro.setCNPJ(dataObject.get("tomaTerceiroCNPJ").toString().equals("") ? null : dataObject.get("tomaTerceiroCNPJ").toString().trim());
        tomaTerceiro.setCPF(dataObject.get("tomaTerceiroCPF").toString().equals("") ? null : dataObject.get("tomaTerceiroCPF").toString().trim());
        tomaTerceiro.setIE(dataObject.get("tomaTerceiroIE").toString().equals("") ? null : dataObject.get("tomaTerceiroIE").toString().trim());
        tomaTerceiro.setXNome(dataObject.get("tomaTerceiroxNome").toString().equals("") ? null : dataObject.get("tomaTerceiroxNome").toString().trim());
        tomaTerceiro.setXFant(dataObject.get("tomaTerceiroxFant").toString().equals("") ? null : dataObject.get("tomaTerceiroxFant").toString().trim());
        tomaTerceiro.setFone(dataObject.get("tomaTerceirofone").toString().equals("") ? null : dataObject.get("tomaTerceirofone").toString().trim());

        TEndereco tomaTerceiroenderToma = new TEndereco();
        tomaTerceiroenderToma.setXLgr(FuncoesString.removeAcento(dataObject.get("tomaTerceiroenderTomaxLgr").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomaxLgr").toString().trim()));
        if (null != tomaTerceiroenderToma.getXLgr()
                && tomaTerceiroenderToma.getXLgr().contains(",")) {
            try {
                tomaTerceiroenderToma.setNro(tomaTerceiroenderToma.getXLgr().substring(tomaTerceiroenderToma.getXLgr().lastIndexOf(",")));
                tomaTerceiroenderToma.setXLgr(FuncoesString.removeAcento(tomaTerceiroenderToma.getXLgr().substring(0, tomaTerceiroenderToma.getXLgr().lastIndexOf(","))));
                Integer.parseInt(tomaTerceiroenderToma.getNro());
            } catch (Exception en) {
                tomaTerceiroenderToma.setNro("0");
            }
        } else {
            tomaTerceiroenderToma.setNro("0");
        }
//                tomaTerceiroenderToma.setXCpl(consulta.getString("tomaTerceiroenderTomaxCpl").equals("") ? null : consulta.getString("tomaTerceiroenderTomaxCpl").trim());
        tomaTerceiroenderToma.setXBairro(FuncoesString.removeAcento(dataObject.get("tomaTerceiroenderTomaxBairro").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomaxBairro").toString().trim()));
        tomaTerceiroenderToma.setCMun(dataObject.get("tomaTerceiroenderTomacMun").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomacMun").toString().trim());
        tomaTerceiroenderToma.setXMun(dataObject.get("tomaTerceiroenderTomaxMun").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomaxMun").toString().trim());
        tomaTerceiroenderToma.setCEP(dataObject.get("tomaTerceiroenderTomaCEP").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomaCEP").toString().trim());
        tomaTerceiroenderToma.setUF(dataObject.get("tomaTerceiroenderTomaUF").toString().equals("") ? null : TUf.fromValue(dataObject.get("tomaTerceiroenderTomaUF").toString()));
        tomaTerceiroenderToma.setCPais(dataObject.get("tomaTerceiroenderTomacPais").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomacPais").toString().trim());
        tomaTerceiroenderToma.setXPais(dataObject.get("tomaTerceiroenderTomaxPais").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomaxPais").toString().trim());
        tomaTerceiro.setEnderToma(tomaTerceiroenderToma);

        tomaTerceiro.setEmail(dataObject.get("tomaTerceiroemail").toString().equals("") ? null : dataObject.get("tomaTerceiroemail").toString().trim());
//                ide.setTomaTerceiro(tomaTerceiro);        
        infCte.setIde(ide);

        TGTVe.InfCte.Compl compl = new TGTVe.InfCte.Compl();
        compl.setXObs(dataObject.get("complxObs").toString().equals("") ? null : dataObject.get("complxObs").toString().trim());
        infCte.setCompl(compl);

        TGTVe.InfCte.Emit emit = new TGTVe.InfCte.Emit();
        emit.setCNPJ(dataObject.get("emitCNPJ").toString().equals("") ? null : dataObject.get("emitCNPJ").toString().trim());
        emit.setIE(dataObject.get("emitIE").toString().equals("") ? null : dataObject.get("emitIE").toString().trim());
        emit.setIEST(dataObject.get("emitIEST").toString().equals("") ? null : dataObject.get("emitIEST").toString().trim());
        emit.setXNome(dataObject.get("emitxNome").toString().equals("") ? null : dataObject.get("emitxNome").toString().trim());
        emit.setXFant(dataObject.get("emitxFant").toString().equals("") ? null : dataObject.get("emitxFant").toString().trim());

        TEndeEmi emitenderEmit = new TEndeEmi();
        emitenderEmit.setXLgr(dataObject.get("emitenderEmitxLgr").toString().trim());
        if (null != emitenderEmit.getXLgr()
                && emitenderEmit.getXLgr().contains(",")) {
            try {
                emitenderEmit.setNro(tomaTerceiroenderToma.getXLgr().substring(tomaTerceiroenderToma.getXLgr().lastIndexOf(",")));
                emitenderEmit.setXLgr(FuncoesString.removeAcento(tomaTerceiroenderToma.getXLgr().substring(0, tomaTerceiroenderToma.getXLgr().lastIndexOf(","))));
                Integer.parseInt(emitenderEmit.getNro());
            } catch (Exception en) {
                emitenderEmit.setNro("0");
            }
        } else {
            emitenderEmit.setNro("0");
        }
//                emitenderEmit.setXCpl(consulta.getString("emitenderEmitxCpl").equals("") ? null : consulta.getString("emitenderEmitxCpl").trim());
        emitenderEmit.setXBairro(FuncoesString.removeAcento(dataObject.get("emitenderEmitxBairro").toString().equals("") ? null : dataObject.get("emitenderEmitxBairro").toString().trim()));
        emitenderEmit.setCMun(dataObject.get("emitenderEmitcMun").toString().equals("") ? null : dataObject.get("emitenderEmitcMun").toString().trim());
        emitenderEmit.setXMun(dataObject.get("emitenderEmitxMun").toString().equals("") ? null : dataObject.get("emitenderEmitxMun").toString().trim());
        emitenderEmit.setCEP(dataObject.get("emitenderEmitCEP").toString().equals("") ? null : dataObject.get("emitenderEmitCEP").toString().trim());
        emitenderEmit.setUF(dataObject.get("emitenderEmitUF").toString().equals("") ? null : TUFSemEX.fromValue(dataObject.get("emitenderEmitUF").toString()));
        emitenderEmit.setFone(dataObject.get("emitenderEmitfone").toString().equals("") ? null : dataObject.get("emitenderEmitfone").toString().trim());
        emit.setEnderEmit(emitenderEmit);
        infCte.setEmit(emit);

        TGTVe.InfCte.Rem rem = new TGTVe.InfCte.Rem();
        rem.setCNPJ(dataObject.get("remCNPJ").toString().equals("") ? null : dataObject.get("remCNPJ").toString().trim());
        rem.setCPF(dataObject.get("remCPF").toString().equals("") ? null : dataObject.get("remCPF").toString().trim());
        rem.setIE(dataObject.get("remIE").toString().equals("") ? null : dataObject.get("remIE").toString().trim());
        rem.setXNome(dataObject.get("remxNome").toString().equals("") ? null : dataObject.get("remxNome").toString().trim());
        rem.setXFant(dataObject.get("remxFant").toString().equals("") ? null : dataObject.get("remxFant").toString().trim());
        rem.setFone(dataObject.get("remfone").toString().equals("") ? null : dataObject.get("remfone").toString().trim());

        TEndereco remenderReme = new TEndereco();
        remenderReme.setXLgr(dataObject.get("remenderRemexLgr").toString().equals("") ? null : dataObject.get("remenderRemexLgr").toString().trim());
        if (null != remenderReme.getXLgr()
                && remenderReme.getXLgr().contains(",")) {
            try {
                remenderReme.setNro(remenderReme.getXLgr().substring(remenderReme.getXLgr().lastIndexOf(",")));
                remenderReme.setXLgr(FuncoesString.removeAcento(remenderReme.getXLgr().substring(0, remenderReme.getXLgr().lastIndexOf(","))));
                Integer.parseInt(remenderReme.getNro());
            } catch (Exception en) {
                remenderReme.setNro("0");
            }
        } else {
            remenderReme.setNro("0");
        }
//                remenderReme.setXCpl(consulta.getString("remenderRemexCpl").equals("") ? null : consulta.getString("remenderRemexCpl").trim());
        remenderReme.setXBairro(FuncoesString.removeAcento(dataObject.get("remenderRemexBairro").toString().equals("") ? null : dataObject.get("remenderRemexBairro").toString().trim()));
        remenderReme.setCMun(dataObject.get("remenderRemecMun").toString().equals("") ? null : dataObject.get("remenderRemecMun").toString().trim());
        remenderReme.setXMun(dataObject.get("remenderRemexMun").toString().equals("") ? null : dataObject.get("remenderRemexMun").toString().trim());
        remenderReme.setCEP(dataObject.get("remenderRemeCEP").toString().equals("") ? null : dataObject.get("remenderRemeCEP").toString().trim());
        remenderReme.setUF(dataObject.get("remenderRemeUF").toString().equals("") ? null : TUf.fromValue(dataObject.get("remenderRemeUF").toString()));
        remenderReme.setCPais(dataObject.get("remenderRemecPais").toString().equals("") ? null : dataObject.get("remenderRemecPais").toString().trim());
        remenderReme.setXPais(dataObject.get("remenderRemexPais").toString().equals("") ? null : dataObject.get("remenderRemexPais").toString().trim());
        rem.setEnderReme(remenderReme);
        rem.setEmail(dataObject.get("rememail").toString().equals("") ? null : dataObject.get("rememail").toString().trim());
        infCte.setRem(rem);

        TGTVe.InfCte.Dest dest = new TGTVe.InfCte.Dest();
        dest.setCNPJ(dataObject.get("destCNPJ").toString().equals("") ? null : dataObject.get("destCNPJ").toString().trim());
        dest.setCPF(dataObject.get("destCPF").toString().equals("") ? null : dataObject.get("destCPF").toString().trim());
        dest.setIE(dataObject.get("destIE").toString().equals("") ? null : dataObject.get("destIE").toString().trim());
        dest.setXNome(dataObject.get("destxNome").toString().equals("") ? null : dataObject.get("destxNome").toString().trim());
        dest.setFone(dataObject.get("destfone").toString().equals("") ? null : dataObject.get("destfone").toString().trim());
        dest.setISUF(dataObject.get("destISUF").toString().equals("") ? null : dataObject.get("destISUF").toString().trim());

        TEndereco destenderDest = new TEndereco();
        destenderDest.setXLgr(dataObject.get("destenderDestxLgr").toString().equals("") ? null : dataObject.get("destenderDestxLgr").toString().trim());
        if (null != destenderDest.getXLgr()
                && destenderDest.getXLgr().contains(",")) {
            try {
                destenderDest.setNro(destenderDest.getXLgr().substring(destenderDest.getXLgr().lastIndexOf(",")));
                destenderDest.setXLgr(FuncoesString.removeAcento(destenderDest.getXLgr().substring(0, destenderDest.getXLgr().lastIndexOf(","))));
                Integer.parseInt(destenderDest.getNro());
            } catch (Exception en) {
                destenderDest.setNro("0");
            }
        } else {
            destenderDest.setNro("0");
        }
//                destenderDest.setXCpl(consulta.getString("destenderDestxCpl").equals("") ? null : consulta.getString("destenderDestxCpl").trim());
        destenderDest.setXBairro(FuncoesString.removeAcento(dataObject.get("destenderDestxBairro").toString().equals("") ? null : dataObject.get("destenderDestxBairro").toString().trim()));
        destenderDest.setCMun(dataObject.get("destenderDestcMun").toString().equals("") ? null : dataObject.get("destenderDestcMun").toString().trim());
        destenderDest.setXMun(dataObject.get("destenderDestxMun").toString().equals("") ? null : dataObject.get("destenderDestxMun").toString().trim());
        destenderDest.setCEP(dataObject.get("destenderDestCEP").toString().equals("") ? null : dataObject.get("destenderDestCEP").toString().trim());
        destenderDest.setUF(dataObject.get("destenderDestUF").toString().equals("") ? null : TUf.fromValue(dataObject.get("destenderDestUF").toString()));
        destenderDest.setCPais(dataObject.get("destenderDestcPais").toString().equals("") ? null : dataObject.get("destenderDestcPais").toString().trim());
        destenderDest.setXPais(dataObject.get("destenderDestxPais").toString().equals("") ? null : dataObject.get("destenderDestxPais").toString().trim());
        dest.setEnderDest(destenderDest);
        dest.setEmail(dataObject.get("destemail").toString().equals("") ? null : dataObject.get("destemail").toString().trim());
        infCte.setDest(dest);

        infCte.setId(dataObject.get("ideIdAnoMes").toString());

        retornoGTVe.setInfCte(infCte);

        // Lista detGTVqCarga
        TGTVe.InfCte.DetGTV.InfEspecie infEspecie;
        Integer detGTVqCarga = 0;
        List<StringMap> dataDetList = (ArrayList) dataObject.get("detListGTVqCarga");

        for (StringMap dataObjectDet : dataDetList) {
            infEspecie = new TGTVe.InfCte.DetGTV.InfEspecie();
            infEspecie.setTpEspecie(dataObjectDet.get("detGTVinfEspecietpEspecie").toString());
            BigDecimal detGTVinfEspecievEspecie = BigDecimal.valueOf(Double.parseDouble(dataObjectDet.get("detGTVinfEspecievEspecie").toString())).setScale(2, RoundingMode.CEILING);

            infEspecie.setVEspecie(detGTVinfEspecievEspecie.toString());
            infEspecie.setTpNumerario(dataObjectDet.get("detGTVinfEspecietpNumerario").toString());
            if (infEspecie.getTpNumerario().equals("2")) {
                infEspecie.setXMoedaEstr(dataObjectDet.get("detGTVinfEspeciexMoedaEstr").toString());
            }

            retornoDet.getInfEspecie().add(infEspecie);

            detGTVqCarga += dataObjectDet.get("detGTVqCarga").toString().equals("") ? 1 : Integer.parseInt(dataObjectDet.get("detGTVqCarga").toString());
        }

        retornoDet.setQCarga(String.valueOf(detGTVqCarga));

        // Lista InfVeiculo
        List<StringMap> dataInfoList = (ArrayList) dataObject.get("InfListVeiculo");
        TGTVe.InfCte.DetGTV.InfVeiculo infVeiculo;

        for (StringMap dataObjectInfo : dataInfoList) {
            infVeiculo = new TGTVe.InfCte.DetGTV.InfVeiculo();
            infVeiculo.setPlaca(dataObjectInfo.get("detGTVinfVeiculoplaca").toString());
            infVeiculo.setUF(dataObjectInfo.get("detGTVinfVeiculoUF").toString().equals("") ? null : TUf.fromValue(dataObjectInfo.get("detGTVinfVeiculoUF").toString()));
            retornoDet.getInfVeiculo().add(infVeiculo);
        }

        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(GTVeException.class, new GTVeExceptionSerializer());
        Gson gson = gsonBuilder.create();
        List processamentos = new ArrayList<>(), trajetosProcessado;

        try {

            // Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao(empresa);
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\" + empresa + "\\GTVe\\"
                    + getDataAtual("SQL") + "\\log.txt";

            this.logerro.Grava("jSonData:\t" + input, this.caminho);

            String sequencia, numero;
            CxForte cxForte;
            Pedido pedido;
            Rotas rota;
            Rt_Perc trajeto;

            TGTVe gtv = retornoGTVe;
            if (gtv == null) {
                throw new GTVeException(new GTVeErrorCode(0));
            }

            String cuf = gtv.getInfCte().getIde().getCUF();
            String AAMM = gtv.getInfCte().getId();
            String cnpj = gtv.getInfCte().getEmit().getCNPJ();
            String mod = gtv.getInfCte().getIde().getMod();
            String s = FuncoesString.PreencheEsquerda(gtv.getInfCte().getIde().getSerie(), 3, "0");
            String nct = FuncoesString.PreencheEsquerda(gtv.getInfCte().getIde().getNCT(), 9, "0");
            String tpemi = gtv.getInfCte().getIde().getTpEmis();
            String cct = FuncoesString.PreencheEsquerda(gtv.getInfCte().getIde().getCCT(), 8, "0");
            String cdv = String.valueOf(Modulo11.modulo11(cuf + AAMM + cnpj + mod + s + nct + tpemi + cct));

            gtv.getInfCte().getIde().setCCT(cct);
            gtv.getInfCte().getIde().setCDV(cdv);

            gtv.getInfCte().setVersao("4.00");

            gtv.getInfCte().setId("CTe" + cuf + AAMM + cnpj + mod + s + nct + tpemi + cct + cdv);

            //        gtv.getInfCte().getAutXML().addAll(gtvDao.getAutXML("882021146", persistencia));
            gtv.getInfCte().setDetGTV(retornoDet);

            gtv.setVersao("4.00");

            if (homolog) {
                gtv.getInfCte().getRem().setXNome("CT-E EMITIDO EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL");
                gtv.getInfCte().getDest().setXNome("CT-E EMITIDO EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL");
            }

            gtv.getInfCte().getIde().setDhCont(null);
            gtv.getInfCte().getIde().setXJust(null);

            this.logerro.Grava("Monta Link", this.caminho);

            TGTVe.InfCTeSupl infCTeSupl = new TGTVe.InfCTeSupl();
            if (cuf.equals("31")) {
                infCTeSupl.setQrCodCTe("https://cte.fazenda.mg.gov.br/portalcte/sistema/qrcode.xhtml?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("51") ) {
                infCTeSupl.setQrCodCTe("https://www.sefaz.mt.gov.br/cte/qrcode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("14")) {
                infCTeSupl.setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("16")) {
                infCTeSupl.setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("26")) {
                infCTeSupl.setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("35")) {
                infCTeSupl.setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("41")) {
                infCTeSupl.setQrCodCTe("http://www.fazenda.pr.gov.br/cte/qrcode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else {
                infCTeSupl.setQrCodCTe("https://dfe-portal.svrs.rs.gov.br/cte/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            }
//            infCTeSupl.setQrCodCTe("https://dfe-portal.svrs.rs.gov.br/cte/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            gtv.setInfCTeSupl(infCTeSupl);

            this.logerro.Grava("XML Envio: " + XML.toXml(gtv, Boolean.FALSE), this.caminho);
            String xml = XML.toXml(gtv, Boolean.FALSE);
            String xmlAssinado = "";
            if (empresa.contains("BRASIFORT")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + Certificados.BRASIFORT_NATAL.getNome(),
                                Certificados.BRASIFORT_NATAL.getSenha()));
            } else if (empresa.contains("FIDELYS")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + CertificadosFid.FIDELYS_BH.getNome(),
                                CertificadosFid.FIDELYS_BH.getSenha()));
            } else if (empresa.contains("PRESERVEPB")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + CertificadosPreservePB.PRESERVE_PB.getNome(),
                                CertificadosPreservePB.PRESERVE_PB.getSenha()));                
            } else if (empresa.contains("PRESERVE")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + CertificadosPreserve.PRESERVE_PE.getNome(),
                                CertificadosPreserve.PRESERVE_PE.getSenha()));
            } else if (empresa.contains("INVIOSEG")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + CertificadosInvioseg.INVIOSEG_SINOP.getNome(),
                                CertificadosInvioseg.INVIOSEG_SINOP.getSenha()));
            } else if (empresa.contains("CORPVS")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + CertificadosCorpvs.CORPVS_FOR.getNome(),
                                CertificadosCorpvs.CORPVS_FOR.getSenha()));
            } else if (empresa.contains("DELTACORP")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + CertificadosDeltaCorp.DELTACORP.getNome(),
                                CertificadosDeltaCorp.DELTACORP.getSenha()));
            } else if (empresa.contains("FORCAALERTA")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + CertificadosForcaAlerta.FORCAALERTA.getNome(),
                                CertificadosForcaAlerta.FORCAALERTA.getSenha()));
            }

            this.logerro.Grava("XML Assinado: " + xmlAssinado, this.caminho);

            try {
                //Get JAXBContext
                JAXBContext jaxbContext = JAXBContext.newInstance(TGTVe.class);

                //Create Unmarshaller
                Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();

                //Setup schema validator
                SchemaFactory sf = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);

                //Schema employeeSchema = sf.newSchema(new File(ImportacaoGTVe.class.getResource("/gtve/GTVe_v3.00.xsd").toString().replace("file:\\", "")
                Schema employeeSchema = sf.newSchema(new File(ImportacaoGTVe.class.getResource("/gtve/GTVe_v4.00.xsd").toString().replace("file:\\", "")
                        .replace("file:/", "")
                        .replace("%20", " ")));
                jaxbUnmarshaller.setSchema(employeeSchema);

                //Unmarshal xml file 
//                TGTVe validacao = (TGTVe) jaxbUnmarshaller.unmarshal(new ByteArrayInputStream(xmlAssinado.getBytes()));
//
//                System.out.println(validacao);
            } catch (JAXBException | SAXException e) {
                this.logerro.Grava("Error Unmarshal xml file : " + e.getMessage(), this.caminho);
                e.printStackTrace();
            }

            ByteArrayOutputStream obj = new ByteArrayOutputStream();
            Base64OutputStream b64os = new Base64OutputStream(obj);
            GZIPOutputStream gzipOS = new GZIPOutputStream(b64os);
            gzipOS.write(xmlAssinado.getBytes("UTF-8"));
            gzipOS.close();

            String xmlEnvio = new String(obj.toByteArray());
            this.logerro.Grava("CML Envio Byte : " + xmlEnvio, this.caminho);
            obj.close();
            b64os.close();

            this.logerro.Grava("Procurar Certificado", this.caminho);
//            InputStream ce = new FileInputStream(File.separator + "Certificados" + File.separator + "cert");
            InputStream ce = null;
            if (empresa.contains("BRASIFORT")) {
                ce = new FileInputStream(File.separator + "Certificados" + File.separator + "BRASIFORTNA");
            } else if (empresa.contains("FIDELYS")) {
                ce = new FileInputStream(File.separator + "Certificados" + File.separator + "FIDELYS");
            } else if (empresa.contains("PRESERVEPB")) {
                ce = new FileInputStream(File.separator + "Certificados" + File.separator + "PRESERVEPB");
            } else if (empresa.contains("PRESERVE")) {
                ce = new FileInputStream(File.separator + "Certificados" + File.separator + "PRESERVE");
            } else if (empresa.contains("INVIOSEG")) {
                ce = new FileInputStream(File.separator + "Certificados" + File.separator + "INVIOSEG");
            } else if (empresa.contains("CORPVS")) {
                ce = new FileInputStream(File.separator + "Certificados" + File.separator + "CORPVS");
            } else if (empresa.contains("FEDERAL")) {
                ce = new FileInputStream(File.separator + "Certificados" + File.separator + "FEDERAL");
            }

            this.logerro.Grava("Encontrou Certificado", this.caminho);

            KeyStore ks = KeyStore.getInstance("PKCS12");
            this.logerro.Grava("Set KS Key Store", this.caminho);
            //ks.load(ce, "123456789".toCharArray());
            ks.load(ce, "12345678".toCharArray());
            this.logerro.Grava("Set KS Senha", this.caminho);

            String alias = "";
            Enumeration<String> aliasesEnum = ks.aliases();
            while (aliasesEnum.hasMoreElements()) {
                alias = (String) aliasesEnum.nextElement();
                if (ks.isKeyEntry(alias)) {
                    break;
                }
            }

            this.logerro.Grava("Encontrou Alias", this.caminho);

            X509Certificate certificate = (X509Certificate) ks.getCertificate(alias);
            //PrivateKey privateKey = (PrivateKey) ks.getKey(alias, "123456789".toCharArray());
            this.logerro.Grava("SET Private Key", this.caminho);
            PrivateKey privateKey = (PrivateKey) ks.getKey(alias, "12345678".toCharArray());
            this.logerro.Grava("SET Socket", this.caminho);
            SocketFactoryDinamico socketFactoryDinamico = new SocketFactoryDinamico(certificate, privateKey);
//            URL cacert = getClass().getResource("eSocialCacerts");
//            socketFactoryDinamico.setFileCacerts(cacert.getPath().replace("%20", " "));
            this.logerro.Grava("SET cacert", this.caminho);
            socketFactoryDinamico.setFileCacerts(File.separator + "Certificados" + File.separator + "cacert.jks");
            this.logerro.Grava("SET Protocol", this.caminho);
            Protocol protocol = new Protocol("https", socketFactoryDinamico, 443);
            Protocol.registerProtocol("https", protocol);

//            assinarConexao();
            this.logerro.Grava("SET CteDadosMsg", this.caminho);
            CTeRecepcaoGTVeStub.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStub.CteDadosMsg();
            cteDadosMsg.setCteDadosMsg(xmlEnvio);

            this.logerro.Grava("SET CTeRecepcaoGTVeResult", this.caminho);
            CTeRecepcaoGTVeStub stub = new CTeRecepcaoGTVeStub();
            this.logerro.Grava("Envio stub.cTeRecepcaoGTVe(cteDadosMsg);", this.caminho);
            CTeRecepcaoGTVeStub.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);
            this.logerro.Grava("Retorno stub.cTeRecepcaoGTVe(cteDadosMsg);", this.caminho);
            String RetornoString = "x";

            try {
                JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                Unmarshaller um = context.createUnmarshaller();
                TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
            } catch (JAXBException e) {
                this.logerro.Grava("Error JAXBContext: " + e.getMessage(), this.caminho);
                e.printStackTrace();
            }
            System.out.println();

//            ServicoEnviarLoteEventosStub.LoteEventos_type0 dadosMsgType0 = new ServicoEnviarLoteEventosStub.LoteEventos_type0();
//            System.setProperty("com.sun.xml.ws.transport.http.client.HttpTransportPipe.dump", "true");
//            System.setProperty("com.sun.xml.internal.ws.transport.http.client.HttpTransportPipe.dump", "true");
//            System.setProperty("com.sun.xml.ws.transport.http.HttpAdapter.dump", "true");
//            System.setProperty("com.sun.xml.internal.ws.transport.http.HttpAdapter.dump", "true");
//            System.setProperty("com.sun.xml.internal.ws.transport.http.HttpAdapter.dumpTreshold", "999999");
//            br.inf.portalfiscal.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVe service = new br.inf.portalfiscal.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVe();
//            service.setHandlerResolver(new HandlerResolver() {
//                @Override
//                public List<Handler> getHandlerChain(PortInfo portInfo) {
//                    return Arrays.asList(new GTVeNamespaceMapper());
//                }
//            });
//            
//            br.inf.portalfiscal.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeSoap12 port = service.getCTeRecepcaoGTVeSoap12();
//            br.inf.portalfiscal.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeResult result = port.cTeRecepcaoGTVe(xmlEnvio);
//            
//            try {
//
//                JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
//                Unmarshaller um = context.createUnmarshaller();
//                TRetGTVe retGTVe = (TRetGTVe) um.unmarshal((Node) result.getAny());
//                System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
//            } catch (JAXBException e) {
//                e.printStackTrace();
//            }
//            System.out.println(Enviar(xmlAssinado));
            // Gravar Dados em BD SatExcel
            Persistencia persistenciaSatExcel = this.pool.getConexao("SATEXCEL");

            XMLNFE xmlNfe = new XMLNFE();
            xmlNfe.setTOKEN(gtv.getInfCte().getId());
            xmlNfe.setCNPJ(emit.getCNPJ());
            xmlNfe.setPraca("0");
            xmlNfe.setSerie(ide.getSerie());
            xmlNfe.setNumero(ide.getNCT());
            xmlNfe.setUF(UFGrava);
            try {
                xmlNfe.setDt_Nota(ide.getDhEmi().split("T")[0]);
            } catch (Exception e) {
                xmlNfe.setDt_Nota(ide.getDhEmi().split(" ")[0]);
            }

            try {
                xmlNfe.setHr_Nota(ide.getDhEmi().split("T")[1].substring(0, 5));
            } catch (Exception e) {
                xmlNfe.setHr_Nota(ide.getDhEmi().split(" ")[1].substring(0, 5));
            }

            String ComplementoXml = "<tomaTerceiroCNPJ>" + tomaTerceiro.getCNPJ() + "</tomaTerceiroCNPJ>"
                    + "<tomaTerceiroxNome>" + tomaTerceiro.getXNome() + "</tomaTerceiroxNome>"
                    + "<tomaTerceiroenderTomaxLgr>" + FuncoesString.removeAcento(tomaTerceiro.getEnderToma().getXLgr()) + "</tomaTerceiroenderTomaxLgr>"
                    + "<tomaTerceiroenderTomaxBairro>" + FuncoesString.removeAcento(tomaTerceiro.getEnderToma().getXBairro()) + "</tomaTerceiroenderTomaxBairro>"
                    + "<tomaTerceiroenderTomaxMun>" + tomaTerceiro.getEnderToma().getXMun() + "</tomaTerceiroenderTomaxMun>"
                    + "<tomaTerceiroenderTomaCEP>" + tomaTerceiro.getEnderToma().getCEP() + "</tomaTerceiroenderTomaCEP>"
                    + "<tomaTerceiroenderTomaUF>" + tomaTerceiro.getEnderToma().getUF().value() + "</tomaTerceiroenderTomaUF>";

            xmlNfe.setXML_Envio(xml.replace("</GTVe>", ComplementoXml + "</GTVe>"));

            InputSource is = new InputSource(new StringReader(RetornoString));
            DocumentBuilder dBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            Document doc = dBuilder.parse(is);

            xmlNfe.setXML_Retorno(RetornoString);
            xmlNfe.setChaveNFE(ProcuraTag(doc, "infProt", "chCTe", 0, false));
            xmlNfe.setProtocolo(ProcuraTag(doc, "infProt", "nProt", 0, false));
            xmlNfe.setStatus(ProcuraTag(doc, "infProt", "cStat", 0, false));
            xmlNfe.setDt_Retorno(getDataAtual("SQL"));
            xmlNfe.setHr_Retorno(getDataAtual("HORA"));

            xmlNfeDao.inserirXMLNFE(xmlNfe, persistenciaSatExcel);

            // Retorno para Cliente
            String LinkViewGuia = "https://mobile.sasw.com.br/SatMobWeb/guia/guia_integracao.html?nProt=" + ProcuraTag(doc, "infProt", "chCTe", 0, false) + "&DtRef=" + getDataAtual("SQL");
            retorno.put("status", "ok");
            retorno.put("resp", RetornoString.replace("</retGTVe>", "<linkViewGuia>" + LinkViewGuia + "</linkViewGuia></retGTVe>"));
        } catch (Exception e) {
            // Salvando em log o que foi mandado
            retorno.clear();
            retorno.put("status", "error");
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);
        } finally {
            this.logerro.Grava(gson.toJson(retorno), this.caminho);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gson.toJson(retorno))
                    .build();
        }
    }

    public Response jsonDataCompleto(String input, String empresa) throws Exception {
        StringMap dataJson = new Gson().fromJson(input, StringMap.class);
        Object datasObject = dataJson.get(0);
        Map retorno = new HashMap<>(), processamento, processamentoTrajeto;
        TGTVe retornoGTVe = new TGTVe();
        TGTVe.InfCte.DetGTV retornoDet = new TGTVe.InfCte.DetGTV();
        this.persistencia = this.pool.getConexao(empresa);
        StringMap dataObject = dataJson;

        // Carregar Objeto Com Informações jSon
        List<StringMap> listaPrincipal = (ArrayList) dataObject.get("dados");
        List<TGTVe.InfCte.Ide> ideList = new ArrayList<>();
        List<RotasGTVe> rotaGTVeList = new ArrayList<>();

        for (StringMap listaPrincipalDet : listaPrincipal) {
            // Objetos
            TGTVe.InfCte infCte = new TGTVe.InfCte();
            TGTVe.InfCte.Ide ide = new TGTVe.InfCte.Ide();

            RotasGTVe rotaGTVe = new RotasGTVe();
            TrajetosGTVe trajetosGTVe = new TrajetosGTVe();

            String fusoHorario = listaPrincipalDet.get("fusoHorarioSEFAZ").toString();
            String UFGrava = listaPrincipalDet.get("ideCUF").toString();

            // Carregar Dados Rota GTVe
            rotaGTVe.setAcao(Integer.getInteger(listaPrincipalDet.get("acao").toString()));
            rotaGTVe.setCodfil(Integer.getInteger(listaPrincipalDet.get("codFil").toString()));
            rotaGTVe.setNumero(listaPrincipalDet.get("numero").toString());
            rotaGTVe.setData(listaPrincipalDet.get("data").toString());
            rotaGTVe.setHoraini(listaPrincipalDet.get("horaIni").toString());
            rotaGTVe.setHorafim(listaPrincipalDet.get("horaFim").toString());

            // Lista Trajetos
            List<StringMap> trajetos = (ArrayList) listaPrincipalDet.get("trajetos");

            for (StringMap trajetoItem : trajetos) {
                trajetosGTVe.setTipo(trajetoItem.get("ideTipo").toString());
                trajetosGTVe.setClassificacao(trajetoItem.get("ideClassificacao").toString());
                trajetosGTVe.setCodcliorigem("?");
                trajetosGTVe.setCodclidestino("?");
                trajetosGTVe.setParada(Integer.getInteger(trajetoItem.get("ideParada").toString()));
                if (!trajetoItem.get("ideParadaDestino").toString().equals("")) {
                    trajetosGTVe.setParadadestino(Integer.getInteger(trajetoItem.get("ideParadaDestino").toString()));
                }
                trajetosGTVe.setPedidocliente(trajetoItem.get("idePedidoCliente").toString());
                trajetosGTVe.setValor(new BigDecimal(trajetoItem.get("ideValor").toString()));
                trajetosGTVe.setMoeda(trajetoItem.get("ideMoeda").toString());
                trajetosGTVe.setHora(trajetoItem.get("idedhSaidaOrig").toString().split("T")[1]);
                trajetosGTVe.setHoradestino(trajetoItem.get("idedhChegadaDest").toString().split("T")[1]);
                trajetosGTVe.setObservacao(trajetoItem.get("complxObs").toString());

                // Buscar Cliente Destino
                List<StringMap> destino = (ArrayList) trajetoItem.get("destino");

                Clientes cliDestino = clientesDao.buscarClienteNomeEndereco(destino.get(0).get("destxNome").toString(),
                        destino.get(0).get("destxNomeFant").toString(),
                        destino.get(0).get("destenderDestxLgr").toString(),
                        listaPrincipalDet.get("codFil").toString(),
                        this.persistencia);

                if (null == cliDestino) {
                    // Inserir Cliente
                }

                ClientesGTVe cliGTVeDestino = new ClientesGTVe();
                cliGTVeDestino.setCnpj(cliDestino.getCGC());
                cliGTVeDestino.setCpf(cliDestino.getCPF());
                cliGTVeDestino.setRazaosocial(cliDestino.getNome());
                cliGTVeDestino.setFantasia(cliDestino.getNRed());
                cliGTVeDestino.setEndereco(FuncoesString.removeAcento(cliDestino.getEnde()));
                cliGTVeDestino.setNumero("");
                cliGTVeDestino.setBairro(FuncoesString.removeAcento(cliDestino.getBairro()));
                cliGTVeDestino.setCidade(cliDestino.getCidade());
                cliGTVeDestino.setCodcidade(cliDestino.getCodCidade());
                cliGTVeDestino.setUf(cliDestino.getEstado());
                cliGTVeDestino.setCep(cliDestino.getCEP());
                cliGTVeDestino.setTelefone(cliDestino.getFone1());
                cliGTVeDestino.setEmail(cliDestino.getEmail());
                cliGTVeDestino.setIe(cliDestino.getIE());
                cliGTVeDestino.setLatitude(cliDestino.getLatitude());
                cliGTVeDestino.setLongitude(cliDestino.getLongitude());

                trajetosGTVe.setDestino(cliGTVeDestino);

                // Buscar Cliente Origem
                List<StringMap> origem = (ArrayList) trajetoItem.get("origem");

                Clientes cliOrigem = clientesDao.buscarClienteNomeEndereco(destino.get(0).get("remxNome").toString(),
                        destino.get(0).get("remxFant").toString(),
                        destino.get(0).get("remenderRemexLgr").toString(),
                        listaPrincipalDet.get("codFil").toString(),
                        this.persistencia);

                if (null == cliOrigem) {
                    // Inserir Cliente
                }

                ClientesGTVe cliGTVeOrigem = new ClientesGTVe();
                cliGTVeOrigem.setCnpj(cliDestino.getCGC());
                cliGTVeOrigem.setCpf(cliDestino.getCPF());
                cliGTVeOrigem.setRazaosocial(cliDestino.getNome());
                cliGTVeOrigem.setFantasia(cliDestino.getNRed());
                cliGTVeOrigem.setEndereco(FuncoesString.removeAcento(cliDestino.getEnde()));
                cliGTVeOrigem.setNumero("");
                cliGTVeOrigem.setBairro(FuncoesString.removeAcento(cliDestino.getBairro()));
                cliGTVeOrigem.setCidade(cliDestino.getCidade());
                cliGTVeOrigem.setCodcidade(cliDestino.getCodCidade());
                cliGTVeOrigem.setUf(cliDestino.getEstado());
                cliGTVeOrigem.setCep(cliDestino.getCEP());
                cliGTVeOrigem.setTelefone(cliDestino.getFone1());
                cliGTVeOrigem.setEmail(cliDestino.getEmail());
                cliGTVeOrigem.setIe(cliDestino.getIE());
                cliGTVeOrigem.setLatitude(cliDestino.getLatitude());
                cliGTVeOrigem.setLongitude(cliDestino.getLongitude());

                trajetosGTVe.setOrigem(cliGTVeOrigem);

                // Buscar Cliente Emissor
                List<StringMap> emissor = (ArrayList) trajetoItem.get("emitente");

                Clientes cliEmissor = clientesDao.buscarClienteNomeEndereco(destino.get(0).get("emitxNome").toString(),
                        destino.get(0).get("emitxFant").toString(),
                        destino.get(0).get("emitenderEmitxLgr").toString(),
                        listaPrincipalDet.get("codFil").toString(),
                        this.persistencia);

                if (null == cliEmissor) {
                    // Inserir Cliente
                }

                ClientesGTVe cliGTVeEmissor = new ClientesGTVe();
                cliGTVeEmissor.setCnpj(cliDestino.getCGC());
                cliGTVeEmissor.setCpf(cliDestino.getCPF());
                cliGTVeEmissor.setRazaosocial(cliDestino.getNome());
                cliGTVeEmissor.setFantasia(cliDestino.getNRed());
                cliGTVeEmissor.setEndereco(cliDestino.getEnde());
                cliGTVeEmissor.setNumero("");
                cliGTVeEmissor.setBairro(FuncoesString.removeAcento(cliDestino.getBairro()));
                cliGTVeEmissor.setCidade(cliDestino.getCidade());
                cliGTVeEmissor.setCodcidade(cliDestino.getCodCidade());
                cliGTVeEmissor.setUf(cliDestino.getEstado());
                cliGTVeEmissor.setCep(cliDestino.getCEP());
                cliGTVeEmissor.setTelefone(cliDestino.getFone1());
                cliGTVeEmissor.setEmail(cliDestino.getEmail());
                cliGTVeEmissor.setIe(cliDestino.getIE());
                cliGTVeEmissor.setLatitude(cliDestino.getLatitude());
                cliGTVeEmissor.setLongitude(cliDestino.getLongitude());

                trajetosGTVe.setEmissor(cliGTVeEmissor);

                // Dados Veículo
                VeiculosGTVe veiculosGTVe = new VeiculosGTVe();
                List<StringMap> veiculo = (ArrayList) trajetoItem.get("veiculo");

                veiculosGTVe.setPlaca(veiculo.get(0).get("detGTVinfVeiculoplaca").toString());
                veiculosGTVe.setTipo(veiculo.get(0).get("detGTVinfTipo").toString());
                veiculosGTVe.setUf(veiculo.get(0).get("detGTVinfVeiculoUF").toString());

                trajetosGTVe.setVeiculo(veiculosGTVe);

                // Dados Guias
                List<GuiasGTVe> listaGuias = new ArrayList<>();
                GuiasGTVe Guia;

                trajetosGTVe.setGuias(listaGuias);

                //trajetosGTVe.setGuias(destino);
            }

            ide.setCUF(UFUtils.codigo(dataObject.get("ideCUF").toString()));
            ide.setCFOP(dataObject.get("ideCFOP").toString().equals("") ? null : dataObject.get("ideCFOP").toString().trim());
            ide.setNatOp(dataObject.get("idenatOp").toString().equals("") ? null : dataObject.get("idenatOp").toString().trim());
            ide.setMod(dataObject.get("idemod").toString().equals("") ? null : dataObject.get("idemod").toString().trim());
            ide.setSerie(dataObject.get("ideserie").toString().equals("") ? null : dataObject.get("ideserie").toString().trim());
            ide.setNCT(dataObject.get("idenCT").toString().equals("") ? null : dataObject.get("idenCT").toString().trim());
            ide.setCCT(FuncoesString.PreencheEsquerda(FuncoesString.RecortaString(dataObject.get("idenCT").toString(), 0, 8), 8, "0"));
            ide.setDhEmi(dataObject.get("idedhEmi").toString() + fusoHorario);
            ide.setTpImp(dataObject.get("idetpImp").toString().equals("") ? null : dataObject.get("idetpImp").toString().trim());
            ide.setTpEmis(dataObject.get("idetpEmis").toString().equals("") ? null : dataObject.get("idetpEmis").toString().trim());
            ide.setCDV(dataObject.get("idecDV").toString().equals("") ? null : dataObject.get("idecDV").toString().trim());
            ide.setTpAmb(dataObject.get("idetpAmb").toString().equals("") ? null : dataObject.get("idetpAmb").toString().trim());
            ide.setTpCTe(dataObject.get("idetpCTe").toString().equals("") ? null : dataObject.get("idetpCTe").toString().trim());
            ide.setVerProc(dataObject.get("ideverProc").toString().equals("") ? null : dataObject.get("ideverProc").toString().trim());
            ide.setCMunEnv(dataObject.get("idecMunEnv").toString().equals("") ? null : dataObject.get("idecMunEnv").toString().trim());
            ide.setXMunEnv(dataObject.get("idexMunEnv").toString().equals("") ? null : dataObject.get("idexMunEnv").toString().trim());
            ide.setUFEnv(TUf.fromValue(dataObject.get("ideUFEnv").toString()));
            ide.setModal(dataObject.get("idemodal").toString().equals("") ? null : dataObject.get("idemodal").toString().trim());
            ide.setTpServ(dataObject.get("idetpServ").toString().equals("") ? null : dataObject.get("idetpServ").toString().trim());
            ide.setIndIEToma(dataObject.get("ideindIEToma").toString().equals("") ? null : dataObject.get("ideindIEToma").toString().trim());
            ide.setDhSaidaOrig(dataObject.get("idedhSaidaOrig").toString() + fusoHorario);
            ide.setDhChegadaDest(dataObject.get("idedhChegadaDest").toString() + fusoHorario);
            ide.setDhCont(dataObject.get("idedhCont").toString() + fusoHorario);
            ide.setXJust(dataObject.get("idexJust").toString().equals("") ? null : dataObject.get("idexJust").toString().trim());

            ideList.add(ide);
        }
        /*
        TGTVe.InfCte.Ide.Toma toma = new TGTVe.InfCte.Ide.Toma();
        toma.setToma(dataObject.get("tomatoma").toString().equals("") ? null : dataObject.get("tomatoma").toString().trim());
        ide.setToma(toma);
        
        TGTVe.InfCte.Ide.TomaTerceiro tomaTerceiro = new TGTVe.InfCte.Ide.TomaTerceiro();
        tomaTerceiro.setToma(dataObject.get("tomaTerceirotoma").toString().equals("") ? null : dataObject.get("tomaTerceirotoma").toString().trim());
        tomaTerceiro.setCNPJ(dataObject.get("tomaTerceiroCNPJ").toString().equals("") ? null : dataObject.get("tomaTerceiroCNPJ").toString().trim());
        tomaTerceiro.setCPF(dataObject.get("tomaTerceiroCPF").toString().equals("") ? null : dataObject.get("tomaTerceiroCPF").toString().trim());
        tomaTerceiro.setIE(dataObject.get("tomaTerceiroIE").toString().equals("") ? null : dataObject.get("tomaTerceiroIE").toString().trim());
        tomaTerceiro.setXNome(dataObject.get("tomaTerceiroxNome").toString().equals("") ? null : dataObject.get("tomaTerceiroxNome").toString().trim());
        tomaTerceiro.setXFant(dataObject.get("tomaTerceiroxFant").toString().equals("") ? null : dataObject.get("tomaTerceiroxFant").toString().trim());
        tomaTerceiro.setFone(dataObject.get("tomaTerceirofone").toString().equals("") ? null : dataObject.get("tomaTerceirofone").toString().trim());
        
        TEndereco tomaTerceiroenderToma = new TEndereco();
        tomaTerceiroenderToma.setXLgr(dataObject.get("tomaTerceiroenderTomaxLgr").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomaxLgr").toString().trim());
        if (null != tomaTerceiroenderToma.getXLgr()
                && tomaTerceiroenderToma.getXLgr().contains(",")) {
            try {
                tomaTerceiroenderToma.setNro(tomaTerceiroenderToma.getXLgr().substring(tomaTerceiroenderToma.getXLgr().lastIndexOf(",")));
                tomaTerceiroenderToma.setXLgr(tomaTerceiroenderToma.getXLgr().substring(0, tomaTerceiroenderToma.getXLgr().lastIndexOf(",")));
                Integer.parseInt(tomaTerceiroenderToma.getNro());
            } catch (Exception en) {
                tomaTerceiroenderToma.setNro("0");
            }
        } else {
            tomaTerceiroenderToma.setNro("0");
        }
//                tomaTerceiroenderToma.setXCpl(consulta.getString("tomaTerceiroenderTomaxCpl").equals("") ? null : consulta.getString("tomaTerceiroenderTomaxCpl").trim());
        tomaTerceiroenderToma.setXBairro(dataObject.get("tomaTerceiroenderTomaxBairro").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomaxBairro").toString().trim());
        tomaTerceiroenderToma.setCMun(dataObject.get("tomaTerceiroenderTomacMun").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomacMun").toString().trim());
        tomaTerceiroenderToma.setXMun(dataObject.get("tomaTerceiroenderTomaxMun").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomaxMun").toString().trim());
        tomaTerceiroenderToma.setCEP(dataObject.get("tomaTerceiroenderTomaCEP").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomaCEP").toString().trim());
        tomaTerceiroenderToma.setUF(dataObject.get("tomaTerceiroenderTomaUF").toString().equals("") ? null : TUf.fromValue(dataObject.get("tomaTerceiroenderTomaUF").toString()));
        tomaTerceiroenderToma.setCPais(dataObject.get("tomaTerceiroenderTomacPais").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomacPais").toString().trim());
        tomaTerceiroenderToma.setXPais(dataObject.get("tomaTerceiroenderTomaxPais").toString().equals("") ? null : dataObject.get("tomaTerceiroenderTomaxPais").toString().trim());
        tomaTerceiro.setEnderToma(tomaTerceiroenderToma);
        
        tomaTerceiro.setEmail(dataObject.get("tomaTerceiroemail").toString().equals("") ? null : dataObject.get("tomaTerceiroemail").toString().trim());
//                ide.setTomaTerceiro(tomaTerceiro);

        infCte.setIde(ide);
        
        TGTVe.InfCte.Compl compl = new TGTVe.InfCte.Compl();
        compl.setXObs(dataObject.get("complxObs").toString().equals("") ? null : dataObject.get("complxObs").toString().trim());
        infCte.setCompl(compl);
        
        TGTVe.InfCte.Emit emit = new TGTVe.InfCte.Emit();
        emit.setCNPJ(dataObject.get("emitCNPJ").toString().equals("") ? null : dataObject.get("emitCNPJ").toString().trim());
        emit.setIE(dataObject.get("emitIE").toString().equals("") ? null : dataObject.get("emitIE").toString().trim());
        emit.setIEST(dataObject.get("emitIEST").toString().equals("") ? null : dataObject.get("emitIEST").toString().trim());
        emit.setXNome(dataObject.get("emitxNome").toString().equals("") ? null : dataObject.get("emitxNome").toString().trim());
        emit.setXFant(dataObject.get("emitxFant").toString().equals("") ? null : dataObject.get("emitxFant").toString().trim());
        
        TEndeEmi emitenderEmit = new TEndeEmi();
        emitenderEmit.setXLgr(dataObject.get("emitenderEmitxLgr").toString().trim());
        if (null != emitenderEmit.getXLgr()
                && emitenderEmit.getXLgr().contains(",")) {
            try {
                emitenderEmit.setNro(tomaTerceiroenderToma.getXLgr().substring(tomaTerceiroenderToma.getXLgr().lastIndexOf(",")));
                emitenderEmit.setXLgr(tomaTerceiroenderToma.getXLgr().substring(0, tomaTerceiroenderToma.getXLgr().lastIndexOf(",")));
                Integer.parseInt(emitenderEmit.getNro());
            } catch (Exception en) {
                emitenderEmit.setNro("0");
            }
        } else {
            emitenderEmit.setNro("0");
        }
//                emitenderEmit.setXCpl(consulta.getString("emitenderEmitxCpl").equals("") ? null : consulta.getString("emitenderEmitxCpl").trim());
        emitenderEmit.setXBairro(dataObject.get("emitenderEmitxBairro").toString().equals("") ? null : dataObject.get("emitenderEmitxBairro").toString().trim());
        emitenderEmit.setCMun(dataObject.get("emitenderEmitcMun").toString().equals("") ? null : dataObject.get("emitenderEmitcMun").toString().trim());
        emitenderEmit.setXMun(dataObject.get("emitenderEmitxMun").toString().equals("") ? null : dataObject.get("emitenderEmitxMun").toString().trim());
        emitenderEmit.setCEP(dataObject.get("emitenderEmitCEP").toString().equals("") ? null : dataObject.get("emitenderEmitCEP").toString().trim());
        emitenderEmit.setUF(dataObject.get("emitenderEmitUF").toString().equals("") ? null : TUFSemEX.fromValue(dataObject.get("emitenderEmitUF").toString()));
        emitenderEmit.setFone(dataObject.get("emitenderEmitfone").toString().equals("") ? null : dataObject.get("emitenderEmitfone").toString().trim());
        emit.setEnderEmit(emitenderEmit);
        infCte.setEmit(emit);
        
        TGTVe.InfCte.Rem rem = new TGTVe.InfCte.Rem();
        rem.setCNPJ(dataObject.get("remCNPJ").toString().equals("") ? null : dataObject.get("remCNPJ").toString().trim());
        rem.setCPF(dataObject.get("remCPF").toString().equals("") ? null : dataObject.get("remCPF").toString().trim());
        rem.setIE(dataObject.get("remIE").toString().equals("") ? null : dataObject.get("remIE").toString().trim());
        rem.setXNome(dataObject.get("remxNome").toString().equals("") ? null : dataObject.get("remxNome").toString().trim());
        rem.setXFant(dataObject.get("remxFant").toString().equals("") ? null : dataObject.get("remxFant").toString().trim());
        rem.setFone(dataObject.get("remfone").toString().equals("") ? null : dataObject.get("remfone").toString().trim());
        
        TEndereco remenderReme = new TEndereco();
        remenderReme.setXLgr(dataObject.get("remenderRemexLgr").toString().equals("") ? null : dataObject.get("remenderRemexLgr").toString().trim());
        if (null != remenderReme.getXLgr()
                && remenderReme.getXLgr().contains(",")) {
            try {
                remenderReme.setNro(remenderReme.getXLgr().substring(remenderReme.getXLgr().lastIndexOf(",")));
                remenderReme.setXLgr(remenderReme.getXLgr().substring(0, remenderReme.getXLgr().lastIndexOf(",")));
                Integer.parseInt(remenderReme.getNro());
            } catch (Exception en) {
                remenderReme.setNro("0");
            }
        } else {
            remenderReme.setNro("0");
        }
//                remenderReme.setXCpl(consulta.getString("remenderRemexCpl").equals("") ? null : consulta.getString("remenderRemexCpl").trim());
        remenderReme.setXBairro(dataObject.get("remenderRemexBairro").toString().equals("") ? null : dataObject.get("remenderRemexBairro").toString().trim());
        remenderReme.setCMun(dataObject.get("remenderRemecMun").toString().equals("") ? null : dataObject.get("remenderRemecMun").toString().trim());
        remenderReme.setXMun(dataObject.get("remenderRemexMun").toString().equals("") ? null : dataObject.get("remenderRemexMun").toString().trim());
        remenderReme.setCEP(dataObject.get("remenderRemeCEP").toString().equals("") ? null : dataObject.get("remenderRemeCEP").toString().trim());
        remenderReme.setUF(dataObject.get("remenderRemeUF").toString().equals("") ? null : TUf.fromValue(dataObject.get("remenderRemeUF").toString()));
        remenderReme.setCPais(dataObject.get("remenderRemecPais").toString().equals("") ? null : dataObject.get("remenderRemecPais").toString().trim());
        remenderReme.setXPais(dataObject.get("remenderRemexPais").toString().equals("") ? null : dataObject.get("remenderRemexPais").toString().trim());
        rem.setEnderReme(remenderReme);
        rem.setEmail(dataObject.get("rememail").toString().equals("") ? null : dataObject.get("rememail").toString().trim());
        infCte.setRem(rem);
        
        TGTVe.InfCte.Dest dest = new TGTVe.InfCte.Dest();
        dest.setCNPJ(dataObject.get("destCNPJ").toString().equals("") ? null : dataObject.get("destCNPJ").toString().trim());
        dest.setCPF(dataObject.get("destCPF").toString().equals("") ? null : dataObject.get("destCPF").toString().trim());
        dest.setIE(dataObject.get("destIE").toString().equals("") ? null : dataObject.get("destIE").toString().trim());
        dest.setXNome(dataObject.get("destxNome").toString().equals("") ? null : dataObject.get("destxNome").toString().trim());
        dest.setFone(dataObject.get("destfone").toString().equals("") ? null : dataObject.get("destfone").toString().trim());
        dest.setISUF(dataObject.get("destISUF").toString().equals("") ? null : dataObject.get("destISUF").toString().trim());
        
        TEndereco destenderDest = new TEndereco();
        destenderDest.setXLgr(dataObject.get("destenderDestxLgr").toString().equals("") ? null : dataObject.get("destenderDestxLgr").toString().trim());
        if (null != destenderDest.getXLgr()
                && destenderDest.getXLgr().contains(",")) {
            try {
                destenderDest.setNro(destenderDest.getXLgr().substring(destenderDest.getXLgr().lastIndexOf(",")));
                destenderDest.setXLgr(destenderDest.getXLgr().substring(0, destenderDest.getXLgr().lastIndexOf(",")));
                Integer.parseInt(destenderDest.getNro());
            } catch (Exception en) {
                destenderDest.setNro("0");
            }
        } else {
            destenderDest.setNro("0");
        }
//                destenderDest.setXCpl(consulta.getString("destenderDestxCpl").equals("") ? null : consulta.getString("destenderDestxCpl").trim());
        destenderDest.setXBairro(dataObject.get("destenderDestxBairro").toString().equals("") ? null : dataObject.get("destenderDestxBairro").toString().trim());
        destenderDest.setCMun(dataObject.get("destenderDestcMun").toString().equals("") ? null : dataObject.get("destenderDestcMun").toString().trim());
        destenderDest.setXMun(dataObject.get("destenderDestxMun").toString().equals("") ? null : dataObject.get("destenderDestxMun").toString().trim());
        destenderDest.setCEP(dataObject.get("destenderDestCEP").toString().equals("") ? null : dataObject.get("destenderDestCEP").toString().trim());
        destenderDest.setUF(dataObject.get("destenderDestUF").toString().equals("") ? null : TUf.fromValue(dataObject.get("destenderDestUF").toString()));
        destenderDest.setCPais(dataObject.get("destenderDestcPais").toString().equals("") ? null : dataObject.get("destenderDestcPais").toString().trim());
        destenderDest.setXPais(dataObject.get("destenderDestxPais").toString().equals("") ? null : dataObject.get("destenderDestxPais").toString().trim());
        dest.setEnderDest(destenderDest);
        dest.setEmail(dataObject.get("destemail").toString().equals("") ? null : dataObject.get("destemail").toString().trim());
        infCte.setDest(dest);
        
        infCte.setId(dataObject.get("ideIdAnoMes").toString());
        
        retornoGTVe.setInfCte(infCte);

        // Lista detGTVqCarga
        TGTVe.InfCte.DetGTV.InfEspecie infEspecie;
        Integer detGTVqCarga = 0;
        List<StringMap> dataDetList = (ArrayList) dataObject.get("detListGTVqCarga");
        
        for (StringMap dataObjectDet : dataDetList) {
            infEspecie = new TGTVe.InfCte.DetGTV.InfEspecie();
            infEspecie.setTpEspecie(dataObjectDet.get("detGTVinfEspecietpEspecie").toString());
            BigDecimal detGTVinfEspecievEspecie = BigDecimal.valueOf(Double.parseDouble(dataObjectDet.get("detGTVinfEspecievEspecie").toString())).setScale(2, RoundingMode.CEILING);
            
            infEspecie.setVEspecie(detGTVinfEspecievEspecie.toString());
            infEspecie.setTpNumerario(dataObjectDet.get("detGTVinfEspecietpNumerario").toString());
            if (infEspecie.getTpNumerario().equals("2")) {
                infEspecie.setXMoedaEstr(dataObjectDet.get("detGTVinfEspeciexMoedaEstr").toString());
            }
            
            retornoDet.getInfEspecie().add(infEspecie);
            
            detGTVqCarga += dataObjectDet.get("detGTVqCarga").toString().equals("") ? 1 : Integer.parseInt(dataObjectDet.get("detGTVqCarga").toString());
        }
        
        retornoDet.setQCarga(String.valueOf(detGTVqCarga));

        // Lista InfVeiculo
        List<StringMap> dataInfoList = (ArrayList) dataObject.get("InfListVeiculo");
        TGTVe.InfCte.DetGTV.InfVeiculo infVeiculo;
        
        for (StringMap dataObjectInfo : dataInfoList) {
            infVeiculo = new TGTVe.InfCte.DetGTV.InfVeiculo();
            infVeiculo.setPlaca(dataObjectInfo.get("detGTVinfVeiculoplaca").toString());
            infVeiculo.setUF(dataObjectInfo.get("detGTVinfVeiculoUF").toString().equals("") ? null : TUf.fromValue(dataObjectInfo.get("detGTVinfVeiculoUF").toString()));
            retornoDet.getInfVeiculo().add(infVeiculo);
        }
         */
        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(GTVeException.class, new GTVeExceptionSerializer());
        Gson gson = gsonBuilder.create();
        List processamentos = new ArrayList<>(), trajetosProcessado;

        try {

            // Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao(empresa);
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\" + empresa + "\\GTVe\\"
                    + getDataAtual("SQL") + "\\log.txt";

            this.logerro.Grava("jSonData:\t" + input, this.caminho);

            String sequencia, numero;
            CxForte cxForte;
            Pedido pedido;
            Rotas rota;
            Rt_Perc trajeto;

            TGTVe gtv = retornoGTVe;
            if (gtv == null) {
                throw new GTVeException(new GTVeErrorCode(0));
            }

            String cuf = gtv.getInfCte().getIde().getCUF();
            String AAMM = gtv.getInfCte().getId();
            String cnpj = gtv.getInfCte().getEmit().getCNPJ();
            String mod = gtv.getInfCte().getIde().getMod();
            String s = FuncoesString.PreencheEsquerda(gtv.getInfCte().getIde().getSerie(), 3, "0");
            String nct = FuncoesString.PreencheEsquerda(gtv.getInfCte().getIde().getNCT(), 9, "0");
            String tpemi = gtv.getInfCte().getIde().getTpEmis();
            String cct = FuncoesString.PreencheEsquerda(gtv.getInfCte().getIde().getCCT(), 8, "0");
            String cdv = String.valueOf(Modulo11.modulo11(cuf + AAMM + cnpj + mod + s + nct + tpemi + cct));

            gtv.getInfCte().getIde().setCCT(cct);
            gtv.getInfCte().getIde().setCDV(cdv);

            gtv.getInfCte().setVersao("4.00");

            gtv.getInfCte().setId("CTe" + cuf + AAMM + cnpj + mod + s + nct + tpemi + cct + cdv);

            //        gtv.getInfCte().getAutXML().addAll(gtvDao.getAutXML("882021146", persistencia));
            gtv.getInfCte().setDetGTV(retornoDet);

            gtv.setVersao("4.00");

            if (homolog) {
                gtv.getInfCte().getRem().setXNome("CT-E EMITIDO EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL");
                gtv.getInfCte().getDest().setXNome("CT-E EMITIDO EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL");
            }

            gtv.getInfCte().getIde().setDhCont(null);
            gtv.getInfCte().getIde().setXJust(null);

            this.logerro.Grava("Monta Link", this.caminho);

            TGTVe.InfCTeSupl infCTeSupl = new TGTVe.InfCTeSupl();
            if (cuf.equals("31")) {
                infCTeSupl.setQrCodCTe("https://cte.fazenda.mg.gov.br/portalcte/sistema/qrcode.xhtml?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("51")) {
                infCTeSupl.setQrCodCTe("https://www.sefaz.mt.gov.br/cte/qrcode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("14")) {
                infCTeSupl.setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("16")) {
                infCTeSupl.setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("26")) {
                infCTeSupl.setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("35")) {
                infCTeSupl.setQrCodCTe("https://nfe.fazenda.sp.gov.br/CTeConsulta/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else if (cuf.equals("41")) {
                infCTeSupl.setQrCodCTe("http://www.fazenda.pr.gov.br/cte/qrcode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            } else {
                infCTeSupl.setQrCodCTe("https://dfe-portal.svrs.rs.gov.br/cte/qrCode?chCTe=" + gtv.getInfCte().getId().replace("CTe", "") + "&tpAmb=" + gtv.getInfCte().getIde().getTpAmb());
            }
            gtv.setInfCTeSupl(infCTeSupl);

            this.logerro.Grava("XML Envio: " + XML.toXml(gtv, Boolean.FALSE), this.caminho);
            String xml = XML.toXml(gtv, Boolean.FALSE);

            String xmlAssinado = "";
            if (empresa.contains("BRASIFORT")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + Certificados.BRASIFORT_NATAL.getNome(),
                                Certificados.BRASIFORT_NATAL.getSenha()));
            }else if (empresa.contains("FIDELYS")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + CertificadosFid.FIDELYS_BH.getNome(),
                                  CertificadosFid.FIDELYS_BH.getSenha()));
            }else if (empresa.contains("PRESERVEPB")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + CertificadosPreservePB.PRESERVE_PB.getNome(),
                                CertificadosPreservePB.PRESERVE_PB.getSenha()));                
            }else if (empresa.contains("PRESERVE")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + CertificadosPreserve.PRESERVE_PE.getNome(),
                                CertificadosPreserve.PRESERVE_PE.getSenha()));
            }else if (empresa.contains("INVIOSEG")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + CertificadosInvioseg.INVIOSEG_SINOP.getNome(),
                                CertificadosInvioseg.INVIOSEG_SINOP.getSenha()));            
            }else if (empresa.contains("CORPVS")) {
                xmlAssinado = AssinarGTVe.assinarGTVe(xml.replace(" xmlns:ns2=\"http://www.w3.org/2000/09/xmldsig#\"", ""),
                        CertificadoService.certificadoPfx(File.separator + "Certificados" + File.separator
                                + CertificadosCorpvs.CORPVS_FOR.getNome(),
                                CertificadosCorpvs.CORPVS_FOR.getSenha()));
            }
            this.logerro.Grava("XML Assinado: " + xmlAssinado, this.caminho);

            try {
                //Get JAXBContext
                JAXBContext jaxbContext = JAXBContext.newInstance(TGTVe.class);

                //Create Unmarshaller
                Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();

                //Setup schema validator
                SchemaFactory sf = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);

                //Schema employeeSchema = sf.newSchema(new File(ImportacaoGTVe.class.getResource("/gtve/GTVe_v3.00.xsd").toString().replace("file:\\", "")
                Schema employeeSchema = sf.newSchema(new File(ImportacaoGTVe.class.getResource("/gtve/GTVe_v4.00.xsd").toString().replace("file:\\", "")
                        .replace("file:/", "")
                        .replace("%20", " ")));
                jaxbUnmarshaller.setSchema(employeeSchema);

                //Unmarshal xml file 
//                TGTVe validacao = (TGTVe) jaxbUnmarshaller.unmarshal(new ByteArrayInputStream(xmlAssinado.getBytes()));
//
//                System.out.println(validacao);
            } catch (JAXBException | SAXException e) {
                this.logerro.Grava("Error Unmarshal xml file : " + e.getMessage(), this.caminho);
                e.printStackTrace();
            }

            ByteArrayOutputStream obj = new ByteArrayOutputStream();
            Base64OutputStream b64os = new Base64OutputStream(obj);
            GZIPOutputStream gzipOS = new GZIPOutputStream(b64os);
            gzipOS.write(xmlAssinado.getBytes("UTF-8"));
            gzipOS.close();

            String xmlEnvio = new String(obj.toByteArray());
            this.logerro.Grava("CML Envio Byte : " + xmlEnvio, this.caminho);
            obj.close();
            b64os.close();

            this.logerro.Grava("Procurar Certificado", this.caminho);
//            InputStream ce = new FileInputStream(File.separator + "Certificados" + File.separator + "cert");
            InputStream ce = null;
            if (empresa.contains("BRASIFORT")) {
               ce = new FileInputStream(File.separator + "Certificados" + File.separator + "BRASIFORTNA");
            } else if (empresa.contains("FIDELYS")) {
               ce = new FileInputStream(File.separator + "Certificados" + File.separator + "FIDELYS");  
            }else if (empresa.contains("PRESERVE")) {
               ce = new FileInputStream(File.separator + "Certificados" + File.separator + "PRESERVE");   
            }else if (empresa.contains("INVIOSEG")) {
               ce = new FileInputStream(File.separator + "Certificados" + File.separator + "INVIOSEG");   
            }else if (empresa.contains("CORPVS")) {
               ce = new FileInputStream(File.separator + "Certificados" + File.separator + "CORPVS");   
            }else if (empresa.contains("FEDERAL")) {
               ce = new FileInputStream(File.separator + "Certificados" + File.separator + "FEDERAL");   
            }else if (empresa.contains("IBL")) {
               ce = new FileInputStream(File.separator + "Certificados" + File.separator + "IBL");      
            }
            this.logerro.Grava("Encontrou Certificado", this.caminho);

            KeyStore ks = KeyStore.getInstance("PKCS12");
            this.logerro.Grava("Set KS Key Store", this.caminho);
            //ks.load(ce, "123456789".toCharArray());
            ks.load(ce, "12345678".toCharArray());
            this.logerro.Grava("Set KS Senha", this.caminho);

            String alias = "";
            Enumeration<String> aliasesEnum = ks.aliases();
            while (aliasesEnum.hasMoreElements()) {
                alias = (String) aliasesEnum.nextElement();
                if (ks.isKeyEntry(alias)) {
                    break;
                }
            }

            this.logerro.Grava("Encontrou Alias", this.caminho);

            X509Certificate certificate = (X509Certificate) ks.getCertificate(alias);
            //PrivateKey privateKey = (PrivateKey) ks.getKey(alias, "123456789".toCharArray());
            this.logerro.Grava("SET Private Key", this.caminho);
            PrivateKey privateKey = (PrivateKey) ks.getKey(alias, "12345678".toCharArray());
            this.logerro.Grava("SET Socket", this.caminho);
            SocketFactoryDinamico socketFactoryDinamico = new SocketFactoryDinamico(certificate, privateKey);
//            URL cacert = getClass().getResource("eSocialCacerts");
//            socketFactoryDinamico.setFileCacerts(cacert.getPath().replace("%20", " "));
            this.logerro.Grava("SET cacert", this.caminho);
            socketFactoryDinamico.setFileCacerts(File.separator + "Certificados" + File.separator + "cacert.jks");
            this.logerro.Grava("SET Protocol", this.caminho);
            Protocol protocol = new Protocol("https", socketFactoryDinamico, 443);
            Protocol.registerProtocol("https", protocol);

//            assinarConexao();
            this.logerro.Grava("SET CteDadosMsg", this.caminho);
            CTeRecepcaoGTVeStub.CteDadosMsg cteDadosMsg = new CTeRecepcaoGTVeStub.CteDadosMsg();
            cteDadosMsg.setCteDadosMsg(xmlEnvio);

            this.logerro.Grava("SET CTeRecepcaoGTVeResult", this.caminho);
            CTeRecepcaoGTVeStub stub = new CTeRecepcaoGTVeStub();
            this.logerro.Grava("Envio stub.cTeRecepcaoGTVe(cteDadosMsg);", this.caminho);
            CTeRecepcaoGTVeStub.CTeRecepcaoGTVeResult result = stub.cTeRecepcaoGTVe(cteDadosMsg);
            this.logerro.Grava("Retorno stub.cTeRecepcaoGTVe(cteDadosMsg);", this.caminho);
            String RetornoString = "x";

            try {
                JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
                Unmarshaller um = context.createUnmarshaller();
                TRetGTVe retGTVe = (TRetGTVe) um.unmarshal(new StringReader(result.getExtraElement().toString()));
                System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
                RetornoString = XML.toXml(retGTVe, Boolean.TRUE);
            } catch (JAXBException e) {
                this.logerro.Grava("Error JAXBContext: " + e.getMessage(), this.caminho);
                e.printStackTrace();
            }
            System.out.println();

//            ServicoEnviarLoteEventosStub.LoteEventos_type0 dadosMsgType0 = new ServicoEnviarLoteEventosStub.LoteEventos_type0();
//            System.setProperty("com.sun.xml.ws.transport.http.client.HttpTransportPipe.dump", "true");
//            System.setProperty("com.sun.xml.internal.ws.transport.http.client.HttpTransportPipe.dump", "true");
//            System.setProperty("com.sun.xml.ws.transport.http.HttpAdapter.dump", "true");
//            System.setProperty("com.sun.xml.internal.ws.transport.http.HttpAdapter.dump", "true");
//            System.setProperty("com.sun.xml.internal.ws.transport.http.HttpAdapter.dumpTreshold", "999999");
//            br.inf.portalfiscal.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVe service = new br.inf.portalfiscal.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVe();
//            service.setHandlerResolver(new HandlerResolver() {
//                @Override
//                public List<Handler> getHandlerChain(PortInfo portInfo) {
//                    return Arrays.asList(new GTVeNamespaceMapper());
//                }
//            });
//            
//            br.inf.portalfiscal.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeSoap12 port = service.getCTeRecepcaoGTVeSoap12();
//            br.inf.portalfiscal.cte.wsdl.cterecepcaogtve.CTeRecepcaoGTVeResult result = port.cTeRecepcaoGTVe(xmlEnvio);
//            
//            try {
//
//                JAXBContext context = JAXBContext.newInstance(TRetGTVe.class);
//                Unmarshaller um = context.createUnmarshaller();
//                TRetGTVe retGTVe = (TRetGTVe) um.unmarshal((Node) result.getAny());
//                System.out.println(XML.toXml(retGTVe, Boolean.TRUE));
//            } catch (JAXBException e) {
//                e.printStackTrace();
//            }
//            System.out.println(Enviar(xmlAssinado));
            // Gravar Dados em BD SatExcel
            Persistencia persistenciaSatExcel = this.pool.getConexao("SATEXCEL");

            XMLNFE xmlNfe = new XMLNFE();
            /*xmlNfe.setTOKEN(gtv.getInfCte().getId());
            xmlNfe.setCNPJ(emit.getCNPJ());
            xmlNfe.setPraca("0");
            xmlNfe.setSerie(ide.getSerie());
            xmlNfe.setNumero(ide.getNCT());
            xmlNfe.setUF(UFGrava);
            try {
                xmlNfe.setDt_Nota(ide.getDhEmi().split("T")[0]);
            } catch (Exception e) {
                xmlNfe.setDt_Nota(ide.getDhEmi().split(" ")[0]);
            }
            
            try {
                xmlNfe.setHr_Nota(ide.getDhEmi().split("T")[1].substring(0, 5));
            } catch (Exception e) {
                xmlNfe.setHr_Nota(ide.getDhEmi().split(" ")[1].substring(0, 5));
            }
            
            String ComplementoXml = "<tomaTerceiroCNPJ>" + tomaTerceiro.getCNPJ() + "</tomaTerceiroCNPJ>"
                    + "<tomaTerceiroxNome>" + tomaTerceiro.getXNome() + "</tomaTerceiroxNome>"
                    + "<tomaTerceiroenderTomaxLgr>" + tomaTerceiro.getEnderToma().getXLgr() + "</tomaTerceiroenderTomaxLgr>"
                    + "<tomaTerceiroenderTomaxBairro>" + tomaTerceiro.getEnderToma().getXBairro() + "</tomaTerceiroenderTomaxBairro>"
                    + "<tomaTerceiroenderTomaxMun>" + tomaTerceiro.getEnderToma().getXMun() + "</tomaTerceiroenderTomaxMun>"
                    + "<tomaTerceiroenderTomaCEP>" + tomaTerceiro.getEnderToma().getCEP() + "</tomaTerceiroenderTomaCEP>"
                    + "<tomaTerceiroenderTomaUF>" + tomaTerceiro.getEnderToma().getUF().value() + "</tomaTerceiroenderTomaUF>";
            
            xmlNfe.setXML_Envio(xml.replace("</GTVe>", ComplementoXml + "</GTVe>"));
             */
            InputSource is = new InputSource(new StringReader(RetornoString));
            DocumentBuilder dBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            Document doc = dBuilder.parse(is);

            xmlNfe.setXML_Retorno(RetornoString);
            xmlNfe.setChaveNFE(ProcuraTag(doc, "infProt", "chCTe", 0, false));
            xmlNfe.setProtocolo(ProcuraTag(doc, "infProt", "nProt", 0, false));
            xmlNfe.setStatus(ProcuraTag(doc, "infProt", "cStat", 0, false));
            xmlNfe.setDt_Retorno(getDataAtual("SQL"));
            xmlNfe.setHr_Retorno(getDataAtual("HORA"));

            xmlNfeDao.inserirXMLNFE(xmlNfe, persistenciaSatExcel);

            // Retorno para Cliente
            String LinkViewGuia = "https://mobile.sasw.com.br/SatMobWeb/guia/guia_integracao.html?nProt=" + ProcuraTag(doc, "infProt", "chCTe", 0, false) + "&DtRef=" + getDataAtual("SQL");
            retorno.put("status", "ok");
            retorno.put("resp", RetornoString.replace("</retGTVe>", "<linkViewGuia>" + LinkViewGuia + "</linkViewGuia></retGTVe>"));

            // Código de Rotas em StandBy, por enquanto
            /*List<RotasGTVe> rotaGTVeList = null;
            if (rotaGTVeList == null) {
                throw new RotaException(new RotaErrorCode(0));
            }

            for (RotasGTVe rotaGTVe : rotaGTVeList) {
                processamento = new HashMap<>();

                try {
                    rota = rotaFromRotasGTVe(rotaGTVe);
                    processamento.put("rota", rota.getRota());

                    sequencia = this.rotasDao.buscarSeqRota(rota.getCodFil().toPlainString(), rota.getRota(), rota.getData(), this.persistencia);
                    if (sequencia == null) {
                        // inserir rota

                        try {
                            sequencia = this.rotasDao.inserirRotaSequencia(rota, this.persistencia);
                        } catch (Exception insertRota) {
                            throw new RotaException(insertRota.getMessage(), new RotaErrorCode(0));
                        }
                    }

                    rota.setSequencia(sequencia);
                    trajetosProcessado = new ArrayList<>();

                    for (TrajetosGTVe trajetoGTVe : rotaGTVe.getTrajetos()) {
                        processamentoTrajeto = new HashMap<>();
                        try {
                            trajeto = trajetoFromTrajetosGTVe(trajetoGTVe, rota);
                            processamentoTrajeto.put("parada", trajeto.getParada());

                            if (this.rt_PercDao.existeRt_Perc(trajeto.getSequencia().toString(), trajeto.getParada(), this.persistencia)) {
                                throw new RotaException(new RotaErrorCode(6));
                            } else {
                                if (trajeto.getER().equals("E")) {
                                    pedido = obterPedido(trajeto);
                                    cxForte = this.cxForteDao.getCxForte(trajeto.getCodFil(), this.persistencia);
                                    pedido.setCodCli1(cxForte.getCodCli());
                                } else if (trajeto.getER().equals("R")) {
                                    pedido = obterPedido(trajeto);
                                } else {
                                    pedido = null;
                                }

                                if (pedido != null) {
                                    numero = this.pedidoDao.inserirPedidoNumero(pedido, this.persistencia);
                                    trajeto.setPedido(numero);
                                } else {
                                    trajeto.setPedido("0");
                                }

                                // insere trajeto
                                trajeto.setDt_Incl(LocalDate.now());
                                trajeto.setHr_Incl(getDataAtual("HORA"));
                                trajeto.setOperIncl(RecortaAteEspaço("SatWebService", 0, 10));
                                try {
                                    this.rt_PercDao.inserirTrajeto(trajeto, this.persistencia);

                                    processamentoTrajeto.put("result", new RotaException(new RotaErrorCode(1)));
                                } catch (Exception insertTrajeto) {
                                    throw new RotaException(insertTrajeto.getMessage(), new RotaErrorCode(-1));
                                }
                            }

                        } catch (RotaException e) {
                            processamentoTrajeto.put("result", e);
                        } catch (Exception e) {
                            processamentoTrajeto.put("result", new RotaException(e.getMessage(), new RotaErrorCode(-1)));
                        }

                        trajetosProcessado.add(processamentoTrajeto);
                    }
                    processamento.put("trajetos", trajetosProcessado);
                } catch (RotaException e) {
                    processamento.put("result", e);
                } catch (Exception e) {
                    processamento.put("result", e.getMessage());
                }

                processamentos.add(processamento);
            }

            retorno.put("status", "ok");
            retorno.put("resp", processamentos);*/
        } catch (Exception e) {
            // Salvando em log o que foi mandado
            retorno.clear();
            retorno.put("status", "error");
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);
        } finally {
            this.logerro.Grava(gson.toJson(retorno), this.caminho);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gson.toJson(retorno))
                    .build();
        }
    }

    private Rotas rotaFromRotasGTVe(RotasGTVe rotaGTVe) {
        Rotas rota = new Rotas();

        rota.setCodFil(String.valueOf(rotaGTVe.getCodfil()));
        rota.setRota(rotaGTVe.getNumero());
        rota.setData(rotaGTVe.getData());
        rota.setDtFim(rotaGTVe.getData());
        rota.setHrLargada(rotaGTVe.getHoraini());
        rota.setHrChegada(rotaGTVe.getHorafim());
        rota.setHrIntIni("00:00");
        rota.setHrIntFim("00:00");
        rota.setFlag_Excl("");
        rota.setTpVeic("F");
        rota.setViagem("N");
        rota.setBACEN("N");
        rota.setAeroporto("N");
        rota.setATM("N");
        rota.setOperador(FuncoesString.RecortaAteEspaço("SatWebService", 0, 10));
        rota.setDt_Alter(LocalDate.now());
        rota.setHr_Alter(getDataAtual("HORA"));

        return rota;
    }

    private List<RotasGTVe> obterListaRotas(String input) throws RotaException {

        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(new TypeToken<StringMap>() {
        }.getType(), new MapDeserializer());
        Gson gson = gsonBuilder.create();

        List<RotasGTVe> retorno = new ArrayList<>();
        StringMap rotasJson = gson.fromJson(input, StringMap.class);
        Object rotasObject = rotasJson.get("rotas");
        List<StringMap> rotasList = null;

        try {
            rotasList = (ArrayList) rotasObject;
        } catch (Exception e) {
            throw new RotaException(new RotaErrorCode(0));
        }

        RotasGTVe rota;
        for (StringMap rotaObject : rotasList) {
            rota = obterRota(rotaObject);
            rota.setTrajetos(obterTrajetos(rotaObject));

            retorno.add(rota);
        }

        return retorno;
    }

    private List<TrajetosGTVe> obterTrajetos(StringMap stringMap) throws RotaException {

        List<TrajetosGTVe> retorno = new ArrayList<>();

        Object trajetosObject = stringMap.get("trajetos");
        List<StringMap> trajestoList = null;

        try {
            trajestoList = (ArrayList) trajetosObject;
        } catch (Exception e) {
            throw new RotaException("trajetos", new RotaErrorCode(0));
        }

        TrajetosGTVe trajeto;
        for (StringMap trajetoObject : trajestoList) {
            trajeto = obterTrajeto(trajetoObject);
            trajeto.setEmissor(obterCliente(trajetoObject, "emissor"));
            trajeto.setOrigem(obterCliente(trajetoObject, "origem"));
            if (trajeto.getTipo().equals("R")) {
                trajeto.setDestino(obterCliente(trajetoObject, "destino"));
            }
            trajeto.setVeiculo(obterVeiculo(trajetoObject));
            trajeto.setGuias(obterGuias(trajetoObject));
            retorno.add(trajeto);
        }

        return retorno;
    }

    private RotasGTVe obterRota(StringMap stringMap) throws RotaException {
        RotasGTVe rota = new RotasGTVe();

        try {
            rota.setAcao((int) stringMap.get("acao"));
            if (rota.getAcao() != 1 && rota.getAcao() != 3) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("rotas.acao", new RotaErrorCode(9));
        }

        try {
            rota.setCodfil((int) stringMap.get("codfil"));
            if (!Validacoes.validacaoInteiro(rota.getCodfil(), 9999)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("rotas.codfil", new RotaErrorCode(9));
        }

        try {
            rota.setNumero(stringMap.get("numero").toString());
            if (!Validacoes.validacaoInteiro(rota.getNumero(), 3)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("rotas.numero", new RotaErrorCode(9));
        }

        try {
            rota.setData(stringMap.get("data").toString());
            // 2020-09-01T12:16:40
//            if (!Validacoes.validacaoData(rota.getData(), "yyyy-MM-dd'T'HH:mm:ss")) {
            if (!Validacoes.validacaoData(rota.getData(), "yyyy-MM-dd")) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("rotas.data", new RotaErrorCode(9));
        }

        try {
            rota.setHoraini(stringMap.get("horaini").toString());
            if (!Validacoes.validacaoHora(rota.getHoraini(), "HH:mm")) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("rotas.horaini", new RotaErrorCode(9));
        }

        try {
            rota.setHorafim(stringMap.get("horafim").toString());
            if (!Validacoes.validacaoHora(rota.getHorafim(), "HH:mm")) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("rotas.horafim", new RotaErrorCode(9));
        }

        return rota;
    }

    private TrajetosGTVe obterTrajeto(StringMap stringMap) throws RotaException {

        TrajetosGTVe trajeto = new TrajetosGTVe();

        try {
            trajeto.setTipo(stringMap.get("tipo").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getTipo(), 1, true)
                    || (!trajeto.getTipo().equals("E") && !trajeto.getTipo().equals("R") && !trajeto.getTipo().equals("T"))) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.tipo", new RotaErrorCode(9));
        }

        try {
            trajeto.setClassificacao(stringMap.get("classificacao").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getClassificacao(), 1, true)
                    || (!trajeto.getClassificacao().equals("V") && !trajeto.getClassificacao().equals("E") && !trajeto.getClassificacao().equals("R") && !trajeto.getClassificacao().equals("A"))) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.classificacao", new RotaErrorCode(9));
        }

        try {
            trajeto.setCodcliorigem(stringMap.get("codcliorigem").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getCodcliorigem(), 7, false)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.codcliorigem", new RotaErrorCode(9));
        }

        try {
            trajeto.setCodclidestino(stringMap.get("codclidestino").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getCodclidestino(), 7, false)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.codclidestino", new RotaErrorCode(9));
        }

        try {
            trajeto.setParada((int) stringMap.get("parada"));
            if (!Validacoes.validacaoInteiro(trajeto.getParada(), 999)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.parada", new RotaErrorCode(9));
        }

        try {
            trajeto.setHora(stringMap.get("hora").toString());
            if (!Validacoes.validacaoHora(trajeto.getHora(), "HH:mm")) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.hora", new RotaErrorCode(9));
        }

        if (trajeto.getTipo().equals("R")) {
            try {
                trajeto.setParadadestino((int) stringMap.get("paradadestino"));
                if (!Validacoes.validacaoInteiro(trajeto.getParadadestino(), 999)) {
                    throw new Exception();
                }
            } catch (Exception e) {
                throw new RotaException("trajetos.paradadestino", new RotaErrorCode(9));
            }

            try {
                trajeto.setHoradestino(stringMap.get("horadestino").toString());
                if (!Validacoes.validacaoHora(trajeto.getHoradestino(), "HH:mm")) {
                    throw new Exception();
                }
            } catch (Exception e) {
                throw new RotaException("trajetos.horadestino", new RotaErrorCode(9));
            }
        }

        try {
            trajeto.setPedidocliente(stringMap.get("pedidocliente").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getPedidocliente(), 20, false)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.pedidocliente", new RotaErrorCode(9));
        }

        try {
            trajeto.setValor(new BigDecimal(stringMap.get("valor").toString()));
            if (!Validacoes.validacaoValor(trajeto.getValor(), "999999999999999", 2)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.valor", new RotaErrorCode(9));
        }

        try {
            trajeto.setMoeda(stringMap.get("moeda").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getMoeda(), 3, true)
                    || (!trajeto.getMoeda().equals("BRL")
                    && !trajeto.getMoeda().equals("MXN")
                    && !trajeto.getMoeda().equals("USD")
                    && !trajeto.getMoeda().equals("EUR")
                    && !trajeto.getMoeda().equals("GBP")
                    && !trajeto.getMoeda().equals("IEN")
                    && !trajeto.getMoeda().equals("OUT"))) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.moeda", new RotaErrorCode(9));
        }

        try {
            trajeto.setObservacao(stringMap.get("observacao").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getObservacao(), 120, trajeto.getMoeda().equals("OUT"))) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.observacao", new RotaErrorCode(9));
        }

        return trajeto;
    }

    private ClientesGTVe obterCliente(StringMap stringMap, String c) throws RotaException {

        try {
            stringMap = (StringMap) stringMap.get(c);
        } catch (Exception e) {
            throw new RotaException(c, new RotaErrorCode(0));
        }

        ClientesGTVe cliente = new ClientesGTVe();

        try {
            cliente.setCpf(stringMap.getOrDefault("cpf", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getCpf(), 11, false)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".cpf", new RotaErrorCode(9));
        }

        try {
            cliente.setCnpj(stringMap.getOrDefault("cnpj", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getCnpj(), 14, false)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".cnpj", new RotaErrorCode(9));
        }

        if (cliente.getCnpj().equals("") && cliente.getCpf().equals("")) {
            throw new RotaException(c + ".cnpj", new RotaErrorCode(9));
        }

        try {
            cliente.setRazaosocial(stringMap.getOrDefault("razaosocial", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getRazaosocial(), 60, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".razaosocial", new RotaErrorCode(9));
        }

        try {
            cliente.setFantasia(stringMap.getOrDefault("fantasia", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getFantasia(), 40, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".fantasia", new RotaErrorCode(9));
        }

        try {
            cliente.setEndereco(stringMap.getOrDefault("endereco", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getEndereco(), 45, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".endereco", new RotaErrorCode(9));
        }

        try {
            cliente.setBairro(stringMap.getOrDefault("bairro", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getBairro(), 25, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".bairro", new RotaErrorCode(9));
        }

        try {
            cliente.setNumero(stringMap.getOrDefault("numero", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getNumero(), 15, false)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".numero", new RotaErrorCode(9));
        }

        try {
            cliente.setCidade(stringMap.getOrDefault("cidade", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getCidade(), 25, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".cidade", new RotaErrorCode(9));
        }

        try {
            cliente.setUf(stringMap.getOrDefault("uf", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getUf(), 2, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".uf", new RotaErrorCode(9));
        }

        try {
            cliente.setCodcidade(stringMap.getOrDefault("codcidade", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getCodcidade(), 7, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".codcidade", new RotaErrorCode(9));
        }

        try {
            cliente.setCep(stringMap.getOrDefault("cep", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getCep(), 8, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".cep", new RotaErrorCode(9));
        }

        try {
            cliente.setTelefone(stringMap.getOrDefault("telefone", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getTelefone(), 11, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".telefone", new RotaErrorCode(9));
        }

        try {
            cliente.setEmail(stringMap.getOrDefault("email", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getEmail(), 80, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".email", new RotaErrorCode(9));
        }

        try {
            cliente.setIe(stringMap.getOrDefault("ie", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getIe(), 20, false)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".ie", new RotaErrorCode(9));
        }

        try {
            cliente.setLatitude(stringMap.getOrDefault("latitude", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getLatitude(), 20, false)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".latitude", new RotaErrorCode(9));
        }

        try {
            cliente.setLongitude(stringMap.getOrDefault("longitude", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getLongitude(), 20, false)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".longitude", new RotaErrorCode(9));
        }

        if (c.equals("emissor")) {
            EmissorGTVe emissor = new EmissorGTVe(cliente);
            try {
                emissor.setPais((int) stringMap.get("pais"));
                if (!Validacoes.validacaoInteiro(emissor.getPais(), 9999)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException(c + ".pais", new RotaErrorCode(9));
            }

            try {
                emissor.setCodigofat(stringMap.getOrDefault("codigofat", "").toString());
                if (!Validacoes.validacaoTamanho(emissor.getCodigofat(), 20, false)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException(c + ".codigofat", new RotaErrorCode(9));
            }

            return emissor;
        } else {
            return cliente;
        }
    }

    private VeiculosGTVe obterVeiculo(StringMap stringMap) throws RotaException {

        try {
            stringMap = (StringMap) stringMap.get("veiculo");
        } catch (Exception e) {
            throw new RotaException("veiculo", new RotaErrorCode(0));
        }

        VeiculosGTVe veiculo = new VeiculosGTVe();

        try {
            veiculo.setTipo(stringMap.get("tipo").toString());
            if (!Validacoes.validacaoTamanho(veiculo.getTipo(), 1, true)
                    || (!veiculo.getTipo().equals("A") && !veiculo.getTipo().equals("B") && !veiculo.getTipo().equals("F") && !veiculo.getTipo().equals("L") && !veiculo.getTipo().equals("P"))) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("veiculo.tipo", new RotaErrorCode(9));
        }

        try {
            veiculo.setPlaca(stringMap.get("placa").toString());
            if (!Validacoes.validacaoTamanho(veiculo.getPlaca(), 12, true)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("veiculo.placa", new RotaErrorCode(9));
        }

        try {
            veiculo.setUf(stringMap.get("uf").toString());
            if (!Validacoes.validacaoTamanho(veiculo.getUf(), 2, true)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("veiculo.uf", new RotaErrorCode(9));
        }

        return veiculo;
    }

    private Pedido obterPedido(Rt_Perc trajeto) throws RotaException {
        Pedido pedido = new Pedido();
        pedido.setSeqRota(trajeto.getSequencia().toBigInteger().toString());

        String hora1 = "08:00", hora2 = "18:00";
        try {
            hora1 = trajeto.getHora1();
            hora2 = LocalTime.parse(hora1, DateTimeFormatter.ofPattern("HH:mm")).plusMinutes(30).format(DateTimeFormatter.ofPattern("HH:mm"));
        } catch (Exception e) {
            throw new RotaException("janela", new RotaErrorCode(9));
        }

        if (trajeto.getER().equals("E")) {
            pedido.setCodCli1("");
            pedido.setCodCli2(trajeto.getCodCli1());
            pedido.setHora1O("08:00");
            pedido.setHora2O("18:00");
            pedido.setHora1D(hora1);
            pedido.setHora2D(hora2);
        } else {
            pedido.setCodCli1(trajeto.getCodCli1());
            pedido.setCodCli2(trajeto.getCodCli2());
            pedido.setHora1O(hora1);
            pedido.setHora2O(hora2);
            pedido.setHora1D("08:00");
            pedido.setHora2D("18:00");
        }

        pedido.setCodFil(trajeto.getCodFil());
        pedido.setData(trajeto.getData());
        pedido.setParada(trajeto.getParada());
        pedido.setClassifSrv(trajeto.getTipoSrv());
        pedido.setTipo("T");
        pedido.setPedidoCliente(trajeto.getPedidoCliente());
        pedido.setValor(trajeto.getValor());
        pedido.setTipoMoeda(trajeto.getMoeda());
        pedido.setObs(RecortaString(trajeto.getObserv(), 0, 80));
        pedido.setOS(trajeto.getOS().toBigInteger().toString());

        pedido.setFlag_Excl("");
        pedido.setOperIncl(RecortaAteEspaço("SatWebService", 0, 10));
        pedido.setDt_Incl(getDataAtual("SQL"));
        pedido.setHr_Incl(getDataAtual("HORA"));

        pedido.setOperador(RecortaAteEspaço("SatWebService", 0, 10));
        pedido.setDt_Alter(getDataAtual("SQL"));
        pedido.setHr_Alter(getDataAtual("HORA"));

        return pedido;
    }

    private Rt_Perc trajetoFromTrajetosGTVe(TrajetosGTVe trajetoGTVe, Rotas rota) {

        Rt_Perc trajeto = new Rt_Perc();
        trajeto.setSequencia(rota.getSequencia().toPlainString());

        trajeto.setCodFil(rota.getCodFil().toPlainString());
        trajeto.setParada(trajetoGTVe.getParada());
        trajeto.setHora1(trajetoGTVe.getHora());
        trajeto.setER(trajetoGTVe.getTipo());
        trajeto.setTipoSrv(trajetoGTVe.getClassificacao());
        trajeto.setPedidoCliente(trajetoGTVe.getPedidocliente());
        trajeto.setValor(trajetoGTVe.getValor().toPlainString());
        trajeto.setMoeda(trajetoGTVe.getMoeda());
        trajeto.setObserv(trajetoGTVe.getObservacao());
        trajeto.setData(rota.getData());
        trajeto.setFlag_Excl("");
        trajeto.setOperador(FuncoesString.RecortaAteEspaço("SatWebService", 0, 10));
        trajeto.setDt_Alter(LocalDate.now());
        trajeto.setHr_Alter(getDataAtual("HORA"));

        Clientes emissor = null;
        try {
            // Buscando primeiro pelo código informado se informado.
            if (emissor == null) {
                try {
                    if (((EmissorGTVe) trajetoGTVe.getEmissor()).getCodigofat() != null
                            && !((EmissorGTVe) trajetoGTVe.getEmissor()).getCodigofat().equals("")) {
                        emissor = this.clientesDao.buscarClienteImportacao(((EmissorGTVe) trajetoGTVe.getEmissor()).getCodigofat(),
                                rota.getCodFil().toPlainString(), this.persistencia);
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo nome, nred e endereço.
            if (emissor == null) {
                try {
                    emissor = this.clientesDao.buscarClienteNomeEndereco(trajetoGTVe.getEmissor().getRazaosocial(),
                            trajetoGTVe.getEmissor().getFantasia(),
                            trajetoGTVe.getEmissor().getEndereco(),
                            rota.getCodFil().toPlainString(),
                            this.persistencia);
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo nome e nred.
            if (emissor == null) {
                try {
                    emissor = this.clientesDao.buscarClienteNome(trajetoGTVe.getEmissor().getRazaosocial(),
                            trajetoGTVe.getEmissor().getFantasia(),
                            rota.getCodFil().toPlainString(),
                            this.persistencia);
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo cnpj.
            if (emissor == null) {
                try {
                    if (trajetoGTVe.getEmissor().getCnpj() != null
                            && !trajetoGTVe.getEmissor().getCnpj().equals("")) {
                        emissor = this.clientesDao.buscarClienteCPNJ(trajetoGTVe.getEmissor().getCnpj(),
                                trajetoGTVe.getEmissor().getFantasia(),
                                rota.getCodFil().toPlainString(), this.persistencia);
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo cpf.
            if (emissor == null) {
                try {
                    if (trajetoGTVe.getEmissor().getCpf() != null
                            && !trajetoGTVe.getEmissor().getCpf().equals("")) {
                        emissor = this.clientesDao.buscarClienteCPF(trajetoGTVe.getEmissor().getCpf(),
                                trajetoGTVe.getEmissor().getFantasia(),
                                rota.getCodFil().toPlainString(), this.persistencia);
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, inserir com as informações enviadas.
            if (emissor == null) {
                try {
                    emissor = new Clientes();
                    emissor.setCodFil(rota.getCodFil());
                    emissor.setCodExt(((EmissorGTVe) trajetoGTVe.getEmissor()).getCodigofat());
                    emissor.setCGC(trajetoGTVe.getEmissor().getCnpj());
                    emissor.setCPF(trajetoGTVe.getEmissor().getCpf());
                    emissor.setNome(trajetoGTVe.getEmissor().getRazaosocial());
                    emissor.setNRed(trajetoGTVe.getEmissor().getFantasia());
                    emissor.setEnde(trajetoGTVe.getEmissor().getEndereco() + trajetoGTVe.getEmissor().getNumero());
                    emissor.setBairro(FuncoesString.removeAcento(trajetoGTVe.getEmissor().getBairro()));
                    emissor.setCidade(trajetoGTVe.getEmissor().getCidade());
                    emissor.setEstado(trajetoGTVe.getEmissor().getUf());
                    emissor.setCodCidade(trajetoGTVe.getEmissor().getCodcidade());
                    emissor.setCEP(trajetoGTVe.getEmissor().getCep());
                    emissor.setFone1(trajetoGTVe.getEmissor().getTelefone());
                    emissor.setEmail(trajetoGTVe.getEmissor().getEmail());
                    emissor.setIE(trajetoGTVe.getEmissor().getIe());
                    emissor.setLatitude(trajetoGTVe.getEmissor().getLatitude());

                    emissor.setOper_Inc("SatWebServ");
                    emissor.setDt_Alter(LocalDate.now());
                    emissor.setHr_Alter(getDataAtual("HORA"));
                    emissor.setSituacao("A");
                    emissor.setRegiao("999");

                    int contador = 0;
                    while (contador <= 30) {
                        try {
                            String codigo = this.clientesDao.getCodCliBancoTpCli(emissor.getCodFil(), "777", "0", this.persistencia);

                            emissor.setTpCli("0");
                            emissor.setBanco(codigo.substring(0, 3));
                            emissor.setCodCli(codigo.substring(4, 7));
                            emissor.setCodigo(codigo);
                            emissor.setAgencia(codigo.substring(3, 7));
                            emissor = (Clientes) removeAcentoObjeto(emissor);

                            this.clientesDao.inserir(emissor, this.persistencia);
                            break;
                        } catch (Exception ex) {
                        } finally {
                            contador++;
                        }
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }
        } catch (Exception eEmissor) {
            this.logerro.Grava(eEmissor.getMessage(), this.caminho);
        }
        trajeto.setCliFat(emissor.getCodigo());

        Clientes origem = null;
        try {
            // Buscando primeiro pelo código informado se informado.
            if (origem == null) {
                try {
                    if (trajetoGTVe.getCodcliorigem() != null
                            && !trajetoGTVe.getCodcliorigem().equals("")) {
                        origem = this.clientesDao.buscarClienteImportacao(trajetoGTVe.getCodcliorigem(), rota.getCodFil().toPlainString(), this.persistencia);
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo nome, nred e endereço.
            if (origem == null) {
                try {
                    origem = this.clientesDao.buscarClienteNomeEndereco(trajetoGTVe.getOrigem().getRazaosocial(),
                            trajetoGTVe.getOrigem().getFantasia(),
                            trajetoGTVe.getOrigem().getEndereco(),
                            rota.getCodFil().toPlainString(),
                            this.persistencia);
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo nome e nred.
            if (origem == null) {
                try {
                    origem = this.clientesDao.buscarClienteNome(trajetoGTVe.getOrigem().getRazaosocial(),
                            trajetoGTVe.getOrigem().getFantasia(),
                            rota.getCodFil().toPlainString(),
                            this.persistencia);
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo cnpj.
            if (origem == null) {
                try {
                    if (trajetoGTVe.getOrigem().getCnpj() != null
                            && !trajetoGTVe.getOrigem().getCnpj().equals("")) {
                        origem = this.clientesDao.buscarClienteCPNJ(trajetoGTVe.getOrigem().getCnpj(),
                                trajetoGTVe.getOrigem().getFantasia(),
                                rota.getCodFil().toPlainString(), this.persistencia);
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo cpf.
            if (origem == null) {
                try {
                    if (trajetoGTVe.getOrigem().getCpf() != null
                            && !trajetoGTVe.getOrigem().getCpf().equals("")) {
                        origem = this.clientesDao.buscarClienteCPF(trajetoGTVe.getOrigem().getCpf(),
                                trajetoGTVe.getOrigem().getFantasia(),
                                rota.getCodFil().toPlainString(), this.persistencia);
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, inserir com as informações enviadas.
            if (origem == null) {
                try {
                    origem = new Clientes();
                    origem.setCodFil(rota.getCodFil());
                    origem.setCodExt(trajetoGTVe.getCodcliorigem());
                    origem.setCGC(trajetoGTVe.getOrigem().getCnpj());
                    origem.setCPF(trajetoGTVe.getOrigem().getCpf());
                    origem.setNome(trajetoGTVe.getOrigem().getRazaosocial());
                    origem.setNRed(trajetoGTVe.getOrigem().getFantasia());
                    origem.setEnde(trajetoGTVe.getOrigem().getEndereco() + trajetoGTVe.getOrigem().getNumero());
                    origem.setBairro(FuncoesString.removeAcento(trajetoGTVe.getOrigem().getBairro()));
                    origem.setCidade(trajetoGTVe.getOrigem().getCidade());
                    origem.setEstado(trajetoGTVe.getOrigem().getUf());
                    origem.setCodCidade(trajetoGTVe.getOrigem().getCodcidade());
                    origem.setCEP(trajetoGTVe.getOrigem().getCep());
                    origem.setFone1(trajetoGTVe.getOrigem().getTelefone());
                    origem.setEmail(trajetoGTVe.getOrigem().getEmail());
                    origem.setIE(trajetoGTVe.getOrigem().getIe());
                    origem.setLatitude(trajetoGTVe.getOrigem().getLatitude());

                    origem.setOper_Inc("SatWebServ");
                    origem.setDt_Alter(LocalDate.now());
                    origem.setHr_Alter(getDataAtual("HORA"));
                    origem.setSituacao("A");
                    origem.setRegiao("999");

                    int contador = 0;
                    while (contador <= 30) {
                        try {
                            String codigo = this.clientesDao.getCodCliBancoTpCli(origem.getCodFil(), "777", "0", this.persistencia);

                            origem.setTpCli("0");
                            origem.setBanco(codigo.substring(0, 3));
                            origem.setCodCli(codigo.substring(4, 7));
                            origem.setCodigo(codigo);
                            origem.setAgencia(codigo.substring(3, 7));
                            origem = (Clientes) removeAcentoObjeto(origem);

                            this.clientesDao.inserir(origem, this.persistencia);
                            break;
                        } catch (Exception ex) {
                        } finally {
                            contador++;
                        }
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }
        } catch (Exception eOrigem) {
            this.logerro.Grava(eOrigem.getMessage(), this.caminho);
        }
        trajeto.setCodCli1(origem.getCodigo());

        Clientes destino = null;
        if (trajeto.getER().equals("R")) {
            try {
                // Buscando primeiro pelo código informado se informado.
                if (destino == null) {
                    try {
                        if (trajetoGTVe.getCodclidestino() != null
                                && !trajetoGTVe.getCodclidestino().equals("")) {
                            destino = this.clientesDao.buscarClienteImportacao(trajetoGTVe.getCodclidestino(), rota.getCodFil().toPlainString(), this.persistencia);
                        }
                    } catch (Exception e) {
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                }

                // Se não encontrar, buscando pelo nome, nred e endereço.
                if (destino == null) {
                    try {
                        destino = this.clientesDao.buscarClienteNomeEndereco(trajetoGTVe.getDestino().getRazaosocial(),
                                trajetoGTVe.getDestino().getFantasia(),
                                trajetoGTVe.getDestino().getEndereco(),
                                rota.getCodFil().toPlainString(),
                                this.persistencia);
                    } catch (Exception e) {
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                }

                // Se não encontrar, buscando pelo nome e nred.
                if (destino == null) {
                    try {
                        destino = this.clientesDao.buscarClienteNome(trajetoGTVe.getDestino().getRazaosocial(),
                                trajetoGTVe.getDestino().getFantasia(),
                                rota.getCodFil().toPlainString(),
                                this.persistencia);
                    } catch (Exception e) {
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                }

                // Se não encontrar, buscando pelo cnpj.
                if (destino == null) {
                    try {
                        if (trajetoGTVe.getDestino().getCnpj() != null
                                && !trajetoGTVe.getDestino().getCnpj().equals("")) {
                            destino = this.clientesDao.buscarClienteCPNJ(trajetoGTVe.getDestino().getCnpj(),
                                    trajetoGTVe.getDestino().getFantasia(),
                                    rota.getCodFil().toPlainString(), this.persistencia);
                        }
                    } catch (Exception e) {
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                }

                // Se não encontrar, buscando pelo cpf.
                if (destino == null) {
                    try {
                        if (trajetoGTVe.getDestino().getCpf() != null
                                && !trajetoGTVe.getDestino().getCpf().equals("")) {
                            destino = this.clientesDao.buscarClienteCPF(trajetoGTVe.getDestino().getCpf(),
                                    trajetoGTVe.getDestino().getFantasia(),
                                    rota.getCodFil().toPlainString(), this.persistencia);
                        }
                    } catch (Exception e) {
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                }

                // Se não encontrar, inserir com as informações enviadas.
                if (destino == null) {
                    try {
                        destino = new Clientes();
                        destino.setCodFil(rota.getCodFil());
                        destino.setCodExt(trajetoGTVe.getCodclidestino());
                        destino.setCGC(trajetoGTVe.getDestino().getCnpj());
                        destino.setCPF(trajetoGTVe.getDestino().getCpf());
                        destino.setNome(trajetoGTVe.getDestino().getRazaosocial());
                        destino.setNRed(trajetoGTVe.getDestino().getFantasia());
                        destino.setEnde(trajetoGTVe.getDestino().getEndereco() + trajetoGTVe.getDestino().getNumero());
                        destino.setBairro(FuncoesString.removeAcento(trajetoGTVe.getDestino().getBairro()));
                        destino.setCidade(trajetoGTVe.getDestino().getCidade());
                        destino.setEstado(trajetoGTVe.getDestino().getUf());
                        destino.setCodCidade(trajetoGTVe.getDestino().getCodcidade());
                        destino.setCEP(trajetoGTVe.getDestino().getCep());
                        destino.setFone1(trajetoGTVe.getDestino().getTelefone());
                        destino.setEmail(trajetoGTVe.getDestino().getEmail());
                        destino.setIE(trajetoGTVe.getDestino().getIe());
                        destino.setLatitude(trajetoGTVe.getDestino().getLatitude());

                        destino.setOper_Inc("SatWebServ");
                        destino.setDt_Alter(LocalDate.now());
                        destino.setHr_Alter(getDataAtual("HORA"));
                        destino.setSituacao("A");
                        destino.setRegiao("999");

                        int contador = 0;
                        while (contador <= 30) {
                            try {
                                String codigo = this.clientesDao.getCodCliBancoTpCli(destino.getCodFil(), "777", "0", this.persistencia);

                                destino.setTpCli("0");
                                destino.setBanco(codigo.substring(0, 3));
                                destino.setCodCli(codigo.substring(4, 7));
                                destino.setCodigo(codigo);
                                destino.setAgencia(codigo.substring(3, 7));
                                destino = (Clientes) removeAcentoObjeto(destino);

                                this.clientesDao.inserir(destino, this.persistencia);
                                break;
                            } catch (Exception ex) {
                            } finally {
                                contador++;
                            }
                        }
                    } catch (Exception e) {
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                }
            } catch (Exception eDestino) {
                this.logerro.Grava(eDestino.getMessage(), this.caminho);
            }
            trajeto.setCodCli2(destino.getCodigo());

            trajeto.setDPar(trajetoGTVe.getParadadestino());
            trajeto.setHora1D(trajetoGTVe.getHoradestino());
        } else {
            destino = origem;
        }

        String os = "0";
        try {
            os = this.os_VigDao.obterOS(origem.getCodigo(), destino.getCodigo(), trajeto.getCodFil().toPlainString(), this.persistencia);
        } catch (Exception eDestino) {
            this.logerro.Grava(eDestino.getMessage(), this.caminho);
        }

        if (os.equals("0")) {
            try {
                OS_VigDao.inserirOsReduzida(
                        origem.getCodFil().toString(),
                        origem.getNRed(),
                        "1",
                        "001",
                        origem.getCodigo(), //Origem
                        origem.getNRed(), //Origem
                        destino.getCodigo(), //Destino
                        destino.getNRed(), //Destino
                        emissor.getCodigo(), //Faturar
                        emissor.getNRed(), //osvig.getNRedFat(), //Faturar
                        "N",
                        "999.O001.1", //osvig.getContrato(), //Contrato
                        "9999", //osvig.getAgrupador().toString(), //Agrupador
                        "9999", //osvig.getOSGrp().toString(),
                        "A",
                        origem.getOper_Inc(),
                        origem.getDt_Alter().toString(),
                        origem.getHr_Alter(),
                        origem.getOper_Alt(),
                        origem.getDt_Alter().toString(),
                        origem.getHr_Alter(),
                        "",
                        "",
                        "999",
                        "0001",
                        "",
                        this.persistencia);
                os = this.os_VigDao.obterOS(origem.getCodigo(), destino.getCodigo(), trajeto.getCodFil().toPlainString(), this.persistencia);
            } catch (Exception eDestino) {
                this.logerro.Grava(eDestino.getMessage(), this.caminho);
            }
        }
        trajeto.setOS(os);

        return trajeto;

    }

    private List<GuiasGTVe> obterGuias(StringMap stringMap) throws RotaException {
        List<GuiasGTVe> retorno = new ArrayList<>();

        Object guiasObject = stringMap.get("guias");
        List<StringMap> guiasList = null;

        try {
            guiasList = (ArrayList) guiasObject;
        } catch (Exception e) {
            throw new RotaException("guias", new RotaErrorCode(0));
        }

        GuiasGTVe guia;
        for (StringMap guiaObject : guiasList) {
            guia = new GuiasGTVe();

            try {
                guia.setNumero(guiaObject.getOrDefault("numero", "").toString());
                if (!Validacoes.validacaoInteiro(guia.getNumero(), 20)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("guia.numero", new RotaErrorCode(9));
            }

            try {
                guia.setSerie(guiaObject.get("serie").toString());
                if (!Validacoes.validacaoTamanho(guia.getNumero(), 3, true)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("guia.serie", new RotaErrorCode(9));
            }

            guia.setVolumes(obterVolumes(guiaObject));
            guia.setComposicao(obterComposicoes(guiaObject));

            retorno.add(guia);
        }

        return retorno;
    }

    private List<VolumesGTVe> obterVolumes(StringMap stringMap) throws RotaException {

        List<VolumesGTVe> retorno = new ArrayList<>();

        Object volumesObject = stringMap.get("volumes");
        List<StringMap> volumesList = null;

        try {
            volumesList = (ArrayList) volumesObject;
        } catch (Exception e) {
            throw new RotaException("volumes", new RotaErrorCode(0));
        }

        VolumesGTVe volume;
        for (StringMap volumeObject : volumesList) {
            volume = new VolumesGTVe();

            try {
                volume.setLacre(volumeObject.get("lacre").toString());
                if (!Validacoes.validacaoTamanho(volume.getLacre(), 20, true)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("volume.lacre", new RotaErrorCode(9));
            }

            try {
                volume.setEspecie((int) volumeObject.get("especie"));
                if (!Validacoes.validacaoInteiro(volume.getEspecie(), 1)
                        || (volume.getEspecie() != 1 && volume.getEspecie() != 2 && volume.getEspecie() != 3 && volume.getEspecie() != 4)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("volume.especie", new RotaErrorCode(9));
            }

            try {
                volume.setMoeda(volumeObject.get("moeda").toString());
                if (!Validacoes.validacaoTamanho(volume.getMoeda(), 3, true)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("volume.moeda", new RotaErrorCode(9));
            }

            try {
                volume.setValor(new BigDecimal(stringMap.get("valor").toString()));
                if (!Validacoes.validacaoValor(volume.getValor(), "999999999999999", 2)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("volume.valor", new RotaErrorCode(9));
            }

            retorno.add(volume);
        }

        return retorno;
    }

    private List<ComposicoesGTVe> obterComposicoes(StringMap stringMap) throws RotaException {

        List<ComposicoesGTVe> retorno = new ArrayList<>();

        Object composicoesObject = stringMap.get("composicoes");
        List<StringMap> composicoesList = null;

        try {
            composicoesList = (ArrayList) composicoesObject;
        } catch (Exception e) {
            throw new RotaException("composicoes", new RotaErrorCode(0));
        }

        ComposicoesGTVe composicao;
        for (StringMap composicaoObject : composicoesList) {
            composicao = new ComposicoesGTVe();

            try {
                composicao.setQtde((int) composicaoObject.get("qtde"));
                if (!Validacoes.validacaoInteiro(composicao.getQtde(), 5)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("composicao.lacre", new RotaErrorCode(9));
            }

            try {
                composicao.setCodigo(new BigDecimal(stringMap.get("codigo").toString()));
                if (!Validacoes.validacaoValor(composicao.getCodigo(), "999999999999999", 2)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("composicao.codigo", new RotaErrorCode(9));
            }

            try {
                composicao.setMoeda(composicaoObject.get("moeda").toString());
                if (!Validacoes.validacaoTamanho(composicao.getMoeda(), 3, true)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("composicao.moeda", new RotaErrorCode(9));
            }

            try {
                composicao.setValor(new BigDecimal(stringMap.get("valor").toString()));
                if (!Validacoes.validacaoValor(composicao.getValor(), "999999999999999", 2)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("composicao.valor", new RotaErrorCode(9));
            }

            retorno.add(composicao);
        }

        return retorno;
    }

    public static void assinarConexao() {
        try {
            File cacert = new File(File.separator + "Certificados" + File.separator + "cacert2.jks");
            System.out.println("cacert: " + cacert.getAbsolutePath());
            File cacertPath = new File(File.separator + "Certificados" + File.separator);
            if (!cacert.exists()) {
                cacertPath.mkdirs();
            }

            KeyStore keyStore = KeyStore.getInstance("JKS");
            if (cacert.exists()) {
                // if exists, load
                keyStore.load(new FileInputStream(cacert), "123456".toCharArray());
            } else {
                // if not exists, create
                keyStore.load(null, null);
                keyStore.store(new FileOutputStream(cacert), "123456".toCharArray());

                File cert = new File(File.separator + "Certificados" + File.separator
                        + "cert");

                InputStream certIs = new FileInputStream(cert);
                KeyStore ks = KeyStore.getInstance("PKCS12");
//                ks.load(certIs, senha.toCharArray());
                ks.load(certIs, "123456789".toCharArray());

                Enumeration<String> e = ks.aliases();

                // print the enumeration 
                Certificate certs0 = null;
                String alias = null;
                while (e.hasMoreElements()) {
                    alias = e.nextElement();
                    System.out.println(alias);
                    certs0 = ks.getCertificate(alias);
                    if (certs0 != null) {
                        break;
                    }
                }

                // Add the certificate
                keyStore.setCertificateEntry(alias, certs0);

                CertificateFactory cf3 = CertificateFactory.getInstance("X.509");
                DataInputStream dis3 = new DataInputStream(new FileInputStream(File.separator + "Certificados" + File.separator + "svrs3.cer"));
                byte[] bytes3 = new byte[dis3.available()];
                dis3.readFully(bytes3);
                ByteArrayInputStream bais3 = new ByteArrayInputStream(bytes3);
                Certificate certs3 = cf3.generateCertificate(bais3);

                // Add the certificate
                keyStore.setCertificateEntry("svrs3", certs3);

                CertificateFactory cf2 = CertificateFactory.getInstance("X.509");
                DataInputStream dis2 = new DataInputStream(new FileInputStream(File.separator + "Certificados" + File.separator + "svrs2.cer"));
                byte[] bytes2 = new byte[dis2.available()];
                dis2.readFully(bytes2);
                ByteArrayInputStream bais2 = new ByteArrayInputStream(bytes2);
                Certificate certs2 = cf2.generateCertificate(bais2);

                // Add the certificate
                keyStore.setCertificateEntry("svrs2", certs2);

                CertificateFactory cf1 = CertificateFactory.getInstance("X.509");
                DataInputStream dis1 = new DataInputStream(new FileInputStream(File.separator + "Certificados" + File.separator + "svrs1.cer"));
                byte[] bytes1 = new byte[dis1.available()];
                dis1.readFully(bytes1);
                ByteArrayInputStream bais1 = new ByteArrayInputStream(bytes1);
                Certificate certs1 = cf1.generateCertificate(bais1);

                // Add the certificate
                keyStore.setCertificateEntry("svrs1", certs1);

                CertificateFactory cf = CertificateFactory.getInstance("X.509");
                DataInputStream dis = new DataInputStream(new FileInputStream(File.separator + "Certificados" + File.separator + "svrs.cer"));
                byte[] bytes = new byte[dis.available()];
                dis.readFully(bytes);
                ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
                Certificate certs = cf.generateCertificate(bais);

                // Add the certificate
                keyStore.setCertificateEntry("svrs", certs);

                // Save the new keystore contents
                FileOutputStream out = new FileOutputStream(cacert);
                keyStore.store(out, "123456".toCharArray());
                out.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        assinarConexao(
                File.separator + "Certificados" + File.separator + "cert",
                "123456789",
                File.separator + "Certificados" + File.separator + "cacert2.jks");
    }

    public static void assinarConexaoMG() {
        try {
            File cacert = new File(File.separator + "Certificados" + File.separator + "cacert2.jks");
            System.out.println("cacert: " + cacert.getAbsolutePath());
            File cacertPath = new File(File.separator + "Certificados" + File.separator);
            if (!cacert.exists()) {
                cacertPath.mkdirs();
            }

            KeyStore keyStore = KeyStore.getInstance("JKS");
            if (cacert.exists()) {
                // if exists, load
                keyStore.load(new FileInputStream(cacert), "123456".toCharArray());
            } else {
                // if not exists, create
                keyStore.load(null, null);
                keyStore.store(new FileOutputStream(cacert), "123456".toCharArray());

                File cert = new File(File.separator + "Certificados" + File.separator
                        + "cert");

                InputStream certIs = new FileInputStream(cert);
                KeyStore ks = KeyStore.getInstance("PKCS12");
//                ks.load(certIs, senha.toCharArray());
                ks.load(certIs, "123456789".toCharArray());

                Enumeration<String> e = ks.aliases();

                // print the enumeration 
                Certificate certs0 = null;
                String alias = null;
                while (e.hasMoreElements()) {
                    alias = e.nextElement();
                    System.out.println(alias);
                    certs0 = ks.getCertificate(alias);
                    if (certs0 != null) {
                        break;
                    }
                }

                // Add the certificate
                keyStore.setCertificateEntry(alias, certs0);

                CertificateFactory cf3 = CertificateFactory.getInstance("X.509");
                DataInputStream dis3 = new DataInputStream(new FileInputStream(File.separator + "Certificados" + File.separator + "svmg.cer"));
                byte[] bytes3 = new byte[dis3.available()];
                dis3.readFully(bytes3);
                ByteArrayInputStream bais3 = new ByteArrayInputStream(bytes3);
                Certificate certs3 = cf3.generateCertificate(bais3);

                // Add the certificate
                keyStore.setCertificateEntry("svmg1", certs3);

                CertificateFactory cf2 = CertificateFactory.getInstance("X.509");
                DataInputStream dis2 = new DataInputStream(new FileInputStream(File.separator + "Certificados" + File.separator + "svmg1.cer"));
                byte[] bytes2 = new byte[dis2.available()];
                dis2.readFully(bytes2);
                ByteArrayInputStream bais2 = new ByteArrayInputStream(bytes2);
                Certificate certs2 = cf2.generateCertificate(bais2);

                // Add the certificate
                keyStore.setCertificateEntry("svmg2", certs2);

                CertificateFactory cf1 = CertificateFactory.getInstance("X.509");
                DataInputStream dis1 = new DataInputStream(new FileInputStream(File.separator + "Certificados" + File.separator + "svmg2.cer"));
                byte[] bytes1 = new byte[dis1.available()];
                dis1.readFully(bytes1);
                ByteArrayInputStream bais1 = new ByteArrayInputStream(bytes1);
                Certificate certs1 = cf1.generateCertificate(bais1);

                // Add the certificate
                keyStore.setCertificateEntry("svmg", certs1);

                CertificateFactory cf = CertificateFactory.getInstance("X.509");
                DataInputStream dis = new DataInputStream(new FileInputStream(File.separator + "Certificados" + File.separator + "svmg.cer"));
                byte[] bytes = new byte[dis.available()];
                dis.readFully(bytes);
                ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
                Certificate certs = cf.generateCertificate(bais);

                // Add the certificate
                keyStore.setCertificateEntry("svmg", certs);

                // Save the new keystore contents
                FileOutputStream out = new FileOutputStream(cacert);
                keyStore.store(out, "123456".toCharArray());
                out.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        assinarConexao(
                File.separator + "Certificados" + File.separator + "cert",
                "123456789",
                File.separator + "Certificados" + File.separator + "cacert2.jks");
    }

    private static void assinarConexao(String keystoreInputPath, String keyStorePassword, String truststoreInputPath) {
        try {
            InputStream keystoreInput = new FileInputStream(keystoreInputPath);
            InputStream truststoreInput = new FileInputStream(truststoreInputPath);
            setSSLFactories(keystoreInput, keyStorePassword, truststoreInput);
            keystoreInput.close();
            truststoreInput.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static SSLContext setSSLFactories(InputStream keyStream, String keyStorePassword, InputStream trustStream) throws Exception {
        // Get keyStore
        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());

        // if your store is password protected then declare it (it can be null however)
        char[] keyPassword = keyStorePassword.toCharArray();

        // load the stream to your store
        keyStore.load(keyStream, keyPassword);

        // initialize a key manager factory with the key store
        KeyManagerFactory keyFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
        keyFactory.init(keyStore, keyPassword);

        // get the key managers from the factory
        KeyManager[] keyManagers = keyFactory.getKeyManagers();

        // Now get trustStore
        KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());

        // if your store is password protected then declare it (it can be null however)
        //char[] trustPassword = password.toCharArray();
        // load the stream to your store
        trustStore.load(trustStream, null);

        // initialize a trust manager factory with the trusted store
        TrustManagerFactory trustFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        trustFactory.init(trustStore);

        // get the trust managers from the factory
        TrustManager[] trustManagers = trustFactory.getTrustManagers();

        // initialize an ssl context to use these managers and set as default
        SSLContext sslContext = SSLContext.getInstance("SSL");
        sslContext.init(keyManagers, trustManagers, null);
//        SSLContext.setDefault(sslContext);
        return sslContext;
    }
}
