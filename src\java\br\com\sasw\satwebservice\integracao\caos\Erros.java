/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.integracao.caos;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasDaos.TesCofresMovDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import Xml.Xmls;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.UriInfo;

/**
 * REST Web Service
 *
 * <AUTHOR>
 */
@Path("/ws-caos/erros/")
public class Erros {
    
    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    private String caminho;
    private TesCofresMovDao tescofresmovdao;

    @Context
    private UriInfo context;

    /**
     * Creates a new instance of CAOS
     */
    public Erros() {
        logerro = new ArquivoLog();
        tescofresmovdao = new TesCofresMovDao();
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho("/Dados/mapconect.txt");
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\SATPRESERVE" +
                "\\CAOS\\erros\\" + DataAtual.getDataAtual("SQL") + ".txt";
    }

    /**
     * Retrieves representation of an instance of IntegracaoCAOS.CAOS
     * @return an instance of java.lang.String
     */
    @GET
//    @Produces(MediaType.APPLICATION_XML)
    public String getNOP() {
        String resp;
        try {
            //Cria a persistencia
            Persistencia persistencia = pool.getConexao("SATPRESERVE");
//            Persistencia persistencia = pool.getConexao("SASW");
            String ultimaMovimentacao = tescofresmovdao.ultimaMovimentacaoIntegracao("351", "999999", persistencia);            
            resp = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + Xmls.tag("nop", null == ultimaMovimentacao ? "0" : ultimaMovimentacao); 
            this.logerro.Grava(resp, caminho);
            persistencia.FechaConexao();
        } catch (Exception e){
            resp = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + Xmls.tag("erro", e.getMessage());
            this.logerro.Grava(e.getMessage(), caminho);
        }
        return resp;
    }

    /**
     * PUT method for updating or creating an instance of Nota
     * @param content representation for the resource
     */
    @PUT
    @Consumes(MediaType.APPLICATION_XML)
    public void putErro(String content) {
        this.logerro.Grava(content, caminho);
    }
}
