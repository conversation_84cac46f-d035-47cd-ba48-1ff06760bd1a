/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.nfe.serializer;

import br.com.sasw.satwebservice.nfe.models.ItensModel;
import br.com.sasw.satwebservice.nfe.models.ProtocoloModel;
import br.com.sasw.satwebservice.nfe.models.RejeicaoModel;
import br.com.sasw.satwebservice.nfe.models.RetornoModel;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RetornoSerializer implements JsonSerializer<RetornoModel> {

    @Override
    public JsonElement serialize(RetornoModel retorno, Type type, JsonSerializationContext jsc) {
        JsonObject jsonObject = null;

        if (retorno.getNfe() == null) {
            // Retorno de processamento sem erro

            jsonObject = new JsonObject();
            jsonObject.add("chave", new JsonPrimitive(retorno.getChave()));
            jsonObject.add("acao", new JsonPrimitive(retorno.getAcao()));
            jsonObject.add("orgao", new JsonPrimitive(retorno.getOrgao()));
            jsonObject.add("natOp", new JsonPrimitive(retorno.getNatOp()));
            jsonObject.add("indPag", new JsonPrimitive(retorno.getIndPag()));
            jsonObject.add("mod", new JsonPrimitive(retorno.getMod()));
            jsonObject.add("serie", new JsonPrimitive(retorno.getSerie()));
            jsonObject.add("nRPS", new JsonPrimitive(retorno.getnRPS()));
            jsonObject.add("datahemi", new JsonPrimitive(retorno.getDatahemi()));
            jsonObject.add("dataSaiEnt", new JsonPrimitive(retorno.getDataSaiEnt()));
            jsonObject.add("HoraSaiEnt", new JsonPrimitive(retorno.getHoraSaiEnt()));
            jsonObject.add("tpNF", new JsonPrimitive(retorno.getTpNF()));
            jsonObject.add("codcidadeicms", new JsonPrimitive(retorno.getCodcidadeicms()));
            jsonObject.add("tipoImpressao", new JsonPrimitive(retorno.getTipoImpressao()));
            jsonObject.add("cnpjemissor", new JsonPrimitive(retorno.getCnpjemissor()));
            jsonObject.add("razaosocialemissor", new JsonPrimitive(retorno.getRazaosocialemissor()));
            jsonObject.add("fantasiaemissor", new JsonPrimitive(retorno.getFantasiaemissor()));
            jsonObject.add("enderecoemissor", new JsonPrimitive(retorno.getEnderecoemissor()));
            jsonObject.add("bairroemissor", new JsonPrimitive(retorno.getBairroemissor()));
            jsonObject.add("cidadeemissor", new JsonPrimitive(retorno.getCidadeemissor()));
            jsonObject.add("ufemissor", new JsonPrimitive(retorno.getUfemissor()));
            jsonObject.add("codcidadeemissor", new JsonPrimitive(retorno.getCodcidadeemissor()));
            jsonObject.add("cepemissor", new JsonPrimitive(retorno.getCepemissor()));
            jsonObject.add("paisemissor", new JsonPrimitive(retorno.getPaisemissor()));
            jsonObject.add("telefoneemissor", new JsonPrimitive(retorno.getTelefoneemissor()));
            jsonObject.add("emailemissor", new JsonPrimitive(retorno.getEmailemissor()));
            jsonObject.add("CNAE", new JsonPrimitive(retorno.getCNAE()));
            jsonObject.add("CRT", new JsonPrimitive(retorno.getCRT()));
            jsonObject.add("cnpjdest", new JsonPrimitive(retorno.getCnpjdest()));
            jsonObject.add("cpfdest", new JsonPrimitive(retorno.getCpfdest()));
            jsonObject.add("razaosocialdest", new JsonPrimitive(retorno.getRazaosocialdest()));
            jsonObject.add("fantasiadest", new JsonPrimitive(retorno.getFantasiadest()));
            jsonObject.add("enderecodest", new JsonPrimitive(retorno.getEnderecodest()));
            jsonObject.add("bairrodest", new JsonPrimitive(retorno.getBairrodest()));
            jsonObject.add("cidadedest", new JsonPrimitive(retorno.getCidadedest()));
            jsonObject.add("ufdest", new JsonPrimitive(retorno.getUfdest()));
            jsonObject.add("codcidadedest", new JsonPrimitive(retorno.getCodcidadedest()));
            jsonObject.add("cepdest", new JsonPrimitive(retorno.getCepdest()));
            jsonObject.add("telefonedest", new JsonPrimitive(retorno.getTelefonedest()));
            jsonObject.add("emaildest", new JsonPrimitive(retorno.getEmaildest()));
            jsonObject.add("iedest", new JsonPrimitive(retorno.getIedest()));
            jsonObject.add("observacao", new JsonPrimitive(retorno.getObservacao()));

            jsonObject.add("Itens", new Gson().toJsonTree(retorno.getItens(), new TypeToken<List<ItensModel>>() {
            }.getType()));

            if (retorno.getProtocolo() != null) {
                jsonObject.add("protocolo", new Gson().toJsonTree(retorno.getProtocolo(), ProtocoloModel.class));
            }

            if (retorno.getRejeicao() != null) {
                jsonObject.add("rejeicao", new Gson().toJsonTree(retorno.getRejeicao(), RejeicaoModel.class));
            }

        } else {
            // Retorno da exceção
            jsonObject = retorno.getNfe();

            if (retorno.getRejeicao() != null) {
                jsonObject.add("rejeicao", new Gson().toJsonTree(retorno.getRejeicao(), RejeicaoModel.class));
            }
        }

        return jsonObject;
    }

}
