/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.integracao.mastercoin;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.TesCofresMov;
import SasDaos.SemaforoDao;
import SasDaos.TesCofresMovDao;
import Xml.Xmls;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import javax.ws.rs.GET;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.UriInfo;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;

/**
 * REST Web Service
 *
 * <AUTHOR>
 */
@Path("/ws-mastercoin/")
public class Mastercoin {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private String caminho;
    private final TesCofresMovDao tescofresmovdao;
    private final SemaforoDao semaforoDao;

    @Context
    private UriInfo context;

    /**
     * Creates a new instance of Mastercoin
     */
    public Mastercoin() {
        logerro = new ArquivoLog();
        tescofresmovdao = new TesCofresMovDao();
        pool = new SasPoolPersistencia();
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
        semaforoDao = new SemaforoDao();
    }

    /**
     * Retrieves representation of an instance of
     * IntegracaoMastercoin.Mastercoin
     *
     * @param empresa
     * @param codcofre
     * @return an instance of java.lang.String
     */
    @GET
    @Path("/{empresa}/{codcofre}")
    public String getNOP(@PathParam("empresa") String empresa, @PathParam("codcofre") String codcofre) {
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ERRO\\mastercoin\\" + getDataAtual("SQL") + ".txt";
        String resp;
        try {
            if (empresa == null || empresa.equals("")) {
                throw new Exception("Empresa inválida");
            }

            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(empresa);
            if (null == persistencia) {
                throw new Exception("Empresa inválida: " + empresa);
            }

            caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\"
                    + empresa + "\\mastercoin\\" + getDataAtual("SQL") + ".txt";

            if (codcofre == null || codcofre.equals("")) {
                throw new Exception("Cofre inválido");
            }
            
            caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\"
                    + empresa + "\\mastercoin\\" + codcofre + "\\" + getDataAtual("SQL") + ".txt";

            String ultimaMovimentacao = tescofresmovdao.ultimaMovimentacaoIntegracaoMasterCoin(codcofre, "9999", persistencia);
            resp = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + Xmls.tag("erro", "") + Xmls.tag("nop", null == ultimaMovimentacao ? "0" : ultimaMovimentacao);

            this.logerro.Grava(resp, caminho);
            persistencia.FechaConexao();
        } catch (Exception e) {
            resp = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + Xmls.tag("erro", e.getMessage());
            this.logerro.Grava("Mastercoin.getNOP - " + e.getMessage(), caminho);
        }
        
        return resp;
    }

    /**
     * PUT method for updating or creating an instance of Nota
     *
     * @param empresa
     * @param codcofre
     * @param content representation for the resource
     * @return
     */
    @PUT
    @Path("/{empresa}/{codcofre}")
    public String putMov(String content, @PathParam("empresa") String empresa, @PathParam("codcofre") String codcofre) {
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ERRO\\mastercoin\\" + getDataAtual("SQL") + ".txt";

        TesCofresMov mov;
        JSONObject movObj;
        String tabela = "TesCofresMov";
        String chave;
        String resp;
        try {

            if (empresa == null || empresa.equals("")) {
                this.logerro.Grava("Empresa inválida", caminho);
                throw new Exception("Empresa inválida");
            }

            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(empresa);
            if (null == persistencia) {
                this.logerro.Grava("Empresa inválida: " + empresa, caminho);
                throw new Exception("Empresa inválida: " + empresa);
            }

            caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\"
                    + empresa + "\\mastercoin\\" + getDataAtual("SQL") + ".txt";

            if (codcofre == null || codcofre.equals("")) {
                this.logerro.Grava("Cofre inválido", caminho);
                throw new Exception("Cofre inválido");
            }

            caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\"
                    + empresa + "\\mastercoin\\" + codcofre + "\\" + getDataAtual("SQL") + ".txt";

            chave = "IntegracaoMastercoin" + codcofre;
            this.logerro.Grava("Chave semaforo: " + chave, caminho);
            if (!semaforoDao.existeSemaforo(tabela, chave, getDataAtual("SQL"), persistencia)) {
                semaforoDao.inserirRegistro("SatWeb", getDataAtual("SQL"), getDataAtual("HORA"), tabela, chave, persistencia);
                this.logerro.Grava("Lendo movimentações.", caminho);
                JSONObject objMov = XML.toJSONObject(content);
                int qtd = objMov.getInt("qtd");
                this.logerro.Grava("Movimentações: " + qtd, caminho);
                if (qtd > 1) {
                    JSONArray arrayMov = objMov.getJSONArray("mov");
                    for (int i = 0; i < qtd; i++) {
                        try {
                            movObj = arrayMov.getJSONObject(i);
                            mov = new TesCofresMov();
                            mov.setCodCofre(new BigDecimal(codcofre));
                            this.logerro.Grava("Data: " + String.valueOf(movObj.get("Data")), caminho);
                            mov.setData(LocalDate.parse(String.valueOf(movObj.get("Data")), DateTimeFormatter.ISO_DATE));
                            this.logerro.Grava("Hora: " + String.valueOf(movObj.get("Hora")), caminho);
                            mov.setHora(String.valueOf(movObj.get("Hora")));
                            this.logerro.Grava("CodCliente: " + String.valueOf(movObj.get("CodCliente")), caminho);
                            mov.setCodCliente((String.valueOf(movObj.get("CodCliente"))));
                            this.logerro.Grava("idUsuario: " + String.valueOf(movObj.get("idUsuario")), caminho);
                            mov.setIdUsuario(String.valueOf(movObj.get("idUsuario")));
                            this.logerro.Grava("NomeUsuario: " + String.valueOf(movObj.get("NomeUsuario")), caminho);
                            mov.setNomeUsuario(String.valueOf(movObj.get("NomeUsuario")));
                            this.logerro.Grava("ValorDeposito: " + String.valueOf(movObj.get("ValorDeposito")), caminho);
                            mov.setValorDeposito(new BigDecimal(String.valueOf(movObj.get("ValorDeposito"))));
                            this.logerro.Grava("TipoMoeda: " + String.valueOf(movObj.get("TipoMoeda")), caminho);
                            mov.setTipoMoeda(String.valueOf(movObj.get("TipoMoeda")));
                            this.logerro.Grava("TipoDeposito: " + String.valueOf(movObj.get("TipoDeposito")), caminho);
                            mov.setTipoDeposito(String.valueOf(movObj.get("TipoDeposito")));
                            this.logerro.Grava("CodigoBarras: " + String.valueOf(movObj.get("CodigoBarras")), caminho);
                            mov.setCodigoBarras(String.valueOf(movObj.get("CodigoBarras")));
                            this.logerro.Grava("Status: " + String.valueOf(movObj.get("Status")), caminho);
                            mov.setStatus(String.valueOf(movObj.get("Status")));
                            this.logerro.Grava("Operador: " + String.valueOf(movObj.get("Operador")), caminho);
                            mov.setOperador(String.valueOf(movObj.get("Operador")));
                            this.logerro.Grava("Dt_Incl: " + String.valueOf(movObj.get("Dt_Incl")), caminho);
                            mov.setDt_Incl(LocalDate.parse(String.valueOf(movObj.get("Dt_Incl")), DateTimeFormatter.ISO_DATE));
                            this.logerro.Grava("Hr_Incl: " + String.valueOf(movObj.get("Hr_Incl")), caminho);
                            mov.setHr_Incl(String.valueOf(movObj.get("Hr_Incl")));
                            this.logerro.Grava("Inserir movimentação no banco.", caminho);
                            tescofresmovdao.inserirMovimentacao(mov, persistencia);
                            this.logerro.Grava("Movimentação inserida.", caminho);
                        } catch (Exception ex) {
                            this.logerro.Grava("Mastercoin.putMov - " + ex.getMessage(), caminho);
                        }
                    }
                } else if (qtd == 1) {
                    movObj = objMov.getJSONObject("mov");
                    mov = new TesCofresMov();
                    mov.setCodCofre(new BigDecimal(codcofre));
                    mov.setData(LocalDate.parse(String.valueOf(movObj.get("Data")), DateTimeFormatter.ISO_DATE));
                    mov.setHora(String.valueOf(movObj.get("Hora")));
                    mov.setCodCliente((String.valueOf(movObj.get("CodCliente"))));
                    mov.setIdUsuario(String.valueOf(movObj.get("idUsuario")));
                    mov.setNomeUsuario(String.valueOf(movObj.get("NomeUsuario")));
                    mov.setValorDeposito(new BigDecimal(String.valueOf(movObj.get("ValorDeposito"))));
                    mov.setTipoMoeda(String.valueOf(movObj.get("TipoMoeda")));
                    mov.setTipoDeposito(String.valueOf(movObj.get("TipoDeposito")));
                    mov.setCodigoBarras(String.valueOf(movObj.get("CodigoBarras")));
                    mov.setStatus(String.valueOf(movObj.get("Status")));
                    mov.setOperador(String.valueOf(movObj.get("Operador")));
                    mov.setDt_Incl(LocalDate.parse(String.valueOf(movObj.get("Dt_Incl")), DateTimeFormatter.ISO_DATE));
                    mov.setHr_Incl(String.valueOf(movObj.get("Hr_Incl")));
                    tescofresmovdao.inserirMovimentacao(mov, persistencia);
                }
                semaforoDao.removerBaixaTabela(tabela, chave, persistencia);
            } else if (semaforoDao.tempoSemaforo(tabela, chave, persistencia) > 10) {
                semaforoDao.removerBaixaTabela(tabela, chave, persistencia);
                this.logerro.Grava("Semáforo Mastercoin removido", caminho);
            }

            resp = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + Xmls.tag("erro", "") + Xmls.tag("msg", "Importação finalizada no servidor.");

            this.logerro.Grava(resp, caminho);
        } catch (Exception e) {
            resp = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + Xmls.tag("erro", e.getMessage());
            this.logerro.Grava("Mastercoin.putMov - " + e.getMessage(), caminho);
        }
        
        this.logerro.Grava(content, caminho);
        return resp;
    }
}
