/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.importacao.exceptions;

import br.com.sasw.satwebservice.importacao.exceptions.*;

/**
 *
 * <AUTHOR>
 */
public class GTVeException extends Exception{
    
    private static final long serialVersionUID = 7718828512143293558L;
    private final GTVeErrorCode code;
    
    public GTVeException(GTVeErrorCode code) {
            super();
            this.code = code;
    }

    public GTVeException(String message, Throwable cause, GTVeErrorCode code) {
            super(message, cause);
            this.code = code;
    }

    public GTVeException(String message, GTVeErrorCode code) {
            super(message);
            this.code = code;
    }

    public GTVeException(Throwable cause, GTVeErrorCode code) {
            super(cause);
            this.code = code;
    }

    public GTVeErrorCode getCode() {
            return this.code;
    }
        
    public static class GTVeErrorCode implements ErrorCode{

        public GTVeErrorCode() {
        }

        public GTVeErrorCode(int code) {
            this.code = code;
        }
        
        private int code;
        private String status;

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        @Override
        public String getStatus() {
            switch(this.code){
                case 0:
                    status = "Guia/Serie não encontrada";
                    break;
                default:
                    status = "Ocorreu um erro desconhecido";
            }
            return status;
        }
    }
}
