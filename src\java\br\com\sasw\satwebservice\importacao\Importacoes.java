/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.importacao;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Rt_Guias;
import SasBeans.XMLGtve;
import br.com.sasw.pacotesuteis.sasbeans.TOKENS;
import br.com.sasw.pacotesuteis.sasbeans.XMLNFE;
import br.com.sasw.pacotesuteis.sasdaos.TOKENSDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.satwebservice.batidaponto.ValidarToken.validarValidade;
import br.com.sasw.satwebservice.importacao.clientes.ImportacaoClientes;
import br.com.sasw.satwebservice.importacao.funcionarios.ImportacaoFuncionarios;
import br.com.sasw.satwebservice.importacao.gtve.ImportacaoGTVe;
import br.com.sasw.satwebservice.importacao.pedidos.ImportacaoPedidos;
import br.com.sasw.satwebservice.importacao.rotas.ImportacaoRotas;
import br.com.sasw.satwebservice.importacao.rotas.ImportacaoRotasGTVe;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import java.util.HashMap;
import java.util.Map;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;
import org.apache.commons.codec.binary.Base64;

/**
 *
 * <AUTHOR>
 */
@Path("/ws-importacao/")
public class Importacoes {

    private @Context
    HttpHeaders httpHeaders;
    private @Context
    UriInfo uriInfo;

    private final Gson gson;
    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;
    private final TOKENSDao tokensDao;

    /**
     * Creates a new instance of ImportacaoClientes
     */
    public Importacoes() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        tokensDao = new TOKENSDao();
        gson = new GsonBuilder().create();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/{empresa}/cliente")
    public Response cliente(@PathParam("empresa") String empresa, String input) {
        ImportacaoClientes clientes = new ImportacaoClientes();
        return clientes.json(empresa, input);
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/{empresa}/pedidos")
    public Response json(@PathParam("empresa") String empresa, String input) {
        ImportacaoPedidos pedidos = new ImportacaoPedidos();
        return pedidos.json(empresa, input);
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/{empresa}/funcion")
    public Response funcion(@PathParam("empresa") String empresa, String input) {
        ImportacaoFuncionarios funcion = new ImportacaoFuncionarios();
        return funcion.json(empresa, input);
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/funcion")
    public Response funcion(String input) {
        try {
            String empresa = getParam();
            ImportacaoFuncionarios funcion = new ImportacaoFuncionarios();
            return funcion.json(empresa, input);
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/rotas2")
    public Response rota(String input) {
        try {
            String empresa = getParam();
            ImportacaoRotas rotas = new ImportacaoRotas();
            return rotas.json(empresa, input);
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/rotas")
    public Response rotaGTVe(String input) {
        try {
            String empresa = getParam();
            ImportacaoRotasGTVe rotas = new ImportacaoRotasGTVe();
            return rotas.json(empresa, input);
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gtve/{guia}/{serie}")
    public Response GTVe(@PathParam("guia") String guia, @PathParam("serie") String serie) {
        try {
            String empresa = getParam();
            //String empresa = "SATIBL";
            //String empresa = "SATBRASIFORT";
            ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);
            return gtve.json(guia, serie, empresa);
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gtve/{empresa}/{guia}/{serie}")
    public Response GTVe(@PathParam("empresa") String empresa, @PathParam("guia") String guia, @PathParam("serie") String serie) {
        try {
            if (validarPermissaoGTVe(empresa)) {
                ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);
                return gtve.json(guia, serie, empresa);
            } else {
                return Response
                        .status(Response.Status.UNAUTHORIZED)
                        .type("application/json")
                        .entity(gson.toJson("Empresa sem Certificado e Senha, cadastrado(s)!"))
                        .build();
            }
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gtveRPV/{empresa}/{rpv}/{serie}")
    public Response GTVeForRPV(@PathParam("empresa") String empresa, @PathParam("rpv") String rpv, @PathParam("serie") String serie) {
        try {
            if (validarPermissaoGTVe(empresa)) {
                this.logerro.Grava("Acessou servico de GTVe: " + empresa + "/" + rpv + "/" + serie, this.caminho);

                ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);
                Rt_Guias guia = gtve.getGuiaSerieForRpv(rpv, empresa);

                if (null != guia) {
                    this.logerro.Grava("Numero Guia: " + guia.getGuia().toPlainString(), this.caminho);
                } else {
                    this.logerro.Grava("Numero Guia: Nao encontrado", this.caminho);
                }

                return gtve.json(guia.getGuia().toPlainString().replace(".0", ""), guia.getSerie(), empresa);
            } else {
                return Response
                        .status(Response.Status.UNAUTHORIZED)
                        .type("application/json")
                        .entity(gson.toJson("Empresa sem Certificado e Senha, cadastrado(s)!"))
                        .build();
            }
        } catch (Exception e) {
            this.logerro.Grava("Erro de Processamento: " + empresa + "/" + rpv + "/" + serie + "\n\n" + e.getMessage(), this.caminho);

            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gtve")
    public Response GTVeData(String input) {
        try {
            String empresa = getParam();
            //String empresa = "SATBRASIFORT";
            ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);
            return gtve.jsonDataCompleto(input, empresa);
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gtve/interface6")
    public Response GTVeControllerAction(String input) {
        try {
            String empresa = getParam();
            //String empresa = "SATPROSEGUR";

            input = FuncoesString.removeAcento(input);
            input = input.replace("Â ", "");
            input = input.replace("º", "");
            input = input.replace("°", "");
            input = input.replace("º", "");
            input = input.replace("ª", "");            
            ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);
            return gtve.dataCteFromJson(input, empresa);
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gtve/interface6_teste")
    public Response GTVeControllerActionTeste(String input) {
        try {
            //String empresa = getParam();
            String empresa = "SATPROSEGUR";

            ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);
            return gtve.testeComXml(input, empresa);
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gtve/interface8/{nProt}")
    public Response GTVeControllerConsulta(@PathParam("nProt") String nProt) {
        try {
            if (null == nProt || nProt.equals("")) {
                return Response
                        .status(Response.Status.BAD_REQUEST)
                        .type("application/json")
                        .entity(gson.toJson("Informe o Procolo"))
                        .build();
            } else {
                String empresa = getParam();

                ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);
                return gtve.consultaProtocolo(empresa, nProt);
            }
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gtve/interface12")
    public Response GTVeControllerCancel(String input) {
        try {
            String empresa = getParam();

            ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);
            return gtve.cancelarGTVe(input, empresa);
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gtve/token")
    public Response gerarToken() {
        Map retorno = new HashMap<>();
        retorno.clear();
        
        try {
           // if (!validAutenticationProsegur("saswprosegur", "6/WP[&wJ^\"@+3{2Z")) {
            if (!validAutenticationProsegur("saswbrinks", "6/WP[&wJ^\"@+3{2Z")) {
                return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson("Login e/ou senha invalido(s)"))
                    .build();
            }

            //String empresa = "SATPROSEGUR";
            String empresa = "SATBRINKS";
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\" + empresa + "\\GTVe\\" + getDataAtual("SQL") + "\\log_token.txt";
            
            Persistencia satellite = pool.getConexao("SATELLITE");
            TOKENSDao tokensDao = new TOKENSDao();
            TOKENS token = new TOKENS();
            token.setBancoDados(empresa);
            token.setModulo("INTEGRACAO");
            token.setChave("WS");
            token.setData(getDataAtual("SQL"));
            token.setHora(getDataAtual("HORA"));
            String Token = tokensDao.gerarToken(token, satellite);

            retorno.put("status", "ok");
            retorno.put("token", Token);

            this.logerro.Grava("Token Gerado - \n" + getDataAtual("TELA") + " " + getDataAtual("HORASEGUNDOS") + "\n" +  "Token: " + Token, this.caminho);
            
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gson.toJson(retorno))
                    .build();
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gtve/full/{guia}/{serie}")
    public Response GTVeDataFull(@PathParam("guia") String guia, @PathParam("serie") String serie) {
        try {
            String empresa = getParam();
            ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gson.toJson(gtve.getGTVeIntegraIBL(empresa, guia, serie)))
                    .build();
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/nfse/vr/{guia}/{serie}/{praca}/{parametro}")
    public Response GTVeDataNfseVr(@PathParam("guia") String guia, @PathParam("serie") String serie, @PathParam("praca") String praca, @PathParam("parametro") String parametro) {
        try {
            String empresa = parametro;
            ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gson.toJson(gtve.getDataNfseVr(empresa, guia, serie, praca)))
                    .build();
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/nfse/vr/xml/{guia}/{serie}/{praca}/{parametro}")
    public Response GTVeDataNfseVrXml(@PathParam("guia") String guia, @PathParam("serie") String serie, @PathParam("praca") String praca, @PathParam("parametro") String parametro) {
        try {
            String empresa = parametro;
            ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gson.toJson(gtve.getDataNfseVrXml(empresa, guia, serie, praca)))
                    .build();
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gtve/consulta/{nProt}/{dtRef}")
    public XMLNFE GTVeConsultaData(@PathParam("nProt") String nProt, @PathParam("dtRef") String dtRef) {
        try {
            String empresa = getParam();
            ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);

            return gtve.dadosXmlNfe(nProt, dtRef);
        } catch (Exception e) {
            return null;
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gtve/consulta/{empresa}/{guia}/{serie}")
    public XMLGtve GTVeConsultaData(@PathParam("empresa") String empresa, @PathParam("guia") String guia, @PathParam("serie") String serie) {
        try {
            ImportacaoGTVe gtve = new ImportacaoGTVe(empresa);

            return gtve.dadosXmGtve(empresa, guia, serie);
        } catch (Exception e) {
            return null;
        }
    }

    private boolean validarPermissaoGTVe(String empresa) {
        boolean Retorno = false;

        switch (empresa) {
            case "IBL":
            case "SATIBL":
            case "BRASIFORT":
            case "SATBRASIFORT":
            case "PROSEGUR":
            case "SATPROSEGUR":
            case "PROSEGURBSB":
            case "SATPROSEGURBSB":
            case "FIDELYS":
            case "SATFIDELYS1":
            case "PRESERVE":
            case "SATPRESERVE":
            case "INVIOSEG":
            case "SATINVLMT":    
            case "CORPVS":
            case "SATCORPVS":                    
            case "SATCORPVS2":
            case "SATCORPVSPE2":
            case "SATDELTACORP":
            case "SATFORCAALERTA":
            case "SATFEDERAL" :
            case "SATCORPVSPE":
            case "SATBRINKS":   
            
                
            Retorno = true;
        }

        return Retorno;
    }

    private Boolean validAutenticationProsegur(String loginAceito, String senhaAceita) throws Exception {
        Boolean retorno = false;

        if (this.httpHeaders.getRequestHeader("authorization") == null || this.httpHeaders.getRequestHeader("authorization").isEmpty()) {
            this.logerro.Grava("Authorization faltando\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Authorization missing");
        }

        String basicToken = "";

        for (String b : this.httpHeaders.getRequestHeader("authorization")) {
            if (b.contains("Basic ")) {
                basicToken = b;
            }
        }

        if (null != basicToken && !basicToken.equals("")) {
            String pair = new String(Base64.decodeBase64(basicToken.substring(6)));
            String login = pair.split(":")[0];
            String senha = pair.split(":")[1];

            if (null != login && !login.equals("")
                    && null != senha && !senha.equals("")) {

                if (login.equals(loginAceito) && senha.equals(senhaAceita)) {
                    retorno = true;
                }
            }
        }

        return retorno;
    }

    private String getParam() throws Exception {

        if (this.httpHeaders.getRequestHeader("authorization") == null || this.httpHeaders.getRequestHeader("authorization").isEmpty()) {
            this.logerro.Grava("Authorization faltando\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Authorization missing");
        }

        String bearer = null;

        for (String b : this.httpHeaders.getRequestHeader("authorization")) {
            if (b.contains("Bearer ")) {
                bearer = b;
                break;
            }
        }

        if (bearer == null) {
            this.logerro.Grava("Authorization Bearer faltando\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Authorization Bearer missing");
        }

        String codigo = bearer.replace("Bearer ", "");
        this.persistencia = this.pool.getConexao("SATELLITE");
        TOKENS token = this.tokensDao.obterToken(codigo, this.persistencia);

        if (token == null) {
            this.logerro.Grava("token inválido: " + codigo + "\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Invalid token: " + codigo);
        }

        if (!token.getModulo().equals("INTEGRACAO") && !token.getModulo().equals("Prosegur")) {
            this.logerro.Grava("token inválido: " + codigo + " modulo(" + token.getModulo() + ")\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Invalid token: " + codigo);
        }

        if (!token.getChave().equals("WS") && !token.getChave().equals("Token Master")) {
            this.logerro.Grava("token inválido: " + codigo + " chave(" + token.getChave() + ")\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Invalid token: " + codigo);
        }

        try {
            validarValidade(token);
        } catch (Exception e) {
            this.logerro.Grava("token expirado: " + codigo + " validade(" + token.getDtValid() + ")\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Expired token: " + codigo);
        }

        return token.getBancoDados();
    }
}
