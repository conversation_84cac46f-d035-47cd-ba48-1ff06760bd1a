package br.com.sasw.satwebservice.segurodesemprego;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import SasBeans.Filiais;
import SasBeansCompostas.SeguroDesemprego;
import SasDaos.FiliaisDao;
import SasDaos.SeguroDesempregoDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ArquivoSeguroDesemprego {

    /**
     * Faz a geração do seguro desemprego
     *
     * @param CodFil - Codigo da Filial
     * @param CodMovFP - Código da Folha
     * @param DtDemis - Data de demissão
     * @param persistencia - Conexão com Banco de Dados
     * @return - retorna String com o arquivo do seguro desemprego já no layout
     * @throws Exception - pode gerar exception
     */
    public String GerarSeguroDesemprego(BigDecimal CodFil, String CodMovFP, String DtDemis, Persistencia persistencia) throws Exception {
        String CNPJ;
        String Header;
        String Registro = "";
        String Trailler;
        ArquivoLog log = new ArquivoLog();
        try {
            FiliaisDao fd = new FiliaisDao();
            List<Filiais> lfilial;
            lfilial = fd.BuscaCNPJ(CodFil, persistencia);
            CNPJ = lfilial.get(0).getCNPJ();

            SeguroDesempregoDao sdd = new SeguroDesempregoDao();
            List<SeguroDesemprego> lsd;
            lsd = sdd.SeguroDesemprego(CodFil, CodMovFP, DtDemis, persistencia);
            Header = GerarHeader(CNPJ);
            for (SeguroDesemprego sd : lsd) {
                Registro += GerarRegistro(sd);
            }
            Trailler = GerarTrailler(String.valueOf(lsd.size()));
            return FuncoesString.formatString(Header + Registro + Trailler);
        } catch (Exception e) {
            throw new Exception("Falha ao gerar arquivo de Seguro Desemprego - " + e.getMessage());
        }
    }

    private static String GerarHeader(String CNPJ) throws Exception {
        String Header = "";
        try {
            Header = "00" //001-002 Tipo de registro - fixo 00
                    + "1" //003-003 Tipo de registro do empregador - fixo 1-CNPJ 2-CEI
                    + CNPJ //004-017 CNPJ
                    + "001" //018-020 Versão do layout - fixo 001
                    + br.com.sasw.pacotesuteis.utilidades.FuncoesString.SpaceString("", 280)
                    + "\r\n";                                                   //021-300 Filler - Em Branco
        } catch (Exception e) {
            throw new Exception("Falha ao gerar Header - " + e.getMessage());
        }
        return Header;
    }

    private String GerarRegistro(SeguroDesemprego sd) throws Exception {
        try {
            String Registro = "";

            String CPF = sd.getFuncion().getCPF();
            String Nome = sd.getFuncion().getNome();
            String Endereco = sd.getFuncion().getEndereco();
            String Complemento = sd.getFuncion().getComplemento();
            String CEP = sd.getFuncion().getCEP();
            String UF = sd.getFuncion().getUF();
            String Telefone = sd.getFuncion().getFone1();
            String Mae = sd.getFuncion().getMae();
            String PIS = sd.getFuncion().getPIS();
            String CTPS = sd.getFuncion().getCTPS_Nro();
            String Serie_CTPS = sd.getFuncion().getCTPS_Serie();
            String UF_CTPS = sd.getFuncion().getCTPS_UF();
            String CBO = sd.getCargos().getCBO();
            String Dt_Admis = DataAtual.voltaData(FuncoesString.limpa(sd.getFuncion().getDt_Admis(), ""), "ddMMaaaa");
            String Dt_Demis = DataAtual.voltaData(sd.getFprescisoes().getDtDemissao().format(DateTimeFormatter.ISO_LOCAL_DATE).replaceAll("-", ""), "ddMMaaaa");
            String Sexo = sd.getFuncion().getSexo();
            if ("M".equals(Sexo)) {
                Sexo = "1";
            } else {
                Sexo = "2";
            }
            String Instrucao = sd.getFuncion().getInstrucao();
            String Dt_Nasc = DataAtual.voltaData(FuncoesString.limpa(sd.getFuncion().getDt_Nasc(), ""), "ddMMaaaa");
            String HorasTrab = sd.getFuncion().getJornada().divide(new BigDecimal("5")).toString();
//            String Salario3 = sd.getFpmensal3().getLiquido().toString();
//            String Salario2 = sd.getFpmensal2().getLiquido().toString();
//            String Salario1 = sd.getFpmensal1().getLiquido().toString();
            String Salario3 = sd.getFpmensal3().getProventos().toString();
            String Salario2 = sd.getFpmensal2().getProventos().toString();
            String Salario1 = sd.getFpmensal1().getProventos().toString();
            String AvisoInden = sd.getFprescisoes().getTipo();
            if (AvisoInden.equals("2")){
               AvisoInden = "1";                       
            } else {
               AvisoInden = "2";
            }

            Telefone = br.com.sasw.pacotesuteis.utilidades.FuncoesString.limpa2(Telefone, "");
            Telefone = Telefone.replaceAll(" ", "");

            Registro = "01" //001-002 tipo de Registro, fixo 01
                    + CPF //003-013 CPF
                    + FuncoesString.RecortaString(Nome + FuncoesString.SpaceString("", 40), 0, 40) //014-053 Nome
                    + FuncoesString.RecortaString(Endereco + FuncoesString.SpaceString("", 40), 0, 40) //054-093 Endereco
                    + FuncoesString.RecortaString(Complemento + FuncoesString.SpaceString("", 16, "."), 0, 16) //094-109 Complemento
                    + FuncoesString.PreencheEsquerda(CEP, 8, "0") //110-117 CEP
                    + FuncoesString.RecortaString(UF + FuncoesString.SpaceString("", 2), 0, 2) //118-119 UF
                    + FuncoesString.PreencheEsquerda(Telefone, 10, "0") //120-129 Telefone
                    + FuncoesString.RecortaString(Mae + FuncoesString.SpaceString("", 40), 0, 40) //130-169 Mae
                    + FuncoesString.PreencheEsquerda(PIS, 11, "0") //170-180 PIS
                    + FuncoesString.PreencheEsquerda(CTPS, 8, "0") //181-188 CTPS
                    + FuncoesString.PreencheEsquerda(Serie_CTPS, 5, "0") //189-193 Série da CTPS
                    + FuncoesString.RecortaString(UF_CTPS + FuncoesString.SpaceString("", 2), 0, 2) //194-195 UF da CTPS
                    + FuncoesString.PreencheEsquerda(CBO, 6, "0") //196-201 CBO
                    + FuncoesString.RecortaString(Dt_Admis + FuncoesString.SpaceString("", 8), 0, 8) //202-209 Data de Admissão
                    + FuncoesString.RecortaString(Dt_Demis + FuncoesString.SpaceString("", 8), 0, 8) //210-217 Data de Demissão
                    + FuncoesString.RecortaString(Sexo + FuncoesString.SpaceString("", 1), 0, 1) //218-218 Sexo
                    + FuncoesString.RecortaString(Escolaridade(Instrucao) + FuncoesString.SpaceString("", 2), 0, 2) //219-220 Grau de instrucao
                    + FuncoesString.RecortaString(Dt_Nasc + FuncoesString.SpaceString("", 8), 0, 8) //221-228 Data de Nascimento
                    + FuncoesString.PreencheEsquerda(HorasTrab, 2, "0") //229-230 Horas Trabalhadas
                    + FuncoesString.PreencheEsquerda(
                            FuncoesString.limpa(
                                    FuncoesString.formatarStringMoeda(Salario3, false), ""),
                            10, "0") //231-240 Antepenúltimo Salário
                    + FuncoesString.PreencheEsquerda(
                            FuncoesString.limpa(
                                    FuncoesString.formatarStringMoeda(Salario2, false), ""),
                            10, "0") //241-250 Penúltimo Salário
                    + FuncoesString.PreencheEsquerda(
                            FuncoesString.limpa(
                                    FuncoesString.formatarStringMoeda(Salario1, false), ""),
                            10, "0") //251-260 Último Salário
                    + "00" //261-262 Meses Trabalhados fixo 00
                    + "0" //263-263 Recebeu os 6 últimos salários fixo 0
                    + FuncoesString.RecortaString(AvisoInden + FuncoesString.SpaceString("", 1), 0, 1) //264-264 Aviso Prévio Indenizado 1-Sim 2-Não                    
                    + "000" //265-267 Código do Banco - fixo 000
                    + "0000" //268-271 Código Agência - fixo 0000
                    + "0" //272-272 DV da Agência - fixo 0
                    + FuncoesString.SpaceString("", 28) //273-300 Filler - Em branco
                    + "\r\n";
            return Registro;
        } catch (Exception e) {
            throw new Exception("Falha ao gerar Registro - " + e.getMessage());
        }
    }

    private static String GerarTrailler(String TotRequerimentos) throws Exception {
        String Header = "";
        try {
            Header = "99" //001-002 Tipo de registro - Fixo 99
                    + FuncoesString.PreencheEsquerda(TotRequerimentos, 5, "0") //003-007 Total de requerimentos enviados
                    + br.com.sasw.pacotesuteis.utilidades.FuncoesString.SpaceString("", 293);                                                   //008-300 Filler - Em branco
            System.out.println(Header.length());
            if (Header.length() < 300) {
                Header = FuncoesString.preencheCom(Header, " ", 300 - Header.length(), 2);
            }
        } catch (Exception e) {
            throw new Exception("Falha ao gerar Trailler - " + e.getMessage());
        }
        return Header;
    }

    private static String Escolaridade(String value) {
        String retorno;
        /*10 analfabeto
        20 primario incompleto
        25 primario completo
        30 1 grau incompleto
        35 1 grau completo
        40 2 grau incompleto
        45 2 grau completo
        50 superior incompleto
        55 superior completo
        65 mestrado completo
        75 doutorado completo*/
        switch (value) {
            case ("10"):
                retorno = "01";
                break;
            case ("20"):
                retorno = "02";
                break;
            case ("25"):
                retorno = "03";
                break;
            case ("30"):
                retorno = "04";
                break;
            case ("35"):
                retorno = "05";
                break;
            case ("40"):
                retorno = "06";
                break;
            case ("45"):
                retorno = "07";
                break;
            case ("50"):
                retorno = "08";
                break;
            case ("55"):
                retorno = "09";
                break;
            case ("65"):
                retorno = "10";
                break;
            case ("70"):
                retorno = "11";
                break;
            default:
                retorno = "01";
        }
        return retorno;
    }

//    private static String Aviso(String value) {
//        if ("2".equals(value)) {
//            return "1";
//        } else {
//            return "2";
//        }
//    }
}
