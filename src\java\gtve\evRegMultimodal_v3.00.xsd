<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/cte" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="eventoCTeTiposBasico_v3.00.xsd"/>
	<xs:element name="evRegMultimodal">
		<xs:annotation>
			<xs:documentation>Schema XML de validação do evento Registro Multimodal 110160</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="descEvento">
					<xs:annotation>
						<xs:documentation>Descrição do Evento - “Registro Multimodal”</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:whiteSpace value="preserve"/>
							<xs:enumeration value="Registro Multimodal"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="xRegistro">
					<xs:annotation>
						<xs:documentation>Informação complementar sobre o registro, indicação do tipo de documento utilizado e demais situações ocorridas no Multimodal (Texto Livre).</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:minLength value="15"/>
							<xs:maxLength value="1000"/>
							<xs:whiteSpace value="preserve"/>
							<xs:pattern value="[!-ÿ]{1}[ -ÿ]{0,}[!-ÿ]{1}|[!-ÿ]{1}"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="nDoc" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Numero do Documento lançado no CT-e Multimodal</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:minLength value="1"/>
							<xs:maxLength value="44"/>
							<xs:whiteSpace value="preserve"/>
							<xs:pattern value="[0-9]{1,44}"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
