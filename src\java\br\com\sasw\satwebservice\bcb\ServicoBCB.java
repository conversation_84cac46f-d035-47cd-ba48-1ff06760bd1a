/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.bcb;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;

/**
 *
 * <AUTHOR>
 */
@Path("/bcb/")
public class ServicoBCB {   
    
    private static final String ARRAY_JSON_VAZIO = "[]";
    private static final String OBJETO_JSON_VAZIO = "{}";
    
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/ler-cotacao-dolar-dia")
    public String getCotacaoDolar(@QueryParam("datacotacao") String datacotacao)  {
        LocalDate dataCotacao = convertStringToLocalDate(datacotacao);
        if (dataCotacao != null) {
            try {
                CliemteServicoBCB clienteServicoBCB = new CliemteServicoBCB();
                try {
                  return clienteServicoBCB.lerCotacaoDolarDia(dataCotacao);
                } finally {
                  clienteServicoBCB.fecharConexao();
                }
            } catch (Exception ex) {
                Logger.getLogger(ServicoBCB.class.getName()).log(Level.SEVERE, 
                        null, ex);
                return "Erro: " + ex.getMessage();
            }
        }
        return OBJETO_JSON_VAZIO;
    }

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/ler-cotacao-periodo")
    public String lerCotacaoPeriodo(@QueryParam("datainicial") String dataInicial,
            @QueryParam("datafinal") String dataFinal)  {
        LocalDate localDateInicial = convertStringToLocalDate(dataInicial);
        LocalDate localDateFinal = convertStringToLocalDate(dataFinal);
        if (localDateInicial != null && localDateFinal != null) {
            try {
                CliemteServicoBCB clienteServicoBCB = new CliemteServicoBCB();
                try{
                    return clienteServicoBCB.lerCotacaoDolarPeriodo(
                            localDateInicial, localDateFinal);
                } finally {
                  clienteServicoBCB.fecharConexao();
                }
                
            } catch (Exception ex) {
                Logger.getLogger(ServicoBCB.class.getName()).log(Level.SEVERE, 
                        null, ex);
                return "Erro: " + ex.getMessage();
            }
        }
        return ARRAY_JSON_VAZIO;
    }

    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/ler-cotacao-moeda-dia")
    public String lerCotacaoMoedaDia(@QueryParam("moeda") String moeda,
            @QueryParam("datacotacao") String dataCotacao)  {
        LocalDate localDateCotacao = convertStringToLocalDate(dataCotacao);
        if (localDateCotacao != null) {
            try {
                CliemteServicoBCB clienteServicoBCB = new CliemteServicoBCB();
                try { 
                    return clienteServicoBCB.lerCotacaoMoedaDia(moeda, 
                            localDateCotacao);
                } finally {
                  clienteServicoBCB.fecharConexao();
                }
                
            } catch (Exception ex) {
                Logger.getLogger(ServicoBCB.class.getName()).log(Level.SEVERE, 
                        null, ex);
                return "Erro: " + ex.getMessage();
            }
        }
        return ARRAY_JSON_VAZIO;
    }
    
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/ler-moedas")
    public String lerMoeda()  {
        try {
            CliemteServicoBCB clienteServicoBCB = new CliemteServicoBCB();
            try { 
                return clienteServicoBCB.lerMoeda();
            } finally {
              clienteServicoBCB.fecharConexao();
            }
                
        } catch (Exception ex) {
            Logger.getLogger(ServicoBCB.class.getName()).log(Level.SEVERE, 
                    null, ex);
            return "Erro: " + ex.getMessage();
        }
    }

    
    public static LocalDate convertStringToLocalDate(String dataCotacao) {
        // Define o padrão do formato da data da string de entrada
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        try {
            // Converte a string para um LocalDate usando o formatter
            return LocalDate.parse(dataCotacao, formatter);
        } catch (DateTimeParseException e) {
            // Lida com a exceção caso a string não esteja no formato esperado
            System.err.println("Erro ao converter a data: A string '" + 
                    dataCotacao + "' não corresponde ao formato 'dd-MM-yyyy'.");
            return null; // Ou jogue uma exceção personalizada, dependendo da sua necessidade
        }
    }
    
}
