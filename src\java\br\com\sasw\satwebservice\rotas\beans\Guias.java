/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.rotas.beans;

import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class Guias {
    
    private String guia;
    private String moeda;
    private String serie;
    private String valor;
    private String rpv;
    private List<Volumes> volumes;
    
    private String guiaBD;
    private String processar;

    public String getGuia() {
        return guia;
    }

    public void setGuia(String guia) {
        this.guia = guia;
    }

    public String getMoeda() {
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public String getValor() {
        return valor;
    }

    public void setValor(String valor) {
        this.valor = valor;
    }

    public String getRpv() {
        return rpv;
    }

    public void setRpv(String rpv) {
        this.rpv = rpv;
    }

    public List<Volumes> getVolumes() {
        return volumes;
    }

    public void setVolumes(List<Volumes> volumes) {
        this.volumes = volumes;
    }

    public String getGuiaBD() {
        return guiaBD;
    }

    public void setGuiaBD(String guiaBD) {
        this.guiaBD = guiaBD;
    }

    public String getProcessar() {
        return processar;
    }

    public void setProcessar(String processar) {
        this.processar = processar;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 71 * hash + Objects.hashCode(this.guia);
        hash = 71 * hash + Objects.hashCode(this.moeda);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final Guias other = (Guias) obj;
        if (!Objects.equals(this.guia, other.guia)) {
            return false;
        }
        if (!Objects.equals(this.moeda, other.moeda)) {
            return false;
        }
        return true;
    }
}
