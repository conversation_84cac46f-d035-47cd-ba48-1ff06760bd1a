/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.rotas;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Pedido;
import br.com.sasw.pacotesuteis.sasdaos.formatadas.RoteirizarDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;

/**
 *
 * <AUTHOR>
 */
@Path("/ws-rotas/roteirizacao/")
public class Roteirizacao {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;

    @Context
    private UriInfo context;

    /**
     * Creates a new instance of Roteirizacao
     */
    public Roteirizacao() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Rotas\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/roteirizar")
    public Response post(String input) {
        Gson gson = new GsonBuilder().create();
        Map retorno = new HashMap<>();
        try {
            // Convertendo o input para java
            List<Pedido> pedidos = gson.fromJson(input, new TypeToken<List<Pedido>>() {
            }.getType());

            // Validando se o que foi enviado não gerou uma lista vazia
            if (null == pedidos || pedidos.isEmpty()) {
                throw new Exception("ListaVazia");
            }

            String empresa = pedidos.get(0).getSolicitante();

            // Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao(empresa);
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Rotas\\" + empresa + "\\"
                    + getDataAtual("SQL") + "\\log.txt";

            // Salvando em log o que foi mandado
            this.logerro.Grava(input, this.caminho);

            // Inserindo no banco os pedidos
            RoteirizarDao roteirizarDao = new RoteirizarDao();
            roteirizarDao.roteirizar(pedidos, this.persistencia);

            // Retorno caso tudo aconteça corretamente
            retorno.put("status", "ok");

        } catch (Exception e) {
            // Salvando em log o que foi mandado
            this.logerro.Grava(input, this.caminho);

            retorno.put("status", "error");
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);
        }  finally {
            try {
                persistencia.FechaConexao();
            } catch (Exception p) {
                this.logerro.Grava("Fecha Conexao: " + p.getMessage(), this.caminho);
            }
            
            this.logerro.Grava("Resposta: "+gson.toJson(retorno), this.caminho);
        }
        
        return Response
                .status(Response.Status.OK)
                .type("application/json")
                .entity(gson.toJson(retorno))
                //                .entity(retorno)
                .build();
    }
}
