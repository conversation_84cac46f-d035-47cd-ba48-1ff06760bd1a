/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.integracao.contato;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.ContatosIntegracao;
import SasDaos.ContatosDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.satwebservice.importacao.pedidos.ImportacaoPedidos;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import java.util.HashMap;
import java.util.Map;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;

/**
 * REST Web Service
 *
 * <AUTHOR>
 */
@Path("/ws-contato/")
public class Contato {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private String caminho;
    private Persistencia persistencia;

    @Context
    private UriInfo context;

    /**
     * Creates a new instance of Digibee
     */
    public Contato() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ERRO\\"
                + "\\Contato\\" + DataAtual.getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/{empresa}/cadastrarContato")
    public Response json(@PathParam("empresa") String empresa, String input) {
        Gson gson = new GsonBuilder().create();

        try {
            // Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao(empresa.toUpperCase());
            
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }

            ContatosIntegracao modelo = gson.fromJson(input, ContatosIntegracao.class);
            ContatosDao contatosDao = new ContatosDao();
            
            modelo.setDt_Alter(getDataAtual("SQL"));
            modelo.setHr_Alter(getDataAtual("HORA"));
            contatosDao.inserirContatoIntegracaoBlip(modelo, this.persistencia);
            
            this.logerro.Grava(input, this.caminho);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity("")
                    .build();
        } catch (Exception e) {
            // Salvando em log o que foi mandado
            Map retorno = new HashMap<>();
            retorno.clear();
            retorno.put("status", "error");
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);

            return Response
                    .status(Response.Status.FORBIDDEN)
                    .type("application/json")
                    .entity(gson.toJson(retorno))
                    .build();
        }
    }
}
