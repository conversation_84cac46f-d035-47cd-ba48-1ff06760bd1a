/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.dto.request;

/**
 *
 * <AUTHOR>
 */
public class TransferenciaRequest {
    
    private String cnpjEmpresaDestino;
    private String senhaConexao;
    private String numeroPedido;
    private String cnpjPontoCliente;
    private String dataOperacao;
    private String valorOperacao;
    private String numeroCofreInteligenteOrigem;
    private String numeroCofreInteligenteDestino;
    private String cnpjEmpresaProcessadora;
    private String cnpjTransportadoraOrigem;

    public TransferenciaRequest() {}

    public String getCnpjEmpresaDestino() {
        return cnpjEmpresaDestino;
    }

    public void setCnpjEmpresaDestino(String cnpjEmpresaDestino) {
        this.cnpjEmpresaDestino = cnpjEmpresaDestino;
    }

    public String getSenhaConexao() {
        return senhaConexao;
    }

    public void setSenhaConexao(String senhaConexao) {
        this.senhaConexao = senhaConexao;
    }

    public String getNumeroPedido() {
        return numeroPedido;
    }

    public void setNumeroPedido(String numeroPedido) {
        this.numeroPedido = numeroPedido;
    }

    public String getCnpjPontoCliente() {
        return cnpjPontoCliente;
    }

    public void setCnpjPontoCliente(String cnpjPontoCliente) {
        this.cnpjPontoCliente = cnpjPontoCliente;
    }

    public String getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(String dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getValorOperacao() {
        return valorOperacao;
    }

    public void setValorOperacao(String valorOperacao) {
        this.valorOperacao = valorOperacao;
    }

    public String getNumeroCofreInteligenteOrigem() {
        return numeroCofreInteligenteOrigem;
    }

    public void setNumeroCofreInteligenteOrigem(String numeroCofreInteligenteOrigem) {
        this.numeroCofreInteligenteOrigem = numeroCofreInteligenteOrigem;
    }

    public String getNumeroCofreInteligenteDestino() {
        return numeroCofreInteligenteDestino;
    }

    public void setNumeroCofreInteligenteDestino(String numeroCofreInteligenteDestino) {
        this.numeroCofreInteligenteDestino = numeroCofreInteligenteDestino;
    }

    public String getCnpjEmpresaProcessadora() {
        return cnpjEmpresaProcessadora;
    }

    public void setCnpjEmpresaProcessadora(String cnpjEmpresaProcessadora) {
        this.cnpjEmpresaProcessadora = cnpjEmpresaProcessadora;
    }

    public String getCnpjTransportadoraOrigem() {
        return cnpjTransportadoraOrigem;
    }

    public void setCnpjTransportadoraOrigem(String cnpjTransportadoraOrigem) {
        this.cnpjTransportadoraOrigem = cnpjTransportadoraOrigem;
    }
}
