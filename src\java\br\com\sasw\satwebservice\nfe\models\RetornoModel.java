/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.nfe.models;

import br.com.sasw.satwebservice.nfe.exception.NFeException;
import com.google.gson.JsonObject;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RetornoModel {
    
    public RetornoModel(NFeException nFeException, JsonObject nfe){
        this.rejeicao = nFeException.getCode().getRejeicao();
        this.nfe = nfe;
        this.nfe.addProperty("acao", 9);
    }
    
    public RetornoModel(NFeException nFeException){
        this.rejeicao = nFeException.getCode().getRejeicao();
        this.nfe = nFeException.getCode().getNfe();
        this.nfe.addProperty("acao", 9);
    }
    
    /**
     * 
     * @param envio
     * @param acao
     * @param chave 
     */
    public RetornoModel(EnvioModel envio, Integer acao, String chave, ProtocoloModel protocolo) {
        this.acao = acao;
        this.chave = chave;
        this.orgao = envio.getOrgao();
        this.natOp = envio.getNatOp();
        this.indPag = envio.getIndPag();
        this.mod = envio.getMod();
        this.serie = envio.getSerie();
        this.nRPS = envio.getnRPS();
        this.datahemi = envio.getDatahemi();
        this.dataSaiEnt = envio.getDataSaiEnt();
        this.HoraSaiEnt = envio.getHoraSaiEnt();
        this.tpNF = envio.getTpNF();
        this.codcidadeicms = envio.getCodcidadeicms();
        this.tipoImpressao = envio.getTipoImpressao();
        this.cnpjemissor = envio.getCnpjemissor();
        this.razaosocialemissor = envio.getRazaosocialemissor();
        this.fantasiaemissor = envio.getFantasiaemissor();
        this.enderecoemissor = envio.getEnderecoemissor();
        this.bairroemissor = envio.getBairroemissor();
        this.cidadeemissor = envio.getCidadeemissor();
        this.ufemissor = envio.getUfemissor();
        this.codcidadeemissor = envio.getCodcidadeemissor();
        this.cepemissor = envio.getCepemissor();
        this.paisemissor = envio.getPaisemissor();
        this.telefoneemissor = envio.getTelefoneemissor();
        this.emailemissor = envio.getEmailemissor();
        this.CNAE = envio.getCNAE();
        this.CRT = envio.getCRT();
        this.cnpjdest = envio.getCnpjdest();
        this.cpfdest = envio.getCpfdest();
        this.razaosocialdest = envio.getRazaosocialdest();
        this.fantasiadest = envio.getFantasiadest();
        this.enderecodest = envio.getEnderecodest();
        this.bairrodest = envio.getBairrodest();
        this.cidadedest = envio.getCidadedest();
        this.ufdest = envio.getUfdest();
        this.codcidadedest = envio.getCodcidadedest();
        this.cepdest = envio.getCepdest();
        this.telefonedest = envio.getTelefonedest();
        this.emaildest = envio.getEmaildest();
        this.iedest = envio.getIedest();
        this.observacao = envio.getObservacao();
        this.itens = envio.getItens();
        this.protocolo = protocolo;
    }
    
    /**
     * 
     * @param envio
     * @param acao
     * @param chave 
     */
    public RetornoModel(EnvioModel envio, Integer acao, String chave, ProtocoloModel protocolo, RejeicaoModel rejeicao) {
        this.acao = acao;
        this.chave = chave;
        this.orgao = envio.getOrgao();
        this.natOp = envio.getNatOp();
        this.indPag = envio.getIndPag();
        this.mod = envio.getMod();
        this.serie = envio.getSerie();
        this.nRPS = envio.getnRPS();
        this.datahemi = envio.getDatahemi();
        this.dataSaiEnt = envio.getDataSaiEnt();
        this.HoraSaiEnt = envio.getHoraSaiEnt();
        this.tpNF = envio.getTpNF();
        this.codcidadeicms = envio.getCodcidadeicms();
        this.tipoImpressao = envio.getTipoImpressao();
        this.cnpjemissor = envio.getCnpjemissor();
        this.razaosocialemissor = envio.getRazaosocialemissor();
        this.fantasiaemissor = envio.getFantasiaemissor();
        this.enderecoemissor = envio.getEnderecoemissor();
        this.bairroemissor = envio.getBairroemissor();
        this.cidadeemissor = envio.getCidadeemissor();
        this.ufemissor = envio.getUfemissor();
        this.codcidadeemissor = envio.getCodcidadeemissor();
        this.cepemissor = envio.getCepemissor();
        this.paisemissor = envio.getPaisemissor();
        this.telefoneemissor = envio.getTelefoneemissor();
        this.emailemissor = envio.getEmailemissor();
        this.CNAE = envio.getCNAE();
        this.CRT = envio.getCRT();
        this.cnpjdest = envio.getCnpjdest();
        this.cpfdest = envio.getCpfdest();
        this.razaosocialdest = envio.getRazaosocialdest();
        this.fantasiadest = envio.getFantasiadest();
        this.enderecodest = envio.getEnderecodest();
        this.bairrodest = envio.getBairrodest();
        this.cidadedest = envio.getCidadedest();
        this.ufdest = envio.getUfdest();
        this.codcidadedest = envio.getCodcidadedest();
        this.cepdest = envio.getCepdest();
        this.telefonedest = envio.getTelefonedest();
        this.emaildest = envio.getEmaildest();
        this.iedest = envio.getIedest();
        this.observacao = envio.getObservacao();
        this.itens = envio.getItens();
        this.protocolo = protocolo;
        this.rejeicao = rejeicao;
    }
    
    /**
     * 
     * @param envio
     * @param acao
     * @param chave 
     */
    public RetornoModel(EnvioModel envio, Integer acao, String chave) {
        this.acao = acao;
        this.chave = chave;
        this.orgao = envio.getOrgao();
        this.natOp = envio.getNatOp();
        this.indPag = envio.getIndPag();
        this.mod = envio.getMod();
        this.serie = envio.getSerie();
        this.nRPS = envio.getnRPS();
        this.datahemi = envio.getDatahemi();
        this.dataSaiEnt = envio.getDataSaiEnt();
        this.HoraSaiEnt = envio.getHoraSaiEnt();
        this.tpNF = envio.getTpNF();
        this.codcidadeicms = envio.getCodcidadeicms();
        this.tipoImpressao = envio.getTipoImpressao();
        this.cnpjemissor = envio.getCnpjemissor();
        this.razaosocialemissor = envio.getRazaosocialemissor();
        this.fantasiaemissor = envio.getFantasiaemissor();
        this.enderecoemissor = envio.getEnderecoemissor();
        this.bairroemissor = envio.getBairroemissor();
        this.cidadeemissor = envio.getCidadeemissor();
        this.ufemissor = envio.getUfemissor();
        this.codcidadeemissor = envio.getCodcidadeemissor();
        this.cepemissor = envio.getCepemissor();
        this.paisemissor = envio.getPaisemissor();
        this.telefoneemissor = envio.getTelefoneemissor();
        this.emailemissor = envio.getEmailemissor();
        this.CNAE = envio.getCNAE();
        this.CRT = envio.getCRT();
        this.cnpjdest = envio.getCnpjdest();
        this.cpfdest = envio.getCpfdest();
        this.razaosocialdest = envio.getRazaosocialdest();
        this.fantasiadest = envio.getFantasiadest();
        this.enderecodest = envio.getEnderecodest();
        this.bairrodest = envio.getBairrodest();
        this.cidadedest = envio.getCidadedest();
        this.ufdest = envio.getUfdest();
        this.codcidadedest = envio.getCodcidadedest();
        this.cepdest = envio.getCepdest();
        this.telefonedest = envio.getTelefonedest();
        this.emaildest = envio.getEmaildest();
        this.iedest = envio.getIedest();
        this.observacao = envio.getObservacao();
        this.itens = envio.getItens();
    }

    private Integer acao;
    private String chave;
    private Integer orgao;
    private String natOp;
    private Integer indPag;
    private String mod;
    private Integer serie;
    private String nRPS;
    private String datahemi;
    private String dataSaiEnt;
    private String HoraSaiEnt;
    private Integer tpNF;
    private Integer codcidadeicms;
    private Integer tipoImpressao;
    private String cnpjemissor;
    private String razaosocialemissor;
    private String fantasiaemissor;
    private String enderecoemissor;
    private String bairroemissor;
    private String cidadeemissor;
    private String ufemissor;
    private Integer codcidadeemissor;
    private String cepemissor;
    private Integer paisemissor;
    private String telefoneemissor;
    private String emailemissor;
    private Integer CNAE;
    private Integer CRT;
    private String cnpjdest;
    private String cpfdest;
    private String razaosocialdest;
    private String fantasiadest;
    private String enderecodest;
    private String bairrodest;
    private String cidadedest;
    private String ufdest;
    private Integer codcidadedest;
    private String cepdest;
    private String telefonedest;
    private String emaildest;
    private String iedest;
    private String observacao;
    private List<ItensModel> itens;
    
    private ProtocoloModel protocolo;
    private RejeicaoModel rejeicao;
    private JsonObject nfe;

    public Integer getAcao() {
        return acao;
    }

    public void setAcao(Integer acao) {
        this.acao = acao;
    }

    public Integer getOrgao() {
        return orgao;
    }

    public void setOrgao(Integer orgao) {
        this.orgao = orgao;
    }

    public String getNatOp() {
        return natOp;
    }

    public void setNatOp(String natOp) {
        this.natOp = natOp;
    }

    public Integer getIndPag() {
        return indPag;
    }

    public void setIndPag(Integer indPag) {
        this.indPag = indPag;
    }

    public String getMod() {
        return mod;
    }

    public void setMod(String mod) {
        this.mod = mod;
    }

    public Integer getSerie() {
        return serie;
    }

    public void setSerie(Integer serie) {
        this.serie = serie;
    }

    public String getnRPS() {
        return nRPS;
    }

    public void setnRPS(String nRPS) {
        this.nRPS = nRPS;
    }

    public String getDatahemi() {
        return datahemi;
    }

    public void setDatahemi(String datahemi) {
        this.datahemi = datahemi;
    }

    public String getDataSaiEnt() {
        return dataSaiEnt;
    }

    public void setDataSaiEnt(String dataSaiEnt) {
        this.dataSaiEnt = dataSaiEnt;
    }

    public String getHoraSaiEnt() {
        return HoraSaiEnt;
    }

    public void setHoraSaiEnt(String HoraSaiEnt) {
        this.HoraSaiEnt = HoraSaiEnt;
    }

    public Integer getTpNF() {
        return tpNF;
    }

    public void setTpNF(Integer tpNF) {
        this.tpNF = tpNF;
    }

    public Integer getCodcidadeicms() {
        return codcidadeicms;
    }

    public void setCodcidadeicms(Integer codcidadeicms) {
        this.codcidadeicms = codcidadeicms;
    }

    public Integer getTipoImpressao() {
        return tipoImpressao;
    }

    public void setTipoImpressao(Integer tipoImpressao) {
        this.tipoImpressao = tipoImpressao;
    }

    public String getCnpjemissor() {
        return cnpjemissor;
    }

    public void setCnpjemissor(String cnpjemissor) {
        this.cnpjemissor = cnpjemissor;
    }

    public String getRazaosocialemissor() {
        return razaosocialemissor;
    }

    public void setRazaosocialemissor(String razaosocialemissor) {
        this.razaosocialemissor = razaosocialemissor;
    }

    public String getFantasiaemissor() {
        return fantasiaemissor;
    }

    public void setFantasiaemissor(String fantasiaemissor) {
        this.fantasiaemissor = fantasiaemissor;
    }

    public String getEnderecoemissor() {
        return enderecoemissor;
    }

    public void setEnderecoemissor(String enderecoemissor) {
        this.enderecoemissor = enderecoemissor;
    }

    public String getBairroemissor() {
        return bairroemissor;
    }

    public void setBairroemissor(String bairroemissor) {
        this.bairroemissor = bairroemissor;
    }

    public String getCidadeemissor() {
        return cidadeemissor;
    }

    public void setCidadeemissor(String cidadeemissor) {
        this.cidadeemissor = cidadeemissor;
    }

    public String getUfemissor() {
        return ufemissor;
    }

    public void setUfemissor(String ufemissor) {
        this.ufemissor = ufemissor;
    }

    public Integer getCodcidadeemissor() {
        return codcidadeemissor;
    }

    public void setCodcidadeemissor(Integer codcidadeemissor) {
        this.codcidadeemissor = codcidadeemissor;
    }

    public String getCepemissor() {
        return cepemissor;
    }

    public void setCepemissor(String cepemissor) {
        this.cepemissor = cepemissor;
    }

    public Integer getPaisemissor() {
        return paisemissor;
    }

    public void setPaisemissor(Integer paisemissor) {
        this.paisemissor = paisemissor;
    }

    public String getTelefoneemissor() {
        return telefoneemissor;
    }

    public void setTelefoneemissor(String telefoneemissor) {
        this.telefoneemissor = telefoneemissor;
    }

    public String getEmailemissor() {
        return emailemissor;
    }

    public void setEmailemissor(String emailemissor) {
        this.emailemissor = emailemissor;
    }

    public Integer getCNAE() {
        return CNAE;
    }

    public void setCNAE(Integer CNAE) {
        this.CNAE = CNAE;
    }

    public Integer getCRT() {
        return CRT;
    }

    public void setCRT(Integer CRT) {
        this.CRT = CRT;
    }

    public String getCnpjdest() {
        return cnpjdest;
    }

    public void setCnpjdest(String cnpjdest) {
        this.cnpjdest = cnpjdest;
    }

    public String getCpfdest() {
        return cpfdest;
    }

    public void setCpfdest(String cpfdest) {
        this.cpfdest = cpfdest;
    }

    public String getRazaosocialdest() {
        return razaosocialdest;
    }

    public void setRazaosocialdest(String razaosocialdest) {
        this.razaosocialdest = razaosocialdest;
    }

    public String getFantasiadest() {
        return fantasiadest;
    }

    public void setFantasiadest(String fantasiadest) {
        this.fantasiadest = fantasiadest;
    }

    public String getEnderecodest() {
        return enderecodest;
    }

    public void setEnderecodest(String enderecodest) {
        this.enderecodest = enderecodest;
    }

    public String getBairrodest() {
        return bairrodest;
    }

    public void setBairrodest(String bairrodest) {
        this.bairrodest = bairrodest;
    }

    public String getCidadedest() {
        return cidadedest;
    }

    public void setCidadedest(String cidadedest) {
        this.cidadedest = cidadedest;
    }

    public String getUfdest() {
        return ufdest;
    }

    public void setUfdest(String ufdest) {
        this.ufdest = ufdest;
    }

    public Integer getCodcidadedest() {
        return codcidadedest;
    }

    public void setCodcidadedest(Integer codcidadedest) {
        this.codcidadedest = codcidadedest;
    }

    public String getCepdest() {
        return cepdest;
    }

    public void setCepdest(String cepdest) {
        this.cepdest = cepdest;
    }

    public String getTelefonedest() {
        return telefonedest;
    }

    public void setTelefonedest(String telefonedest) {
        this.telefonedest = telefonedest;
    }

    public String getEmaildest() {
        return emaildest;
    }

    public void setEmaildest(String emaildest) {
        this.emaildest = emaildest;
    }

    public String getIedest() {
        return iedest;
    }

    public void setIedest(String iedest) {
        this.iedest = iedest;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public List<ItensModel> getItens() {
        if (itens == null) {
            itens = new ArrayList<>();
        } 
        return itens;
    }

    public void setItens(List<ItensModel> itens) {
        this.itens = itens;
    }

    public ProtocoloModel getProtocolo() {
        return protocolo;
    }

    public void setProtocolo(ProtocoloModel protocolo) {
        this.protocolo = protocolo;
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public RejeicaoModel getRejeicao() {
        return rejeicao;
    }

    public void setRejeicao(RejeicaoModel rejeicao) {
        this.rejeicao = rejeicao;
    }

    public JsonObject getNfe() {
        return nfe;
    }

    public void setNfe(JsonObject nfe) {
        this.nfe = nfe;
    }
}
