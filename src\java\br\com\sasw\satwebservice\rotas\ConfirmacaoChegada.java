/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.rotas;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Rt_PercDet;
import SasDaos.Rt_PercDao;
import SasDaos.Rt_PercDetDao;
import SasDaos.Rt_PercSlaDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.Horaminuto;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;

/**
 *
 * <AUTHOR>
 */
@Path("/ws-rotas/confirmarchegada/")
public class ConfirmacaoChegada {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;

    @Context
    private UriInfo context;

    /**
     * Creates a new instance of BaixaServico
     */
    public ConfirmacaoChegada() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Rotas\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public Response post(String input) {

        Gson gson = new GsonBuilder().create();
        Map retorno = new HashMap<>();

        String sequencia = null;

        try {
            // Convertendo o input para java
            JsonObject jsonObject;
            try{
                jsonObject = new JsonParser().parse(input).getAsJsonObject();
            } catch (Exception e){
                jsonObject = new JsonParser().parse(input).getAsJsonArray().get(0).getAsJsonObject();
            }

            // Buscando as informações do json
            String param;
            try {
                param = jsonObject.get("param").getAsString().replace(".0", "");
            } catch (Exception ee) {
                // Salvando em log o que foi mandado
                this.logerro.Grava(input, this.caminho);
                throw new Exception("param");
            }
            
            // Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao(param);
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + param + ")");
            }

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Rotas\\" + param + "\\"
                    + getDataAtual("SQL") + "\\log.txt";

            // Salvando em log o que foi mandado
            this.logerro.Grava(input, this.caminho);

            String operador;
            try {
                operador = jsonObject.get("operador").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("operador");
            }

            String dataAtual;
            try {
                dataAtual = jsonObject.get("dataAtual").getAsString().replace(".0", "");
            } catch (Exception ee) {
                dataAtual = getDataAtual("SQL");
            }

            String horaAtual;
            try {
                horaAtual = jsonObject.get("horaAtual").getAsString().replace(".0", "");
            } catch (Exception ee) {
                horaAtual = getDataAtual("HORA");
            }

            try {
                sequencia = jsonObject.get("sequencia").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("sequencia");
            }

            String codfil;
            try {
                codfil = jsonObject.get("codfil").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("codfil");
            }

            String parada;
            try {
                parada = jsonObject.get("parada").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("parada");
            }

            String hora1;
            try {
                hora1 = jsonObject.get("hora1").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("hora1");
            }

            String hrcheg;
            try {
                hrcheg = jsonObject.get("hrcheg").getAsString().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("hrcheg");
            }

            String hrchegvei;
            try {
                hrchegvei = jsonObject.get("hrchegvei").getAsString().replace(".0", "");
            } catch (Exception ee) {
                hrchegvei = hrcheg;
            }

            String KM;
            try {
                KM = jsonObject.get("km").getAsString().replace(".0", "");
            } catch (Exception ee) {
                KM = "";
            }

            Rt_PercDao rt_percDao = new Rt_PercDao();
            Rt_PercSlaDao rt_percSlaDao = new Rt_PercSlaDao();

            Horaminuto conversor = new Horaminuto(hrcheg, hora1);
            String atraso = String.valueOf(conversor.iDifHora1Hora2min());

            rt_percDao.updateHrCheg(sequencia, parada, hrcheg, atraso, persistencia);
            rt_percSlaDao.inserirHorarioChegada(new BigDecimal(sequencia), Integer.parseInt(parada), 
                    new BigDecimal(codfil), hrchegvei, operador, dataAtual, horaAtual, persistencia);

            // gravando dados em Rt_PercDet 
            try {

                Rt_PercDet rt_percdet = new Rt_PercDet();
                Rt_PercDetDao rt_percdetdao = new Rt_PercDetDao();
                rt_percdet.setCodFil(codfil);
                rt_percdet.setSequencia(sequencia);
                rt_percdet.setParada(Integer.valueOf(parada));
                rt_percdet.setKM(KM);
                if (rt_percdetdao.existeDet(rt_percdet, persistencia)) {
                    rt_percdetdao.AtualizaKMDet(rt_percdet, persistencia);
                } else {
                    rt_percdetdao.InserirDet(rt_percdet, persistencia);
                }
            } catch (Exception e) {
                this.logerro.Grava("Rt_PercDet. Erro ao atualizar dados. SeqRota: " + sequencia + " Parada: " + parada + " KM: " + KM + " " + e.getMessage(), this.caminho);
            }
            retorno.put("resp", 1); // Tudo OK  
        } catch (Exception e) {
            this.logerro.Grava("Erro geral: " + e.getMessage(), this.caminho);
            retorno.put("resp", 0);
            retorno.put("erro", e.getMessage());
        } finally {
            try {
                persistencia.FechaConexao();
            } catch (Exception p) {
                this.logerro.Grava("Fecha Conexao: " + p.getMessage(), this.caminho);
            }

            this.logerro.Grava("Resposta: " + gson.toJson(retorno), this.caminho);
        }

        return Response
                .status(Response.Status.OK)
                .type("application/json")
                .entity(gson.toJson(retorno))
                //                .entity(retorno)
                .build();
    }
}
