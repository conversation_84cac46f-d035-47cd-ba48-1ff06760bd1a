/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.nfe.models;

/**
 *
 * <AUTHOR>
 */
public class ItensModel {

    private Integer Numero;
    private String Codigo;
    private String Descriçao;
    private String CFOP;
    private String unCom;
    private Float Qtde;
    private Float valorUn;
    private Float valor;
    private Float valorFrete;
    private Float valorSeg;
    private Float valorDesc;
    private Float valorOutros;
    private Integer indTributo;
    // ImpModel
    private Integer Ori;
    private String CST;
    private Float aliqISS;
    private Float valorISS;
    private Float aliqICMS;
    private Float valorICMS;
    private Float valorBC;
    private Float aliqPIS;
    private Float valorPIS;
    private Float aliqCOFINS;
    private Float valorCOFINS;
    private Float aliqCSL;
    private Float valorCSL;
    private Float aliqIR;
    private Float valorIR;
    private Float valorRetISS;
    private Float valorRetICMS;
    private Float valorRetPIS;
    private Float valorRetCSL;
    private Float valorRetIR;

    public Integer getNumero() {
        return Numero;
    }

    public void setNumero(Integer Numero) {
        this.Numero = Numero;
    }

    public String getCodigo() {
        return Codigo;
    }

    public void setCodigo(String Codigo) {
        this.Codigo = Codigo;
    }

    public String getDescriçao() {
        return Descriçao;
    }

    public void setDescriçao(String Descriçao) {
        this.Descriçao = Descriçao;
    }

    public String getCFOP() {
        return CFOP;
    }

    public void setCFOP(String CFOP) {
        this.CFOP = CFOP;
    }

    public String getUnCom() {
        return unCom;
    }

    public void setUnCom(String unCom) {
        this.unCom = unCom;
    }

    public Float getQtde() {
        return Qtde;
    }

    public void setQtde(Float Qtde) {
        this.Qtde = Qtde;
    }

    public Float getValorUn() {
        return valorUn;
    }

    public void setValorUn(Float valorUn) {
        this.valorUn = valorUn;
    }

    public Float getValor() {
        return valor;
    }

    public void setValor(Float valor) {
        this.valor = valor;
    }

    public Float getValorFrete() {
        return valorFrete;
    }

    public void setValorFrete(Float valorFrete) {
        this.valorFrete = valorFrete;
    }

    public Float getValorSeg() {
        return valorSeg;
    }

    public void setValorSeg(Float valorSeg) {
        this.valorSeg = valorSeg;
    }

    public Float getValorDesc() {
        return valorDesc;
    }

    public void setValorDesc(Float valorDesc) {
        this.valorDesc = valorDesc;
    }

    public Float getValorOutros() {
        return valorOutros;
    }

    public void setValorOutros(Float valorOutros) {
        this.valorOutros = valorOutros;
    }

    public Integer getIndTributo() {
        return indTributo;
    }

    public void setIndTributo(Integer indTributo) {
        this.indTributo = indTributo;
    }

    public Integer getOri() {
        return Ori;
    }

    public void setOri(Integer Ori) {
        this.Ori = Ori;
    }

    public String getCST() {
        return CST;
    }

    public void setCST(String CST) {
        this.CST = CST;
    }

    public Float getAliqISS() {
        return aliqISS;
    }

    public void setAliqISS(Float aliqISS) {
        this.aliqISS = aliqISS;
    }

    public Float getValorISS() {
        return valorISS;
    }

    public void setValorISS(Float valorISS) {
        this.valorISS = valorISS;
    }

    public Float getAliqICMS() {
        return aliqICMS;
    }

    public void setAliqICMS(Float aliqICMS) {
        this.aliqICMS = aliqICMS;
    }

    public Float getValorICMS() {
        return valorICMS;
    }

    public void setValorICMS(Float valorICMS) {
        this.valorICMS = valorICMS;
    }

    public Float getValorBC() {
        return valorBC;
    }

    public void setValorBC(Float valorBC) {
        this.valorBC = valorBC;
    }

    public Float getAliqPIS() {
        return aliqPIS;
    }

    public void setAliqPIS(Float aliqPIS) {
        this.aliqPIS = aliqPIS;
    }

    public Float getValorPIS() {
        return valorPIS;
    }

    public void setValorPIS(Float valorPIS) {
        this.valorPIS = valorPIS;
    }

    public Float getAliqCOFINS() {
        return aliqCOFINS;
    }

    public void setAliqCOFINS(Float aliqCOFINS) {
        this.aliqCOFINS = aliqCOFINS;
    }

    public Float getValorCOFINS() {
        return valorCOFINS;
    }

    public void setValorCOFINS(Float valorCOFINS) {
        this.valorCOFINS = valorCOFINS;
    }

    public Float getAliqCSL() {
        return aliqCSL;
    }

    public void setAliqCSL(Float aliqCSL) {
        this.aliqCSL = aliqCSL;
    }

    public Float getValorCSL() {
        return valorCSL;
    }

    public void setValorCSL(Float valorCSL) {
        this.valorCSL = valorCSL;
    }

    public Float getAliqIR() {
        return aliqIR;
    }

    public void setAliqIR(Float aliqIR) {
        this.aliqIR = aliqIR;
    }

    public Float getValorIR() {
        return valorIR;
    }

    public void setValorIR(Float valorIR) {
        this.valorIR = valorIR;
    }

    public Float getValorRetISS() {
        return valorRetISS;
    }

    public void setValorRetISS(Float valorRetISS) {
        this.valorRetISS = valorRetISS;
    }

    public Float getValorRetICMS() {
        return valorRetICMS;
    }

    public void setValorRetICMS(Float valorRetICMS) {
        this.valorRetICMS = valorRetICMS;
    }

    public Float getValorRetPIS() {
        return valorRetPIS;
    }

    public void setValorRetPIS(Float valorRetPIS) {
        this.valorRetPIS = valorRetPIS;
    }

    public Float getValorRetCSL() {
        return valorRetCSL;
    }

    public void setValorRetCSL(Float valorRetCSL) {
        this.valorRetCSL = valorRetCSL;
    }

    public Float getValorRetIR() {
        return valorRetIR;
    }

    public void setValorRetIR(Float valorRetIR) {
        this.valorRetIR = valorRetIR;
    }
}