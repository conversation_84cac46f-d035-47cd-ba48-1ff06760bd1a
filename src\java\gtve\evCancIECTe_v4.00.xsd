<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2014 rel. 2 (http://www.altova.com) by private (private) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/cte" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="eventoCTeTiposBasico_v4.00.xsd"/>
	<xs:element name="evCancIECTe">
		<xs:annotation>
			<xs:documentation>Schema XML de validação do evento cancelamento do insucesso de entrega eletrônico do CT-e 
110191</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="descEvento">
					<xs:annotation>
						<xs:documentation>Descrição do Evento - “Cancelamento do Insucesso de Entrega do CT-e”</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:whiteSpace value="preserve"/>
							<xs:enumeration value="Cancelamento do Insucesso de Entrega do CT-e"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="nProt" type="TProt">
					<xs:annotation>
						<xs:documentation>Número do Protocolo de autorização do CT-e</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="nProtIE" type="TProt">
					<xs:annotation>
						<xs:documentation>Número do Protocolo de autorização do evento a ser cancelado </xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
