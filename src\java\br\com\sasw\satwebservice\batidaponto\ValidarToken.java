/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.batidaponto;

import br.com.sasw.pacotesuteis.sasbeans.TOKENS;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 *
 * <AUTHOR>
 */
public class ValidarToken {

    public static void validarExistencia(TOKENS token) throws BatidaPontoException {
        try{
            if(token == null) throw new BatidaPontoException();
            if(token.getCodigo() == null) throw new BatidaPontoException();
            if(token.getCodigo().equals("")) throw new BatidaPontoException();
        } catch (Exception e){
            throw new BatidaPontoException("TokenInvalido");
        }
    }

    /**
     * TOKENS.dtValid deve estar no formato 'yyyyMMdd'
     * @param token
     * @throws BatidaPontoException 
     */
    public static void validarValidade(TOKENS token) throws BatidaPontoException {
        try{
            if(token.getDtValid() == null) throw new BatidaPontoException();
            if(token.getDtValid().equals("")) throw new BatidaPontoException();
            
            LocalDateTime validadeToken = LocalDateTime.parse(token.getDtValid(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime dataAtual = LocalDateTime.parse(getDataAtual("SQL-L") + " " + getDataAtual("HORASEGUNDOS"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            
            if(dataAtual.isAfter(validadeToken)) throw new BatidaPontoException();
        } catch (Exception e){
            throw new BatidaPontoException("TokenExpirado");
        }
    }
    
    /**
     * Valida se o token informado se refere à matrícula também informada
     * @param token
     * @param matr
     * @throws BatidaPontoException 
     */
    public static void validarMatricula(TOKENS token, String matr) throws BatidaPontoException {
        try{
            if(!matr.replace(".0", "").equals(token.getChave().replace("SUPERVISOR - ", "").replace(".0",""))) throw new BatidaPontoException();
        } catch (Exception e){
            throw new BatidaPontoException("TokenMatriculaInvalida");
        }
    }
}
