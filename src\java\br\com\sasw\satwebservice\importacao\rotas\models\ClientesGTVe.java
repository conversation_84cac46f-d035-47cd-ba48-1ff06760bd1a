/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.importacao.rotas.models;

/**
 *
 * <AUTHOR>
 */
public class ClientesGTVe {
    
    private String cnpj;
    private String cpf;
    private String razaosocial;
    private String fantasia;
    private String endereco;
    private String numero;
    private String bairro;
    private String cidade;
    private String codcidade;
    private String uf;
    private String cep;
    private String telefone;
    private String email;
    private String ie;
    private String latitude;
    private String longitude;

    public ClientesGTVe() {
    }

    public ClientesGTVe(ClientesGTVe clientesGTVe) {   
        this.cnpj = clientesGTVe.getCnpj();
        this.cpf = clientesGTVe.getCpf();
        this.razaosocial = clientesGTVe.getRazaosocial();
        this.fantasia = clientesGTVe.getFantasia();
        this.endereco = clientesGTVe.getEndereco();
        this.numero = clientesGTVe.getNumero();
        this.bairro = clientesGTVe.getBairro();
        this.cidade = clientesGTVe.getCidade();
        this.codcidade = clientesGTVe.getCodcidade();
        this.uf = clientesGTVe.getUf();
        this.cep = clientesGTVe.getCep();
        this.telefone = clientesGTVe.getTelefone();
        this.email = clientesGTVe.getEmail();
        this.ie = clientesGTVe.getIe();
        this.latitude = clientesGTVe.getLatitude();
        this.longitude = clientesGTVe.getLongitude();
    }

    public String getCnpj() {
        return cnpj;
    }

    public void setCnpj(String cnpj) {
        this.cnpj = cnpj;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getRazaosocial() {
        return razaosocial;
    }

    public void setRazaosocial(String razaosocial) {
        this.razaosocial = razaosocial;
    }

    public String getFantasia() {
        return fantasia;
    }

    public void setFantasia(String fantasia) {
        this.fantasia = fantasia;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getCidade() {
        return cidade;
    }

    public void setCidade(String cidade) {
        this.cidade = cidade;
    }

    public String getCodcidade() {
        return codcidade;
    }

    public void setCodcidade(String codcidade) {
        this.codcidade = codcidade;
    }

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getIe() {
        return ie;
    }

    public void setIe(String ie) {
        this.ie = ie;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
    
}
