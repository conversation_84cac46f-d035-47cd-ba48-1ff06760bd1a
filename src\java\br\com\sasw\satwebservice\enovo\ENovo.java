/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.enovo;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Clientes;
import SasBeans.CxForte;
import SasBeans.Pedido;
import SasBeans.Rotas;
import SasBeans.Rt_Perc;
import SasDaos.ClientesDao;
import SasDaos.CxForteDao;
import SasDaos.OS_VigDao;
import SasDaos.PedidoDao;
import SasDaos.RotasDao;
import SasDaos.Rt_PercDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.removeAcentoObjeto;
import br.com.sasw.pacotesuteis.utilidades.Validacoes;
import br.com.sasw.satwebservice.importacao.exceptions.RotaException;
import br.com.sasw.satwebservice.importacao.rotas.MapDeserializer;
import br.com.sasw.satwebservice.importacao.rotas.RotaExceptionSerializer;
import br.com.sasw.satwebservice.importacao.rotas.models.ClientesGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.ComposicoesGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.EmissorGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.GuiasGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.RotasGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.TrajetosGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.VeiculosGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.VolumesGTVe;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.internal.StringMap;
import com.google.gson.reflect.TypeToken;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.Path;
import javax.ws.rs.core.Response;

/**
 *
 * <AUTHOR>
 */
@Path("/ws-enovo/")
public class ENovo {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;
    private final ClientesDao clientesDao = new ClientesDao();
    private final CxForteDao cxForteDao = new CxForteDao();
    private final OS_VigDao os_VigDao = new OS_VigDao();
    private final PedidoDao pedidoDao = new PedidoDao();
    private final RotasDao rotasDao = new RotasDao();
    private final Rt_PercDao rt_PercDao = new Rt_PercDao();

    /**
     * Creates a new instance of ENovo
     */
    public ENovo() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    public Response json(String empresa, String guia, String serie) {
//
//        empresa = "SASW";

        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(RotaException.class, new RotaExceptionSerializer());
        Gson gson = gsonBuilder.create();
        Map retorno = new HashMap<>(), processamento, processamentoTrajeto;
        List processamentos = new ArrayList<>(), trajetosProcessado;
        
        try {
            // Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao(empresa);
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\" + empresa + "\\Rotas\\"
                    + getDataAtual("SQL") + "\\log.txt";

//            this.logerro.Grava(input, this.caminho);

            String sequencia, numero;
            CxForte cxForte;
            Pedido pedido;
            Rotas rota;
            Rt_Perc trajeto;

            List<RotasGTVe> rotaGTVeList = null;
//            try{
//                rotaGTVeList = obterListaRotas(input);
//            } catch (Exception e){
//                try {
//                    input = java.net.URLDecoder.decode(input, StandardCharsets.UTF_8.name());
//                    this.logerro.Grava("Decodificado\r\n"+input, this.caminho);
//                    rotaGTVeList = obterListaRotas(input);
//                } catch (UnsupportedEncodingException eU) {
//                    
//                }
//            }
            
            if(rotaGTVeList == null) {
                throw new RotaException(new RotaException.RotaErrorCode(0));
            }
            
            for (RotasGTVe rotaGTVe : rotaGTVeList) {
                processamento = new HashMap<>();

                try {
                    rota = rotaFromRotasGTVe(rotaGTVe);
                    processamento.put("rota", rota.getRota());

                    sequencia = this.rotasDao.buscarSeqRota(rota.getCodFil().toPlainString(), rota.getRota(), rota.getData(), this.persistencia);
                    if (sequencia == null) {
                        // inserir rota

                        try {
                            sequencia = this.rotasDao.inserirRotaSequencia(rota, this.persistencia);
                        } catch (Exception insertRota) {
                            throw new RotaException(insertRota.getMessage(), new RotaException.RotaErrorCode(0));
                        }
                    }

                    rota.setSequencia(sequencia);
                    trajetosProcessado = new ArrayList<>();
                    
                    for (TrajetosGTVe trajetoGTVe : rotaGTVe.getTrajetos()) {
                        processamentoTrajeto = new HashMap<>();
                        try {
                            trajeto = trajetoFromTrajetosGTVe(trajetoGTVe, rota);
                            processamentoTrajeto.put("parada", trajeto.getParada());
                            
                            if (this.rt_PercDao.existeRt_Perc(trajeto.getSequencia().toString(), trajeto.getParada(), this.persistencia)) {
                                throw new RotaException(new RotaException.RotaErrorCode(6));
                            } else {
                                if (trajeto.getER().equals("E")) {
                                    pedido = obterPedido(trajeto);
                                    cxForte = this.cxForteDao.getCxForte(trajeto.getCodFil(), this.persistencia);
                                    pedido.setCodCli1(cxForte.getCodCli());
                                } else if (trajeto.getER().equals("R")) {
                                    pedido = obterPedido(trajeto);
                                } else {
                                    pedido = null;
                                }

                                if (pedido != null) {
                                    numero = this.pedidoDao.inserirPedidoNumero(pedido, this.persistencia);
                                    trajeto.setPedido(numero);
                                } else {
                                    trajeto.setPedido("0");
                                }

                                // insere trajeto
                                trajeto.setDt_Incl(LocalDate.now());
                                trajeto.setHr_Incl(getDataAtual("HORA"));
                                trajeto.setOperIncl(RecortaAteEspaço("SatWebService", 0, 10));
                                try {
                                    this.rt_PercDao.inserirTrajeto(trajeto, this.persistencia);

                                    processamentoTrajeto.put("result", new RotaException(new RotaException.RotaErrorCode(1)));
                                } catch (Exception insertTrajeto) {
                                    throw new RotaException(insertTrajeto.getMessage(), new RotaException.RotaErrorCode(-1));
                                }
                            }

                        } catch (RotaException e) {
                            processamentoTrajeto.put("result", e);
                        } catch (Exception e) {
                            processamentoTrajeto.put("result", new RotaException(e.getMessage(), new RotaException.RotaErrorCode(-1)));
                        }
                        
                        trajetosProcessado.add(processamentoTrajeto);
                    }
                    processamento.put("trajetos", trajetosProcessado);
                } catch (RotaException e) {
                    processamento.put("result", e);
                } catch (Exception e) {
                    processamento.put("result", e.getMessage());
                }

                processamentos.add(processamento);
            }

            retorno.put("status", "ok");
            retorno.put("resp", processamentos);

        } catch (Exception e) {
            // Salvando em log o que foi mandado
            retorno.clear();
            retorno.put("status", "error");
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);
        } finally {
            this.logerro.Grava(gson.toJson(retorno), this.caminho);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gson.toJson(retorno))
                    .build();
        }
    }

    private Rotas rotaFromRotasGTVe(RotasGTVe rotaGTVe) {
        Rotas rota = new Rotas();

        rota.setCodFil(String.valueOf(rotaGTVe.getCodfil()));
        rota.setRota(rotaGTVe.getNumero());
        rota.setData(rotaGTVe.getData());
        rota.setDtFim(rotaGTVe.getData());
        rota.setHrLargada(rotaGTVe.getHoraini());
        rota.setHrChegada(rotaGTVe.getHorafim());
        rota.setHrIntIni("00:00");
        rota.setHrIntFim("00:00");
        rota.setFlag_Excl("");
        rota.setTpVeic("F");
        rota.setViagem("N");
        rota.setBACEN("N");
        rota.setAeroporto("N");
        rota.setATM("N");
        rota.setOperador(FuncoesString.RecortaAteEspaço("SatWebService", 0, 10));
        rota.setDt_Alter(LocalDate.now());
        rota.setHr_Alter(getDataAtual("HORA"));

        return rota;
    }

    private List<RotasGTVe> obterListaRotas(String input) throws RotaException {

        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(new TypeToken<StringMap>() {
        }.getType(), new MapDeserializer());
        Gson gson = gsonBuilder.create();

        List<RotasGTVe> retorno = new ArrayList<>();
        StringMap rotasJson = gson.fromJson(input, StringMap.class);
        Object rotasObject = rotasJson.get("rotas");
        List<StringMap> rotasList = null;

        try {
            rotasList = (ArrayList) rotasObject;
        } catch (Exception e) {
            throw new RotaException(new RotaException.RotaErrorCode(0));
        }

        RotasGTVe rota;
        for (StringMap rotaObject : rotasList) {
            rota = obterRota(rotaObject);
            rota.setTrajetos(obterTrajetos(rotaObject));

            retorno.add(rota);
        }

        return retorno;
    }

    private List<TrajetosGTVe> obterTrajetos(StringMap stringMap) throws RotaException {

        List<TrajetosGTVe> retorno = new ArrayList<>();

        Object trajetosObject = stringMap.get("trajetos");
        List<StringMap> trajestoList = null;

        try {
            trajestoList = (ArrayList) trajetosObject;
        } catch (Exception e) {
            throw new RotaException("trajetos", new RotaException.RotaErrorCode(0));
        }

        TrajetosGTVe trajeto;
        for (StringMap trajetoObject : trajestoList) {
            trajeto = obterTrajeto(trajetoObject);
            trajeto.setEmissor(obterCliente(trajetoObject, "emissor"));
            trajeto.setOrigem(obterCliente(trajetoObject, "origem"));
            if (trajeto.getTipo().equals("R")) {
                trajeto.setDestino(obterCliente(trajetoObject, "destino"));
            }
            trajeto.setVeiculo(obterVeiculo(trajetoObject));
            trajeto.setGuias(obterGuias(trajetoObject));
            retorno.add(trajeto);
        }

        return retorno;
    }

    private RotasGTVe obterRota(StringMap stringMap) throws RotaException {
        RotasGTVe rota = new RotasGTVe();

        try {
            rota.setAcao((int) stringMap.get("acao"));
            if (rota.getAcao() != 1 && rota.getAcao() != 3) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("rotas.acao", new RotaException.RotaErrorCode(9));
        }

        try {
            rota.setCodfil((int) stringMap.get("codfil"));
            if (!Validacoes.validacaoInteiro(rota.getCodfil(), 9999)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("rotas.codfil", new RotaException.RotaErrorCode(9));
        }

        try {
            rota.setNumero(stringMap.get("numero").toString());
            if (!Validacoes.validacaoInteiro(rota.getNumero(), 3)) { 
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("rotas.numero", new RotaException.RotaErrorCode(9));
        }

        try {
            rota.setData(stringMap.get("data").toString());
            // 2020-09-01T12:16:40
//            if (!Validacoes.validacaoData(rota.getData(), "yyyy-MM-dd'T'HH:mm:ss")) {
            if (!Validacoes.validacaoData(rota.getData(), "yyyy-MM-dd")) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("rotas.data", new RotaException.RotaErrorCode(9));
        }

        try {
            rota.setHoraini(stringMap.get("horaini").toString());
            if (!Validacoes.validacaoHora(rota.getHoraini(), "HH:mm")) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("rotas.horaini", new RotaException.RotaErrorCode(9));
        }

        try {
            rota.setHorafim(stringMap.get("horafim").toString());
            if (!Validacoes.validacaoHora(rota.getHorafim(), "HH:mm")) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("rotas.horafim", new RotaException.RotaErrorCode(9));
        }

        return rota;
    }

    private TrajetosGTVe obterTrajeto(StringMap stringMap) throws RotaException {

        TrajetosGTVe trajeto = new TrajetosGTVe();

        try {
            trajeto.setTipo(stringMap.get("tipo").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getTipo(), 1, true)
                    || (!trajeto.getTipo().equals("E") && !trajeto.getTipo().equals("R") && !trajeto.getTipo().equals("T"))) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.tipo", new RotaException.RotaErrorCode(9));
        }

        try {
            trajeto.setClassificacao(stringMap.get("classificacao").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getClassificacao(), 1, true)
                    || (!trajeto.getClassificacao().equals("V") && !trajeto.getClassificacao().equals("E") && !trajeto.getClassificacao().equals("R") && !trajeto.getClassificacao().equals("A"))) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.classificacao", new RotaException.RotaErrorCode(9));
        }

        try {
            trajeto.setCodcliorigem(stringMap.get("codcliorigem").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getCodcliorigem(), 7, false)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.codcliorigem", new RotaException.RotaErrorCode(9));
        }

        try {
            trajeto.setCodclidestino(stringMap.get("codclidestino").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getCodclidestino(), 7, false)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.codclidestino", new RotaException.RotaErrorCode(9));
        }

        try {
            trajeto.setParada((int) stringMap.get("parada"));
            if (!Validacoes.validacaoInteiro(trajeto.getParada(), 999)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.parada", new RotaException.RotaErrorCode(9));
        }

        try {
            trajeto.setHora(stringMap.get("hora").toString());
            if (!Validacoes.validacaoHora(trajeto.getHora(), "HH:mm")) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.hora", new RotaException.RotaErrorCode(9));
        }

        if (trajeto.getTipo().equals("R")) {
            try {
                trajeto.setParadadestino((int) stringMap.get("paradadestino"));
                if (!Validacoes.validacaoInteiro(trajeto.getParadadestino(), 999)) {
                    throw new Exception();
                }
            } catch (Exception e) {
                throw new RotaException("trajetos.paradadestino", new RotaException.RotaErrorCode(9));
            }

            try {
                trajeto.setHoradestino(stringMap.get("horadestino").toString());
                if (!Validacoes.validacaoHora(trajeto.getHoradestino(), "HH:mm")) {
                    throw new Exception();
                }
            } catch (Exception e) {
                throw new RotaException("trajetos.horadestino", new RotaException.RotaErrorCode(9));
            }
        }

        try {
            trajeto.setPedidocliente(stringMap.get("pedidocliente").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getPedidocliente(), 20, false)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.pedidocliente", new RotaException.RotaErrorCode(9));
        }

        try {
            trajeto.setValor(new BigDecimal(stringMap.get("valor").toString()));
            if (!Validacoes.validacaoValor(trajeto.getValor(), "999999999999999", 2)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.valor", new RotaException.RotaErrorCode(9));
        }

        try {
            trajeto.setMoeda(stringMap.get("moeda").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getMoeda(), 3, true)
                    || (!trajeto.getMoeda().equals("BRL")
                    && !trajeto.getMoeda().equals("MXN")
                    && !trajeto.getMoeda().equals("USD")
                    && !trajeto.getMoeda().equals("EUR")
                    && !trajeto.getMoeda().equals("GBP")
                    && !trajeto.getMoeda().equals("IEN")
                    && !trajeto.getMoeda().equals("OUT"))) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.moeda", new RotaException.RotaErrorCode(9));
        }

        try {
            trajeto.setObservacao(stringMap.get("observacao").toString());
            if (!Validacoes.validacaoTamanho(trajeto.getObservacao(), 120, trajeto.getMoeda().equals("OUT"))) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("trajetos.observacao", new RotaException.RotaErrorCode(9));
        }

        return trajeto;
    }

    private ClientesGTVe obterCliente(StringMap stringMap, String c) throws RotaException {

        try {
            stringMap = (StringMap) stringMap.get(c);
        } catch (Exception e) {
            throw new RotaException(c, new RotaException.RotaErrorCode(0));
        }

        ClientesGTVe cliente = new ClientesGTVe();

        try {
            cliente.setCpf(stringMap.getOrDefault("cpf", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getCpf(), 11, false)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".cpf", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setCnpj(stringMap.getOrDefault("cnpj", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getCnpj(), 14, false)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".cnpj", new RotaException.RotaErrorCode(9));
        }

        if (cliente.getCnpj().equals("") && cliente.getCpf().equals("")) {
            throw new RotaException(c + ".cnpj", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setRazaosocial(stringMap.getOrDefault("razaosocial", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getRazaosocial(), 60, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".razaosocial", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setFantasia(stringMap.getOrDefault("fantasia", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getFantasia(), 40, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".fantasia", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setEndereco(stringMap.getOrDefault("endereco", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getEndereco(), 45, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".endereco", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setBairro(stringMap.getOrDefault("bairro", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getBairro(), 25, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".bairro", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setNumero(stringMap.getOrDefault("numero", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getNumero(), 15, false)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".numero", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setCidade(stringMap.getOrDefault("cidade", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getCidade(), 25, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".cidade", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setUf(stringMap.getOrDefault("uf", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getUf(), 2, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".uf", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setCodcidade(stringMap.getOrDefault("codcidade", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getCodcidade(), 7, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".codcidade", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setCep(stringMap.getOrDefault("cep", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getCep(), 8, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".cep", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setTelefone(stringMap.getOrDefault("telefone", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getTelefone(), 11, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".telefone", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setEmail(stringMap.getOrDefault("email", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getEmail(), 80, true)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".email", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setIe(stringMap.getOrDefault("ie", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getIe(), 20, false)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".ie", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setLatitude(stringMap.getOrDefault("latitude", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getLatitude(), 20, false)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".latitude", new RotaException.RotaErrorCode(9));
        }

        try {
            cliente.setLongitude(stringMap.getOrDefault("longitude", "").toString());
            if (!Validacoes.validacaoTamanho(cliente.getLongitude(), 20, false)) {
                throw new Exception("");
            }
        } catch (Exception e) {
            throw new RotaException(c + ".longitude", new RotaException.RotaErrorCode(9));
        }

        if (c.equals("emissor")) {
            EmissorGTVe emissor = new EmissorGTVe(cliente);
            try {
                emissor.setPais((int) stringMap.get("pais"));
                if (!Validacoes.validacaoInteiro(emissor.getPais(), 9999)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException(c + ".pais", new RotaException.RotaErrorCode(9));
            }

            try {
                emissor.setCodigofat(stringMap.getOrDefault("codigofat", "").toString());
                if (!Validacoes.validacaoTamanho(emissor.getCodigofat(), 20, false)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException(c + ".codigofat", new RotaException.RotaErrorCode(9));
            }

            return emissor;
        } else {
            return cliente;
        }
    }

    private VeiculosGTVe obterVeiculo(StringMap stringMap) throws RotaException {

        try {
            stringMap = (StringMap) stringMap.get("veiculo");
        } catch (Exception e) {
            throw new RotaException("veiculo", new RotaException.RotaErrorCode(0));
        }

        VeiculosGTVe veiculo = new VeiculosGTVe();

        try {
            veiculo.setTipo(stringMap.get("tipo").toString());
            if (!Validacoes.validacaoTamanho(veiculo.getTipo(), 1, true)
                    || (!veiculo.getTipo().equals("A") && !veiculo.getTipo().equals("B") && !veiculo.getTipo().equals("F") && !veiculo.getTipo().equals("L") && !veiculo.getTipo().equals("P"))) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("veiculo.tipo", new RotaException.RotaErrorCode(9));
        }

        try {
            veiculo.setPlaca(stringMap.get("placa").toString());
            if (!Validacoes.validacaoTamanho(veiculo.getPlaca(), 12, true)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("veiculo.placa", new RotaException.RotaErrorCode(9));
        }

        try {
            veiculo.setUf(stringMap.get("uf").toString());
            if (!Validacoes.validacaoTamanho(veiculo.getUf(), 2, true)) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("veiculo.uf", new RotaException.RotaErrorCode(9));
        }

        return veiculo;
    }

    private Pedido obterPedido(Rt_Perc trajeto) throws RotaException {
        Pedido pedido = new Pedido();
        pedido.setSeqRota(trajeto.getSequencia().toBigInteger().toString());

        String hora1 = "08:00", hora2 = "18:00";
        try {
            hora1 = trajeto.getHora1();
            hora2 = LocalTime.parse(hora1, DateTimeFormatter.ofPattern("HH:mm")).plusMinutes(30).format(DateTimeFormatter.ofPattern("HH:mm"));
        } catch (Exception e) {
            throw new RotaException("janela", new RotaException.RotaErrorCode(9));
        }

        if (trajeto.getER().equals("E")) {
            pedido.setCodCli1("");
            pedido.setCodCli2(trajeto.getCodCli1());
            pedido.setHora1O("08:00");
            pedido.setHora2O("18:00");
            pedido.setHora1D(hora1);
            pedido.setHora2D(hora2);
        } else {
            pedido.setCodCli1(trajeto.getCodCli1());
            pedido.setCodCli2(trajeto.getCodCli2());
            pedido.setHora1O(hora1);
            pedido.setHora2O(hora2);
            pedido.setHora1D("08:00");
            pedido.setHora2D("18:00");
        }

        pedido.setCodFil(trajeto.getCodFil());
        pedido.setData(trajeto.getData());
        pedido.setParada(trajeto.getParada());
        pedido.setClassifSrv(trajeto.getTipoSrv());
        pedido.setTipo("T");
        pedido.setPedidoCliente(trajeto.getPedidoCliente());
        pedido.setValor(trajeto.getValor());
        pedido.setTipoMoeda(trajeto.getMoeda());
        pedido.setObs(RecortaString(trajeto.getObserv(), 0, 80));
        pedido.setOS(trajeto.getOS().toBigInteger().toString());

        pedido.setFlag_Excl("");
        pedido.setOperIncl(RecortaAteEspaço("SatWebService", 0, 10));
        pedido.setDt_Incl(getDataAtual("SQL"));
        pedido.setHr_Incl(getDataAtual("HORA"));

        pedido.setOperador(RecortaAteEspaço("SatWebService", 0, 10));
        pedido.setDt_Alter(getDataAtual("SQL"));
        pedido.setHr_Alter(getDataAtual("HORA"));

        return pedido;
    }

    private Rt_Perc trajetoFromTrajetosGTVe(TrajetosGTVe trajetoGTVe, Rotas rota) {

        Rt_Perc trajeto = new Rt_Perc();
        trajeto.setSequencia(rota.getSequencia().toPlainString());

        trajeto.setCodFil(rota.getCodFil().toPlainString());
        trajeto.setParada(trajetoGTVe.getParada());
        trajeto.setHora1(trajetoGTVe.getHora());
        trajeto.setER(trajetoGTVe.getTipo());
        trajeto.setTipoSrv(trajetoGTVe.getClassificacao());
        trajeto.setPedidoCliente(trajetoGTVe.getPedidocliente());
        trajeto.setValor(trajetoGTVe.getValor().toPlainString());
        trajeto.setMoeda(trajetoGTVe.getMoeda());
        trajeto.setObserv(trajetoGTVe.getObservacao());
        trajeto.setData(rota.getData());
        trajeto.setFlag_Excl("");
        trajeto.setOperador(FuncoesString.RecortaAteEspaço("SatWebService", 0, 10));
        trajeto.setDt_Alter(LocalDate.now());
        trajeto.setHr_Alter(getDataAtual("HORA"));

        Clientes emissor = null;
        try {
            // Buscando primeiro pelo código informado se informado.
            if (emissor == null) {
                try {
                    if (((EmissorGTVe) trajetoGTVe.getEmissor()).getCodigofat() != null
                            && !((EmissorGTVe) trajetoGTVe.getEmissor()).getCodigofat().equals("")) {
                        emissor = this.clientesDao.buscarClienteImportacao(((EmissorGTVe) trajetoGTVe.getEmissor()).getCodigofat(),
                                rota.getCodFil().toPlainString(), this.persistencia);
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo nome, nred e endereço.
            if (emissor == null) {
                try {
                    emissor = this.clientesDao.buscarClienteNomeEndereco(trajetoGTVe.getEmissor().getRazaosocial(),
                            trajetoGTVe.getEmissor().getFantasia(),
                            trajetoGTVe.getEmissor().getEndereco(),
                            rota.getCodFil().toPlainString(),
                            this.persistencia);
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo nome e nred.
            if (emissor == null) {
                try {
                    emissor = this.clientesDao.buscarClienteNome(trajetoGTVe.getEmissor().getRazaosocial(),
                            trajetoGTVe.getEmissor().getFantasia(),
                            rota.getCodFil().toPlainString(),
                            this.persistencia);
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo cnpj.
            if (emissor == null) {
                try {
                    if (trajetoGTVe.getEmissor().getCnpj() != null
                            && !trajetoGTVe.getEmissor().getCnpj().equals("")) {
                        emissor = this.clientesDao.buscarClienteCPNJ(trajetoGTVe.getEmissor().getCnpj(),
                                trajetoGTVe.getEmissor().getFantasia(),
                                rota.getCodFil().toPlainString(), this.persistencia);
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo cpf.
            if (emissor == null) {
                try {
                    if (trajetoGTVe.getEmissor().getCpf() != null
                            && !trajetoGTVe.getEmissor().getCpf().equals("")) {
                        emissor = this.clientesDao.buscarClienteCPF(trajetoGTVe.getEmissor().getCpf(), 
                                trajetoGTVe.getEmissor().getFantasia(),
                                rota.getCodFil().toPlainString(), this.persistencia);
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, inserir com as informações enviadas.
            if (emissor == null) {
                try {
                    emissor = new Clientes();
                    emissor.setCodFil(rota.getCodFil());
                    emissor.setCodExt(((EmissorGTVe) trajetoGTVe.getEmissor()).getCodigofat());
                    emissor.setCGC(trajetoGTVe.getEmissor().getCnpj());
                    emissor.setCPF(trajetoGTVe.getEmissor().getCpf());
                    emissor.setNome(trajetoGTVe.getEmissor().getRazaosocial());
                    emissor.setNRed(trajetoGTVe.getEmissor().getFantasia());
                    emissor.setEnde(trajetoGTVe.getEmissor().getEndereco() + trajetoGTVe.getEmissor().getNumero());
                    emissor.setBairro(trajetoGTVe.getEmissor().getBairro());
                    emissor.setCidade(trajetoGTVe.getEmissor().getCidade());
                    emissor.setEstado(trajetoGTVe.getEmissor().getUf());
                    emissor.setCodCidade(trajetoGTVe.getEmissor().getCodcidade());
                    emissor.setCEP(trajetoGTVe.getEmissor().getCep());
                    emissor.setFone1(trajetoGTVe.getEmissor().getTelefone());
                    emissor.setEmail(trajetoGTVe.getEmissor().getEmail());
                    emissor.setIE(trajetoGTVe.getEmissor().getIe());
                    emissor.setLatitude(trajetoGTVe.getEmissor().getLatitude());

                    emissor.setOper_Inc("SatWebServ");
                    emissor.setDt_Alter(LocalDate.now());
                    emissor.setHr_Alter(getDataAtual("HORA"));
                    emissor.setSituacao("A");
                    emissor.setRegiao("999");

                    int contador = 0;
                    while (contador <= 30) {
                        try {
                            String codigo = this.clientesDao.getCodCliBancoTpCli(emissor.getCodFil(), "777", "0", this.persistencia);

                            emissor.setTpCli("0");
                            emissor.setBanco(codigo.substring(0, 3));
                            emissor.setCodCli(codigo.substring(4, 7));
                            emissor.setCodigo(codigo);
                            emissor.setAgencia(codigo.substring(3, 7));
                            emissor = (Clientes) removeAcentoObjeto(emissor);

                            this.clientesDao.inserir(emissor, this.persistencia);
                            break;
                        } catch (Exception ex) {
                        } finally {
                            contador++;
                        }
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }
        } catch (Exception eEmissor) {
            this.logerro.Grava(eEmissor.getMessage(), this.caminho);
        }
        trajeto.setCliFat(emissor.getCodigo());

        Clientes origem = null;
        try {
            // Buscando primeiro pelo código informado se informado.
            if (origem == null) {
                try {
                    if (trajetoGTVe.getCodcliorigem() != null
                            && !trajetoGTVe.getCodcliorigem().equals("")) {
                        origem = this.clientesDao.buscarClienteImportacao(trajetoGTVe.getCodcliorigem(), rota.getCodFil().toPlainString(), this.persistencia);
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo nome, nred e endereço.
            if (origem == null) {
                try {
                    origem = this.clientesDao.buscarClienteNomeEndereco(trajetoGTVe.getOrigem().getRazaosocial(),
                            trajetoGTVe.getOrigem().getFantasia(),
                            trajetoGTVe.getOrigem().getEndereco(),
                            rota.getCodFil().toPlainString(),
                            this.persistencia);
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo nome e nred.
            if (origem == null) {
                try {
                    origem = this.clientesDao.buscarClienteNome(trajetoGTVe.getOrigem().getRazaosocial(),
                            trajetoGTVe.getOrigem().getFantasia(),
                            rota.getCodFil().toPlainString(),
                            this.persistencia);
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo cnpj.
            if (origem == null) {
                try {
                    if (trajetoGTVe.getOrigem().getCnpj() != null
                            && !trajetoGTVe.getOrigem().getCnpj().equals("")) {
                        origem = this.clientesDao.buscarClienteCPNJ(trajetoGTVe.getOrigem().getCnpj(), 
                                trajetoGTVe.getOrigem().getFantasia(),
                                rota.getCodFil().toPlainString(), this.persistencia);
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, buscando pelo cpf.
            if (origem == null) {
                try {
                    if (trajetoGTVe.getOrigem().getCpf() != null
                            && !trajetoGTVe.getOrigem().getCpf().equals("")) {
                        origem = this.clientesDao.buscarClienteCPF(trajetoGTVe.getOrigem().getCpf(),
                                trajetoGTVe.getOrigem().getFantasia(),
                                rota.getCodFil().toPlainString(), this.persistencia);
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }

            // Se não encontrar, inserir com as informações enviadas.
            if (origem == null) {
                try {
                    origem = new Clientes();
                    origem.setCodFil(rota.getCodFil());
                    origem.setCodExt(trajetoGTVe.getCodcliorigem());
                    origem.setCGC(trajetoGTVe.getOrigem().getCnpj());
                    origem.setCPF(trajetoGTVe.getOrigem().getCpf());
                    origem.setNome(trajetoGTVe.getOrigem().getRazaosocial());
                    origem.setNRed(trajetoGTVe.getOrigem().getFantasia());
                    origem.setEnde(trajetoGTVe.getOrigem().getEndereco() + trajetoGTVe.getOrigem().getNumero());
                    origem.setBairro(trajetoGTVe.getOrigem().getBairro());
                    origem.setCidade(trajetoGTVe.getOrigem().getCidade());
                    origem.setEstado(trajetoGTVe.getOrigem().getUf());
                    origem.setCodCidade(trajetoGTVe.getOrigem().getCodcidade());
                    origem.setCEP(trajetoGTVe.getOrigem().getCep());
                    origem.setFone1(trajetoGTVe.getOrigem().getTelefone());
                    origem.setEmail(trajetoGTVe.getOrigem().getEmail());
                    origem.setIE(trajetoGTVe.getOrigem().getIe());
                    origem.setLatitude(trajetoGTVe.getOrigem().getLatitude());

                    origem.setOper_Inc("SatWebServ");
                    origem.setDt_Alter(LocalDate.now());
                    origem.setHr_Alter(getDataAtual("HORA"));
                    origem.setSituacao("A");
                    origem.setRegiao("999");

                    int contador = 0;
                    while (contador <= 30) {
                        try {
                            String codigo = this.clientesDao.getCodCliBancoTpCli(origem.getCodFil(), "777", "0", this.persistencia);

                            origem.setTpCli("0");
                            origem.setBanco(codigo.substring(0, 3));
                            origem.setCodCli(codigo.substring(4, 7));
                            origem.setCodigo(codigo);
                            origem.setAgencia(codigo.substring(3, 7));
                            origem = (Clientes) removeAcentoObjeto(origem);

                            this.clientesDao.inserir(origem, this.persistencia);
                            break;
                        } catch (Exception ex) {
                        } finally {
                            contador++;
                        }
                    }
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }
            }
        } catch (Exception eOrigem) {
            this.logerro.Grava(eOrigem.getMessage(), this.caminho);
        }
        trajeto.setCodCli1(origem.getCodigo());

        Clientes destino = null;
        if (trajeto.getER().equals("R")) {
            try {
                // Buscando primeiro pelo código informado se informado.
                if (destino == null) {
                    try {
                        if (trajetoGTVe.getCodclidestino() != null
                                && !trajetoGTVe.getCodclidestino().equals("")) {
                            destino = this.clientesDao.buscarClienteImportacao(trajetoGTVe.getCodclidestino(), rota.getCodFil().toPlainString(), this.persistencia);
                        }
                    } catch (Exception e) {
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                }

                // Se não encontrar, buscando pelo nome, nred e endereço.
                if (destino == null) {
                    try {
                        destino = this.clientesDao.buscarClienteNomeEndereco(trajetoGTVe.getDestino().getRazaosocial(),
                                trajetoGTVe.getDestino().getFantasia(),
                                trajetoGTVe.getDestino().getEndereco(),
                                rota.getCodFil().toPlainString(),
                                this.persistencia);
                    } catch (Exception e) {
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                }

                // Se não encontrar, buscando pelo nome e nred.
                if (destino == null) {
                    try {
                        destino = this.clientesDao.buscarClienteNome(trajetoGTVe.getDestino().getRazaosocial(),
                                trajetoGTVe.getDestino().getFantasia(),
                                rota.getCodFil().toPlainString(),
                                this.persistencia);
                    } catch (Exception e) {
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                }

                // Se não encontrar, buscando pelo cnpj.
                if (destino == null) {
                    try {
                        if (trajetoGTVe.getDestino().getCnpj() != null
                                && !trajetoGTVe.getDestino().getCnpj().equals("")) {
                            destino = this.clientesDao.buscarClienteCPNJ(trajetoGTVe.getDestino().getCnpj(), 
                                trajetoGTVe.getDestino().getFantasia(),
                                rota.getCodFil().toPlainString(), this.persistencia);
                        }
                    } catch (Exception e) {
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                }

                // Se não encontrar, buscando pelo cpf.
                if (destino == null) {
                    try {
                        if (trajetoGTVe.getDestino().getCpf() != null
                                && !trajetoGTVe.getDestino().getCpf().equals("")) {
                            destino = this.clientesDao.buscarClienteCPF(trajetoGTVe.getDestino().getCpf(), 
                                trajetoGTVe.getDestino().getFantasia(),
                                rota.getCodFil().toPlainString(), this.persistencia);
                        }
                    } catch (Exception e) {
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                }

                // Se não encontrar, inserir com as informações enviadas.
                if (destino == null) {
                    try {
                        destino = new Clientes();
                        destino.setCodFil(rota.getCodFil());
                        destino.setCodExt(trajetoGTVe.getCodclidestino());
                        destino.setCGC(trajetoGTVe.getDestino().getCnpj());
                        destino.setCPF(trajetoGTVe.getDestino().getCpf());
                        destino.setNome(trajetoGTVe.getDestino().getRazaosocial());
                        destino.setNRed(trajetoGTVe.getDestino().getFantasia());
                        destino.setEnde(trajetoGTVe.getDestino().getEndereco() + trajetoGTVe.getDestino().getNumero());
                        destino.setBairro(trajetoGTVe.getDestino().getBairro());
                        destino.setCidade(trajetoGTVe.getDestino().getCidade());
                        destino.setEstado(trajetoGTVe.getDestino().getUf());
                        destino.setCodCidade(trajetoGTVe.getDestino().getCodcidade());
                        destino.setCEP(trajetoGTVe.getDestino().getCep());
                        destino.setFone1(trajetoGTVe.getDestino().getTelefone());
                        destino.setEmail(trajetoGTVe.getDestino().getEmail());
                        destino.setIE(trajetoGTVe.getDestino().getIe());
                        destino.setLatitude(trajetoGTVe.getDestino().getLatitude());

                        destino.setOper_Inc("SatWebServ");
                        destino.setDt_Alter(LocalDate.now());
                        destino.setHr_Alter(getDataAtual("HORA"));
                        destino.setSituacao("A");
                        destino.setRegiao("999");

                        int contador = 0;
                        while (contador <= 30) {
                            try {
                                String codigo = this.clientesDao.getCodCliBancoTpCli(destino.getCodFil(), "777", "0", this.persistencia);

                                destino.setTpCli("0");
                                destino.setBanco(codigo.substring(0, 3));
                                destino.setCodCli(codigo.substring(4, 7));
                                destino.setCodigo(codigo);
                                destino.setAgencia(codigo.substring(3, 7));
                                destino = (Clientes) removeAcentoObjeto(destino);

                                this.clientesDao.inserir(destino, this.persistencia);
                                break;
                            } catch (Exception ex) {
                            } finally {
                                contador++;
                            }
                        }
                    } catch (Exception e) {
                        this.logerro.Grava(e.getMessage(), this.caminho);
                    }
                }
            } catch (Exception eDestino) {
                this.logerro.Grava(eDestino.getMessage(), this.caminho);
            }
            trajeto.setCodCli2(destino.getCodigo());

            trajeto.setDPar(trajetoGTVe.getParadadestino());
            trajeto.setHora1D(trajetoGTVe.getHoradestino());
        } else {
            destino = origem;
        }

        String os = "0";
        try {
            os = this.os_VigDao.obterOS(origem.getCodigo(), destino.getCodigo(), trajeto.getCodFil().toPlainString(), this.persistencia);
        } catch (Exception eDestino) {
            this.logerro.Grava(eDestino.getMessage(), this.caminho);
        }

        if (os.equals("0")) {
            try {
                OS_VigDao.inserirOsReduzida(
                        origem.getCodFil().toString(),
                        origem.getNRed(),
                        "1",
                        "001",
                        origem.getCodigo(), //Origem
                        origem.getNRed(), //Origem
                        destino.getCodigo(), //Destino
                        destino.getNRed(), //Destino
                        emissor.getCodigo(), //Faturar
                        emissor.getNRed(), //osvig.getNRedFat(), //Faturar
                        "N",
                        "999.O001.1", //osvig.getContrato(), //Contrato
                        "9999", //osvig.getAgrupador().toString(), //Agrupador
                        "9999", //osvig.getOSGrp().toString(),
                        "A",
                        origem.getOper_Inc(),
                        origem.getDt_Alter().toString(),
                        origem.getHr_Alter(),
                        origem.getOper_Alt(),
                        origem.getDt_Alter().toString(),
                        origem.getHr_Alter(),
                        "",
                        "",
                        "999",
                        "0001",
                        "",
                        this.persistencia);
                os = this.os_VigDao.obterOS(origem.getCodigo(), destino.getCodigo(), trajeto.getCodFil().toPlainString(), this.persistencia);
            } catch (Exception eDestino) {
                this.logerro.Grava(eDestino.getMessage(), this.caminho);
            }
        }
        trajeto.setOS(os);

        return trajeto;

    }

    private List<GuiasGTVe> obterGuias(StringMap stringMap) throws RotaException {
        List<GuiasGTVe> retorno = new ArrayList<>();

        Object guiasObject = stringMap.get("guias");
        List<StringMap> guiasList = null;

        try {
            guiasList = (ArrayList) guiasObject;
        } catch (Exception e) {
            throw new RotaException("guias", new RotaException.RotaErrorCode(0));
        }

        GuiasGTVe guia;
        for (StringMap guiaObject : guiasList) {
            guia = new GuiasGTVe();

            try {
                guia.setNumero(guiaObject.getOrDefault("numero", "").toString());
                if (!Validacoes.validacaoInteiro(guia.getNumero(), 20)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("guia.numero", new RotaException.RotaErrorCode(9));
            }

            try {
                guia.setSerie(guiaObject.get("serie").toString());
                if (!Validacoes.validacaoTamanho(guia.getNumero(), 3, true)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("guia.serie", new RotaException.RotaErrorCode(9));
            }

            guia.setVolumes(obterVolumes(guiaObject));
            guia.setComposicao(obterComposicoes(guiaObject));
            
            retorno.add(guia);
        }

        return retorno;
    }

    private List<VolumesGTVe> obterVolumes(StringMap stringMap) throws RotaException {
        
        List<VolumesGTVe> retorno = new ArrayList<>();

        Object volumesObject = stringMap.get("volumes");
        List<StringMap> volumesList = null;

        try {
            volumesList = (ArrayList) volumesObject;
        } catch (Exception e) {
            throw new RotaException("volumes", new RotaException.RotaErrorCode(0));
        }

        VolumesGTVe volume;
        for (StringMap volumeObject : volumesList) {
            volume = new VolumesGTVe();

            try {
                volume.setLacre(volumeObject.get("lacre").toString());
                if (!Validacoes.validacaoTamanho(volume.getLacre(), 20, true)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("volume.lacre", new RotaException.RotaErrorCode(9));
            }

            try {
                volume.setEspecie((int) volumeObject.get("especie"));
                if (!Validacoes.validacaoInteiro(volume.getEspecie(), 1)
                    || (volume.getEspecie() != 1 && volume.getEspecie() != 2 && volume.getEspecie() != 3 && volume.getEspecie() != 4)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("volume.especie", new RotaException.RotaErrorCode(9));
            }

            try {
                volume.setMoeda(volumeObject.get("moeda").toString());
                if (!Validacoes.validacaoTamanho(volume.getMoeda(), 3, true)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("volume.moeda", new RotaException.RotaErrorCode(9));
            }

            try {
                volume.setValor(new BigDecimal(stringMap.get("valor").toString()));
                if (!Validacoes.validacaoValor(volume.getValor(), "999999999999999", 2)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("volume.valor", new RotaException.RotaErrorCode(9));
            }
        
            retorno.add(volume);
        }
        
        return retorno;
    }

    private List<ComposicoesGTVe> obterComposicoes(StringMap stringMap) throws RotaException {
        
        List<ComposicoesGTVe> retorno = new ArrayList<>();

        Object composicoesObject = stringMap.get("composicoes");
        List<StringMap> composicoesList = null;

        try {
            composicoesList = (ArrayList) composicoesObject;
        } catch (Exception e) {
            throw new RotaException("composicoes", new RotaException.RotaErrorCode(0));
        }

        ComposicoesGTVe composicao;
        for (StringMap composicaoObject : composicoesList) {
            composicao = new ComposicoesGTVe();

            try {
                composicao.setQtde((int) composicaoObject.get("qtde"));
                if (!Validacoes.validacaoInteiro(composicao.getQtde(), 5)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("composicao.lacre", new RotaException.RotaErrorCode(9));
            }

            try {
                composicao.setCodigo(new BigDecimal(stringMap.get("codigo").toString()));
                if (!Validacoes.validacaoValor(composicao.getCodigo(), "999999999999999", 2)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("composicao.codigo", new RotaException.RotaErrorCode(9));
            }

            try {
                composicao.setMoeda(composicaoObject.get("moeda").toString());
                if (!Validacoes.validacaoTamanho(composicao.getMoeda(), 3, true)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("composicao.moeda", new RotaException.RotaErrorCode(9));
            }

            try {
                composicao.setValor(new BigDecimal(stringMap.get("valor").toString()));
                if (!Validacoes.validacaoValor(composicao.getValor(), "999999999999999", 2)) {
                    throw new Exception("");
                }
            } catch (Exception e) {
                throw new RotaException("composicao.valor", new RotaException.RotaErrorCode(9));
            }
        
            retorno.add(composicao);
        }
        
        return retorno;
    }
}
