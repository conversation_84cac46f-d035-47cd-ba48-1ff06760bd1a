/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.importacao.rotas.models;

import br.com.sasw.satwebservice.importacao.rotas.models.VolumesGTVe;
import br.com.sasw.satwebservice.importacao.rotas.models.ComposicoesGTVe;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GuiasGTVe {

    private String numero;
    private String serie;
    private float valor;
    private List<VolumesGTVe> volumes;
    private List<ComposicoesGTVe> composicao;

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getSerie() {
        return serie;
    }

    public void setSerie(String serie) {
        this.serie = serie;
    }

    public float getValor() {
        return valor;
    }

    public void setValor(float valor) {
        this.valor = valor;
    }

    public List<VolumesGTVe> getVolumes() {
        if(volumes == null){
            volumes = new ArrayList<>();
        }
        return volumes;
    }

    public void setVolumes(List<VolumesGTVe> volumes) {
        this.volumes = volumes;
    }

    public List<ComposicoesGTVe> getComposicao() {
        if(composicao == null){
            composicao = new ArrayList<>();
        }
        return composicao;
    }

    public void setComposicao(List<ComposicoesGTVe> composicao) {
        this.composicao = composicao;
    }
}
