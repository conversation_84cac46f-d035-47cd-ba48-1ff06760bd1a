/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.dao;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.satwebservice.bradesconovo.dao.entity.BradescoCertificateEntity;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class BradescoIntegraDao {

    public void save(String response, String path, String queryParam, String database, String method) {
        Persistencia persistencia;
        try {
            String sql = "INSERT INTO bradintegra\n"
                    + "(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)\n"
                    + "VALUES((select COALESCE(max(Sequencia) + 1,1) from bradintegra) , ?, ?, ?, ?, '', GETDATE(), FORMAT(GETDATE(),'hh:mm:ss'));";
            
            persistencia = new Persistencia(database, "sasw", "s@$26bd1", "");
            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(path);
            consulta.setString(queryParam);
            consulta.setString(method);
            consulta.setString(response);
            consulta.insert();
            BradescoCertificateEntity bradescoCertificateEntity = new BradescoCertificateEntity();
            consulta.Close();
        } catch (Exception ex) {
            Logger.getLogger(BradescoCertificateDao.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
}
