/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.rotas;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.CxFGuias;
import SasBeans.CxFGuiasVol;
import SasBeans.CxForte;
import SasBeans.MobileProcAnt;
import SasBeans.Paramet;
import SasBeans.RPV;
import SasBeans.Rt_Guias;
import SasBeans.Rt_PercDet;
import SasDaos.ClientesDao;
import SasDaos.CxFGuiasDao;
import SasDaos.CxFGuiasVolDao;
import SasDaos.CxForteDao;
import SasDaos.EGtvDao;
import SasDaos.GTVDao;
import SasDaos.LacresDao;
import SasDaos.MobileProcAntDao;
import SasDaos.ParametDao;
import SasDaos.RPVDao;
import SasDaos.RotasDao;
import SasDaos.Rt_GuiasDao;
import SasDaos.Rt_PercDao;
import SasDaos.Rt_PercDetDao;
import SasDaos.Rt_PercSlaDao;
import SasDaos.SASLogDao;
import SasDaos.SemaforoDao;
import SasLibrary.GuiasMobile;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.sasbeans.Rt_GuiasFat;
import br.com.sasw.pacotesuteis.sasbeans.Rt_GuiasMoeda;
import br.com.sasw.pacotesuteis.sasdaos.Rt_GuiasFatDao;
import br.com.sasw.pacotesuteis.sasdaos.Rt_GuiasMoedaDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.satwebservice.rotas.beans.BaixaServicoBean;
import br.com.sasw.satwebservice.rotas.beans.Guias;
import br.com.sasw.satwebservice.rotas.beans.Volumes;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;

/**
 *
 * <AUTHOR>
 */
@Path("/ws-rotas/baixaservico/")
public class BaixaServico {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;
    private final SemaforoDao semaforoDao;

    @Context
    private UriInfo context;

    /**
     * Creates a new instance of BaixaServico
     */
    public BaixaServico() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Rotas\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        semaforoDao = new SemaforoDao();
    }

    private Boolean isTranspCacamba(String empresa) throws Exception {
        Persistencia inSatellite = pool.getConexao("SATELLITE");

        ParametDao parametDao = new ParametDao();
        Paramet parametGoogle = parametDao.getParametGoogleApi(empresa, inSatellite);

        return parametGoogle.getTranspCacamba().equals("0") ? false : true;
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    public Response post(String input) {

        Gson gson = new GsonBuilder().create();
        Map retorno = new HashMap<>();

        String sequencia = null;
        String semaforoBaixaHorariosRota = "BaixaHorariosRota";
        String semaforoGTV = "GTV";
        String chaveSemaforoGTV = "Geração de Guias";

        try {
            // Convertendo o input para java
            JsonObject jsonObject;
            try {
                jsonObject = new JsonParser().parse(input).getAsJsonObject();
            } catch (Exception e) {
                jsonObject = new JsonParser().parse(input).getAsJsonArray().get(0).getAsJsonObject();
            }
            BaixaServicoBean baixa = gson.fromJson(jsonObject, new TypeToken<BaixaServicoBean>() {
            }.getType());

            // Validando se o que foi enviado não gerou uma lista vazia
            if (null == baixa) {
                throw new Exception("BaixaVazia");
            }

            // Buscando as informações do json
            String param;
            try {
                param = baixa.getParam().replace(".0", "");
            } catch (Exception ee) {
                // Salvando em log o que foi mandado
                this.logerro.Grava(input, this.caminho);
                throw new Exception("param");
            }

            // Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao(param);
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + param + ")");
            }

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\BaixaServico\\" + param + "\\"
                    + getDataAtual("SQL") + "\\log.txt";

            // Salvando em log o que foi mandado
            this.logerro.Grava(input, this.caminho);

            String sCodPessoa;
            try {
                sCodPessoa = baixa.getCodpessoa().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("codpessoa");
            }

            String operador;
            try {
                operador = RecortaAteEspaço(baixa.getOperador().replace(".0", ""), 0, 10);
            } catch (Exception ee) {
                throw new Exception("operador");
            }

            String dataAtual;
            try {
                dataAtual = baixa.getDataAtual().replace(".0", "");
            } catch (Exception ee) {
                dataAtual = getDataAtual("SQL");
            }

            String horaAtual;
            try {
                horaAtual = baixa.getHoraAtual().replace(".0", "");
            } catch (Exception ee) {
                horaAtual = getDataAtual("HORA");
            }

            int vBaixaDuplicidade = 0;

            try {
                sequencia = baixa.getSequencia().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("sequencia");
            }

            String codCliAuth;
            try {
                codCliAuth = baixa.getCodCliAuth().replace(".0", "");
            } catch (Exception ee) {
                codCliAuth = sCodPessoa;
            }

            String codfil;
            try {
                codfil = baixa.getCodfil().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("codfil");
            }

            String parada;
            try {
                parada = baixa.getParada().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("parada");
            }

            String er;
            try {
                er = baixa.getEr().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("er");
            }

            String tiposerv;
            try {
                tiposerv = baixa.getTiposerv().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("tiposerv");
            }

            String hora1;
            try {
                hora1 = baixa.getHora1().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("hora1");
            }

            String hrcheg;
            try {
                hrcheg = baixa.getHrcheg().replace(".0", "");
            } catch (Exception ee) {
                throw new Exception("hrcheg");
            }

            String hrsaida;
            try {
                hrsaida = baixa.getHrsaida().replace(".0", "");
            } catch (Exception ee) {
                hrsaida = horaAtual;
            }

            String horaSaidaVei;
            try {
                horaSaidaVei = baixa.getHrsaidavei().replace(".0", "");
                if (horaSaidaVei == null || horaSaidaVei.equals("")) {
                    horaSaidaVei = hrsaida;
                }
            } catch (Exception ee) {
                horaSaidaVei = hrsaida;
            }

            String moedaPadrao;
            try {
                ParametDao parametDao = new ParametDao();
                moedaPadrao = parametDao.getMoedaPdrMobile(baixa.getCodfil(), persistencia);
            } catch (Exception ee) {
                moedaPadrao = "BRL";
            }

            boolean isPortalGuias = false;

            try {
                if (baixa.getPortalGuias().equals("S")) {
                    isPortalGuias = true;
                }
            } catch (Exception ex) {

            }

            // Guia do portal - Tratamentos específicos
            if (isPortalGuias) {
                EGtvDao eGtvDao = new EGtvDao();

                // Tratamento quando estiver salvando guia do portal - Exclui dados anteriores
                if (null != baixa.getGuias()
                        && baixa.getGuias().size() > 0) {
                    List<Guias> guias = baixa.getGuias();

                    for (Guias guias1 : guias) {
                        eGtvDao.excluirGuiaPortal(guias1.getGuia(), guias1.getSerie(), persistencia);
                        this.logerro.Grava("Excluir Guia Portal1 >> " + guias1.getGuia() + " | " + guias1.getSerie(), this.caminho);
                    }
                } else {
                    List<Rt_Guias> lista = eGtvDao.listaGuiasParaExclusao(baixa.getSequencia(), baixa.getParada(), persistencia);

                    for (Rt_Guias lista1 : lista) {
                        eGtvDao.excluirGuiaPortal(lista1.getGuia().toPlainString().replace(".0", ""), lista1.getSerie(), persistencia);
                        this.logerro.Grava("Excluir Guia Portal2 >> " + lista1.getGuia() + " | " + lista1.getSerie(), this.caminho);
                    }
                }

                // Verificação de Somente excluir Guias
                if (null != baixa.getSomenteExclusao()
                        && baixa.getSomenteExclusao().equals("S")) {
                    return Response
                            .status(Response.Status.OK)
                            .type("application/json")
                            .entity(gson.toJson(retorno))
                            .build();
                }
            }

            if (!semaforoDao.existeSemaforo(semaforoBaixaHorariosRota, sequencia, dataAtual, persistencia)) {
                // Mudança de localização do semáforo
                semaforoDao.inserirRegistro(sCodPessoa, dataAtual, horaAtual, semaforoBaixaHorariosRota, sequencia, persistencia);
                this.logerro.Grava("Semaforo fechado (" + semaforoBaixaHorariosRota + ", " + sequencia + ")", this.caminho);

                List<Guias> guias = baixa.getGuias();

                if (er.equals("E") && (guias == null || guias.isEmpty())) {  // Busca as guias de suprimento em caixa-forte caso nao estejam listadas
//                if (er.equals("E") && "".equals(guia)) {  // Busca as guias de suprimento em caixa-forte caso nao estejam listadas
                    guias = new ArrayList<>();
                    Guias guia;
                    Volumes volume;
                    CxFGuiasDao cxfguiasDao = new CxFGuiasDao();
                    CxFGuiasVolDao cxfguiasvolDao = new CxFGuiasVolDao();

                    String hora1hhmm = hora1.replace(":", "");
                    List<CxFGuias> ListaGuiasE = cxfguiasDao.getCxfGuiasEntrega(sequencia, hora1hhmm, persistencia);

                    for (CxFGuias cxFGuias : ListaGuiasE) {
                        guia = new Guias();
                        guia.setGuia(cxFGuias.getGuia().replace(".0", ""));
                        guia.setSerie(cxFGuias.getSerie().replace(".0", ""));
                        guia.setValor(cxFGuias.getValor().replace(".0", ""));
                        guia.setMoeda(moedaPadrao);
                        guia.setRpv("0");

                        guia.setVolumes(new ArrayList<>());
                        List<CxFGuiasVol> volumes = cxfguiasvolDao.getLacres(cxFGuias.getGuia().replace(".0", ""), cxFGuias.getSerie(), persistencia);
                        for (CxFGuiasVol cxFGuiasVol : volumes) {
                            volume = new Volumes();
                            volume.setLacre(cxFGuiasVol.getLacre());
                            volume.setObservacaoLacres(cxFGuiasVol.getObs());
                            volume.setQtvolumes(cxFGuiasVol.getQtde());
                            volume.setTipoLacres(cxFGuiasVol.getTipo());
                            volume.setValoresLacres(cxFGuiasVol.getValor().toPlainString());
                            guia.getVolumes().add(volume);
                        }
                        guias.add(guia);
                        this.logerro.Grava("Busca GTV entrega. Sequencia (" + sequencia + ") hora1d (" + hora1hhmm + ") Guia(s): " + guia.getGuia() + " Serie(s): " + guia.getSerie(), this.caminho);
                    }

                }

                String data_sql = dataAtual;

                String latitude;
                try {
                    latitude = baixa.getLatitude().replace(".0", "");
                } catch (Exception ee) {
                    latitude = "";
                }

                String longitude;
                try {
                    longitude = baixa.getLongitude().replace(".0", "");
                } catch (Exception ee) {
                    longitude = "";
                }

                String KM;
                try {
                    KM = baixa.getKm().replace(".0", "");
                } catch (Exception ee) {
                    KM = "";
                }

                String OBS;
                try {
                    OBS = baixa.getObs().replace(".0", "");
                } catch (Exception ee) {
                    OBS = "";
                }

                int vRPVNAK = 0;
                int vGuiasNAK = 0;

                Rt_PercDao rt_percDao = new Rt_PercDao();
                Rt_PercSlaDao rt_percSlaDao = new Rt_PercSlaDao();
                RotasDao rotasDao = new RotasDao();
                MobileProcAntDao mobileProcAntDAO = new MobileProcAntDao();
                RPVDao rpvdao = new RPVDao();

                String dataAnterior = rotasDao.existeRota(new BigDecimal(sequencia), dataAtual, persistencia);

                String nred = rt_percDao.obterNred(parada, sequencia, persistencia);
//                if (("E".equals(er) && "".equals(guia) && rt_percDao.isCaixaForte(parada, sequencia, persistencia))
                if (("E".equals(er) && (guias == null || guias.isEmpty()) && rt_percDao.isCaixaForte(parada, sequencia, persistencia))
                        || nred.contains("ALMOCO")) {//Verififica se parada e caixa forte.

                    this.logerro.Grava("Parada de caixa forte >> " + parada + " | " + sequencia, this.caminho);

                    rt_percDao.updateHrSaida(sequencia, parada, hrsaida, hrcheg, dataAtual, persistencia);
                    rt_percSlaDao.inserirHorarioSaida(sequencia, parada, horaSaidaVei, horaAtual, dataAtual, operador, persistencia);

                    // gravando dados em Rt_PercDet 
                    try {

                        Rt_PercDet rt_percdet = new Rt_PercDet();
                        Rt_PercDetDao rt_percdetdao = new Rt_PercDetDao();
                        rt_percdet.setCodFil(codfil);
                        rt_percdet.setSequencia(sequencia);
                        rt_percdet.setParada(Integer.valueOf(parada));
                        rt_percdet.setKM(KM);
                        if (rt_percdetdao.existeDet(rt_percdet, persistencia)) {
                            rt_percdetdao.AtualizaKMDet(rt_percdet, persistencia);
                        } else {
                            rt_percdetdao.InserirDet(rt_percdet, persistencia);
                        }
                        //Erros ao inserir - 14/12/2016
                        retorno.put("resp", 1);
                    } catch (Exception e) {
                        this.logerro.Grava("Rt_PercDet. Erro ao atualizar dados. SeqRota: " + sequencia + " Parada: " + parada + " KM: " + KM + " " + e.getMessage(), this.caminho);
                        retorno.put("resp", 0);
                    }

                    //grava log da baixa (botao X na tela operacoes TV
                    SASLogDao sasLogDao = new SASLogDao();
                    boolean repete = false;
                    int cont = 1;
                    while (!repete) {
                        String seq_log = sasLogDao.maxSasLog(persistencia);
                        repete = sasLogDao.gravaSasLogParada(seq_log, sequencia, parada, data_sql, hrsaida, sCodPessoa, persistencia);
                        if (cont == 20) {
                            repete = true;
                        }
                        cont++;
                    }
//                } else if ("".equals(dataAnterior) && !("".equals(guia) && "R".equals(er))) {  // Serviço inconsistente. Recolhimento sem guia
                } else if ("".equals(dataAnterior) && !((guias == null || guias.isEmpty()) && "R".equals(er))) {  // Serviço inconsistente. Recolhimento sem guia

                    RPV rtv;
                    GTVDao gtvDao = new GTVDao();
                    EGtvDao eGtvDao = new EGtvDao();

                    for (Guias guia : guias) {  // Primeira fase: inserindo trace RPV          
                        this.logerro.Grava("Gerando trace RPV. Guia(s) (" + guia.getGuia() + ") Series: (" + guia.getSerie() + ")" + " RPV: (" + guia.getRpv() + ")", this.caminho);

                        if (null == guia.getValor() || guia.getValor().equals("")) {//Realiza o tratamento do valor digitado
                            guia.setValor("0.00");
                        }

                        if (null == guia.getMoeda() || guia.getMoeda().equals("")) {
                            guia.setMoeda(moedaPadrao);
                        }

                        BigDecimal qtdVolume = BigDecimal.ZERO;
                        for (Volumes volume : guia.getVolumes()) {
                            /* Tratamento para quando o mobile envia "null" para qtvolumes e/ou valoreslacres 23/10/2017 */
                            if (null == volume.getQtvolumes() || volume.getQtvolumes().equals("null")) {
                                volume.setQtvolumes("1");
                            }
                            qtdVolume = qtdVolume.add(new BigDecimal(volume.getQtvolumes()));

                            if (null == volume.getValoresLacres() || volume.getValoresLacres().equals("null")) {
                                volume.setValoresLacres(guia.getValor());
                            }
                        }

                        if (er.equals("R")
                                //                                            && new BigDecimal(guiasRpv).compareTo(new BigDecimal("200000000000")) == 1
                                && "53".equals(guia.getSerie())
                                && "0".equals(guia.getRpv())) {
                            guia.setRpv("1"); // Conserto erro 10-03-2017 - Aplicacao com erro ajuste no servidor
                        }

                        if (er.equals("E") || isTranspCacamba(param)) { // RPVs nao sao permitidos em entregas
                            guia.setRpv("0");
                        }

                        if ("0".equals(guia.getRpv())) { // Processar guia convencional
                            try {
                                if (!rpvdao.existeGuiaRPV(guia.getGuia(), guia.getSerie(), sequencia, parada, persistencia)) {

                                    guia.setProcessar("1");
                                    guia.setGuiaBD(guia.getGuia());
                                    rtv = new RPV();
                                    rtv.setCodPessoAut(codCliAuth);
                                    rtv.setGuia(guia.getGuia());
                                    rtv.setSerie(guia.getSerie());
                                    rtv.setRpv("0");
                                    rtv.setParada(Integer.parseInt(parada));
                                    rtv.setSeqRota(Float.parseFloat(sequencia));
                                    rtv.setData(dataAtual);
                                    rtv.setHora(horaAtual);
                                    rtv.setFlag_excl("");
                                    rtv.setValor(Float.parseFloat(guia.getValor().replace(" ", "")));
                                    rtv.setVolumes(qtdVolume.toBigInteger().toString());
                                    // rtv.setVolumes(volumeRpv);
                                    rpvdao.salvarInformacoes(rtv, persistencia);

                                    try {
                                        Rt_GuiasMoeda rt_guiasMoeda = new Rt_GuiasMoeda();
                                        rt_guiasMoeda.setSequencia(sequencia);
                                        rt_guiasMoeda.setParada(parada);
                                        rt_guiasMoeda.setGuia(guia.getGuia());
                                        rt_guiasMoeda.setSerie(guia.getSerie());
                                        rt_guiasMoeda.setMoeda(guia.getMoeda());
                                        rt_guiasMoeda.setOperador(RecortaAteEspaço(operador, 0, 10));
                                        rt_guiasMoeda.setDt_Alter(dataAtual);
                                        rt_guiasMoeda.setHr_Alter(horaAtual);

                                        Rt_GuiasMoedaDao rt_guiasMoedaDao = new Rt_GuiasMoedaDao();
                                        rt_guiasMoedaDao.inserirRt_GuiasMoedasDao(rt_guiasMoeda, persistencia);
                                    } catch (Exception em) {
                                        this.logerro.Grava("Rt_GuiasMoeda - " + em.getMessage(), this.caminho);
                                    }
                                } else {

                                    this.logerro.Grava("RPV - Tentativa de baixa em duplicidade: Guia: " + guia.getGuia() + " Serie: " + guia.getSerie() + " SeqRota/Parada: " + sequencia + "/" + parada, this.caminho);
                                    guia.setProcessar("0");
                                    vRPVNAK += 1;
                                    vBaixaDuplicidade += 1;
                                }
                            } catch (Exception e) {
                                guia.setProcessar("0");
                                vRPVNAK += 1;
                                this.logerro.Grava("RPV - Erro de gravacao. Provavel violacao de chave: Guia: " + guia.getGuia() + " Serie: " + guia.getSerie() + " SeqRota/Parada: " + sequencia + "/" + parada + "\r\n" + e.getMessage(), this.caminho);
                            }

                            if (isTranspCacamba(param)) {
                                try {
                                    String formaPgto;
                                    try {
                                        formaPgto = baixa.getFormaPgto().replace(".0", "");
                                    } catch (Exception ee) {
                                        throw new Exception("Sem infoPgto.");
                                    }
                                    String obsFormaPgto;
                                    try {
                                        obsFormaPgto = baixa.getObsFormaPgto().replace(".0", "");
                                    } catch (Exception ee) {
                                        obsFormaPgto = "";
                                    }

                                    String valorPagamento;
                                    try {
                                        valorPagamento = baixa.getValorPagamento().replace(".0", "");
                                    } catch (Exception ee) {
                                        valorPagamento = "";
                                    }

                                    if (formaPgto == null || formaPgto.equals("") || formaPgto.equals("null")) {
                                        throw new Exception("Sem infoPgto.");
                                    }
                                    this.logerro.Grava("Rt_GuiasFat - " + "ËntrouFat", this.caminho);
                                    Rt_GuiasFat rt_guiasFat = new Rt_GuiasFat();
                                    rt_guiasFat.setSequencia(sequencia);
                                    rt_guiasFat.setParada(parada);
                                    rt_guiasFat.setGuia(guia.getGuia());
                                    rt_guiasFat.setSerie(guia.getSerie());
                                    rt_guiasFat.setCodFil(codfil);
                                    rt_guiasFat.setOS(rt_percDao.obterOS(sequencia, parada, persistencia));
                                    rt_guiasFat.setEmbarques("1");
                                    rt_guiasFat.setValorEmb(valorPagamento);
                                    rt_guiasFat.setValorAst("");
                                    rt_guiasFat.setValorAdv("");
                                    rt_guiasFat.setValorTot(valorPagamento);
                                    rt_guiasFat.setFormaPgto(formaPgto);
                                    rt_guiasFat.setObs(obsFormaPgto);
                                    rt_guiasFat.setOperador(operador);
                                    rt_guiasFat.setDt_Alter(getDataAtual("SQL"));
                                    rt_guiasFat.setHr_Alter(getDataAtual("HORA"));

                                    Rt_GuiasFatDao rt_GuiasFatDao = new Rt_GuiasFatDao();
                                    rt_GuiasFatDao.inserirRt_GuiasFat(rt_guiasFat, persistencia);

                                } catch (Exception e) {
                                    this.logerro.Grava("ProcessaGuiaMobile", this.caminho);
                                }
                            }

                        } else if ("1".equals(guia.getRpv()) && er.equals("R")) {  // Processar RPV (Gerar guia eletrônica)
                            try {
                                String guiaExiste = rpvdao.retornaGuia(guia.getGuia(), persistencia);
                                if (guia.getGuia().equals(guiaExiste)) {
                                    this.logerro.Grava("RPV - Tentativa de baixa em duplicidade: Guia: " + guiaExiste, this.caminho);
                                    guia.setProcessar("0"); // TAG QUE FALTAVA 13/07/2017 - Richard
                                } else {
                                    BigDecimal gtv = BigDecimal.ZERO;
                                    // Nesse caso a variavel guiasRpv é um RPV

                                    rtv = new RPV();
                                    rtv.setCodPessoAut(codCliAuth);
                                    rtv.setSerie(guia.getSerie());
                                    rtv.setRpv(guia.getGuia());
                                    rtv.setParada(Integer.parseInt(parada));
                                    rtv.setSeqRota(Float.parseFloat(sequencia));
                                    rtv.setData(dataAtual);
                                    rtv.setHora(horaAtual);
                                    rtv.setFlag_excl("");
                                    rtv.setValor(Float.parseFloat(guia.getValor().replace(" ", "")));
                                    rtv.setVolumes(String.valueOf(qtdVolume)); // 01/10/2017
                                    // rtv.setVolumes(volumeRpv);

                                    if (!rpvdao.existeRPV(guia.getGuia(), persistencia)) {
                                        // GERAÇÃO DE GUIA AQUI
                                        for (int tentativas = 1;; tentativas++) {
                                            if (!semaforoDao.existeSemaforo(semaforoGTV, chaveSemaforoGTV, dataAtual, persistencia)) {
                                                try {
                                                    semaforoDao.inserirRegistro(operador, dataAtual, horaAtual, semaforoGTV, chaveSemaforoGTV, persistencia);
                                                    gtv = gtvDao.gerarGuia(guia.getSerie(), guia.getGuia(), codfil, persistencia);
                                                    this.logerro.Grava("Inserindo eGTV.", this.caminho);
                                                    eGtvDao.inserirEGtv(gtv.toBigInteger().toString(), guia.getSerie(), getDataAtual("SQL"), getDataAtual("HORA"), persistencia);
                                                    this.logerro.Grava("Guia " + gtv.toBigInteger().toString() + " gerada em " + tentativas + " tentativas.", this.caminho);
                                                    break;
                                                } catch (Exception semaforos) {
                                                    this.logerro.Grava(semaforos.getMessage(), this.caminho);
                                                } finally {
                                                    semaforoDao.removerBaixaTabela(semaforoGTV, chaveSemaforoGTV, persistencia);
                                                }
                                            } else if (semaforoDao.tempoSemaforo(semaforoGTV, chaveSemaforoGTV, persistencia) > 5) {
                                                semaforoDao.removerBaixaTabela(semaforoGTV, chaveSemaforoGTV, persistencia);
                                                this.logerro.Grava("Semáforo GTV removido", this.caminho);
                                            }
                                            Thread.sleep(5000);
                                        }

                                        guia.setGuiaBD(gtv.toBigInteger().toString());
                                        guia.setProcessar("1");
                                        this.logerro.Grava("Não existe RPV ainda. RPV geracao de guia. RPV: " + guia.getRpv()
                                                + " Guia: " + guia.getGuiaBD(), this.caminho);

                                        rtv.setGuia(guia.getGuiaBD());

                                        rpvdao.salvarInformacoes(rtv, persistencia);
                                        // Salvar RPV NA BASE CENTRAL
                                        // Persistencia satellite = pool.getConexao("SATELLITE");
                                        // rpvdao.salvarInformacoes(rtv, satellite);

                                        try {
                                            Rt_GuiasMoeda rt_guiasMoeda = new Rt_GuiasMoeda();
                                            rt_guiasMoeda.setSequencia(sequencia);
                                            rt_guiasMoeda.setParada(parada);
                                            rt_guiasMoeda.setGuia(guia.getGuiaBD());
                                            rt_guiasMoeda.setSerie(guia.getSerie());
                                            rt_guiasMoeda.setMoeda(guia.getMoeda());
                                            rt_guiasMoeda.setOperador(RecortaAteEspaço(operador, 0, 10));
                                            rt_guiasMoeda.setDt_Alter(dataAtual);
                                            rt_guiasMoeda.setHr_Alter(horaAtual);

                                            Rt_GuiasMoedaDao rt_guiasMoedaDao = new Rt_GuiasMoedaDao();
                                            rt_guiasMoedaDao.inserirRt_GuiasMoedasDao(rt_guiasMoeda, persistencia);
                                        } catch (Exception e) {
                                            this.logerro.Grava("Rt_GuiasMoeda - " + e.getMessage(), this.caminho);
                                        }

                                        try {
                                            String formaPgto;
                                            try {
                                                formaPgto = baixa.getFormaPgto().replace(".0", "");
                                            } catch (Exception ee) {
                                                throw new Exception("Sem infoPgto.");
                                            }
                                            String obsFormaPgto;
                                            try {
                                                obsFormaPgto = baixa.getObsFormaPgto().replace(".0", "");
                                            } catch (Exception ee) {
                                                obsFormaPgto = "";
                                            }

                                            String valorPagamento;
                                            try {
                                                valorPagamento = baixa.getValorPagamento().replace(".0", "");
                                            } catch (Exception ee) {
                                                valorPagamento = "";
                                            }

                                            if (formaPgto == null || formaPgto.equals("") || formaPgto.equals("null")) {
                                                throw new Exception("Sem infoPgto.");
                                            }

                                            this.logerro.Grava("Baixar Servico - " + "Entrou Faturamento", this.caminho);
                                            Rt_GuiasFat rt_guiasFat = new Rt_GuiasFat();
                                            rt_guiasFat.setSequencia(sequencia);
                                            rt_guiasFat.setParada(parada);
                                            rt_guiasFat.setGuia(guia.getGuiaBD());
                                            rt_guiasFat.setSerie(guia.getSerie());
                                            rt_guiasFat.setCodFil(codfil);
                                            rt_guiasFat.setOS(rt_percDao.obterOS(sequencia, parada, persistencia));
                                            rt_guiasFat.setEmbarques("1");
                                            rt_guiasFat.setValorEmb(valorPagamento);
                                            rt_guiasFat.setValorAst("");
                                            rt_guiasFat.setValorAdv("");
                                            rt_guiasFat.setValorTot(valorPagamento);
                                            rt_guiasFat.setFormaPgto(formaPgto);
                                            rt_guiasFat.setObs(obsFormaPgto);
                                            rt_guiasFat.setOperador(operador);
                                            rt_guiasFat.setDt_Alter(getDataAtual("SQL"));
                                            rt_guiasFat.setHr_Alter(getDataAtual("HORA"));

                                            Rt_GuiasFatDao rt_GuiasFatDao = new Rt_GuiasFatDao();
                                            rt_GuiasFatDao.inserirRt_GuiasFat(rt_guiasFat, persistencia);

                                        } catch (Exception e) {
                                            this.logerro.Grava("Rt_GuiasFat - " + e.getMessage(), this.caminho);
                                        }

                                        gtvDao.atualizaGTV(persistencia, dataAtual, horaAtual,
                                                OBS, codfil, guia.getGuiaBD(), guia.getSerie());
                                    } else if (!rpvdao.existeRPVNaoExcluido(guia.getGuia(), persistencia)) {
                                        /*
                                                GTVSeqDao gtvSeqDao = new GTVSeqDao();
                                                guiaBD = gtvSeqDao.maxGuia53(codfil, sCodPessoa.contains(".0") ? sCodPessoa.replace(".0", "") : sCodPessoa, persistencia);
                                         */

                                        Rt_GuiasDao rt_guiasDao = new Rt_GuiasDao();
                                        gtv = rt_guiasDao.maxGuia53(persistencia);

                                        guia.setGuiaBD(gtv.toBigInteger().toString());
                                        guia.setProcessar("1");
                                        this.logerro.Grava("Não existe RPV não excluído. RPV geracao de guia. RPV: " + guia.getRpv()
                                                + " Guia: " + guia.getGuiaBD(), this.caminho);

                                        rtv.setGuia(guia.getGuiaBD());

                                        rpvdao.salvarInformacoes(rtv, persistencia);

                                        gtvDao.atualizaGTV(persistencia, dataAtual, horaAtual, OBS, codfil, guia.getGuiaBD(), guia.getSerie());
                                    } else {
                                        this.logerro.Grava("Guia não será processada, já deve existir entrada em RPV.", this.caminho);
                                        guia.setProcessar("0");

                                    }
                                }
                            } catch (Exception e) {
                                this.logerro.Grava("RPV - Falha em tentativa de processar novo RPV. Numero:  " + guia.getGuia() + " " + e.getMessage(), this.caminho);
                                vRPVNAK += 1;
                            }
                        }
                        this.logerro.Grava("RPV - processamento concluido. RPV: " + guia.getGuia(), this.caminho);
                    }

                    try {
                        // Trartamento nao Gravar Guia em Faturamento - Carlos 16/11/2022

                        this.logerro.Grava("Rt_Guias - Inicio processamento. Guia(s): " + getGuias(guias) + " Serie: " + getSeries(guias), this.caminho);
                        String ret = processaGuiaMobile(sequencia, parada, er, codfil, guias, sCodPessoa, dataAtual, horaAtual, persistencia);
                        vGuiasNAK += Integer.parseInt(ret.split(" ")[0]);
                        this.logerro.Grava("Rt_Guias - Termino processamento. Guia(s) NAK: " + ret, this.caminho);

                        if (vGuiasNAK == 0) {
                            this.logerro.Grava("Guia(s) processadas com sucesso. Guia (" + getGuias(guias) + ") " + getSeries(guias), this.caminho);
                        }
                        // Se for Guia Portal nao salva em faturamento Carlos 16/11/2022 - Exclui do faturamento
                        if (!isPortalGuias) {
                            eGtvDao.excluirGuiaPortal(getGuias(guias), getSeries(guias), persistencia);
                            this.logerro.Grava("Guia(s) Excluida com sucesso. Guia (" + getGuias(guias) + ") " + getSeries(guias), this.caminho);                            
                        }
                    } catch (Exception e) {  // A falha geral vira formatada de ProcessaGuiaMobile
                        this.logerro.Grava(e.getMessage(), this.caminho);
                        vGuiasNAK += 1;
                    }

                    try {
                        rt_percDao.updateHrSaida(sequencia, parada, hrsaida, hrcheg, dataAtual, persistencia);
                        rt_percSlaDao.inserirHorarioSaida(sequencia, parada, horaSaidaVei, horaAtual, dataAtual, operador, persistencia);

                        // gravando dados em Rt_PercDet 
                        Rt_PercDet rt_percdet = new Rt_PercDet();
                        Rt_PercDetDao rt_percdetdao = new Rt_PercDetDao();
                        rt_percdet.setCodFil(codfil);
                        rt_percdet.setSequencia(sequencia);
                        rt_percdet.setParada(Integer.valueOf(parada));
                        rt_percdet.setKM(KM);
                        if (rt_percdetdao.existeDet(rt_percdet, persistencia)) {
                            rt_percdetdao.AtualizaKMDet(rt_percdet, persistencia);
                        } else {
                            rt_percdetdao.InserirDet(rt_percdet, persistencia);
                        }
                    } catch (Exception e) {
                        this.logerro.Grava("Rt_PercDet. Erro ao atualizar dados. SeqRota: " + sequencia + " Parada: " + parada + " KM: " + KM + " " + e.getMessage(), this.caminho);
                    }

                    //grava log da baixa (botao X na tela operacoes TV
                    SASLogDao sasLogDao = new SASLogDao();
                    boolean repete = false;
                    int cont = 1;
                    while (!repete) {
                        String seq_log = sasLogDao.maxSasLog(persistencia);
                        repete = sasLogDao.gravaSasLogParada(seq_log, sequencia, parada, data_sql, hrsaida, sCodPessoa, persistencia);
                        if (cont == 20) {
                            repete = true;
                        }
                        cont++;
                    }

                    try {
                        if (!"".equals(latitude) && !"".equals(longitude)) {
                            ClientesDao clidao = new ClientesDao();
                            clidao.updateLglt(latitude, longitude, sequencia, parada, persistencia);
                        }
                    } catch (Exception e) {
                        this.logerro.Grava("Falha ao gravar posição do cliente\r\n" + e.getMessage(), this.caminho);
                    }

                    if ((vRPVNAK + vGuiasNAK) > 0 && (vBaixaDuplicidade == 0)) {   // Se houveram rejeições
                        retorno.put("resp", 0);  //Resposta negativa
                    } else {
                        retorno.put("resp", 1); // Tudo OK  
                    }
                } else {

                    // Processamento de requisições de datas anteriores. Guarda no log para registro
                    // Realizando o salvamento do registors
                    try {
                        MobileProcAnt mobileProcAnt = new MobileProcAnt();
                        mobileProcAnt.setSequencia(mobileProcAntDAO.gerarSequencia(persistencia));
                        mobileProcAnt.setDtProcAnt(dataAnterior);
                        mobileProcAnt.setDtRecebido(dataAtual);
                        mobileProcAnt.setParam(param);
                        mobileProcAnt.setHrRecebido(horaAtual);
                        mobileProcAnt.setComando(input);
                        mobileProcAntDAO.salvarRegistros(mobileProcAnt, persistencia);

                        rt_percDao.updateHrSaida(sequencia, parada, hrsaida, hrcheg, dataAtual, persistencia);
                        rt_percSlaDao.inserirHorarioSaida(sequencia, parada, horaSaidaVei, horaAtual, dataAtual, operador, persistencia);
                        try {

                            Rt_PercDet rt_percdet = new Rt_PercDet();
                            Rt_PercDetDao rt_percdetdao = new Rt_PercDetDao();
                            rt_percdet.setCodFil(codfil);
                            rt_percdet.setSequencia(sequencia);
                            rt_percdet.setParada(Integer.valueOf(parada));
                            rt_percdet.setKM(KM);
                            if (rt_percdetdao.existeDet(rt_percdet, persistencia)) {
                                rt_percdetdao.AtualizaKMDet(rt_percdet, persistencia);
                            } else {
                                rt_percdetdao.InserirDet(rt_percdet, persistencia);
                            }
                        } catch (Exception e) {
                            this.logerro.Grava("Rt_PercDet. Erro ao atualizar dados. SeqRota: " + sequencia + " Parada: " + parada + " KM: " + KM + " " + e.getMessage(), this.caminho);
                        }
                        this.logerro.Grava("MobileProcAnt. Gravacao proc ant ou GTV zerada em recolhimento.", this.caminho);
                    } catch (Exception e) {
                        this.logerro.Grava("MobileProcAnt. Falha ao gravar processamento." + e.getMessage(), this.caminho);
                    }

                    retorno.put("resp", 1); // Tudo OK  
                }

                // Excluir do semaforo
                semaforoDao.removerBaixaTabela(semaforoBaixaHorariosRota, sequencia, persistencia);
                this.logerro.Grava("Semaforo aberto (" + semaforoBaixaHorariosRota + ", " + sequencia + ")", this.caminho);
            } else {
                retorno.put("resp", 0);
                this.logerro.Grava("Baixa existe em semaforo (" + semaforoBaixaHorariosRota + ", " + codCliAuth + ")", this.caminho);
                if (semaforoDao.tempoSemaforo(semaforoBaixaHorariosRota, sequencia, persistencia) > 2) {
                    semaforoDao.removerBaixaTabela(semaforoBaixaHorariosRota, sequencia, persistencia);
                    this.logerro.Grava("Semáforo removido".toUpperCase(), this.caminho);
                }
            }
        } catch (Exception e) {
            this.logerro.Grava("Erro geral: " + e.getMessage(), this.caminho);
            retorno.put("resp", 0);
            retorno.put("erro", e.getMessage());
            if (sequencia != null) {
                try {
                    semaforoDao.removerBaixaTabela(semaforoBaixaHorariosRota, sequencia, persistencia);
                    this.logerro.Grava("Semaforo aberto (" + semaforoBaixaHorariosRota + ", " + sequencia + ")", this.caminho);
                } catch (Exception s) {
                    this.logerro.Grava("Remover Semáforo: " + s.getMessage(), this.caminho);
                }
            }
        } finally {
            try {
                persistencia.FechaConexao();
            } catch (Exception p) {
                this.logerro.Grava("Fecha Conexao: " + p.getMessage(), this.caminho);
            }

            this.logerro.Grava("Resposta: " + gson.toJson(retorno), this.caminho);
        }

        return Response
                .status(Response.Status.OK)
                .type("application/json")
                .entity(gson.toJson(retorno))
                //                .entity(retorno)
                .build();
    }

    public String processaGuiaMobile(String sequencia, String parada, String er, String codFil,
            List<Guias> guias, String sCodPessoa, String dataAtual, String horaAtual,
            Persistencia persistencia) throws Exception {
        try {
            GuiasMobile gm = new GuiasMobile();
            String xml;
            String falhas_processamento;
            String falhas_processamentog = "";
            String falhas_processamentol = "";
            String serie_guia_gravada;
            BigDecimal valor_parada = BigDecimal.ZERO;
            int vGuiasNAK = 0;

            LacresDao lacreDao = new LacresDao();
            Rt_PercDao rt_percDao = new Rt_PercDao();

            CxForteDao cxfdao = new CxForteDao();
            CxForte cxforte = cxfdao.getCxForte(new BigDecimal(codFil), persistencia);

            for (Guias guia : guias) {

                if (guia.getProcessar().equals("1")) {
                    // Processamento de guias de Assistência técnica
                    // Richard - 11/07/2018
//                        if (er.equals("R")  || (er.equals("E") && !tiposrv.equals("A"))) {
                    try {
                        serie_guia_gravada = gm.processamentoGuia(persistencia, sequencia, parada,
                                guia.getGuiaBD().replace(".0", ""), guia.getSerie(), codFil, guia.getValor(),
                                logerro, caminho, dataAtual, horaAtual);
                        //acumula o valor da parada
                        valor_parada = valor_parada.add(new BigDecimal(guia.getValor()));
                    } catch (Exception e) {
                        falhas_processamentog += Xmls.tag("guia", guia.getGuiaBD().replace(".0", ""))
                                + Xmls.tag("serie", guia.getSerie())
                                + Xmls.tag("valor", guia.getValor())
                                + Xmls.tag("casua", e.getMessage());
                        serie_guia_gravada = null;
                        vGuiasNAK += 1;
                    }

                    String lacre, qtvolumes_lacre, valores_lacre, observacao_lacre, tipo_lacre;
                    for (int i = 0; i < guia.getVolumes().size(); i++) {
                        Volumes volume = guia.getVolumes().get(i);
                        try {
                            lacre = volume.getLacre();
                        } catch (Exception e) {
                            lacre = "0";
                        }
                        try {
                            qtvolumes_lacre = volume.getQtvolumes();
                        } catch (Exception e) {
                            qtvolumes_lacre = "1";
                        }
                        try {
                            valores_lacre = volume.getValoresLacres();
                        } catch (Exception e) {
                            valores_lacre = "0.00";
                        }
                        try {
                            observacao_lacre = volume.getObservacaoLacres();
                            if (observacao_lacre.equals("null")) {
                                observacao_lacre = "";
                            }
                        } catch (Exception e) {
                            observacao_lacre = "";
                        }
                        try {
                            tipo_lacre = volume.getTipoLacres();
                            if (tipo_lacre.equals("null")) {
                                tipo_lacre = "1";
                            }
                        } catch (Exception e) {
                            tipo_lacre = "1";
                        }

                        if (er.equals("R") || isTranspCacamba(persistencia.getEmpresa())) {
                            try {
                                if (serie_guia_gravada != null) {
                                    if (!lacreDao.LacreValido(guia.getGuiaBD().replace(".0", ""), serie_guia_gravada, lacre, codFil, persistencia)) {
                                        int volumeQtd;
                                        try {
                                            volumeQtd = Integer.parseInt(qtvolumes_lacre);
                                        } catch (Exception e) {
                                            volumeQtd = 1;
                                        }
                                        String valLacre = valores_lacre;
                                        if (null == valLacre || valLacre.equals("null")) {
                                            valLacre = "0.00";
                                        }
                                        if (persistencia.getEmpresa().toUpperCase().contains("TRANSVIP")) {
                                            lacreDao.insereLacre(codFil, cxforte.getCodCli(), guia.getGuiaBD().replace(".0", ""), serie_guia_gravada, i, volumeQtd, lacre, 1, valLacre, persistencia);
                                        } else {
                                            lacreDao.insereLacre(codFil, cxforte.getCodCli(), guia.getGuiaBD().replace(".0", ""), serie_guia_gravada, i, volumeQtd, lacre,
                                                    tipo_lacre, valLacre, observacao_lacre, persistencia);
                                        }
                                    }
                                }
                            } catch (Exception e) {

                                logerro.Grava("lacreDao.insereLacre(" + codFil + ", " + cxforte.getCodCli() + ", " + guia.getGuiaBD().replace(".0", "") + ", "
                                        + serie_guia_gravada + ", i, " + qtvolumes_lacre + ", " + lacre + ", 1, " + valores_lacre + ", persistencia);\r\n"
                                        + e.getMessage(), caminho);
                                vGuiasNAK += 1;
                                falhas_processamentol += Xmls.tag("guia", guia.getGuiaBD().replace(".0", ""))
                                        + Xmls.tag("serie", guia.getSerie())
                                        + Xmls.tag("valor", guia.getValor())
                                        + Xmls.tag("lacre", lacre)
                                        + Xmls.tag("casua", e.getMessage());
                            }
                        }
                    }
                } else {
                    vGuiasNAK += 1;
                    falhas_processamentol += "processar=0 " + Xmls.tag("guia", guia.getGuiaBD().replace(".0", ""))
                            + Xmls.tag("serie", guia.getSerie())
                            + Xmls.tag("valor", guia.getValor())
                            + Xmls.tag("casua", "processar!=1");
                }

                //grava total da parada em caso de recolhimento
                if (guia.getProcessar().equals("1") && "R".equals(er)) {
                    rt_percDao.defineValorParada(persistencia, sequencia, valor_parada.toPlainString(), parada);
                }
            }

            falhas_processamento = Xmls.tag("erro", Xmls.tag("guias", falhas_processamentog)
                    + Xmls.tag("lacres", falhas_processamentol));
            String vGuiasNAKStr = String.format("%1$03d", vGuiasNAK);  // Formatacao 3d="000" 4d="0000" 5d = "00000" ...
            xml = vGuiasNAKStr + " " + falhas_processamento;
            return xml;
        } catch (Exception e) {
            throw new Exception("ProcessaGuiaMobile falha geral. " + e.getMessage());
        }
    }

    // Guias da lista de guias
    public String getGuias(List<Guias> guias) {
        StringBuilder retorno = new StringBuilder();
        for (Guias guia : guias) {
            retorno.append(guia.getGuia()).append("; ");
        }
        return retorno.toString();
    }

    // Séries da lista de guias
    public String getSeries(List<Guias> guias) {
        StringBuilder retorno = new StringBuilder();
        for (Guias guia : guias) {
            retorno.append(guia.getSerie()).append("; ");
        }
        return retorno.toString();
    }
}
