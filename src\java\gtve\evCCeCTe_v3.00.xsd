<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/cte" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="eventoCTeTiposBasico_v3.00.xsd"/>
	<xs:element name="evCCeCTe">
		<xs:annotation>
			<xs:documentation>Schema XML de validação do evento carta de correção 
110110</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="descEvento">
					<xs:annotation>
						<xs:documentation>Descrição do Evento - “Carta de Correção”</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:whiteSpace value="preserve"/>
							<xs:enumeration value="Carta de Correção"/>
							<xs:enumeration value="Carta de Correcao"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="infCorrecao" maxOccurs="unbounded">
					<xs:annotation>
						<xs:documentation>Grupo de Informações de Correção</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="grupoAlterado">
								<xs:annotation>
									<xs:documentation>Indicar o grupo de informações que pertence o campoAlterado. Ex: ide</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="preserve"/>
										<xs:minLength value="1"/>
										<xs:maxLength value="20"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="campoAlterado">
								<xs:annotation>
									<xs:documentation>Nome do campo modificado do CT-e Original.</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="preserve"/>
										<xs:minLength value="1"/>
										<xs:maxLength value="20"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="valorAlterado">
								<xs:annotation>
									<xs:documentation>Valor correspondente à alteração.</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="preserve"/>
										<xs:minLength value="1"/>
										<xs:maxLength value="500"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="nroItemAlterado" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Preencher com o indice do item alterado caso a alteração ocorra em uma lista. 
OBS: O indice inicia sempre  em 1</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="preserve"/>
										<xs:pattern value="[1-9][0-9]|0?[1-9]"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="xCondUso">
					<xs:annotation>
						<xs:documentation>Condições de uso da Carta de Correção,</xs:documentation>
						<xs:documentation>informar a literal :Condições de uso da Carta de Correção, informar a literal:
“A Carta de Correção é disciplinada pelo Art. 58-B do CONVÊNIO/SINIEF 06/89: Fica permitida a utilização de carta de correção, para regularização de erro ocorrido na emissão de documentos fiscais relativos à prestação de serviço de transporte, desde que o erro não esteja relacionado com: I - as variáveis que determinam o valor do imposto tais como: base de cálculo, alíquota, diferença de preço, quantidade, valor da prestação;II - a correção de dados cadastrais que implique mudança do emitente, tomador, remetente ou do destinatário;III - a data de emissão ou de saída.” (texto com acentuação)  ou “A Carta de Correcao e disciplinada pelo Art. 58-B do CONVENIO/SINIEF 06/89: Fica permitida a utilizacao de carta de correcao, para regularizacao de erro ocorrido na emissao de documentos fiscais relativos a prestacao de servico de transporte, desde que o erro nao esteja relacionado com: I - as variaveis que determinam o valor do imposto tais como: base de calculo, aliquota, diferenca de preco, quantidade, valor da prestacao;II - a correcao de dados cadastrais que implique mudança do emitente, tomador, remetente ou do destinatario;III - a data de emissao ou de saida.” (texto sem acentuação)</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:whiteSpace value="preserve"/>
							<xs:enumeration value="A Carta de Correção é disciplinada pelo Art. 58-B do CONVÊNIO/SINIEF 06/89: Fica permitida a utilização de carta de correção, para regularização de erro ocorrido na emissão de documentos fiscais relativos à prestação de serviço de transporte, desde que o erro não esteja relacionado com: I - as variáveis que determinam o valor do imposto tais como: base de cálculo, alíquota, diferença de preço, quantidade, valor da prestação;II - a correção de dados cadastrais que implique mudança do emitente, tomador, remetente ou do destinatário;III - a data de emissão ou de saída."/>
							<xs:enumeration value="A Carta de Correcao e disciplinada pelo Art. 58-B do CONVENIO/SINIEF 06/89: Fica permitida a utilizacao de carta de correcao, para regularizacao de erro ocorrido na emissao de documentos fiscais relativos a prestacao de servico de transporte, desde que o erro nao esteja relacionado com: I - as variaveis que determinam o valor do imposto tais como: base de calculo, aliquota, diferenca de preco, quantidade, valor da prestacao;II - a correcao de dados cadastrais que implique mudanca do emitente, tomador, remetente ou do destinatario;III - a data de emissao ou de saida."/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
