/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.dto.request;

/**
 *
 * <AUTHOR>
 */
public class ComposicaoRequest {

    private String classificacaoCedulaMoeda;
    private int categoriaCedulaMoeda;
    private String especieCedulaMoeda;
    private int denominacaoCedulaMoeda;
    private int quantidadeCedulaMoeda;
    private double valorTotalCedulaMoeda;

    public String getClassificacaoCedulaMoeda() {
        return classificacaoCedulaMoeda;
    }

    public void setClassificacaoCedulaMoeda(String classificacaoCedulaMoeda) {
        this.classificacaoCedulaMoeda = classificacaoCedulaMoeda;
    }

    public int getCategoriaCedulaMoeda() {
        return categoriaCedulaMoeda;
    }

    public void setCategoriaCedulaMoeda(int categoriaCedulaMoeda) {
        this.categoriaCedulaMoeda = categoriaCedulaMoeda;
    }

    public String getEspecieCedulaMoeda() {
        return especieCedulaMoeda;
    }

    public void setEspecieCedulaMoeda(String especieCedulaMoeda) {
        this.especieCedulaMoeda = especieCedulaMoeda;
    }

    public int getDenominacaoCedulaMoeda() {
        return denominacaoCedulaMoeda;
    }

    public void setDenominacaoCedulaMoeda(int denominacaoCedulaMoeda) {
        this.denominacaoCedulaMoeda = denominacaoCedulaMoeda;
    }

    public int getQuantidadeCedulaMoeda() {
        return quantidadeCedulaMoeda;
    }

    public void setQuantidadeCedulaMoeda(int quantidadeCedulaMoeda) {
        this.quantidadeCedulaMoeda = quantidadeCedulaMoeda;
    }

    public double getValorTotalCedulaMoeda() {
        return valorTotalCedulaMoeda;
    }

    public void setValorTotalCedulaMoeda(double valorTotalCedulaMoeda) {
        this.valorTotalCedulaMoeda = valorTotalCedulaMoeda;
    }
}
