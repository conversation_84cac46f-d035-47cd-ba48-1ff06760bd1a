/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.faceid;

import com.facephi.sdk.licensing.LicenseActivationException;
import com.facephi.sdk.matcher.AuthenticationResult;
import com.facephi.sdk.matcher.MatcherException;
import com.facephi.sdk.matcher.MatcherType;
import com.facephi.sdk.matcher.MatchingSecurityLevel;
import com.facephi.sdk.matcher.TemplateReliability;

/**
 *
 * <AUTHOR>
 */
public class MatcherWrapper {

    private static com.facephi.sdk.matcher.MatcherConfigurationManager MatcherConfigurationManager = null;

    private static com.facephi.sdk.matcher.Matcher InstanceMatcher() throws Exception {
        try {
            MatcherConfigurationManager = new com.facephi.sdk.matcher.MatcherConfigurationManager();
            MatcherConfigurationManager.setMatchingSecurityLevel(MatchingSecurityLevel.HighSecurityLevel);
            MatcherConfigurationManager.setMatcherType(MatcherType.Any);
            MatcherConfigurationManager.setTemplateReliability(TemplateReliability.ExtremeTemplateReliability);

            return new com.facephi.sdk.matcher.Matcher(MatcherConfigurationManager);
        } catch(java.lang.UnsatisfiedLinkError e){
            throw e;
        } catch (MatcherException e) {
            // TODO: manage SDK internal error: try again, if continue restart service, device or contact with FacePhi support
            throw e;
        } catch (LicenseActivationException e) {
            // TODO: manage licensing error: check licensing server (LM-X License Server - FACEPHI) - check log file [SDK_PATH]\LicenseActivator\lmx-serv-facephi.log
            throw e;
        } catch (Exception e) {
            // TODO: manage generic exception
            throw e;
        }
    }
    
    public static byte[] CreateUser(byte[] template) throws Exception {
        if (template == null) {
            // TODO: manage parameter error. Template parameter can't be null
            throw new Exception("Template parameter can't be null");
        }

        com.facephi.sdk.matcher.Matcher matcher = InstanceMatcher();
        if (matcher == null) {
            // TODO: Manage matcher instance problem
            throw new Exception("Matcher instance problem");
        }

        try {
            return matcher.createUser(template);
        } catch (MatcherException e) {
            // TODO: manage parameter error. Template hasn't a valid structure
            throw e;
        } catch (LicenseActivationException e) {
            // TODO: manage licensing error: check licensing server (LM-X License Server - FACEPHI) - check log file [SDK_PATH]\LicenseActivator\lmx-serv-facephi.log
            throw e;
        } catch (Exception e) {
            // TODO: manage generic exception 
            throw e;
        }
    }

    public static AuthenticationResult Authenticate(byte[] structure, byte[] template) throws Exception {
        if (structure == null || template == null) {
            // TODO: manage parameter error. Structure and template parameter can't be null
            throw new Exception("Structure and template parameter can't be null");
        }

        com.facephi.sdk.matcher.Matcher matcher = InstanceMatcher();
        if (matcher == null) {
            // TODO: Manage matcher instance problem
            throw new Exception("Matcher instance problem");
        }

        try {
            return matcher.authenticateRetrain(structure, template);
        } catch (MatcherException e) {
            // TODO: manage parameter error. Structure and/or template haven't a valid structure
            throw e;
        } catch (LicenseActivationException e) {
            // TODO: manage licensing error: check licensing server (LM-X License Server - FACEPHI) - check log file [SDK_PATH]\LicenseActivator\lmx-serv-facephi.log
            throw e;
        } catch (Exception e) {
            // TODO: manage generic exception
            throw e;
        }
    }

    public static byte[] RetrainUser(byte[] structure, byte[] template) throws Exception {
        if (structure == null || template == null) {
            // TODO: manage parameter error. Structure and template parameters can't be null
            throw new Exception("Structure and template parameter can't be null");
        }

        com.facephi.sdk.matcher.Matcher matcher = InstanceMatcher();
        if (matcher == null) {
            // TODO: Manage matcher instance problem
            throw new Exception("Matcher instance problem");
        }

        try {
            return matcher.retrainUserAuthenticated(structure, template);
        } catch (MatcherException e) {
            // TODO: manage parameter error. Structure and/or template haven't a valid structure
            throw new Exception(e.getMessage());
        } catch (LicenseActivationException e) {
            // TODO: manage licensing error: check licensing server (LM-X License Server - FACEPHI) - check log file [SDK_PATH]\LicenseActivator\lmx-serv-facephi.log
            throw e;
        } catch (Exception e) {
            // TODO: manage generic exception
            throw e;
        }
    }

}
