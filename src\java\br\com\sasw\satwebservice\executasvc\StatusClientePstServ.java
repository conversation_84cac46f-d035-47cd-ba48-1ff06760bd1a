/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.executasvc;

import Arquivo.ArquivoLog;
import Dados.Consulta;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasDaos.FiliaisDao;
import br.com.sasw.pacotesuteis.sasbeans.TOKENS;
import br.com.sasw.pacotesuteis.sasdaos.TOKENSDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogo;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.satwebservice.Utilidades.obterParametros;
import br.com.sasw.satwebservice.messages.Messages;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.ResultSet;
import java.util.Map;
import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
@Path("/executasvc/")
public class StatusClientePstServ {

    private final ArquivoLog logerro;
    private ArquivoLog logexecucao;
    private ArquivoLog arquivohtml;
    private final SasPoolPersistencia pool;
    private String caminho;
    private String caminhoweb;
    private final Messages messages;
    private final FiliaisDao filiaisDao;
    private final TOKENSDao tokenDao;
    private ResultSet vResultadoConsulta;
    private String vRetorno;
    private String vRetornoPessoa;
    private String vRetornoObra;
    private String vRetornoCacamba;
    private String vRetornoVeiculo;
    private String vRetornoCtr;
    private String vRetornoAlocar;
    private Boolean vEndeOK = false;
    private String vRetornoEGuardian;
    private final String vGuiaParam = "";
    private final String vSerieParam = "";

    public StatusClientePstServ() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\erro.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        messages = new Messages();

        filiaisDao = new FiliaisDao();
        tokenDao = new TOKENSDao();
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/obterFuncion")
    public Response obterFuncion(String param) throws UnsupportedEncodingException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vSecao = (String) parametros.getOrDefault("secao", null);
        String vToken = (String) parametros.getOrDefault("token", null);
        String vData = (String) parametros.getOrDefault("databatida", null);
        String vCodfil = (String) parametros.getOrDefault("codfil", null);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "", vSQLF = "";

        try {
            //Validando se a matrícula informada não está vazia
            if (vSecao == null || vSecao.equals("")) {
                throw new StatusClientePstServException("SecaoInvalida");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + qToken.getBancoDados() + "\\.txt";

            }

            try {
                vSQL = " Select Clientes.Nome, Clientes.Nred, PstServ.Secao, PstServ.Local,\n "
                        + " Clientes.Ende, Clientes.Bairro, Clientes.Cidade, Clientes.Estado, \n "
                        + "Clientes.CEP, Clientes.Fone1, Clientes.Latitude, Clientes.Longitude, \n"
                        + " Clientes.email from PstServ \n"
                        + "Left JOin Clientes on Clientes.Codigo = PstServ.CodCLi\n"
                        + "                  and Clientes.CodFil = PstServ.CodFil\n"
                        + "where PstServ.Secao = '" + vSecao + "' \n"
                        + "  and PStServ.CodFil = " + vCodfil;
                //+ "                          where saspw.codpessoa = ? and paramet.path = ?) ";        
                Consulta qTmpX = new Consulta(vSQL, dbpadrao);
                ///consulta.setString(param);
                qTmpX.select();
                JSONObject vjnPstServ = new JSONObject();
                while (qTmpX.Proximo()) {
                    vjnPstServ.put("nome", qTmpX.getString("Nome"));
                    vjnPstServ.put("Nred", qTmpX.getString("Nred"));
                    vjnPstServ.put("secao", qTmpX.getString("Secao"));
                    vjnPstServ.put("local", qTmpX.getString("Local"));
                    vjnPstServ.put("endereco", qTmpX.getString("Ende"));
                    vjnPstServ.put("bairro", qTmpX.getString("bairro"));
                    vjnPstServ.put("cidade", qTmpX.getString("Cidade"));
                    vjnPstServ.put("estado", qTmpX.getString("estado"));
                    vjnPstServ.put("cep", qTmpX.getString("CEP"));
                    vjnPstServ.put("fone1", qTmpX.getString("Fone1"));
                    vjnPstServ.put("latitude", qTmpX.getString("Latitude"));
                    vjnPstServ.put("longitude", qTmpX.getString("longitude"));
                    vjnPstServ.put("parametro", qToken.getBancoDados());

                    //Trata Funcionarios do Posto
                    JSONObject vjnFuncionHora;
                    JSONArray vjnPstServFun = new JSONArray();
                    vSQLF = " Select Funcion.Matr, Funcion.Nome, Funcion.Cargo, Cargos.Descricao, \n"
                            + "(Select Hora from RHPonto where RhPonto.Matr = Funcion.Matr and RHPonto.DtBatida = '" + vData + "' and Batida = 1) Hora1,\n"
                            + "(Select Hora from RHPonto where RhPonto.Matr = Funcion.Matr and RHPonto.DtBatida = '" + vData + "' and Batida = 4) Hora4 from Funcion\n"
                            + "Left Join Cargos on Cargos.Cargo = Funcion.Cargo\n"
                            + "where Funcion.Secao = '" + vSecao + "'\n"
                            + "  and Funcion.CodFil = " + vCodfil + "\n"
                            + "  and Funcion.Situacao = 'A' \n"
                            //+ "  and (Select Hora from RHPonto where RhPonto.Matr = Funcion.Matr and RHPonto.DtBatida = CONVERT(VARCHAR(8), GETDATE(), 112) and Batida = 1) is not null\n"
                            + "  and (Select Hora from RHPonto where RhPonto.Matr = Funcion.Matr and RHPonto.DtBatida = '" + vData + "' and batida = 1) is not null\n"
                            + "Order by Funcion.Matr";
                    //+ "                          where saspw.codpessoa = ? and paramet.path = ?) ";        
                    Consulta qTmpXF = new Consulta(vSQLF, dbpadrao);
                    qTmpXF.select();
                    while (qTmpXF.Proximo()) {
                        String vMatr = qTmpXF.getString("Matr").replace(".0", "");;

                        vMatr.replace(".0", "");

                        if (vMatr.length() == 1) {
                            vMatr = "0000000" + vMatr;
                        } else if (vMatr.length() == 2) {
                            vMatr = "000000" + vMatr;
                        } else if (vMatr.length() == 3) {
                            vMatr = "00000" + vMatr;
                        } else if (vMatr.length() == 4) {
                            vMatr = "0000" + vMatr;
                        } else if (vMatr.length() == 5) {
                            vMatr = "000" + vMatr;
                        } else if (vMatr.length() == 6) {
                            vMatr = "00" + vMatr;
                        } else if (vMatr.length() == 7) {
                            vMatr = "0" + vMatr;
                        }

                        vMatr.replace(".0", "");

                        String faceID = "'https://mobile.sasw.com.br:9091/satellite/fotos/" + qToken.getBancoDados()
                                + "/ponto/" + getDataAtual("SQL") + "/" + vMatr + "_1.jpg'";
                        File imgfoto = new File("C:\\xampp\\htdocs\\satellite\\fotos\\" + qToken.getBancoDados() + "\\ponto\\" + getDataAtual("SQL") + "\\" + vMatr + "_1.jpg");
                        if (imgfoto.exists()) {
                            byte[] fileContent = Files.readAllBytes(imgfoto.toPath());
                            String base64 = new sun.misc.BASE64Encoder().encode(fileContent);
                        } else {
                            faceID = "";
                        }

                        vjnFuncionHora = new JSONObject();
                        vjnFuncionHora.put("matr", qTmpXF.getString("Matr"));
                        vjnFuncionHora.put("nome", qTmpXF.getString("Nome"));
                        vjnFuncionHora.put("cargo", qTmpXF.getString("Cargo"));
                        vjnFuncionHora.put("descricao", qTmpXF.getString("Descricao"));
                        vjnFuncionHora.put("hora1", qTmpXF.getString("Hora1"));
                        vjnFuncionHora.put("hora4", qTmpXF.getString("Hora4"));
                        vjnFuncionHora.put("faceid", faceID);
                        vjnPstServFun.put(vjnFuncionHora);

                    }
                    qTmpXF.Close();

                    vjnPstServ.put("funcion", vjnPstServFun);
                    vjnRetorno.put("pstserv", vjnPstServ);
                }
                qTmpX.Close();

            } catch (Exception e) {
                throw new Exception("PstServDao.listarPostosPessoa - " + e.getMessage() + "\r\n"
                        + vSQL);
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/obterFoto")
    public Response obterFoto(String param) throws UnsupportedEncodingException, IOException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vMatr = (String) parametros.getOrDefault("matr", null);
        String vToken = (String) parametros.getOrDefault("token", null);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "";
        String vPathWeb = "";
        String vPathLocal = "";
//            
        //File img = new File("C:\\xampp\\htdocs\\satellite\\fotos\\SATSASEX\\ponto\\20210610\\08000107_1.jpg");
        //byte[] fileContent = Files.readAllBytes(img.toPath());
        //String base64 = new sun.misc.BASE64Encoder().encode(fileContent);
        try {
            //Validando se a matrícula informada não está vazia
            if (vMatr == null || vMatr.equals("")) {
                throw new StatusClientePstServException("SecaoInvalida");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vFace = "ECAF";

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vFace + "\\.txt";
            }
            vPathWeb = "'https://mobile.sasw.com.br:9091/satellite/fotos/" + qToken.getBancoDados() + "/_F_ID/";
            vPathLocal = "C:\\xampp\\htdocs\\satellite\\fotos\\" + vFace + "\\_F_ID\\";
            vMatr.replace(".0", "");
            if (vMatr.length() == 1) {
                vMatr = "0000000" + vMatr;
            } else if (vMatr.length() == 2) {
                vMatr = "000000" + vMatr;
            } else if (vMatr.length() == 3) {
                vMatr = "00000" + vMatr;
            } else if (vMatr.length() == 4) {
                vMatr = "0000" + vMatr;
            } else if (vMatr.length() == 5) {
                vMatr = "000" + vMatr;
            } else if (vMatr.length() == 6) {
                vMatr = "00" + vMatr;
            } else if (vMatr.length() == 7) {
                vMatr = "0" + vMatr;
            }

            //        + "/_F_ID/F" + vMatr + "_F1.jpg'";
            /*File vImgFoto = new File("C:\\xampp\\htdocs\\satellite\\fotos\\" + qToken.getBancoDados() ;//"\\_F_ID\\F" + vMatr + "_F1.jpg");
            if (vImgFoto.exists()) {
                byte[] fileContent = Files.readAllBytes(vImgFoto.toPath());
                String base64 = new sun.misc.BASE64Encoder().encode(fileContent);
            } else {
                vPathFaceID = "''";
            }*/
            try {

                vSQL = " Select Pessoa.Codigo, Pessoa.Nome, Pessoa.Matr,  \n"
                        + " PessoaDet.FaceID, PessoaDet.Foto1, PessoaDet.Foto2, PessoaDet.Foto3 \n"
                        + "from Pessoa \n"
                        + " Left Join PessoaDet on PessoaDet.Codigo = Pessoa.Codigo \n"
                        + " where Pessoa.matr = " + vMatr;
                Consulta qTmpX = new Consulta(vSQL, dbsatellite);
                qTmpX.select();
                JSONObject vjnPessoaFoto = new JSONObject();
                int vConta = 0;
                //if (qTmpX.getString("Matr") != "") {
                //.Proximo();
                //qTmpX.
                while (qTmpX.Proximo()) {
                    vConta++;
                    vjnPessoaFoto.put("matr", qTmpX.getString("Matr"));
                    vjnPessoaFoto.put("nome", qTmpX.getString("Nome"));
                    // vjnPessoaFoto.put("faceid", qTmpX.getString("FaceID"));                    
                    if (qTmpX.getString("Foto1").equals("") || qTmpX.getString("Foto2").equals("")) {
                        vjnPessoaFoto.put("nfoto1", "NAO REGISTRADO");
                        vjnPessoaFoto.put("nfoto2", "NAO REGISTRADO");
                        vjnPessoaFoto.put("nfoto3", "NAO REGISTRADO");
                        vjnPessoaFoto.put("faceid", "NAO REGISTRADO");
                        vjnPessoaFoto.put("imgfoto", "NAO REGISTRADO");
                    } else {
                        vjnPessoaFoto.put("nfoto1", qTmpX.getString("Foto1"));

                        vjnPessoaFoto.put("nfoto2", qTmpX.getString("Foto2"));
                        vjnPessoaFoto.put("nfoto3", qTmpX.getString("Foto3"));
                        vjnPessoaFoto.put("faceid", vPathWeb + qTmpX.getString("Foto1"));
                        vjnPessoaFoto.put("imgfoto", vPathLocal + qTmpX.getString("Foto1"));
                    }
                    vjnPessoaFoto.put("codigo", qTmpX.getString("Codigo"));
                    vjnPessoaFoto.put("pathlocal", vPathLocal);
                    vjnPessoaFoto.put("pathweb", vPathWeb);
                    vjnRetorno.put("funcion", vjnPessoaFoto);
                    qTmpX.Close();

                    //                  pstServJO.put("funcion", funcionJA);
                    vjnRetorno.put("funcion", vjnPessoaFoto);
                }
                if (vConta == 0) {
                    vjnRetorno.put("matr", "0");
                    vjnRetorno.put("nome", "NAO LOCALIZADO");
                    vjnPessoaFoto.put("nfoto1", "NAO REGISTRADO");
                    vjnPessoaFoto.put("nfoto2", "NAO REGISTRADO");
                    vjnPessoaFoto.put("nfoto3", "NAO REGISTRADO");
                    vjnRetorno.put("codigo", "0");
                    vjnRetorno.put("funcion", vjnPessoaFoto);
                }
                qTmpX.Close();

            } catch (Exception e) {
                throw new Exception("FuncionPessoa.obterFoto - " + e.getMessage() + "\r\n"
                        + vSQL);

            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gravaFotoPessoa")
    public Response gravaFotoPessoa(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\loggravaFotoPessoa.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vMatr = (String) parametros.getOrDefault("matr", null);
        String vToken = (String) parametros.getOrDefault("token", null);
        String vNFoto = (String) parametros.getOrDefault("nfoto", null);
        String vFotoCap = (((String) parametros.getOrDefault("fotocap", null)).replace(" ", "+")).replace("data:image/jpeg;base64,", "");
        String vChaveBio = (String) parametros.getOrDefault("chavebio", null);

        logexecucao.Grava("parametros: Matr=" + vMatr + ";Token=" + vToken + ";FotoCap=" + vFotoCap + ";NFoto=" + vNFoto + ";ChaveBio=" + vChaveBio, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        try {
            //Validando se a matrícula informada não está vazia
            if (vMatr == null || vMatr.equals("")) {
                throw new StatusClientePstServException("Cadastro Inexistente.");
            }

            if (vChaveBio == null || vChaveBio.equals("")) {
                vChaveBio = "";
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vPathWeb = "", vPathLocal = "";

            vPathWeb = "'https://mobile.sasw.com.br:9091/satellite/fotos/" + qToken.getBancoDados() + "/_F_ID/";
            vPathLocal = "C:\\xampp\\htdocs\\satellite\\fotos\\" + qToken.getBancoDados() + "\\" + "\\_F_ID\\";

            Consulta qTmpX;

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + qToken.getBancoDados() + "\\.txt";
            }

            vMatr.replace(".0", "");

            if (vMatr.length() == 1) {
                vMatr = "0000000" + vMatr;
            } else if (vMatr.length() == 2) {
                vMatr = "000000" + vMatr;
            } else if (vMatr.length() == 3) {
                vMatr = "00000" + vMatr;
            } else if (vMatr.length() == 4) {
                vMatr = "0000" + vMatr;
            } else if (vMatr.length() == 5) {
                vMatr = "000" + vMatr;
            } else if (vMatr.length() == 6) {
                vMatr = "00" + vMatr;
            } else if (vMatr.length() == 7) {
                vMatr = "0" + vMatr;
            }
            //Busca no Banco se existe a Pessoa Se nao exisitir insere, existindo Update.
            vSQL = " Select Pessoa.Codigo, Pessoa.Nome, Pessoa.Matr,  \n"
                    + " PessoaDet.FaceID, PessoaDet.Foto1, PessoaDet.Foto2, PessoaDet.Foto3, \n"
                    + " PessoaDet.Codigo CodPessoaDet "
                    + "from Pessoa \n"
                    + " Left Join FaceAutentica on  FaceAutentica.CodPessoa = Pessoa.Codigo \n"
                    + "                         and FaceAutentica.Codigo = '" + vChaveBio + "' \n"
                    + " Left Join PessoaDet on PessoaDet.Codigo = Pessoa.Codigo \n"
                    + " where Pessoa.matr = " + vMatr + " \n";
            try {
                qTmpX = new Consulta(vSQL, dbpadrao);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                throw new Exception("FuncionPessoa.obterFotoPw - " + e.getMessage() + "\r\n"
                        + vSQL);
            }
            JSONObject vjnPessoaFoto = new JSONObject();
            int vConta = 0;
            //if (qTmpX.getString("Matr") != "") {
            //.Proximo();
            //qTmpX.
            String vDirImagem = "C:\\xampp\\htdocs\\satellite\\fotos\\" + qToken.getBancoDados() + "\\_F_ID\\";
            String vDirImagemCap = "C:\\xampp\\htdocs\\rec2\\image\\";
            Consulta SQLPdr;

            File diretorio = new File(vDirImagem);
            if (!diretorio.exists()) {
                diretorio.mkdirs();  // cria diretórios caso não estejam criados
            }

            String vPathImgFoto1 = "F" + vMatr + "_F" + vNFoto + ".jpg";

            while (qTmpX.Proximo()) {
                vConta++;
                if (!qTmpX.getString("Matr").equals(null)) {
                    vjnPessoaFoto.put("matr", qTmpX.getString("Matr"));
                    vjnPessoaFoto.put("nome", qTmpX.getString("Nome"));
                    // vjnPessoaFoto.put("faceid", qTmpX.getString("FaceID"));                    
                    String vCodPessoaDet = qTmpX.getString("CodPessoaDet");
                    if (vNFoto.equals("1") || vNFoto.equals("2")) {
                        if (qTmpX.getString("CodPessoaDet") == null || qTmpX.getString("CodPessoaDet").equals("")) {
                            vjnPessoaFoto.put("nfoto" + vNFoto, "NAO REGISTRADO");
                            try {
                                vSQLExec = "insert into PessoaDet(Codigo, Faceid, Foto1, Operador, Dt_Alter, Hr_Alter) Values(\n"
                                        + qTmpX.getString("Codigo") + ", \n"
                                        + "''" + ", \n"
                                        + "'" + vPathImgFoto1 + "', \n"
                                        + "'CADBIO', \n"
                                        + "'" + getDataAtual("SQL") + "', \n"
                                        + "'" + getDataAtual("HORA") + "')\n";
                                SQLPdr = new Consulta(vSQLExec, dbpadrao);
                                SQLPdr.insert();
                            } catch (Exception e) {
                                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                throw new Exception("PessoDet.gravaFotoPessoa - " + e.getMessage() + "\r\n"
                                        + vSQL);
                            }
                            SQLPdr.close();
                        } else {
                            vjnPessoaFoto.put(" " + vNFoto, vPathImgFoto1);
                            try {
                                vSQLExec = "Update PessoaDet Set \n"
                                        + "Foto" + vNFoto + " = '" + vPathImgFoto1 + "', \n"
                                        + "Operador = 'CADBIO', \n"
                                        + "Dt_Alter = '" + getDataAtual("SQL") + "', \n"
                                        + "Hr_Alter = '" + getDataAtual("HORA") + "' \n "
                                        + " where Codigo = " + qTmpX.getString("Codigo");
                                SQLPdr = new Consulta(vSQLExec, dbpadrao);
                                SQLPdr.update();
                            } catch (Exception e) {
                                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                throw new Exception("PessoDEt.gravaFotoPessoa - " + e.getMessage() + "\r\n"
                                        + vSQL);
                            }

                            SQLPdr.close();
                            vjnPessoaFoto.put("faceid", vPathWeb + vPathImgFoto1);
                            vjnPessoaFoto.put("imgfoto", vPathLocal + vPathImgFoto1);
                        }
                    }
                    vjnPessoaFoto.put("codigo", qTmpX.getString("Codigo"));
                    vjnPessoaFoto.put("pathlocal", vPathLocal);
                    vjnPessoaFoto.put("pathweb", vPathWeb);
                    qTmpX.Close();

                    // Gravar Imagem em Arquivo
                    //Criando Imagem Original            
                    //Criando imagem
                    byte[] vFotoB641 = new sun.misc.BASE64Decoder().decodeBuffer(vFotoCap);
                    //byte[] vFotoB642 = new sun.misc.BASE64Decoder().decodeBuffer(vFotoCad2);

                    if (!vFotoCap.equals("")) {
                        //Cria o primeiro arquivo e finaliza                             
                        FileOutputStream vFileOS1 = new FileOutputStream(vDirImagem + vPathImgFoto1);
                        vFileOS1.write(vFotoB641);
                        FileDescriptor vFileD1 = vFileOS1.getFD();
                        vFileOS1.flush();
                        vFileD1.sync();
                        vFileOS1.close();
                    }
                    File vFileImg = new File(vDirImagem + vPathImgFoto1);
                    if (vFileImg.exists()) {
                        vjnRetorno.put("matr", vMatr);
                        vjnRetorno.put("data", getDataAtual("SQL"));
                        vjnRetorno.put("fotocad", vPathImgFoto1);
                        vjnRetorno.put("fotocap", vPathImgFoto1);
                        vjnRetorno.put("nfoto", vNFoto);
                        vjnRetorno.put("resposta", "SUCESSO");
                    } else {
                        vjnRetorno.put("matr", vMatr);
                        vjnRetorno.put("data", getDataAtual("SQL"));
                        vjnRetorno.put("fotocad", "");
                        vjnRetorno.put("fotocad2", "");
                        vjnRetorno.put("resposta", "FALHA");
                    }
                } else {
                    vjnRetorno.put("matr", vMatr);
                    vjnRetorno.put("data", getDataAtual("SQL"));
                    vjnRetorno.put("fotocad", "");
                    vjnRetorno.put("fotocad2", "");
                    vjnRetorno.put("resposta", "FALHA");
                }
//Final de imagem
            }

        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/autenticarPessoa")
    public Response autenticarPessoa(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\logautenticarPessoa.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vCodPessoa = (String) parametros.getOrDefault("codpessoa", null); //Obrigatorio
        String vToken = (String) parametros.getOrDefault("token", null);
        String vPW = (String) parametros.getOrDefault("pw", null);
        logexecucao.Grava("parametros: codpessoa=" + vCodPessoa + ";Token=" + vToken + ";PW=" + vPW, caminho);
        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "", vTokenGerado = "", vCodPessoaBD = "";
        String vPathWeb = "";
        String vPathLocal = "";
        String vPathFotoPonto = "";
        String vPathFotoPontoLocal = "";

        try {
            //Validando se a matrícula informada não está vazia
            if (vCodPessoa == null || vCodPessoa.equals("")) {
                throw new StatusClientePstServException("CodPessoaInvalida");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            dbsatellite.FechaConexao();

            String vBancoDados = "";

            Consulta qTmpX;

            String vData = getDataAtual("SQL");
            String vFace = "ECAF";

            vPathWeb = "https://mobile.sasw.com.br:9091/satellite/fotos/" + vFace + "/_F_ID/";
            vPathLocal = "C:\\xampp\\htdocs\\satellite\\fotos\\" + vFace + "\\_F_ID\\";
            vPathFotoPonto = "https://mobile.sasw.com.br:9091/satellite/fotos/" + vFace
                    + "/ponto/" + vData + "/";
            vPathFotoPontoLocal = "C:\\xampp\\htdocs\\satellite\\fotos\\" + vFace + "\\ponto\\" + vData + "\\";

            try {

                vSQL = " Select Pessoa.Codigo CodPessoa, Pessoa.Nome,  \n"
                        + " PessoaDet.FaceID, PessoaDet.Foto1, PessoaDet.Foto2, PessoaDet.Foto3, \n"
                        + " Pessoa.Fone1, Pessoa.Email, PessoaLogin.BancoDados, PessoaLogin.CodPessoaBD, \n"
                        + " Paramet.Nome_Empr, Paramet.Filial_Pdr \n"
                        + "from Pessoa \n"
                        + " Left Join PessoaDet on PessoaDet.Codigo = Pessoa.Codigo \n"
                        + "                     and PessoaDet.Ordem = (Select Max(Ordem) From PessoaDet PD where PD.Codigo = Pessoa.Codigo)"
                        + " Left Join PessoaLogin on PessoaLogin.Codigo = Pessoa.Codigo "
                        + " Left Join Paramet on Paramet.Path = PessoaLogin.BancoDados "
                        + " where Pessoa.Codigo = " + vCodPessoa + "\n"
                        + "   and Pessoa.PWWeb = '" + vPW + "' \n"
                        + " Order By Paramet.Path, Paramet.Filial_Pdr \n";
                try {
                    qTmpX = new Consulta(vSQL, dbsatellite);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("pessoaAutentica.obterFotoPw - " + e.getMessage() + "\r\n"
                            + vSQL);
                }

                JSONObject vjnEnvio;
                JSONArray vjnArray = new JSONArray();
                JSONObject vjnEnvioP;
                JSONArray vjnArrayP = new JSONArray();
                int vConta = 0;
                String vBDx = "";
                String vPath = "";
                while (qTmpX.Proximo()) {
                    vConta = ++vConta;
                    if (!qTmpX.getString("BancoDados").equals(vPath)) {
                        vPath = qTmpX.getString("BancoDados");

                        vBDx = qTmpX.getString("BancoDados");
                        vCodPessoa = qTmpX.getString("CodPessoa");
                        vCodPessoaBD = qTmpX.getString("CodPessoaBD");

                        if ((!qTmpX.getString("CodPessoa").equals(null))
                                && (vConta == 1)) {
                            try {
                                vjnEnvio = new JSONObject();
                                vjnEnvio.put("codpessoa", vCodPessoa);
                                vjnEnvio.put("nome", qTmpX.getString("Nome"));
                                vjnEnvio.put("fone", qTmpX.getString("Fone1"));
                                vjnEnvio.put("email", qTmpX.getString("Email"));

                                if (qTmpX.getString("Foto1").equals("") || qTmpX.getString("Foto2").equals("")) {
                                    vjnEnvio.put("nfoto1", "NAO REGISTRADO");
                                    vjnEnvio.put("nfoto2", "NAO REGISTRADO");
                                    vjnEnvio.put("nfoto3", "NAO REGISTRADO");
                                    vjnEnvio.put("faceid", "NAO REGISTRADO");
                                    vjnEnvio.put("imgfoto", "NAO REGISTRADO");
                                } else {
                                    vjnEnvio.put("nfoto1", qTmpX.getString("Foto1"));

                                    vjnEnvio.put("nfoto2", qTmpX.getString("Foto2"));
                                    vjnEnvio.put("nfoto3", qTmpX.getString("Foto3"));
                                    vjnEnvio.put("faceid", vPathWeb + qTmpX.getString("Foto1"));
                                    vjnEnvio.put("imgfoto", vPathLocal + qTmpX.getString("Foto1"));
                                }
                                vjnEnvio.put("pathlocal", vPathLocal);
                                vjnEnvio.put("pathweb", vPathWeb);
                                vjnEnvio.put("bancodados", vBDx);
                                vjnEnvio.put("nome_empr", qTmpX.getString("Nome_Empr"));
                                vjnEnvio.put("filial_pdr", qTmpX.getString("Filial_Pdr"));
                                vjnEnvio.put("codpessoabd", vCodPessoaBD);
                                vjnArray.put(vjnEnvio);

                            } catch (Exception e) {
                                logexecucao.Grava("Erro geração Retorno Json", caminho);
                                throw new Exception("FuncionPessoa.obterFotoPw - " + e.getMessage() + "\r\n");
                            }
                        } else if (vConta > 1) {
                            vjnEnvio = new JSONObject();
                            vjnEnvio.put("bancodados", vBDx);
                            vjnEnvio.put("codpessoabd", vCodPessoaBD);
                            vjnEnvio.put("pathlocal", vPathLocal);
                            vjnEnvio.put("pathweb", vPathWeb);
                            vjnEnvio.put("bancodados", vBDx);
                            vjnEnvio.put("nome_empr", qTmpX.getString("Nome_Empr"));
                            vjnEnvio.put("filial_pdr", qTmpX.getString("Filial_Pdr"));
                            vjnEnvio.put("codpessoabd", vCodPessoaBD);
                            vjnArray.put(vjnEnvio);
                        }
                        vjnRetorno.put("login", vjnArray);
                    }
                }
                if (vConta == 1) {
                    //Gerar token no Banco Central - Somente 1 PessoaLogin
                    TOKENSDao tokensDao = new TOKENSDao();
                    TOKENS vGerToken = new TOKENS();
                    vGerToken.setBancoDados(vBDx);
                    vGerToken.setModulo("SATMOB");
                    vGerToken.setChave("WS");
                    vGerToken.setData(getDataAtual("SQL"));
                    vGerToken.setHora(getDataAtual("HORA"));
                    vTokenGerado = tokensDao.gerarToken(vGerToken, dbsatellite);
                    TOKENS qToken = this.tokenDao.obterToken(vTokenGerado, dbsatellite);
                    dbpadrao = this.pool.getConexao(qToken.getBancoDados());
                    vSQL = "Select Pessoa.Matr, Funcion.Cargo, Funcion.Secao, Cargos.Descricao CargoDesc, \n"
                            + " PstServ.Secao, PstServ.Local, Filiais.CodFil, Filiais.Descricao FilialDesc \n"
                            + " from Pessoa "
                            + " Left Join Funcion on Funcion.Matr = Pessoa.Matr \n"
                            + " Left Join PstServ on PstServ.Secao =  Funcion.Secao \n"
                            + "                  and PstServ.CodFil = Funcion.CodFil \n"
                            + " Left Join Cargos on Cargos.Cargo = Funcion.Cargo "
                            + " Left Join Filiais on Filiais.Codfil = Funcion.CodFil \n"
                            + " where Pessoa.Codigo = " + vCodPessoaBD;
                    Consulta qTmpX2 = new Consulta(vSQL, dbpadrao);
                    qTmpX2.select();
                    vjnEnvioP = new JSONObject();
                    while (qTmpX2.Proximo()) {
                        if (!qTmpX2.getString("Matr").equals(null)) {
                            vjnEnvioP.put("token", vTokenGerado);
                            vjnEnvioP.put("matr", qTmpX2.getString("Matr"));
                            vjnEnvioP.put("codfil", qTmpX2.getString("Codfil"));
                            vjnEnvioP.put("filialdesc", qTmpX2.getString("FilialDesc"));
                            vjnEnvioP.put("cargo", qTmpX2.getString("Cargo"));
                            vjnEnvioP.put("cargodesc", qTmpX2.getString("CargoDesc"));
                            vjnEnvioP.put("secao", qTmpX2.getString("Secao"));
                            vjnEnvioP.put("posto", qTmpX2.getString("Local"));
                        } else {
                            vjnEnvioP.put("matr", "0");
                        }
                        vjnArrayP.put(vjnEnvioP);
                    }
                    qTmpX2.Close();
                    dbpadrao.FechaConexao();
                }
                qTmpX.Close();
                if (vConta == 0) {
                    vjnEnvio = new JSONObject();
                    vjnEnvio.put("codpessoa", "0");
                    vjnEnvio.put("nome", "USUARIO OU SENHA INCORRETA");
                    vjnEnvio.put("nfoto1", "NAO REGISTRADO");
                    vjnEnvio.put("nfoto2", "NAO REGISTRADO");
                    vjnEnvio.put("nfoto3", "NAO REGISTRADO");
                    vjnEnvio.put("codigo", "0");
                    vjnArray.put(vjnEnvio);
                    vjnRetorno.put("login", vjnArray);
                }
            } catch (Exception e) {
                logexecucao.Grava("Erro try principal " + e.getMessage(), caminho);
                throw new Exception("AutenticarPessoa - " + e.getMessage() + "\r\n"
                        + vSQL);

            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/validaSenha")
    public Response validaSenha(String param) throws UnsupportedEncodingException, IOException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vCodPessoa = (String) parametros.getOrDefault("codpessoa", null);
        String vMatr = (String) parametros.getOrDefault("matr", null);
        String vPW = (String) parametros.getOrDefault("senha", null);
        String vToken = (String) parametros.getOrDefault("token", null);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "", vNome = "", vBancodeDados = "", vEmpresa = "", vTokenGerado = "", vMensagem = "";
        int vQtdeReg = 0, vConta = 0;
        boolean vProcessa = false;

        try {
            //Validando se a matrícula informada não está vazia
            if (vMatr == null && vMatr.equals("") && vCodPessoa == null && vCodPessoa.equals("")) {
                throw new StatusClientePstServException("FuncionarioPessoainvalidos");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            dbpadrao = this.pool.getConexao("");

            if ((vCodPessoa.equals("0") || vCodPessoa.equals("")) && !vToken.equals("")) {
                TOKENS qTmpToken = this.tokenDao.obterToken(vToken, dbsatellite);
                ValidarToken.validarExistencia(qTmpToken);
                ValidarToken.validarValidade(qTmpToken);
                dbpadrao = this.pool.getConexao(qTmpToken.getBancoDados());
                vProcessa = true;
                if (dbpadrao != null) { // Atualiza o caminho do log
                    this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                            + getDataAtual("SQL") + "\\" + qTmpToken.getBancoDados() + "\\.txt";
                }
            } else if (!vCodPessoa.equals("0") && !vCodPessoa.equals("")) {
                if (dbsatellite != null) { // Atualiza o caminho do log
                    this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                            + getDataAtual("SQL") + "\\" + "SATELLITE" + "\\.txt";
                }
            }

            vMatr.replace(".0", "");
            if (vMatr.length() == 1) {
                vMatr = "0000000" + vMatr;
            } else if (vMatr.length() == 2) {
                vMatr = "000000" + vMatr;
            } else if (vMatr.length() == 3) {
                vMatr = "00000" + vMatr;
            } else if (vMatr.length() == 4) {
                vMatr = "0000" + vMatr;
            } else if (vMatr.length() == 5) {
                vMatr = "000" + vMatr;
            } else if (vMatr.length() == 6) {
                vMatr = "00" + vMatr;
            } else if (vMatr.length() == 7) {
                vMatr = "0" + vMatr;
            };
            Consulta qTmpX;
            qTmpX = new Consulta("", dbsatellite);
            try {
                if (!vCodPessoa.equals("0") && !vCodPessoa.equals("")) {
                    vSQL = " Select Pessoa.Codigo, Pessoa.Nome, Pessoa.Matr, Pessoa.PWWeb,  \n"
                            + " PessoaLogin.BancoDados, PessoaLogin.CodPessoaBD, \n "
                            + " (Select Count(*)  from PessoaLogin PL where PL.Codigo = PessoaLogin.Codigo) QtdeEmpresas, "
                            + " (Select top 1 Nome_empr from Paramet where Path = PessoaLogin.BancoDados) Nome_Empr "
                            + "from Pessoa \n"
                            + " Left Join PessoaLogin on PessoaLogin.Codigo = Pessoa.Codigo \n"
                            + " where Pessoa.Codigo = " + vCodPessoa + "\n";
                    qTmpX = new Consulta(vSQL, dbsatellite);
                    qTmpX.select();
                    vProcessa = true;
                } else {
                    vSQL = " Select Pessoa.Codigo, Pessoa.Nome, Pessoa.Matr, Pessoa.PWWeb,  \n"
                            + " PessoaLogin.BancoDados, PessoaLogin.CodPessoaBD, \n "
                            + " (Select Count(*)  from PessoaLogin PL where PL.Codigo = PessoaLogin.Codigo) QtdeEmpresas, "
                            + " (Select top 1 Nome_empr from Paramet where Path = PessoaLogin.BancoDados) Nome_Empr "
                            + "from Pessoa \n"
                            + " Left Join PessoaLogin on PessoaLogin.Codigo = Pessoa.Codigo \n"
                            + " where Pessoa.Matr = " + vMatr + "\n";
                    if (dbpadrao != null) {
                        qTmpX = new Consulta(vSQL, dbpadrao);
                        qTmpX.select();
                    } else {
                        vProcessa = false;
                    }
                }
                if (vProcessa) {
                    JSONObject vjnValidaSenha;
                    vjnValidaSenha = new JSONObject();
                    while (qTmpX.Proximo()) {
                        if (vConta == 0) {
                            vQtdeReg = qTmpX.getInt("QtdeEmpresas");

                            if (qTmpX.getString("PWWeb").equals(vPW)) {
                                vNome = qTmpX.getString("Nome");
                                vBancodeDados = qTmpX.getString("BancoDados");
                                vEmpresa = qTmpX.getString("Nome_Empr");
                                if (vQtdeReg == 1) {
                                    if (!vCodPessoa.equals("0") && !vCodPessoa.equals("")) {
                                        //Gerar TOKEN Validade 1 Hora
                                        TOKENSDao tokensDao = new TOKENSDao();
                                        TOKENS vGerToken = new TOKENS();
                                        vGerToken.setBancoDados(vBancodeDados);
                                        vGerToken.setModulo("SATMOB");
                                        vGerToken.setChave("WS");
                                        vGerToken.setData(getDataAtual("SQL"));
                                        vGerToken.setHora(getDataAtual("HORA"));
                                        vTokenGerado = tokensDao.gerarToken(vGerToken, dbsatellite);

                                        vjnValidaSenha.put("nome", vNome);
                                        vjnValidaSenha.put("bancodedados", "");
                                        vjnValidaSenha.put("empresa", "");
                                        vjnValidaSenha.put("token", vTokenGerado);
                                        vjnValidaSenha.put("mensagem", "Acesso Autorizado");
                                    } else {
                                        vjnValidaSenha.put("nome", vNome);
                                        vjnValidaSenha.put("bancodedados", "");
                                        vjnValidaSenha.put("empresa", "");
                                        vjnValidaSenha.put("token", "");
                                        vjnValidaSenha.put("mensagem", "Acesso Autorizado");
                                    }
                                } else if (vQtdeReg > 1) {
                                    vjnValidaSenha.put("nome", vNome);
                                    vjnValidaSenha.put("bancodedados", "");
                                    vjnValidaSenha.put("empresa", vEmpresa);
                                    vjnValidaSenha.put("token", "");
                                    vjnValidaSenha.put("mensagem", "Aguardando Seleção de Empresa");
                                } else if (!vMatr.equals("")) {
                                    vjnValidaSenha.put("nome", vNome);
                                    vjnValidaSenha.put("bancodedados", "");
                                    vjnValidaSenha.put("empresa", "");
                                    vjnValidaSenha.put("token", "");
                                    vjnValidaSenha.put("mensagem", "Acesso Autorizado");
                                } else {
                                    vjnValidaSenha.put("nome", "");
                                    vjnValidaSenha.put("bancodedados", "");
                                    vjnValidaSenha.put("empresa", "");
                                    vjnValidaSenha.put("token", "");
                                    vjnValidaSenha.put("mensagem", "Pessoa/Senha incorreta");
                                }
                            } else {
                                vjnValidaSenha.put("nome", "");
                                vjnValidaSenha.put("bancodedados", "");
                                vjnValidaSenha.put("empresa", "");
                                vjnValidaSenha.put("token", "");
                                if (!vCodPessoa.equals("0") && !vCodPessoa.equals("")) {
                                    vjnValidaSenha.put("mensagem", "Pessoa/Senha incorreta");
                                } else {
                                    vjnValidaSenha.put("mensagem", "Funcionário/Senha incorreta");
                                }
                            }
                        }
                        vConta = ++vConta;
                    }
                    vjnRetorno.put("validapessoa", vjnValidaSenha);
                    qTmpX.Close();
                }
                if (vConta == 0) {
                    JSONObject vjnValidaSenha;
                    vjnValidaSenha = new JSONObject();
                    vjnValidaSenha.put("nome", "");
                    vjnValidaSenha.put("bancodedados", "");
                    vjnValidaSenha.put("empresa", "");
                    vjnValidaSenha.put("token", "");
                    if (!vCodPessoa.equals("0") && !vCodPessoa.equals("")) {
                        vjnValidaSenha.put("mensagem", "Pessoa/Senha incorreta");
                    } else {
                        if (!vProcessa) {
                            vjnValidaSenha.put("mensagem", "Token inválido");
                        } else {
                            vjnValidaSenha.put("mensagem", "Funcionário/Senha incorreta");
                        }
                    }
                    vjnRetorno.put("validapessoa", vjnValidaSenha);
                }

                dbsatellite.FechaConexao();
            } catch (Exception e) {
                throw new Exception("Pessoa.ValidaSenha - " + e.getMessage() + "\r\n"
                        + vSQL);

            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/faceAutentica")
    public Response faceAutentica(String param) throws UnsupportedEncodingException, IOException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vFotoBase = (((String) parametros.getOrDefault("fotobase", null)).replace(" ", "+")).replace("data:image/jpeg;base64,", "");
        String vFotoN1 = (((String) parametros.getOrDefault("foton1", null)).replace(" ", "+")).replace("data:image/jpeg;base64,", "");
        String vFotoS1 = (((String) parametros.getOrDefault("fotos1", null)).replace(" ", "+")).replace("data:image/jpeg;base64,", "");

        String vCodPessoa = (String) parametros.getOrDefault("codpessoa", null);
        String vBancoDados = (String) parametros.getOrDefault("bancodados", null);
        String vMatr = (String) parametros.getOrDefault("matr", null);
        String vNota1 = (String) parametros.getOrDefault("nota1", null);
        String vNota2 = (String) parametros.getOrDefault("nota2", null);
        String vNota3 = (String) parametros.getOrDefault("nota3", null);
        String vNota4 = (String) parametros.getOrDefault("nota4", null);
        String vCadBio = (String) parametros.getOrDefault("cadbio", null);
        String vToken = (String) parametros.getOrDefault("token", null);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnPessoaValida = new JSONObject();

        try {
            //Validando se a matrícula informada não está vazia
            if (vMatr == null || vMatr.equals("")) {
                throw new StatusClientePstServException("Cadastro Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vSQLCadBio = "", vPathWeb = "", vPathLocal = "",
                    vNomeFotoBase = "", vNomeFotoN1 = "", vNomeFotoS1 = "";
            vBancoDados = qToken.getBancoDados();

            //Trata Notas NULL
            if (vNota1.equals(null) || vNota1.equals("")) {
                vNota1 = "0";
            }
            if (vNota2.equals(null) || vNota2.equals("")) {
                vNota2 = "0";
            }
            if (vNota3.equals(null) || vNota3.equals("")) {
                vNota3 = "0";
            }
            if (vNota4.equals(null) || vNota4.equals("")) {
                vNota4 = "0";
            }

            vPathWeb = "'https://mobile.sasw.com.br:9091/satellite/fotos/" + vBancoDados + "/_F_ID/";
            vPathLocal = "C:\\xampp\\htdocs\\satellite\\fotos\\" + vBancoDados + "\\" + "\\_F_ID\\";

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }

            vMatr.replace(".0", "");

            if (vMatr.length() == 1) {
                vMatr = "0000000" + vMatr;
            } else if (vMatr.length() == 2) {
                vMatr = "000000" + vMatr;
            } else if (vMatr.length() == 3) {
                vMatr = "00000" + vMatr;
            } else if (vMatr.length() == 4) {
                vMatr = "0000" + vMatr;
            } else if (vMatr.length() == 5) {
                vMatr = "000" + vMatr;
            } else if (vMatr.length() == 6) {
                vMatr = "00" + vMatr;
            } else if (vMatr.length() == 7) {
                vMatr = "0" + vMatr;
            }
            //Busca no Banco se existe a Pessoa Se nao existir insere
            vSQL = " Select Pessoa.Codigo, Pessoa.Nome, Pessoa.Matr,  \n"
                    + " PessoaDet.FaceID, PessoaDet.Foto1, PessoaDet.Foto2, PessoaDet.Foto3, \n"
                    + " PessoaDet.Codigo CodPessoaDet, \n"
                    + " FaceAutentica.FotoBase, FaceAutentica.Codigo CadBio \n"
                    + "from Pessoa \n"
                    + " Left Join PessoaDet on PessoaDet.Codigo = Pessoa.Codigo \n"
                    + " Left Join FaceAutentica on FaceAutentica.CodPessoa = Pessoa.Codigo \n";
            if (!vMatr.equals(null) && !vMatr.equals("")) {
                vSQL += " where Pessoa.matr = " + vMatr + " \n";
            } else if (!vCodPessoa.equals(null) && !vCodPessoa.equals("")) {
                vSQL += " where Pessoa.Codigo = " + vCodPessoa + "\n";
            } else if (vCodPessoa.equals(null)) {
                vCodPessoa = "0";
            }

            Consulta qTmpX = new Consulta(vSQL, dbpadrao);
            qTmpX.select();

            int vConta = 0;

            String vDirImagem = "C:\\xampp\\htdocs\\satellite\\fotos\\" + qToken.getBancoDados() + "\\_F_ID\\";
            String vDirImagemCap = "C:\\xampp\\htdocs\\satellite\\fotos\\" + qToken.getBancoDados() + "\\_F_ID\\";

            File diretorio = new File(vDirImagem);
            if (!diretorio.exists()) {
                diretorio.mkdirs();  // cria diretórios caso não estejam criados
            }

            vNomeFotoBase = "F" + vMatr + "_FBase1.jpg";
            vNomeFotoN1 = "F" + vMatr + "_FN1.jpg";
            vNomeFotoS1 = "F" + vMatr + "_FS1.jpg";

            while (qTmpX.Proximo()) {
                vConta++;
                if (!qTmpX.getString("Matr").equals(null)) {
                    vjnPessoaValida.put("matr", qTmpX.getString("Matr"));
                    vjnPessoaValida.put("nome", qTmpX.getString("Nome"));
                    String vCodPessoaDet = qTmpX.getString("CodPessoaDet");
                    vSQLCadBio = "DECLARE @Codigo Varchar(32)\n"
                            + "DECLARE @CodigoAnterior Varchar(32)\n"
                            + "DECLARE @Bloco1Int INT\n"
                            + "DECLARE @Bloco2Int INT\n"
                            + "DECLARE @Bloco3Int INT\n"
                            + "DECLARE @Bloco1 Varchar(6)\n"
                            + "DECLARE @Bloco2 Varchar(6)\n"
                            + "DECLARE @Bloco3 Varchar(6)\n"
                            + "\n"
                            + "SET @Bloco1Int = FLOOR(RAND()*(16777216)) -- FFFFFF\n"
                            + "SET @Bloco2Int = FLOOR(RAND()*(16777216))\n"
                            + "SET @Bloco3Int = FLOOR(RAND()*(16777216))\n"
                            + "\n"
                            + "SET @Bloco1 = REPLICATE('0',6-LEN(RTRIM(FORMAT(@Bloco1Int,'X')))) + RTRIM(FORMAT(@Bloco1Int,'X'))\n"
                            + "SET @Bloco2 = REPLICATE('0',6-LEN(RTRIM(FORMAT(@Bloco2Int,'X')))) + RTRIM(FORMAT(@Bloco2Int,'X'))\n"
                            + "SET @Bloco3 = REPLICATE('0',6-LEN(RTRIM(FORMAT(@Bloco3Int,'X')))) + RTRIM(FORMAT(@Bloco3Int,'X'))\n"
                            + "\n" //SET NOCOUNT ON
                            + "\n"
                            + "SET NOCOUNT ON\n"
                            + "\n"
                            + "SET @CodigoAnterior = (SELECT TOP 1 FORMAT(CONVERT(INT, CONVERT(VARBINARY, '0' + SUBSTRING(ISNULL(Codigo, 'B4FFFF2'), 1 ,7), 2)) + 14,'X') FROM TOKENS ORDER BY Codigo DESC)\n"
                            + "SET @Codigo = CONCAT(@CodigoAnterior,\n"
                            + "    @Bloco1, @Bloco2, @Bloco3,\n"
                            + "    REVERSE(@CodigoAnterior)) \n"
                            + " SELECT @Codigo Token \n";
                    Consulta qTmpX2 = new Consulta(vSQLCadBio, dbpadrao);
                    qTmpX2.select();
                    if (qTmpX2.Proximo()) {
                        vCadBio = qTmpX2.getString("Token");
                    }
                    qTmpX2.Close();

                    vSQLExec = "Insert into FaceAutentica(Sequencia, Codigo, FotoBase, FotoN1, FotoS1, \n"
                            + " CodPessoa, BancoDados, Matr, Nota1, Nota2, Nota3, Nota4, \n"
                            + "Operador, Dt_Alter, Hr_Alter) \n"
                            + "Values((Select isnull(Max(sequencia),0)+1 from FaceAutentica), "
                            + "'" + vCadBio + "', \n"
                            + "'" + vFotoBase + "', "
                            + "'" + vFotoN1 + "', "
                            + "'" + vFotoS1 + "', "
                            + qTmpX.getString("Codigo") + ", \n"
                            + "'" + vBancoDados + "', \n"
                            + vMatr + ", \n"
                            + vNota1 + ", \n"
                            + vNota2 + ", \n"
                            + vNota3 + ", \n"
                            + vNota4 + ", \n"
                            + "'SATMOB', \n"
                            + "'" + getDataAtual("SQL") + "', \n"
                            + "'" + getDataAtual("HORA") + "')\n";
                    Consulta sQLPdr = new Consulta(vSQLExec, dbpadrao);
                    vjnPessoaValida.put("cadbio", vCadBio);
                    vjnRetorno.put("resposta", vjnPessoaValida);
                    sQLPdr.insert();
                    sQLPdr.close();
                    vjnPessoaValida.put("codigo", qTmpX.getString("Codigo"));
                    vjnPessoaValida.put("pathlocal", vPathLocal);
                    vjnPessoaValida.put("pathweb", vPathWeb);
                    qTmpX.Close();

                    // Gravar Imagem em Arquivo
                    //Criando Imagem Original            
                    //Criando imagem
                    byte[] vFotoB641 = new sun.misc.BASE64Decoder().decodeBuffer(vFotoBase);
                    byte[] vFotoB642 = new sun.misc.BASE64Decoder().decodeBuffer(vFotoN1);
                    byte[] vFotoB643 = new sun.misc.BASE64Decoder().decodeBuffer(vFotoS1);

                    if (!vFotoBase.equals("")) {
                        //Cria o primeiro arquivo e finaliza                             
                        String vArqFotoCap = vDirImagem + vNomeFotoBase;
                        Files.deleteIfExists(Paths.get(vArqFotoCap));

                        FileOutputStream vFileOS1 = new FileOutputStream(vDirImagem + vNomeFotoBase);
                        vFileOS1.write(vFotoB641);
                        FileDescriptor vFileD1 = vFileOS1.getFD();
                        vFileOS1.flush();
                        vFileD1.sync();
                        vFileOS1.close();
                        vjnRetorno.put("resposta", vjnPessoaValida);
                    }
                    if (!vFotoN1.equals("")) {
                        //Cria o primeiro arquivo e finaliza                             
                        String vArqFotoCap2 = vDirImagem + vNomeFotoBase;
                        Files.deleteIfExists(Paths.get(vArqFotoCap2));

                        FileOutputStream vFileOS2 = new FileOutputStream(vDirImagem + vNomeFotoN1);
                        vFileOS2.write(vFotoB642);
                        FileDescriptor vFileD2 = vFileOS2.getFD();
                        vFileOS2.flush();
                        vFileD2.sync();
                        vFileOS2.close();
                    }
                    if (!vFotoS1.equals("")) {
                        //Cria o primeiro arquivo e finaliza                             
                        String vArqFotoCap3 = vDirImagem + vNomeFotoS1;
                        Files.deleteIfExists(Paths.get(vArqFotoCap3));

                        FileOutputStream vFileOS3 = new FileOutputStream(vDirImagem + vNomeFotoS1);
                        vFileOS3.write(vFotoB641);
                        FileDescriptor vFileD3 = vFileOS3.getFD();
                        vFileOS3.flush();
                        vFileD3.sync();
                        vFileOS3.close();
                        vjnRetorno.put("resposta", vjnPessoaValida);
                    }
                    if (vFotoBase.equals("")) {
                        vjnPessoaValida.put("mensagem", "FALHA NO CADASTRAMENTO");
                        vjnRetorno.put("resposta", vjnPessoaValida);
                    }
                } else {
                    vjnPessoaValida.put("matr", "");
                    vjnPessoaValida.put("data", getDataAtual("SQL"));
                    vjnRetorno.put("resposta", vjnPessoaValida);
                    vjnRetorno.put("resposta", vjnPessoaValida);
                }
//Final de imagem
                vjnRetorno.put("resposta", vjnPessoaValida);
            }
            vjnRetorno.put("resposta", vjnPessoaValida);
            dbsatellite.FechaConexao();
        } finally {
            //vjnRetorno.put("resposta", vjnPessoaFoto);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/faceAutenticaAudit")
    public Response faceAutenticaAudit(String param) throws UnsupportedEncodingException, IOException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vBancoDados = (String) parametros.getOrDefault("bancodados", null);
        String vQtdeReg = (String) parametros.getOrDefault("qtdereg", null);
        String vSeqMin = (String) parametros.getOrDefault("seqmin", null);
        String vSeqMax = (String) parametros.getOrDefault("seqmax", null);

        String vFotoBase = "", vFotoN1 = "", vFotoS1 = "", vCodPessoa = "", vMatr = "", vNota1 = "",
                vNota2 = "", vNota3 = "", vNota4 = "", vToken = "";
        int vConta = 0;

        if (vToken.equals("")) {
            vToken = "B8489FF7205E3217B4E043D70FF9848B";
        }

        Persistencia dbpadrao, dbsatellite;

        if (vSeqMin.equals("") || vSeqMin.equals(null)) {
            vSeqMin = "0";
        }

        if (vSeqMax.equals("") || vSeqMax.equals(null)) {
            vSeqMax = "99999";
        }

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnPessoaAudit = new JSONObject();
        JSONArray vjnPessoaAuditDet = new JSONArray();
        try {
            //Validando se a matrícula informada não está vazia
            if (vBancoDados == null || vBancoDados.equals("")) {
                throw new StatusClientePstServException("Sem banco de dados.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vSQLCadBio = "", vPathWeb = "", vPathLocal = "",
                    vNomeFotoBase = "", vNomeFotoN1 = "", vNomeFotoS1 = "", vFaceID = "";
            vBancoDados = qToken.getBancoDados();

            vPathWeb = "https://mobile.sasw.com.br:9091/satellite/fotos/" + qToken.getBancoDados() + "/_F_ID/";
            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }

            vSQL = " Select top " + vQtdeReg + " Pessoa.Codigo, Pessoa.Nome, Pessoa.Matr, PessoaDet.Foto1, \n"
                    + " FaceAutentica.Sequencia, FaceAutentica.FotoBase, FaceAutentica.FotoN1,FaceAutentica.FotoS1,  \n"
                    + " FaceAutentica.BancoDados, FaceAutentica.CodPessoa, FaceAutentica.Nota1, \n"
                    + " FaceAutentica.Nota2, FaceAutentica.Nota3, FaceAutentica.Nota4, FaceAutentica.Operador, \n"
                    + " convert(varchar, FaceAutentica.Dt_Alter, 112) Dt_Alter, FaceAutentica.Hr_Alter "
                    + " from FaceAutentica \n"
                    + " Left Join Pessoa on Pessoa.Codigo   = FaceAutentica.CodPessoa \n"
                    + " Left Join PessoaDet on PessoaDet.Codigo = FaceAutentica.CodPessoa \n"
                    + " where FaceAutentica.BancoDados = '" + vBancoDados + "' \n"
                    + "   and FaceAutentica.Sequencia >= " + vSeqMin
                    + "   and FaceAutentica.Sequencia <= " + vSeqMax
                    + " order by FaceAutentica.Sequencia desc";

            Consulta qTmpX = new Consulta(vSQL, dbpadrao);
            qTmpX.select();
            while (qTmpX.Proximo()) {
                vMatr = qTmpX.getString("Matr");
                if (!qTmpX.getString("Foto1").equals("") && !qTmpX.getString("Foto1").equals(null)) {
                    vFaceID = vPathWeb + qTmpX.getString("Foto1");
                } else {
                    vFaceID = "";
                }
                if (vMatr.length() == 1) {
                    vMatr = "0000000" + vMatr;
                } else if (vMatr.length() == 2) {
                    vMatr = "000000" + vMatr;
                } else if (vMatr.length() == 3) {
                    vMatr = "00000" + vMatr;
                } else if (vMatr.length() == 4) {
                    vMatr = "0000" + vMatr;
                } else if (vMatr.length() == 5) {
                    vMatr = "000" + vMatr;
                } else if (vMatr.length() == 6) {
                    vMatr = "00" + vMatr;
                } else if (vMatr.length() == 7) {
                    vMatr = "0" + vMatr;
                }

                vMatr.replace(".0", "");
                vConta++;

                vjnPessoaAudit = new JSONObject();

                vjnPessoaAudit.put("fotobase", qTmpX.getString("FotoBase"));
                vjnPessoaAudit.put("foton1", qTmpX.getString("FotoN1"));
                vjnPessoaAudit.put("fotoS1", qTmpX.getString("FotoS1"));
                vjnPessoaAudit.put("faceid", vFaceID);
                vjnPessoaAudit.put("bancodados", qTmpX.getString("BancoDados"));
                vjnPessoaAudit.put("sequencia", qTmpX.getString("Sequencia"));
                vjnPessoaAudit.put("codpessoa", qTmpX.getString("CodPessoa"));
                vjnPessoaAudit.put("nome", qTmpX.getString("Nome"));
                vjnPessoaAudit.put("matr", vMatr);
                vjnPessoaAudit.put("nota1", "'" + qTmpX.getString("Nota1") + "'");
                vjnPessoaAudit.put("nota2", "'" + qTmpX.getString("Nota2") + "'");
                vjnPessoaAudit.put("nota3", "'" + qTmpX.getString("Nota3") + "'");
                vjnPessoaAudit.put("nota4", "'" + qTmpX.getString("Nota4") + "'");
                vjnPessoaAudit.put("Operador", qTmpX.getString("Operador"));
                vjnPessoaAudit.put("Dt_Alter", qTmpX.getString("Dt_Alter"));
                vjnPessoaAudit.put("Hr_Alter", qTmpX.getString("Hr_Alter"));
                vjnPessoaAudit.put("mensagem", "SUCESSO");
                vjnPessoaAuditDet.put(vjnPessoaAudit);
            }
            vjnRetorno.put("resposta", vjnPessoaAuditDet);
            dbsatellite.FechaConexao();
        } finally {
            //vjnRetorno.put("resposta", vjnPessoaFoto);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/autenticarPessoaBio")
    public Response autenticarPessoaBio(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\autenticarPessoaBio.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vFotoN1 = (String) parametros.getOrDefault("foton1", null);
        String vFotoS1 = (String) parametros.getOrDefault("fotos1", null);
        String vToken = (String) parametros.getOrDefault("token", null);
        logexecucao.Grava("parametros: Foton1=" + vFotoN1 + "FotoS1=" + vFotoS1 + ";Token=" + vToken, caminho);
        String vFotoFisicaN1 = "", vFotoFisicaS1 = "", vFotoSrvN1 = "", vFotoSrvS1 = "", vCodPessoa = "", vMatr = "", vNota1 = "",
                vNota2 = "", vNota3 = "", vNota4 = "";

        int vConta = 0;

        if (vToken.equals("")) {
            vToken = "B8489FF7205E3217B4E043D70FF9848B";
        }

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnAutenticarPessoaBio = new JSONObject();
        try {
            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vSQLCadBio = "", vPathWeb = "", vPathLocal = "",
                    vNomeFotoBase = "", vNomeFotoN1 = "", vNomeFotoS1 = "", vFaceID = "",
                    vBancoDados = "", vFotoCad = "";
            vBancoDados = qToken.getBancoDados();
            Consulta qTmpX;

            vPathWeb = "https://mobile.sasw.com.br:9091/satellite/fotos/" + qToken.getBancoDados() + "/_F_ID/";
            vPathLocal = "C:\\xampp\\htdocs\\satellite\\fotos\\";
            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }
            try {
                vSQL = " Select Pessoa.Codigo, Pessoa.Nome, Pessoa.Matr, PessoaDet.Foto1, \n"
                        + " PessoaDet.Foto2 \n"
                        + " from PessoaDet \n"
                        + " Left Join Pessoa on Pessoa.Codigo   = PessoaDet.Codigo \n"
                        + " where PessoaDet.Foto1 is not null  \n";

                qTmpX = new Consulta(vSQL, dbpadrao);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro try Consulta " + e.getMessage() + "\r\n"
                        + vSQL, caminho);
                throw new Exception("PessoaDet.autenticarPessoaBio - " + e.getMessage() + "\r\n"
                        + vSQL);

            }
            while (qTmpX.Proximo()) {
                vMatr = qTmpX.getString("Matr");
                if (vMatr.length() == 1) {
                    vMatr = "0000000" + vMatr;
                } else if (vMatr.length() == 2) {
                    vMatr = "000000" + vMatr;
                } else if (vMatr.length() == 3) {
                    vMatr = "00000" + vMatr;
                } else if (vMatr.length() == 4) {
                    vMatr = "0000" + vMatr;
                } else if (vMatr.length() == 5) {
                    vMatr = "000" + vMatr;
                } else if (vMatr.length() == 6) {
                    vMatr = "00" + vMatr;
                } else if (vMatr.length() == 7) {
                    vMatr = "0" + vMatr;
                }

                vMatr.replace(".0", "");
                vConta++;

                //vFotoSrvS = qTmpX.getString("Foto1");
                //Pegar Foto1 Fisica e Transforma em Base64
                File vImgFotoFisicaN1 = new File("C:\\xampp\\htdocs\\satellite\\fotos\\" + vBancoDados + "\\_F_ID\\" + qTmpX.getString("Foto1"));
                if (vImgFotoFisicaN1.exists()) {
                    byte[] fileContent = Files.readAllBytes(vImgFotoFisicaN1.toPath());
                    vFotoSrvN1 = new sun.misc.BASE64Encoder().encodeBuffer(fileContent);
                } else {
                    vFotoSrvN1 = "";
                }

                //Pegar Foto2 Fisica e Transforma em Base64
                File vImgFotoFisicaS1 = new File("C:\\xampp\\htdocs\\satellite\\fotos\\" + vBancoDados + "\\_F_ID\\" + qTmpX.getString("Foto2"));
                if (vImgFotoFisicaS1.exists()) {
                    byte[] fileContent2 = Files.readAllBytes(vImgFotoFisicaS1.toPath());
                    vFotoSrvS1 = new sun.misc.BASE64Encoder().encode(fileContent2);
                } else {
                    vFotoSrvS1 = "";
                }
                //Compara fotos
                //vMatr = comparaFoto(vFotoSrvN1, vFotoSrvS1, vFotoN1, vFotoS1);

                logexecucao.Grava("Parametros Antes enviar comparaFotos - "
                        + "FotoSrvN1:" + vFotoSrvN1 + ";"
                        + "FotoSrvS1:" + vFotoSrvS1 + ";"
                        + "FotoN1:" + vFotoN1 + ";"
                        + "FotoS1:" + vFotoS1 + ";", caminho);

                try {
                    comparaFoto(vFotoSrvN1, vFotoSrvS1, vFotoN1, vFotoS1);
                } catch (Exception e) {
                    logexecucao.Grava("Erro chamada comparaFoto " + e.getMessage()
                            + "RetortoJS:" + vRetorno, caminho);
                    throw new Exception("PessoaDet.autenticarPessoaBio - " + e.getMessage() + "\r\n"
                    );

                }
                vjnAutenticarPessoaBio = new JSONObject();
                if (!vMatr.equals("0")) {
                    vjnAutenticarPessoaBio.put("codpessoa", qTmpX.getString("Codigo"));
                    vjnAutenticarPessoaBio.put("nome", qTmpX.getString("Nome"));
                    vjnAutenticarPessoaBio.put("matr", vMatr);
                    vjnAutenticarPessoaBio.put("retornojs", vRetorno);
                    break;
                }
                //Fim Compara foto
            }
            if (vMatr.equals("0")) {
                vjnAutenticarPessoaBio.put("codpessoa", "0");
                vjnAutenticarPessoaBio.put("nome", "ROSTO NAO IDENTIFICADO");
                vjnAutenticarPessoaBio.put("matr", "0");
            }
            try {
                vjnRetorno.put("resposta", vjnAutenticarPessoaBio);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("PessoaDet.autenticarPessoaBio - " + e.getMessage() + "\r\n"
                );

            }
            dbsatellite.FechaConexao();
        } finally {
            //vjnRetorno.put("resposta", vjnPessoaFoto);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/multi")
    public Response multi(String param) throws UnsupportedEncodingException, IOException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vNumeroX = (String) parametros.getOrDefault("numero", null);

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnNumero = new JSONObject();

        try {
            String vNumRet = "";
            multiplicarNumero(Integer.parseInt(vNumeroX));
            vNumRet = vRetorno;
            vjnNumero.put("numero", vNumRet);
            vjnRetorno.put("resposta", vjnNumero);
        } finally {
            //vjnRetorno.put("resposta", vjnPessoaFoto);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/enviaWhatsAppSASW")
    public Response enviaWhatsAppSASW(String param) throws UnsupportedEncodingException, IOException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vFoneX = (String) parametros.getOrDefault("telefone", null);
        String vImagemX = (String) parametros.getOrDefault("imagem", null);
        String vArqHTMLX = (String) parametros.getOrDefault("arqhtml", null);

        String vRet = "";

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnNumero = new JSONObject();

        try {

            enviarWhatsApp(vFoneX, vImagemX, vArqHTMLX);
            vRet = vRetorno;
            vjnNumero.put("numero", vRet);
            vjnRetorno.put("resposta", vjnNumero);
        } finally {
            //vjnRetorno.put("resposta", vjnPessoaFoto);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    public void multiplicarNumero(int vNumero) throws NoSuchMethodException, FileNotFoundException, ScriptException {

        ScriptEngineManager factory = new ScriptEngineManager(); // Permite escolher linguagens diferentes da tradicional Java
        ScriptEngine engine = factory.getEngineByName("JavaScript"); //Escolhemos a linguagem que será utilizada, nesse caso é o JavaScript
        Invocable invocable = (Invocable) engine;

        engine.eval(new FileReader("C:\\xampp\\htdocs\\satellite\\js\\multi.js"));
        Double vResultado = (Double) invocable.invokeFunction("multiplicar", vNumero);
        vRetorno = "O resultado é: " + vResultado;
    }

    public void comparaFoto(String vFotoSrvN1X, String vFotoSrvS1X, String vFotoN1X, String vFotoS1X) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\logcomparaFoto.txt";
        arquivohtml = new ArquivoLog();
        caminhoweb = "C:\\xampp\\htdocs\\rec\\frmFaceAutentica.html";
        String vResultado = "";
        String vArquivoHtml = "<html lang=\"pt-BR\">\n"
                + "\n"
                + "    <head>\n"
                + "        <meta charset=\"UTF-8\">\n"
                + "        <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n"
                + "        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n"
                + "\n"
                + "        <title>frmBiometriaCadastra</title>\n"
                + "        <script defer src=\"face-api.min.js\"></script>\n"
                + "        <script defer src=\"frmFaceAutentica.js\"></script>				\n"
                + "\n"
                + "    </head>\n"
                + "    <body>\n"
                + "        <div class=\"frmFaceAutentica\" style=\"display: flex; flex-direction: column; justify-content: center; align-items: center\">	\n"
                + "		   <div>\n"
                + "				<img id=\"imgSrv1\" style=\"margin-right: 5px;\" src=\"vImgSrv1\" width=\"55\" height=\"57\">\n"
                + "				<img id=\"imgSrv2\" style=\"margin-right: 5px;\"src=\"vImgSrv2\" width=\"55\" height=\"57\">\n"
                + "				<img id=\"imgAutentica1\" style=\"margin-right: 5px;\" src=\"vImgAutentica1\" width=\"55\" height=\"57\">\n"
                + "				<img id=\"imgAutentica2\" style=\"margin-right: 5px;\" src=\"vImgAutentica2\" width=\"55\" height=\"57\">\n"
                + "			</div>\n"
                + "			<div>\n"
                + "				<input id=\"editFaceApiCompara1\"  type=\"text\" style=\"background: transparent; margin-right: 10px\" size=\"5\" readonly/>\n"
                + "				<input id=\"editFaceApiCompara2\"  type=\"text\" style=\"background: transparent; margin-right: 10px\" size=\"5\" readonly/>\n"
                + "				<input id=\"editFaceApiCompara3\"  type=\"text\" style=\"background: transparent; margin-right: 10px\" size=\"5\" readonly/>\n"
                + "				<input id=\"editFaceApiCompara4\"  type=\"text\" style=\"background: transparent; margin-right: 10px\" size=\"5\" readonly/>\n"
                + "				<input id=\"editResultado\"  type=\"text\" style=\"background: transparent; margin-right: 10px\" size=\"5\" readonly/>\n"
                + "			</div>\n"
                + "		     <button onclick=\"autenticar()\" id=\"btnTeste\" class=\"btn btn-primary\" style=\"width: 75px\">Clicar</button>			 \n"
                + "		</div>\n"
                + "    </body>";
        vFotoN1X = vFotoN1X.replace(" ", "+");
        vFotoS1X = vFotoS1X.replace(" ", "+");
        vArquivoHtml = vArquivoHtml.replace("vImgSrv1", "data:image/jpg;base64," + vFotoSrvN1X);
        vArquivoHtml = vArquivoHtml.replace("vImgSrv2", "data:image/jpg;base64," + vFotoSrvS1X);
        vArquivoHtml = vArquivoHtml.replace("vImgAutentica1", vFotoN1X);
        vArquivoHtml = vArquivoHtml.replace("vImgAutentica2", vFotoS1X);
        Files.deleteIfExists(Paths.get(caminhoweb));
        arquivohtml.GravaHTML(vArquivoHtml, caminhoweb);
        try {
            URL vWSsas = new URL("https://mobile.sasw.com.br:9091/rec/frmbiometriaautentica.html");
            //URL vWSsas = new URL("http://localhost:9080/rec/frmFaceAutentica.html");

            URLConnection yc = vWSsas.openConnection();

            yc.setRequestProperty("Request-Method", "POST");
            yc.setDoInput(true);
            yc.setDoOutput(false);
            yc.connect();
            //yc.ge
            BufferedReader in = new BufferedReader(
                    new InputStreamReader(
                            yc.getInputStream()));

            String inputLine;

            while ((inputLine = in.readLine()) != null) {
                System.out.println(inputLine);
                vResultado = vResultado + inputLine;
            }
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do JS" + e.getMessage(), caminho);
        }
        vRetorno = "A resposta é: " + vResultado;
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/consultaBatidasPonto")
    public Response consultaBatidasPonto(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\consultaBatidasPonto.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));
        String vData = (String) parametros.getOrDefault("data", null);
        String vToken = (String) parametros.getOrDefault("token", null);
        logexecucao.Grava("parametros: data=" + vData + ";Token=" + vToken, caminho);
        int vConta = 0;

        if (vToken.equals("")) {
            vToken = "B8489FF7205E3217B4E043D70FF9848B";
        }

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnConsultaBatidasPonto = new JSONObject();
        JSONArray vjnBatidaHora = new JSONArray();
        try {
            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "", vBancoDados = "";
            vBancoDados = qToken.getBancoDados();
            Consulta qTmpX;

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }
            try {
                vSQL = "Select top 1 '06:00-07:00' Intervalo_HS,  \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + "where RHP.DtBatida = RHPonto.DtBatida  \n"
                        + "and RHP.Hora >= '06:00' \n"
                        + "and RHP.Hora <= '07:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n"
                        + " Union \n"
                        + "Select top 1 '07:01-08:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + "where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '07:01'  \n"
                        + "   and RHP.Hora <= '08:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida = '" + vData + "' \n"
                        + " Union \n"
                        + "Select top 1 '08:01-09:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + "where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '08:01' \n"
                        + "   and RHP.Hora <= '09:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n"
                        + " Union \n"
                        + "Select top 1 '09:01-10:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + "where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '09:01' \n"
                        + "   and RHP.Hora <= '10:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n"
                        + " Union \n"
                        + "Select top 1 '10:01-11:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + " where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '10:01' \n"
                        + "   and RHP.Hora <= '11:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n"
                        + " Union \n"
                        + "Select top 1 '11:01-12:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + " where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '11:01' \n"
                        + "   and RHP.Hora <= '12:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n"
                        + " Union \n"
                        + "Select top 1 '12:01-13:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + "where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '12:01' \n"
                        + "   and RHP.Hora <= '13:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n"
                        + " Union \n"
                        + " Select top 1 '13:01-14:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + " where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '13:01' \n"
                        + "   and RHP.Hora <= '14:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n"
                        + " Union \n"
                        + "Select top 1 '14:01-15:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + " where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '14:01' \n"
                        + "   and RHP.Hora <= '15:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n"
                        + " Union \n"
                        + "Select top 1 '15:01-15:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + " where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '15:01' \n"
                        + "   and RHP.Hora <= '16:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n"
                        + " Union \n"
                        + "Select top 1 '16:01-17:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + " where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '16:01' \n"
                        + "   and RHP.Hora <= '17:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n"
                        + " Union \n"
                        + "Select top 1 '17:01-18:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + " where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '17:01' \n"
                        + "   and RHP.Hora <= '18:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n"
                        + " Union \n"
                        + "Select top 1 '18:01-19:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + " where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '18:01' \n"
                        + "   and RHP.Hora <= '19:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n"
                        + " Union \n"
                        + " Select top 1 '19:01-20:00' Intervalo_HS, \n"
                        + "(Select  Count(*) from rhponto RHP \n"
                        + " where RHP.DtBatida = RHPonto.DtBatida \n"
                        + "   and RHP.Hora >= '19:01' \n"
                        + "   and RHP.Hora <= '20:00') 'QtdeReg' from Rhponto where RHPonto.DtBatida =  '" + vData + "' \n";
                qTmpX = new Consulta(vSQL, dbpadrao);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro try Consulta " + e.getMessage() + "\r\n"
                        + vSQL, caminho);
                throw new Exception("consultaBatidasPonto - " + e.getMessage() + "\r\n"
                        + vSQL);

            }
            while (qTmpX.Proximo()) {
                vConta++;

                vjnConsultaBatidasPonto = new JSONObject();
                vjnConsultaBatidasPonto.put("data", vData);
                vjnConsultaBatidasPonto.put("intervalo_hs", qTmpX.getString("Intervalo_HS"));
                vjnConsultaBatidasPonto.put("qtdereg", qTmpX.getString("QtdeReg"));
                vjnBatidaHora.put(vjnConsultaBatidasPonto);
            }
            if (vConta == 0) {
                vjnConsultaBatidasPonto.put("resposta", "NAO HA DADOS");
                vjnBatidaHora.put(vjnConsultaBatidasPonto);
            }
            try {
                vjnRetorno.put("resposta", vjnBatidaHora);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("ConsultaBatidasPonto - " + e.getMessage() + "\r\n"
                );

            }
            dbsatellite.FechaConexao();
        } finally {
            //vjnRetorno.put("resposta", vjnPessoaFoto);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    public void enviarWhatsApp(String vFone, String vImagem, String vArqHTML) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\enviarWhatsApp.txt";
        String vResultado = "", vLink = "", vTokenUtalk = "", vID = "";
        vID = "202203302304";

        vLink = "https://v1.utalk.chat/send/z5ftof3?cmd=chat&id=" + vID + "&to=" + vFone + "@c.us&msg=" + vArqHTML;

        vImagem = "data:image/jpg;base64," + vImagem;

        try {
            //URL vWSsas = new URL("https://mobile.sasw.com.br:9091/recx/frmbiometriaautentica.html");
            //URL vWSsas = new URL("http://localhost:9080/rec/frmFaceAutentica.html");
            URL vWSsas = new URL(vLink);

            URLConnection yc = vWSsas.openConnection();

            //yc.setRequestProperty("Request-Method", "GET");
            yc.setDoInput(true);
            yc.setDoOutput(false);
            yc.connect();
            //yc.ge
            BufferedReader in = new BufferedReader(
                    new InputStreamReader(
                            yc.getInputStream()));

            String inputLine;

            while ((inputLine = in.readLine()) != null) {
                System.out.println(inputLine);
                vResultado = vResultado + inputLine;
            }
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do JS" + e.getMessage(), caminho);
        }
        vRetorno = "A resposta é: " + vResultado;
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/listaOcorrencia")
    public Response listaOcorrencia(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\loglistOcorrencia.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vCodPessoa = (String) parametros.getOrDefault("codpessoa", null);
        String vToken = (String) parametros.getOrDefault("token", null);
        String vPW = (String) parametros.getOrDefault("pw", null);

        if (vToken.equals("")) {
            vToken = "B8489FF7205E3217B4E043D70FF9848B";
        }
        logexecucao.Grava("parametros: codpessoa=" + vCodPessoa + ";Token=" + vToken + ";pw=" + vPW, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnListaOcorrenciaGer = new JSONObject();
        JSONArray vjnListaOcorrencia = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vCodPessoa == null || vCodPessoa.equals("")) {
                throw new StatusClientePstServException("Cadastro Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vNumOcor = "", vPstLocal = "", vStatus = "",
                    vResponsavel = "", vData = "", vHora = "", vContato = "",
                    vAssunto = "", vSQLAdic;

            Consulta qTmpX;

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + qToken.getBancoDados() + "\\.txt";
            }

            vCodPessoa.replace(".0", "");
            vSQLAdic = "Select top 1 SASPW.CodPessoa from SASPW \n"
                    + "Left Join SASPWAc on  SASPWAc.Nome = SASPW.Nome \n"
                    + "where SASPW.Codpessoa = 17 \n"
                    + " and (SASPWAc.Sistema = 401301 or SASPWAc.Sistema = 401302)";

            vSQL = "Select TMktdet.Sequencia, TMktdet.Andamento, TMktdet.Data, TMktdet.Hora, TMktdet.TipoCont, TMktdet.CodPessoa, \n"
                    + "TMktdet.Historico, Pessoa.Nome, Pessoa.email, Pessoa.PWweb, PstServ.Local, Contatos.Nome NomeContato, TMktdet.Situacao, \n"
                    + " TMktdet.Ciente, tMKTDet.CodCont from TMktdet \n"
                    + "Left Join Contatos on Contatos.Codigo = TMKtDet.CodCont \n "
                    + "Left Join Clientes on Clientes.Codigo = Contatos.Codcli \n "
                    + "                  and Clientes.Codfil = Contatos.CodFil \n "
                    + "Left Join PStServ on PstServ.Codcli = Clientes.Codigo \n "
                    + "                 and PStServ.CodFil = Clientes.CodFil \n "
                    + "Left Join Pessoa  on Pessoa.Codigo = TMKtDet.CodPessoa \n "
                    + " Where TMKtDet.CodPessoa = " + vCodPessoa
                    + "   and Pessoa.PWWeb = '" + vPW + "'"
                    + "   and TMktdet.Historico <> '' \n "
                    + "   and Pessoa.Codigo in(" + vSQLAdic + ") "
                    + "Group by TMktdet.Sequencia, TMktdet.Andamento, TMktdet.Data, TMktdet.Hora, TMktdet.TipoCont, TMktdet.CodPessoa,   \n "
                    + "TMktdet.Historico,   Pessoa.Nome, Pessoa.email, Pessoa.PWweb, PstServ.Local, Contatos.Nome, TMktdet.Situacao,  \n"
                    + "TMktdet.Ciente, tMKTDet.CodCont ";
            try {
                qTmpX = new Consulta(vSQL, dbpadrao);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                throw new Exception("Lista.Ocorrencia - " + e.getMessage() + "\r\n"
                        + vSQL);
            }

            int vConta = 0;

            while (qTmpX.Proximo()) {
                vConta++;
                vjnListaOcorrenciaGer = new JSONObject();
                vNumOcor = qTmpX.getString("Sequencia");
                vPstLocal = qTmpX.getString("Local");
                vStatus = qTmpX.getString("Situacao");
                vResponsavel = qTmpX.getString("Nome");
                vData = qTmpX.getString("Data");
                vHora = qTmpX.getString("Hora");
                vContato = qTmpX.getString("NomeContato");
                vAssunto = qTmpX.getString("Historico");
                vjnListaOcorrenciaGer.put("id", vNumOcor);
                vjnListaOcorrenciaGer.put("local", vContato);
                vjnListaOcorrenciaGer.put("pstlocal", vPstLocal);
                vjnListaOcorrenciaGer.put("status", vStatus);
                vjnListaOcorrenciaGer.put("assunto", vAssunto);
                vjnListaOcorrenciaGer.put("responsavel", vResponsavel);
                vjnListaOcorrenciaGer.put("data", vData);
                vjnListaOcorrenciaGer.put("hora", vHora);
                vjnListaOcorrenciaGer.put("codpessoa", vCodPessoa);
                vjnListaOcorrencia.put(vjnListaOcorrenciaGer);
            }
            if (vConta == 0) {
                vjnListaOcorrenciaGer.put("resposta", "NAO HA DADOS");
                vjnListaOcorrencia.put(vjnListaOcorrenciaGer);
            }
            try {
                vjnRetorno.put("resposta", vjnListaOcorrencia);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("listOcorrrencia - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/listaOcorrenciaDet")
    public Response listaOcorrenciaDet(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\loglistOcorrenciaDet.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vCodPessoa = (String) parametros.getOrDefault("codpessoa", null);
        String vToken = (String) parametros.getOrDefault("token", null);
        String vNumOcor = (String) parametros.getOrDefault("numocor", null);

        if (vToken.equals("")) {
            vToken = "B8489FF7205E3217B4E043D70FF9848B";
        }
        logexecucao.Grava("parametros: codpessoa=" + vCodPessoa + ";Token=" + vToken + ";numOcor=" + vNumOcor, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnListaOcorrenciaGer = new JSONObject();
        JSONArray vjnListaOcorrencia = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vCodPessoa == null || vCodPessoa.equals("")) {
                throw new StatusClientePstServException("Cadastro Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vPstLocal = "", vStatus = "",
                    vResponsavel = "", vData = "", vHora = "", vContato = "",
                    vAssunto = "", vDetalhes = "";

            Consulta qTmpX;

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + qToken.getBancoDados() + "\\.txt";
            }

            vCodPessoa.replace(".0", "");

            vSQL = "Select TMktdet.Sequencia, TMktdet.Andamento, TMktdet.Data, TMktdet.Hora, TMktdet.TipoCont, TMktdet.CodPessoa, \n"
                    + "TMktdet.Historico, TMktdet.Detalhes, Pessoa.Nome, Pessoa.email, Pessoa.PWweb, PstServ.Local, Contatos.Nome NomeContato, TMktdet.Situacao, \n"
                    + " TMktdet.Ciente, tMKTDet.CodCont from TMktdet \n"
                    + "Left Join Contatos on Contatos.Codigo = TMKtDet.CodCont \n "
                    + "Left Join Clientes on Clientes.Codigo = Contatos.Codcli \n "
                    + "                  and Clientes.Codfil = Contatos.CodFil \n "
                    + "Left Join PStServ on PstServ.Codcli = Clientes.Codigo \n "
                    + "                 and PStServ.CodFil = Clientes.CodFil \n "
                    + "Left Join Pessoa  on Pessoa.Codigo = TMKtDet.CodPessoa \n "
                    + " Where TMKtDet.Sequencia = " + vNumOcor
                    + "   and TMKtDet.CodPessoa = " + vCodPessoa
                    + "   and TMktdet.Historico <> '' \n ";
            try {
                qTmpX = new Consulta(vSQL, dbpadrao);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Ërro SQL: " + vSQL, caminho);
                throw new Exception("Lista.OcorrenciaDet- " + e.getMessage() + "\r\n"
                        + vSQL);
            }

            int vConta = 0;

            while (qTmpX.Proximo()) {
                vConta++;
                vjnListaOcorrenciaGer = new JSONObject();
                vNumOcor = qTmpX.getString("Sequencia");
                vPstLocal = qTmpX.getString("Local");
                vStatus = qTmpX.getString("Situacao");
                vResponsavel = qTmpX.getString("Nome");
                vData = qTmpX.getString("Data");
                vHora = qTmpX.getString("Hora");
                vContato = qTmpX.getString("NomeContato");
                vAssunto = qTmpX.getString("Historico");
                vDetalhes = qTmpX.getString("Detalhes");
                vjnListaOcorrenciaGer.put("id", vNumOcor);
                vjnListaOcorrenciaGer.put("local", vContato);
                vjnListaOcorrenciaGer.put("pstlocal", vPstLocal);
                vjnListaOcorrenciaGer.put("status", vStatus);
                vjnListaOcorrenciaGer.put("assunto", vAssunto);
                vjnListaOcorrenciaGer.put("detalhes", vDetalhes);
                vjnListaOcorrenciaGer.put("responsavel", vStatus);
                vjnListaOcorrenciaGer.put("data", vData);
                vjnListaOcorrenciaGer.put("hora", vHora);
                vjnListaOcorrenciaGer.put("codpessoa", vCodPessoa);
                vjnListaOcorrencia.put(vjnListaOcorrenciaGer);
            }
            if (vConta == 0) {
                vjnListaOcorrenciaGer.put("resposta", "NAO HA DADOS");
                vjnListaOcorrencia.put(vjnListaOcorrenciaGer);
            }
            try {
                vjnRetorno.put("resposta", vjnListaOcorrencia);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("listOcorrrenciaDet - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gravaOcorrencia")
    public Response gravaOcorrencia(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\loggravaOcorrencia.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vToken = (String) parametros.getOrDefault("token", null);
        String vCodPessoa = (String) parametros.getOrDefault("codpessoa", null);
        String vNumOcor = (String) parametros.getOrDefault("numOcor", null);
        String vAndamento = (String) parametros.getOrDefault("andamento", null);
        String vData = (String) parametros.getOrDefault("data", null);
        String vHora = (String) parametros.getOrDefault("hora", null);
        String vSituacao = (String) parametros.getOrDefault("situacao", null);
        String vCodContato = (String) parametros.getOrDefault("codcontato", null);
        String vContato = (String) parametros.getOrDefault("contato", null);
        String vFone = (String) parametros.getOrDefault("fone", null);
        String vFone2 = (String) parametros.getOrDefault("fone2", null);
        String vHistorico = (String) parametros.getOrDefault("historico", null);
        String vDetalhes = (String) parametros.getOrDefault("detalhes", null);
        String vAnexo = (String) parametros.getOrDefault("anexo", null);

        if (vToken.equals("")) {
            vToken = "B8489FF7205E3217B4E043D70FF9848B";
        }
        logexecucao.Grava("parametros: codpessoa=" + vCodPessoa + ";Token=" + vToken + ";numOcor=" + vNumOcor, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnListaOcorrenciaGer = new JSONObject();
        JSONArray vjnListaOcorrencia = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vCodPessoa == null || vCodPessoa.equals("")) {
                throw new StatusClientePstServException("Cadastro Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vPstLocal = "", vStatus = "",
                    vResponsavel = "";

            Consulta SQLPdr;

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + qToken.getBancoDados() + "\\.txt";
            }

            vCodPessoa.replace(".0", "");

            //Inicia Gravacao
            vSQLExec = "Insert into TMktDet (Sequencia, Andamento, Data, Hora, TipoCont, CodPessoa, Situacao, CodCont, Contato, "
                    + " Fone, Fone2, Historico, Detalhes, Operador, Dt_Alter, Hr_Alter) Values ("
                    + "(Select isnull(Max(Sequencia)+1,0) from TMKtDet), "
                    + "'" + vAndamento + "', "
                    + "'" + vData + "',"
                    + "'" + getDataAtual("HORA") + "',"
                    + "'A',"
                    + vCodPessoa + ", "
                    + "'" + vSituacao + "', "
                    + vCodContato + ", "
                    + "'" + vContato + "', "
                    + "'" + vFone + "', "
                    + "'" + vFone2 + "', "
                    + "'" + vHistorico + "', "
                    + "'" + vDetalhes + "', "
                    + "'SatWS', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')\n";

            //Finaliza Gravacao
            try {
                SQLPdr = new Consulta(vSQLExec, dbpadrao);
                SQLPdr.insert();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQLExec, caminho);
                throw new Exception("gravaOcorrencia- " + e.getMessage() + "\r\n"
                        + vSQL);
            }

            int vConta = 0;

            vjnListaOcorrenciaGer.put("resposta", "GRAVADO COM SUCESSO");
            vjnListaOcorrencia.put(vjnListaOcorrenciaGer);
            try {
                vjnRetorno.put("resposta", vjnListaOcorrencia);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("listOcorrrenciaDet - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/gravaFoto")
    public Response gravaFoto(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\loggravaFoto.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vMatr = (String) parametros.getOrDefault("matr", null);
        String vToken = (String) parametros.getOrDefault("token", null);
        String vNFoto = (String) parametros.getOrDefault("nfoto", null);
        String vFotoCap = (((String) parametros.getOrDefault("fotocap", null)).replace(" ", "+")).replace("data:image/jpeg;base64,", "");
        String vChaveBio = (String) parametros.getOrDefault("chavebio", null);

        logexecucao.Grava("parametros: Matr=" + vMatr + ";Token=" + vToken + ";FotoCap=" + vFotoCap + ";NFoto=" + vNFoto + ";ChaveBio=" + vChaveBio, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        try {
            //Validando se a matrícula informada não está vazia
            if (vMatr == null || vMatr.equals("")) {
                throw new StatusClientePstServException("Cadastro Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vPathWeb = "", vPathLocal = "";
            int vOrdem = 0;
            String vFace = "ECAF";

            vPathWeb = "'https://mobile.sasw.com.br:9091/satellite/fotos/" + vFace + "/_F_ID/";
            vPathLocal = "C:\\xampp\\htdocs\\satellite\\fotos\\" + vFace + "\\" + "\\_F_ID\\";

            Consulta qTmpX;

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vFace + "\\.txt";
            }

            vMatr.replace(".0", "");

            if (vMatr.length() == 1) {
                vMatr = "0000000" + vMatr;
            } else if (vMatr.length() == 2) {
                vMatr = "000000" + vMatr;
            } else if (vMatr.length() == 3) {
                vMatr = "00000" + vMatr;
            } else if (vMatr.length() == 4) {
                vMatr = "0000" + vMatr;
            } else if (vMatr.length() == 5) {
                vMatr = "000" + vMatr;
            } else if (vMatr.length() == 6) {
                vMatr = "00" + vMatr;
            } else if (vMatr.length() == 7) {
                vMatr = "0" + vMatr;
            }
            //Busca no Banco se existe a Pessoa Se nao exisitir insere, existindo Update.
            vSQL = "Select isnull(Max(PessoaDet.Ordem),0) ordem from Pessoa "
                    + "Left Join PessoaDet on PessoaDet.Codigo = Pessoa.Codigo"
                    + " where Pessoa.Matr = " + vMatr;
            qTmpX = new Consulta(vSQL, dbsatellite);
            qTmpX.select();
            while (qTmpX.Proximo()) {
                vOrdem = qTmpX.getInt("Ordem");
            }
            qTmpX.Close();
            vSQL = " Select Pessoa.Codigo, Pessoa.Nome, Pessoa.Matr,  \n"
                    + " PessoaDet.FaceID, PessoaDet.Ordem, PessoaDet.Foto1, PessoaDet.Foto2, PessoaDet.Foto3, \n"
                    + " PessoaDet.Codigo CodPessoaDet "
                    + "from Pessoa \n"
                    + " Left Join FaceAutentica on  FaceAutentica.CodPessoa = Pessoa.Codigo \n"
                    + "                         and FaceAutentica.Codigo = '" + vChaveBio + "' \n"
                    + " Left Join PessoaDet on PessoaDet.Codigo = Pessoa.Codigo \n"
                    + "                    and PessoaDet.Ordem = " + vOrdem
                    + " where Pessoa.matr = " + vMatr + " \n";
            try {
                qTmpX = new Consulta(vSQL, dbsatellite);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                throw new Exception("GravaFoto - " + e.getMessage() + "\r\n"
                        + vSQL);
            }
            JSONObject vjnPessoaFoto = new JSONObject();
            int vConta = 0;
            //if (qTmpX.getString("Matr") != "") {
            //.Proximo();
            //qTmpX.
            String vDirImagem = "C:\\xampp\\htdocs\\satellite\\fotos\\" + vFace + "\\_F_ID\\";
            String vDirImagemCap = "C:\\xampp\\htdocs\\rec2\\image\\";
            Consulta SQLPdr;

            File diretorio = new File(vDirImagem);
            if (!diretorio.exists()) {
                diretorio.mkdirs();  // cria diretórios caso não estejam criados
            }

            String vPathImgFoto1 = "F" + vMatr + "_F" + vNFoto + ".jpg";

            while (qTmpX.Proximo()) {
                vConta++;
                if (!qTmpX.getString("Matr").equals(null)) {
                    vjnPessoaFoto.put("matr", qTmpX.getString("Matr"));
                    vjnPessoaFoto.put("nome", qTmpX.getString("Nome"));
                    // vjnPessoaFoto.put("faceid", qTmpX.getString("FaceID"));                    
                    String vCodPessoaDet = qTmpX.getString("CodPessoaDet");
                    if (vNFoto.equals("1") || vNFoto.equals("2") || vNFoto.equals("3")) {
                        if (qTmpX.getString("CodPessoaDet") == null || qTmpX.getString("CodPessoaDet").equals("")) {
                            vjnPessoaFoto.put("nfoto" + vNFoto, "NAO REGISTRADO");

                            vOrdem++;

                            try {
                                vSQLExec = "insert into PessoaDet(Codigo, Ordem, Faceid, Foto1, Operador, Dt_Alter, Hr_Alter) Values(\n"
                                        + qTmpX.getString("Codigo") + ", \n"
                                        + vOrdem + ", \n"
                                        + "''" + ", \n"
                                        + "'" + vPathImgFoto1 + "', \n"
                                        + "'CADBIO', \n"
                                        + "'" + getDataAtual("SQL") + "', \n"
                                        + "'" + getDataAtual("HORA") + "')\n";
                                SQLPdr = new Consulta(vSQLExec, dbsatellite);
                                SQLPdr.insert();
                            } catch (Exception e) {
                                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                throw new Exception("PessoDEt.gravaFoto - " + e.getMessage() + "\r\n"
                                        + vSQL);
                            }
                            SQLPdr.close();
                        } else if ((qTmpX.getString("Foto2") == null) || (qTmpX.getString("Foto2").equals(""))
                                || (qTmpX.getString("Foto3") == null) || (qTmpX.getString("Foto3").equals(""))) {
                            vjnPessoaFoto.put(" " + vNFoto, vPathImgFoto1);
                            try {
                                vSQLExec = "Update PessoaDet Set \n"
                                        + "Foto" + vNFoto + " = '" + vPathImgFoto1 + "', \n"
                                        + "Operador = 'CADBIO', \n"
                                        + "Dt_Alter = '" + getDataAtual("SQL") + "', \n"
                                        + "Hr_Alter = '" + getDataAtual("HORA") + "' \n "
                                        + " where Codigo = " + qTmpX.getString("Codigo") + " \n "
                                        + "   and Ordem = " + vOrdem;
                                SQLPdr = new Consulta(vSQLExec, dbsatellite);
                                SQLPdr.update();
                            } catch (Exception e) {
                                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                throw new Exception("PessoDEt.gravaFoto - " + e.getMessage() + "\r\n"
                                        + vSQL);
                            }

                            SQLPdr.close();
                            vjnPessoaFoto.put("faceid", vPathWeb + vPathImgFoto1);
                            vjnPessoaFoto.put("imgfoto", vPathLocal + vPathImgFoto1);
                        }
                    }
                    vjnPessoaFoto.put("codigo", qTmpX.getString("Codigo"));
                    vjnPessoaFoto.put("pathlocal", vPathLocal);
                    vjnPessoaFoto.put("pathweb", vPathWeb);
                    qTmpX.Close();

                    // Gravar Imagem em Arquivo
                    //Criando Imagem Original            
                    //Criando imagem
                    byte[] vFotoB641 = new sun.misc.BASE64Decoder().decodeBuffer(vFotoCap);
                    //byte[] vFotoB642 = new sun.misc.BASE64Decoder().decodeBuffer(vFotoCad2);

                    if (!vFotoCap.equals("")) {
                        //Cria o primeiro arquivo e finaliza     
                        if (vNFoto.equals("3")) {
                            String vArqFotoCap = vDirImagem + vPathImgFoto1;
                            Files.deleteIfExists(Paths.get(vArqFotoCap));
                        }
                        FileOutputStream vFileOS1 = new FileOutputStream(vDirImagem + vPathImgFoto1);
                        vFileOS1.write(vFotoB641);
                        FileDescriptor vFileD1 = vFileOS1.getFD();
                        vFileOS1.flush();
                        vFileD1.sync();
                        vFileOS1.close();
                    }
                    File vFileImg = new File(vDirImagem + vPathImgFoto1);
                    if (vFileImg.exists()) {
                        vjnRetorno.put("matr", vMatr);
                        vjnRetorno.put("data", getDataAtual("SQL"));
                        vjnRetorno.put("fotocad", vPathImgFoto1);
                        vjnRetorno.put("fotocap", vPathImgFoto1);
                        vjnRetorno.put("nfoto", vNFoto);
                        vjnRetorno.put("resposta", "SUCESSO");
                    } else {
                        vjnRetorno.put("matr", vMatr);
                        vjnRetorno.put("data", getDataAtual("SQL"));
                        vjnRetorno.put("fotocad", "");
                        vjnRetorno.put("fotocad2", "");
                        vjnRetorno.put("resposta", "FALHA");
                    }
                } else {
                    vjnRetorno.put("matr", vMatr);
                    vjnRetorno.put("data", getDataAtual("SQL"));
                    vjnRetorno.put("fotocad", "");
                    vjnRetorno.put("fotocad2", "");
                    vjnRetorno.put("resposta", "FALHA");
                }
//Final de imagem
            }
            dbsatellite.FechaConexao();
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/autenticaOcorrencia")
    public Response autenticaOcorrencia(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\logautenticaOCorrencia.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vCodPessoa = (String) parametros.getOrDefault("codpessoa", null);
        String vToken = (String) parametros.getOrDefault("token", null);
        String vPW = (String) parametros.getOrDefault("pw", null);

        if (vToken.equals("")) {
            vToken = "B8489FF7205E3217B4E043D70FF9848B";
        }
        logexecucao.Grava("parametros: codpessoa=" + vCodPessoa + ";Token=" + vToken + ";pw=" + vPW, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnListaOcorrenciaGer = new JSONObject();
        JSONArray vjnListaOcorrencia = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vCodPessoa == null || vCodPessoa.equals("")) {
                throw new StatusClientePstServException("Cadastro Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vNumOcor = "", vPstLocal = "", vStatus = "",
                    vResponsavel = "", vData = "", vHora = "", vContato = "",
                    vAssunto = "", vSQLAdic = "", vNome = "", vMatr = "";

            Consulta qTmpX, qTmpXC, qTmpXP;

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + qToken.getBancoDados() + "\\.txt";
            }

            vCodPessoa.replace(".0", "");
            vSQLAdic = "Select top 1 SASPW.CodPessoa from SASPW \n"
                    + "Left Join SASPWAc on  SASPWAc.Nome = SASPW.Nome \n"
                    + "where SASPW.Codpessoa = 17 \n"
                    + " and (SASPWAc.Sistema = 401301 or SASPWAc.Sistema = 401302)";

            vSQL = "Select  Pessoa.Nome, Pessoa.email, Pessoa.PWweb, Pessoa.Matr, Pessoa.CodPessoaWeb  \n"
                    + " from Pessoa \n"
                    + " Where Pessoa.Codigo = " + vCodPessoa
                    + "   and Pessoa.PWWeb = '" + vPW + "'";
            try {
                qTmpX = new Consulta(vSQL, dbsatellite);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                throw new Exception("Lista.Ocorrencia - " + e.getMessage() + "\r\n"
                        + vSQL);
            }

            int vConta = 0;
            while (qTmpX.Proximo()) {
                vNome = qTmpX.getString("Nome");
                vMatr = qTmpX.getString("Matr");
            }
            qTmpX.close();
            vSQL = "Select ";
            qTmpX = new Consulta(vSQL, dbpadrao);
            while (qTmpX.Proximo()) {
                vConta++;
                vjnListaOcorrenciaGer = new JSONObject();
                vNumOcor = qTmpX.getString("Sequencia");
                vPstLocal = qTmpX.getString("Local");
                vStatus = qTmpX.getString("Situacao");
                vResponsavel = qTmpX.getString("Nome");
                vData = qTmpX.getString("Data");
                vHora = qTmpX.getString("Hora");
                vContato = qTmpX.getString("NomeContato");
                vAssunto = qTmpX.getString("Historico");
                vjnListaOcorrenciaGer.put("id", vNumOcor);
                vjnListaOcorrenciaGer.put("local", vContato);
                vjnListaOcorrenciaGer.put("pstlocal", vPstLocal);
                vjnListaOcorrenciaGer.put("status", vStatus);
                vjnListaOcorrenciaGer.put("assunto", vAssunto);
                vjnListaOcorrenciaGer.put("responsavel", vResponsavel);
                vjnListaOcorrenciaGer.put("data", vData);
                vjnListaOcorrenciaGer.put("hora", vHora);
                vjnListaOcorrenciaGer.put("codpessoa", vCodPessoa);
                vjnListaOcorrencia.put(vjnListaOcorrenciaGer);
            }
            if (vConta == 0) {
                vjnListaOcorrenciaGer.put("resposta", "NAO HA DADOS");
                vjnListaOcorrencia.put(vjnListaOcorrenciaGer);
            }
            try {
                vjnRetorno.put("resposta", vjnListaOcorrencia);
                dbsatellite.FechaConexao();
                dbpadrao.FechaConexao();
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("listOcorrrencia - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/obterFotoPW")
    public Response obterFotoPW(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\logobterfotopw.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vMatr = (String) parametros.getOrDefault("matr", null); //Obrigatorio
        String vToken = (String) parametros.getOrDefault("token", null);
        String vPW = (String) parametros.getOrDefault("pw", null);
        String vDtBatida = (String) parametros.getOrDefault("data", null);
        String vChaveBio = (String) parametros.getOrDefault("chavebio", null);
        logexecucao.Grava("parametros: Matr=" + vMatr + ";Token=" + vToken + ";PW=" + vPW + ";Data=" + vDtBatida + ";ChaveBio=" + vChaveBio, caminho);
        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "";
        String vPathWeb = "";
        String vPathLocal = "";
        String vPathFotoPonto = "";
        String vPathFotoPontoLocal = "";
        String vData = "";

        try {
            //Validando se a matrícula informada não está vazia
            if (vMatr == null || vMatr.equals("")) {
                throw new StatusClientePstServException("MatriculaInvalida");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            String vBancoDados = qToken.getBancoDados();
            dbpadrao = this.pool.getConexao(vBancoDados);
            Consulta qTmpX;

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }

            if (vDtBatida.equals("") || vDtBatida.length() != 8) {
                vData = "20220201"; //vData = getDataAtual("SQL");
            } else {
                vData = vDtBatida;
            }

            vPathWeb = "https://mobile.sasw.com.br:9091/satellite/fotos/" + vBancoDados + "/_F_ID/";
            vPathLocal = "C:\\xampp\\htdocs\\satellite\\fotos\\" + vBancoDados + "\\_F_ID\\";
            vPathFotoPonto = "https://mobile.sasw.com.br:9091/satellite/fotos/" + vBancoDados
                    + "/ponto/" + vData + "/";
            vPathFotoPontoLocal = "C:\\xampp\\htdocs\\satellite\\fotos\\" + vBancoDados + "\\ponto\\" + vData + "\\";
            vMatr.replace(".0", "");
            if (vMatr.length() == 1) {
                vMatr = "0000000" + vMatr;
            } else if (vMatr.length() == 2) {
                vMatr = "000000" + vMatr;
            } else if (vMatr.length() == 3) {
                vMatr = "00000" + vMatr;
            } else if (vMatr.length() == 4) {
                vMatr = "0000" + vMatr;
            } else if (vMatr.length() == 5) {
                vMatr = "000" + vMatr;
            } else if (vMatr.length() == 6) {
                vMatr = "00" + vMatr;
            } else if (vMatr.length() == 7) {
                vMatr = "0" + vMatr;
            }

            try {

                vSQL = " Select Pessoa.Codigo, Pessoa.Nome, Pessoa.Matr,  \n"
                        + " PessoaDet.FaceID, PessoaDet.Foto1, PessoaDet.Foto2, PessoaDet.Foto3, RHPontoGer.DtBatida,  \n"
                        + "(Select Hora from RHPonto where RhPonto.Matr = Pessoa.Matr and RHPonto.DtCompet = RHPontoGer.DtCompet  and Batida = 1) Hora1,\n"
                        + "(Select Hora from RHPonto where RhPonto.Matr = Pessoa.Matr and RHPonto.DtCompet = RHPontoGer.DtCompet  and Batida = 2) Hora2,\n"
                        + "(Select Hora from RHPonto where RhPonto.Matr = Pessoa.Matr and RHPonto.DtCompet = RHPontoGer.DtCompet  and Batida = 3) Hora3,\n"
                        + "(Select Hora from RHPonto where RhPonto.Matr = Pessoa.Matr and RHPonto.DtCompet = RHPontoGer.DtCompet  and Batida = 4) Hora4, \n"
                        + " Pessoa.Fone1, Pessoa.Email, Funcion.Cargo, Cargos.Descricao, PstServ.Secao, PstServ.Local, Filiais.Descricao FilialDesc, \n"
                        + " FaceAutentica.Codigo ChaveBio \n"
                        + "from Pessoa \n"
                        + " Left Join PessoaDet on PessoaDet.Codigo = Pessoa.Codigo \n"
                        + " Left Join Funcion on Funcion.Matr = Pessoa.Matr \n"
                        + " Left Join PstServ on PstServ.Secao =  Funcion.Secao \n"
                        + "                  and PstServ.CodFil = Funcion.CodFil \n"
                        + " Left Join Cargos on Cargos.Cargo = Funcion.Cargo "
                        + " Left Join Filiais on Filiais.Codfil = Funcion.CodFil "
                        + " Left Join RHponto RHPontoGer on RHPontoGer.Matr = Funcion.Matr \n"
                        + "   and RHPontoGer.DtCompet >= '" + vData + "' \n"
                        + "  and RHPontoger.Batida = 1 \n"
                        + " Left Join FaceAutentica on  FaceAutentica.CodPessoa = Pessoa.Codigo \n"
                        + "                         and FaceAutentica.Codigo = '" + vChaveBio + "' \n"
                        + " where Pessoa.matr = " + vMatr + "\n"
                        + "   and (pwWeb = '" + vPW + "' or FaceAutentica.Codigo = '" + vChaveBio + "') \n"
                        + " order by RHPontoGer.DtCompet desc";
                try {
                    qTmpX = new Consulta(vSQL, dbpadrao);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Ërro SQL: " + vSQL, caminho);
                    throw new Exception("FuncionPessoa.obterFotoPw - " + e.getMessage() + "\r\n"
                            + vSQL);
                }
                JSONObject vjnPessoaFoto;
                JSONArray vjnPessoaData = new JSONArray();
                int vConta = 0;
                //if (qTmpX.getString("Matr") != "") {
                //.Proximo();
                //qTmpX.                
                while (qTmpX.Proximo()) {
                    vConta = ++vConta;

                    if (!qTmpX.getString("Matr").equals(null)) {

                        //Gerar Chave biometrica para autorizaçào com senha
                        vChaveBio = qTmpX.getString("ChaveBio");
                        if (vChaveBio.equals("") && vConta == 1) {
                            String vCodPessoaDet = qTmpX.getString("Codigo");
                            String vSQLCadBio = "";
                            vSQLCadBio = "DECLARE @Codigo Varchar(32)\n"
                                    + "DECLARE @CodigoAnterior Varchar(32)\n"
                                    + "DECLARE @Bloco1Int INT\n"
                                    + "DECLARE @Bloco2Int INT\n"
                                    + "DECLARE @Bloco3Int INT\n"
                                    + "DECLARE @Bloco1 Varchar(6)\n"
                                    + "DECLARE @Bloco2 Varchar(6)\n"
                                    + "DECLARE @Bloco3 Varchar(6)\n"
                                    + "\n"
                                    + "SET @Bloco1Int = FLOOR(RAND()*(16777216)) -- FFFFFF\n"
                                    + "SET @Bloco2Int = FLOOR(RAND()*(16777216))\n"
                                    + "SET @Bloco3Int = FLOOR(RAND()*(16777216))\n"
                                    + "\n"
                                    + "SET @Bloco1 = REPLICATE('0',6-LEN(RTRIM(FORMAT(@Bloco1Int,'X')))) + RTRIM(FORMAT(@Bloco1Int,'X'))\n"
                                    + "SET @Bloco2 = REPLICATE('0',6-LEN(RTRIM(FORMAT(@Bloco2Int,'X')))) + RTRIM(FORMAT(@Bloco2Int,'X'))\n"
                                    + "SET @Bloco3 = REPLICATE('0',6-LEN(RTRIM(FORMAT(@Bloco3Int,'X')))) + RTRIM(FORMAT(@Bloco3Int,'X'))\n"
                                    + "\n" //SET NOCOUNT ON
                                    + "\n"
                                    + "SET NOCOUNT ON\n"
                                    + "\n"
                                    + "SET @CodigoAnterior = (SELECT TOP 1 FORMAT(CONVERT(INT, CONVERT(VARBINARY, '0' + SUBSTRING(ISNULL(Codigo, 'B4FFFF2'), 1 ,7), 2)) + 14,'X') FROM TOKENS ORDER BY Codigo DESC)\n"
                                    + "SET @Codigo = CONCAT(@CodigoAnterior,\n"
                                    + "    @Bloco1, @Bloco2, @Bloco3,\n"
                                    + "    REVERSE(@CodigoAnterior)) \n"
                                    + " SELECT @Codigo Token \n";
                            Consulta qTmpX2 = new Consulta(vSQLCadBio, dbpadrao);
                            qTmpX2.select();
                            if (qTmpX2.Proximo()) {
                                vChaveBio = qTmpX2.getString("Token");
                                logexecucao.Grava("GeraToken: " + vChaveBio, caminho);
                            }
                            qTmpX2.Close();
                            String vSQLExec = "";
                            //Insere Token Base Central e base Local
                            vSQLExec = "Insert into Tokens(Codigo, BancoDados, Modulo, Chave, Data, Hora, DtValid) Values("
                                    + "'" + vChaveBio + "', \n "
                                    + "'" + vBancoDados + "', \n "
                                    + "'SATMOB', \n "
                                    + "'WS', \n "
                                    + "'" + getDataAtual("SQL") + "', \n "
                                    + "'" + getDataAtual("HORA") + "',\n "
                                    + "DATEADD(HOUR, 1, '" + getDataAtual("SQL") + "')) \n ";
                            try {
                                Consulta qSQLPdrCentral = new Consulta(vSQLExec, dbsatellite);
                                qSQLPdrCentral.insert();
                                qSQLPdrCentral.close();
                            } catch (Exception e) {
                                logexecucao.Grava("Erro SQL Insert Tokens: " + vSQLExec, caminho);
                                throw new Exception("FuncionPessoa.obterFotoPw - " + e.getMessage() + "\r\n"
                                        + vSQLExec);
                            }
                            //
                            vSQLExec = "Insert into FaceAutentica(Sequencia, Codigo, FotoBase, FotoN1, FotoS1, \n"
                                    + " CodPessoa, BancoDados, Matr, Nota1, Nota2, Nota3, Nota4, \n"
                                    + "Operador, Dt_Alter, Hr_Alter) \n"
                                    + "Values((Select isnull(Max(sequencia),0)+1 from FaceAutentica), "
                                    + "'" + vChaveBio + "', \n"
                                    + "'', "
                                    + "'', "
                                    + "'', "
                                    + qTmpX.getString("Codigo") + ", \n"
                                    + "'" + vBancoDados + "', \n"
                                    + vMatr + ", \n"
                                    + "0, \n"
                                    + "0, \n"
                                    + "0, \n"
                                    + "0, \n"
                                    + "'SENHAUSR', \n"
                                    + "'" + getDataAtual("SQL") + "', \n"
                                    + "'" + getDataAtual("HORA") + "')\n";
                            try {
                                Consulta sQLPdr = new Consulta(vSQLExec, dbpadrao);
                                sQLPdr.insert();
                                sQLPdr.close();
                            } catch (Exception e) {
                                logexecucao.Grava("Erro SQL Insert FaceAutentica: " + vSQLExec, caminho);
                                throw new Exception("FuncionPessoa.obterFotoPw - " + e.getMessage() + "\r\n"
                                        + vSQLExec);
                            }
                        }
                        try {
                            vjnPessoaFoto = new JSONObject();
                            vjnPessoaFoto.put("matr", qTmpX.getString("Matr"));
                            vjnPessoaFoto.put("nome", qTmpX.getString("Nome"));
                            vjnPessoaFoto.put("cargo", qTmpX.getString("Cargo"));
                            vjnPessoaFoto.put("cargodescricao", qTmpX.getString("Descricao"));
                            vjnPessoaFoto.put("filialdesc", qTmpX.getString("FilialDesc"));
                            vjnPessoaFoto.put("secao", qTmpX.getString("Secao"));
                            vjnPessoaFoto.put("posto", qTmpX.getString("Local"));
                            vjnPessoaFoto.put("fone", qTmpX.getString("Fone1"));
                            vjnPessoaFoto.put("email", qTmpX.getString("Email"));
                            vjnPessoaFoto.put("data", qTmpX.getString("DtBatida"));
                            vjnPessoaFoto.put("chavebio", vChaveBio);

                            if (!qTmpX.getString("Hora1").equals("")) {
                                vjnPessoaFoto.put("hora1", qTmpX.getString("Hora1"));
                                File vFotoBatida1 = new File(vPathFotoPontoLocal + vMatr + "_1.jpg");
                                if (vFotoBatida1.exists()) {
                                    vjnPessoaFoto.put("fotobatida1", vPathFotoPonto + vMatr + "_1.jpg");
                                } else {
                                    vjnPessoaFoto.put("fotobatida1", "");
                                }
                            } else {
                                vjnPessoaFoto.put("hora1", "");
                            }

                            if (!qTmpX.getString("Hora2").equals("")) {
                                vjnPessoaFoto.put("hora2", qTmpX.getString("Hora2"));
                                File vFotoBatida2 = new File(vPathFotoPontoLocal + vMatr + "_2.jpg");
                                if (vFotoBatida2.exists()) {
                                    vjnPessoaFoto.put("fotobatida2", vPathFotoPonto + vMatr + "_2.jpg");
                                } else {
                                    vjnPessoaFoto.put("fotobatida2", "");
                                }
                            } else {
                                vjnPessoaFoto.put("hora2", "");
                            }

                            if (!qTmpX.getString("Hora3").equals("")) {
                                vjnPessoaFoto.put("hora3", qTmpX.getString("Hora3"));
                                File vFotoBatida3 = new File(vPathFotoPontoLocal + vMatr + "_3.jpg");
                                if (vFotoBatida3.exists()) {
                                    vjnPessoaFoto.put("fotobatida3", vPathFotoPonto + vMatr + "_3.jpg");
                                } else {
                                    vjnPessoaFoto.put("fotobatida3", "");
                                }
                            } else {
                                vjnPessoaFoto.put("hora3", "");
                            }

                            if (!qTmpX.getString("Hora4").equals("")) {
                                vjnPessoaFoto.put("hora4", qTmpX.getString("Hora4"));
                                File vFotoBatida4 = new File(vPathFotoPontoLocal + vMatr + "_4.jpg");
                                if (vFotoBatida4.exists()) {
                                    vjnPessoaFoto.put("fotobatida4", vPathFotoPonto + vMatr + "_4.jpg");
                                } else {
                                    vjnPessoaFoto.put("fotobatida4", "");
                                }
                            } else {
                                vjnPessoaFoto.put("hora4", "");
                            }
                            if (qTmpX.getString("Foto1").equals("") || qTmpX.getString("Foto2").equals("")) {
                                vjnPessoaFoto.put("nfoto1", "NAO REGISTRADO");
                                vjnPessoaFoto.put("nfoto2", "NAO REGISTRADO");
                                vjnPessoaFoto.put("nfoto3", "NAO REGISTRADO");
                                vjnPessoaFoto.put("faceid", "NAO REGISTRADO");
                                vjnPessoaFoto.put("imgfoto", "NAO REGISTRADO");
                            } else {
                                vjnPessoaFoto.put("nfoto1", qTmpX.getString("Foto1"));

                                vjnPessoaFoto.put("nfoto2", qTmpX.getString("Foto2"));
                                vjnPessoaFoto.put("nfoto3", qTmpX.getString("Foto3"));
                                vjnPessoaFoto.put("faceid", vPathWeb + qTmpX.getString("Foto1"));
                                vjnPessoaFoto.put("imgfoto", vPathLocal + qTmpX.getString("Foto1"));
                            }
                            vjnPessoaFoto.put("codigo", qTmpX.getString("Codigo"));
                            vjnPessoaFoto.put("pathlocal", vPathLocal);
                            vjnPessoaFoto.put("pathweb", vPathWeb);

                            vjnPessoaData.put(vjnPessoaFoto);
                        } catch (Exception e) {
                            logexecucao.Grava("Erro geração Retorno Json", caminho);
                            throw new Exception("FuncionPessoa.obterFotoPw - " + e.getMessage() + "\r\n");
                        }
                    }

                    vjnRetorno.put("funcion", vjnPessoaData);
                    //                  pstServJO.put("funcion", funcionJA);                

                }
                qTmpX.Close();
                if (vConta == 0) {
                    vjnRetorno.put("matr", "0");
                    vjnRetorno.put("nome", "USUARIO OU SENHA INCORRETA");
                    vjnRetorno.put("nfoto1", "NAO REGISTRADO");
                    vjnRetorno.put("nfoto2", "NAO REGISTRADO");
                    vjnRetorno.put("nfoto3", "NAO REGISTRADO");
                    vjnRetorno.put("codigo", "0");
                    vjnRetorno.put("funcion", vjnPessoaData);
                }
            } catch (Exception e) {
                logexecucao.Grava("Erro try principal " + e.getMessage(), caminho);
                throw new Exception("FuncionPessoa.obterFotoPw - " + e.getMessage() + "\r\n"
                        + vSQL);

            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/rotasEficienciaBase")
    public Response rotasEficienciaBase(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\logrotasEficienciaBase.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vToken = (String) parametros.getOrDefault("token", null);
        String vCodFil = (String) parametros.getOrDefault("codfil", null);
        String vData = (String) parametros.getOrDefault("data", null);
        String vRotaIni = (String) parametros.getOrDefault("rotaini", null);
        String vRotaFim = (String) parametros.getOrDefault("rotafim", null);

        if (vToken.equals("")) {
            vToken = "B8489FF7205E3217B4E043D70FF9848B";
        }
        logexecucao.Grava("parametros: token=" + vToken + ";codfil=" + vCodFil + ";data=" + vData + ";rotaini=" + vRotaIni
                + vData + ";rotafim=" + vRotaFim, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnEnvio = new JSONObject();
        JSONArray vjnArray = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vRotaIni == null || vRotaIni.equals("")) {
                throw new StatusClientePstServException("Rota Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vRota = "", vHrLargada = "", vHrSaidaBase = "", vMatrMot = "", vNomeMot = "",
                    vHrAcessoMot = "", vHrPontoMot = "", vHrSaiArma = "", vMatrChe = "", vHrPontoChe = "",
                    vNomeChe = "", vHrAcessoChe = "", vMatrVig1 = "", vNomeVig1 = "", vHrAcessoVig1 = "",
                    vHrPontoVig1 = "", vMatrVig2 = "", vNomeVig2 = "", vHrAcessoVig2 = "", vHrPontoVig2 = "",
                    vBancoDados = "";
            Consulta qTmpX;
            vBancoDados = qToken.getBancoDados();

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }

            vSQL = "Select Rotas.Data, Rotas.Rota, Rotas.HrLargada, \n"
                    + "(Select Top 1 HrSaida from Rt_Perc \n"
                    + "where Rt_Perc.Sequencia = Rotas.Sequencia \n"
                    + "  and Rt_Perc.NRed like '%base%' \n"
                    + "  and Rt_Perc.Flag_Excl <> '*' \n"
                    + "  order by Hora1) HrSaidaBase, \n"
                    //+ "  --Motorista\n"
                    + "  Escala.MatrMot, FunMot.Nome NomeMot, \n"
                    + "(Select top 1 SegAcessos.HrEntrada from SegAcessos \n"
                    + "where DtEntrada = Rotas.Data \n"
                    + "  and SegAcessos.CodPessoa = PessoaMot.Codigo) HrAcessoMot, \n"
                    + "(Select top 1 RHPMot.Hora from RHponto RHPMot \n"
                    + " where RHPMot.Matr = Escala.MatrMot \n"
                    + "   and RHPMot.DtBatida = CONVERT(VARCHAR(10),Rotas.Data, 112) \n"
                    + "   and RHPMot.Batida = 1) HrPontoMot, \n"
                    + "(Select Top 1 ArmasMovDia.HrSaida from ArmasMovDia \n"
                    + " where ArmasMovDia.DtSaida = Rotas.Data \n"
                    + "   and ARMASMOVDIA.Matr = Escala.MatrMot) HrSaidaArma, \n"
                    //+ "  --Chefe Equipe\n"
                    + "Escala.MatrChe, FunChe.Nome NomeChe, \n"
                    + "(Select top 1 SegAcessos.HrEntrada from SegAcessos \n"
                    + "where DtEntrada = Rotas.Data \n"
                    + "  and SegAcessos.CodPessoa = PessoaChe.Codigo) HrAcessoChe, \n"
                    + "(Select top 1 RHPChe.Hora from RHponto RHPChe \n"
                    + " where RHPChe.Matr = Escala.MatrChe \n"
                    + "   and RHPChe.DtBatida = CONVERT(VARCHAR(10),Rotas.Data, 112)\n"
                    + "   and RHPChe.Batida = 1) HrPontoChe,\n"
                    //+ "  -- Vigilante 1\n"
                    + "Escala.MatrVig1, FunVig1.Nome NomeVig1, \n"
                    + "(Select top 1 SegAcessos.HrEntrada from SegAcessos \n"
                    + "where DtEntrada = Rotas.Data \n"
                    + "  and SegAcessos.CodPessoa = PessoaVig1.Codigo) HrAcessoVig1, \n"
                    + "(Select top 1 RHPVig1.Hora from RHponto RHPVig1 \n"
                    + " where RHPVig1.Matr = Escala.MatrVig1 \n"
                    + "   and RHPVig1.DtBatida = CONVERT(VARCHAR(10),Rotas.Data, 112) \n"
                    + "   and RHPVig1.Batida = 1) HrPontoVig1,  \n"
                    //+ "  -- Vigilante 2 \n"
                    + "Escala.MatrVig2, FunVig2.Nome NomeVig2, \n"
                    + "(Select top 1 SegAcessos.HrEntrada from SegAcessos \n"
                    + "where DtEntrada = Rotas.Data \n"
                    + "  and SegAcessos.CodPessoa = PessoaVig2.Codigo) HrAcessoVig2, \n"
                    + "(Select top 1 RHPVig2.Hora from RHponto RHPVig2 \n"
                    + " where RHPVig2.Matr = Escala.MatrVig2 \n"
                    + "   and RHPVig2.DtBatida = CONVERT(VARCHAR(10),Rotas.Data, 112) \n"
                    + "   and RHPVig2.Batida = 1) HrPontoVig2 \n"
                    + "    from Rotas \n"
                    + "Left Join Escala on Escala.SeqRota = Rotas.Sequencia \n"
                    + "                and Escala.CoDFil = Rotas.CodFil \n"
                    + "Left Join Funcion FunMot on FunMot.Matr = Escala.MatrMot \n"
                    + "                        and FunMot.Codfil = Escala.CodFil \n"
                    + "Left Join Pessoa PessoaMot on PessoaMot.Matr = FunMot.Matr \n"
                    + "Left Join Funcion FunChe on FunChe.Matr = Escala.MatrChe \n"
                    + "                        and FunChe.Codfil = Escala.CodFil \n"
                    + "Left Join Pessoa PessoaChe on PessoaChe.Matr = FunChe.Matr \n"
                    + "Left Join Funcion FunVig1 on FunVig1.Matr = Escala.MatrVig1 \n"
                    + "                        and FunVig1.Codfil = Escala.CodFil \n"
                    + "Left Join Pessoa PessoaVig1 on PessoaVig1.Matr = FunVig1.Matr \n"
                    + "Left Join Funcion FunVig2 on FunVig2.Matr = Escala.MatrVig2 \n"
                    + "                        and FunVig2.Codfil = Escala.CodFil \n"
                    + "Left Join Pessoa PessoaVig2 on PessoaVig2.Matr = FunVig2.Matr \n"
                    + "where rotas.Data = '" + vData + "' \n"
                    + "  and Rotas.CodFil = " + vCodFil + " \n"
                    + "  and Rotas.Rota >= '" + vRotaIni + "' \n"
                    + " and Rotas.Rota >= '" + vRotaIni + "'"
                    + " and (Select Top 1 HrSaida from Rt_Perc \n"
                    + "where Rt_Perc.Sequencia = Rotas.Sequencia \n"
                    + "  and Rt_Perc.NRed like '%base%' \n"
                    + "  and Rt_Perc.Flag_Excl <> '*' \n"
                    + "  order by Hora1) is not null";
            try {
                qTmpX = new Consulta(vSQL, dbpadrao);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                throw new Exception("rotasEficienciaBase - " + e.getMessage() + "\r\n"
                        + vSQL);
            }

            int vConta = 0;

            while (qTmpX.Proximo()) {
                vConta++;
                vjnEnvio = new JSONObject();
                vRota = qTmpX.getString("Rota");
                vHrLargada = qTmpX.getString("HrLargada");
                vHrSaidaBase = qTmpX.getString("HrSaidaBase");
                vMatrMot = qTmpX.getString("MatrMot");
                vNomeMot = qTmpX.getString("NomeMot");
                vHrAcessoMot = qTmpX.getString("HrAcessoMot");
                vHrPontoMot = qTmpX.getString("HrPontoMot");
                vHrSaiArma = qTmpX.getString("HrSaidaArma");
                vMatrChe = qTmpX.getString("MatrChe");
                vNomeChe = qTmpX.getString("NomeChe");
                vHrAcessoChe = qTmpX.getString("HrAcessoChe");
                vHrPontoChe = qTmpX.getString("HrPontoChe");
                vMatrVig1 = qTmpX.getString("MatrVig1");
                vNomeVig1 = qTmpX.getString("NomeVig1");
                vHrAcessoVig1 = qTmpX.getString("HrAcessoVig1");
                vHrPontoVig1 = qTmpX.getString("HrPontoVig1");
                vMatrVig2 = qTmpX.getString("MatrVig2");
                vNomeVig2 = qTmpX.getString("NomeVig2");
                vHrAcessoVig2 = qTmpX.getString("HrAcessoVig2");
                vHrPontoVig2 = qTmpX.getString("HrPontoVig2");

                vjnEnvio.put("rota", vRota);
                vjnEnvio.put("hrlargada", vHrLargada);
                vjnEnvio.put("hrsaidabase", vHrSaidaBase);
                vjnEnvio.put("matrmot", vMatrMot);
                vjnEnvio.put("nomemot", vNomeMot);
                vjnEnvio.put("hracessomot", vHrAcessoMot);
                vjnEnvio.put("hrpontomot", vHrPontoMot);
                vjnEnvio.put("hrsaidaarma", vHrSaiArma);

                vjnEnvio.put("matrche", vMatrChe);
                vjnEnvio.put("nomeche", vNomeChe);
                vjnEnvio.put("hracessoche", vHrAcessoChe);
                vjnEnvio.put("hrpontoche", vHrPontoChe);

                vjnEnvio.put("matrvig1", vMatrVig1);
                vjnEnvio.put("nomevig1", vNomeVig1);
                vjnEnvio.put("hracessovig1", vHrAcessoVig1);
                vjnEnvio.put("hrpontovig1", vHrPontoVig1);

                vjnEnvio.put("matrvig2", vMatrVig2);
                vjnEnvio.put("nomevig2", vNomeVig2);
                vjnEnvio.put("hracessovig2", vHrAcessoVig2);
                vjnEnvio.put("hrpontovig2", vHrPontoVig2);

                vjnArray.put(vjnEnvio);
            }
            if (vConta == 0) {
                vjnEnvio.put("resposta", "NAO HA DADOS");
                vjnArray.put(vjnEnvio);
            }
            try {
                vjnRetorno.put("rota", vjnArray);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("rotasEficienciaBase - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    public void enviarreg(String vFone, String vImagem, String vArqHTML) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\enviarWhatsApp.txt";
        String vResultado = "", vLink = "", vTokenUtalk = "", vID = "";
        vID = "202203302304";

        vLink = "https://v1.utalk.chat/send/z5ftof3?cmd=chat&id=" + vID + "&to=" + vFone + "@c.us&msg=" + vArqHTML;

        vImagem = "data:image/jpg;base64," + vImagem;

        try {
            //URL vWSsas = new URL("https://mobile.sasw.com.br:9091/recx/frmbiometriaautentica.html");
            //URL vWSsas = new URL("http://localhost:9080/rec/frmFaceAutentica.html");
            URL vWSsas = new URL(vLink);

            URLConnection yc = vWSsas.openConnection();

            //yc.setRequestProperty("Request-Method", "GET");
            yc.setDoInput(true);
            yc.setDoOutput(false);
            yc.connect();
            //yc.ge
            BufferedReader in = new BufferedReader(
                    new InputStreamReader(
                            yc.getInputStream()));

            String inputLine;

            while ((inputLine = in.readLine()) != null) {
                System.out.println(inputLine);
                vResultado = vResultado + inputLine;
            }
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do JS" + e.getMessage(), caminho);
        }
        vRetorno = "A resposta é: " + vResultado;
    }

    public String obterTokenSLU(String vClientID, String vClientKey, String vEmail) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\obterTpkenSLU.txt";
        String vTokenSLU = "";
        vClientID = "Satellite";
        vClientKey = "b0c54429-ec77-4c49-b307-2fa88fd773fc";
        vEmail = "<EMAIL>";
        // Definir a URL do Web Service                        

        try {
            // Comando PowerShell que você deseja executar            
            String vArquivo = "$headers = New-Object \"System.Collections.Generic.Dictionary[[String],[String]]\"\n"
                    + "$headers.Add(\"Content-Type\", \"application/json\")\n"
                    + "\n"
                    + "$body = \"{`\"clientId`\": `\"Satellite`\", `\"clientKey`\":`\"b0c54429-ec77-4c49-b307-2fa88fd773fc`\"}\"\n"
                    + "\n"
                    + "$response = Invoke-RestMethod 'https://api.slu.df.gov.br/rcc/login' -Method 'POST' -Headers $headers -Body $body\n"
                    + "$response | ConvertTo-Json";

            // Caminho para o arquivo do script PowerShell
            String scriptPath = "C:\\Clientes\\executar.ps1";
            File file = new File(scriptPath);
            if (file.exists()) {
                file.delete();
            }
            FileWriter vArq = new FileWriter(scriptPath);
            PrintWriter gravarArq = new PrintWriter(vArq);

            gravarArq.printf(vArquivo);
            vArq.close();

            // Cria o ProcessBuilder com o comando para executar o script
            ProcessBuilder processBuilder = new ProcessBuilder("powershell.exe", "-File", scriptPath);

            // Redireciona a saída do processo para ler a resposta
            processBuilder.redirectErrorStream(true);

            // Inicia o processo
            Process process = processBuilder.start();

            // Lê a saída do processo
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                vRetorno = vRetorno + line;
            }

            vRetorno = vRetorno.replace("null", "");
            try {
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(vRetorno).getAsJsonObject();
                this.logerro.Grava(vRetorno, caminho);
                if (!jsonObject.get("token").getAsString().equals(null)) {
                    vRetorno = jsonObject.get("token").getAsString();
                } else {
                    vRetorno = vRetorno;
                }
            } catch (JSONException e) {
                this.logerro.Grava(e.toString(), caminho);
            }
            // Aguarda o término do processo
            int exitCode = process.waitFor();
            //vRetorno = vRetorno + " - Processo encerrado com código de saída: " + exitCode;

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        //URL url = new URL("https://api.slu.df.gov.br/rcc/login");        
        return vRetorno;
    }

    public void buscarPessoaSLUGet(String vToken, String vCPFCNPJ) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\buscarPessoaSLUGet.txt";
        String vTokenSLU = "";

        try {
            //URL obj = new URL("https://wst-slu-brasilia.ercc.com.br/ctr/solicitar?material="+vMaterial+
            //                  "&tipo_transporte="+vTipoTransp+"&veiculo_placa="+vPlaca+"&cacamba_numero="+vCacamba+"&pessoa[email]="+vEmail);            

            URL url = new URL("https://rcc.slu.df.gov.br/api/v2/pessoa/findbycpfcnpj?cpfCnpj=" + vCPFCNPJ + "&perfis=0");
            HttpURLConnection http = (HttpURLConnection) url.openConnection();
            http.setRequestProperty("Accept", "application/json");
            http.setRequestProperty("Authorization", "Bearer " + vToken);

            System.out.println(http.getResponseCode() + " " + http.getResponseMessage());
            http.disconnect();

            //con.setRequestProperty("Content-Type", "application/json");            
            //this.logerro.Grava("https://wst-slu-brasilia.ercc.com.br/ctr/solicitar?material="+vMaterial+
//                              "&tipo_transporte="+vTipoTransp+"&veiculo_placa="+vPlaca+"&cacamba_numero="+
            //                 vCacamba+"&pessoa[email]="+vEmail, caminho);
            this.logerro.Grava("Parametros:" + "cpfCnpj=" + vCPFCNPJ + "&perfis=0 REsposta" + http.getResponseMessage(), caminho);

            // For POST only - START
            // For POST only - END
            int responseCode = http.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) { //success
                BufferedReader in = new BufferedReader(new InputStreamReader(http.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject().getAsJsonObject("response");
                this.logerro.Grava(response.toString(), caminho);
                vRetorno = response.toString();//jsonObject.get("ctr").getAsString();
                //String tokenId = jsonObject.get("token").getAsString();

            } else {
                BufferedReader in = new BufferedReader(new InputStreamReader(http.getErrorStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

                // print result
                this.logerro.Grava(response.toString() + " Resposta" + http.getResponseCode(), caminho);
                vRetorno = response.toString();
            }
            vRetorno = "A resposta é: " + http.getResponseCode();
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do WS" + e.getMessage(), caminho);
        }
    }

    public void solicitarServicoSLU(String vToken, String vMaterial, String vTipoTransp, String vPlaca, String vCacamba, String vEmail) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\solicitarServicoSLU.txt";
        String vTokenSLU = "";
        vEmail = "<EMAIL>";

        try {
            //URL obj = new URL("https://wst-slu-brasilia.ercc.com.br/ctr/solicitar?material="+vMaterial+
            //                  "&tipo_transporte="+vTipoTransp+"&veiculo_placa="+vPlaca+"&cacamba_numero="+vCacamba+"&pessoa[email]="+vEmail);
            URL obj = new URL("https://wst-slu-brasilia.ercc.com.br/ctr/solicitar");
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();

            String boundary = Long.toString(System.currentTimeMillis());

            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("Accept", "application/json");
            con.setRequestProperty("Method", "POST");
            con.setRequestProperty("Authorization", vToken);

            //this.logerro.Grava("https://wst-slu-brasilia.ercc.com.br/ctr/solicitar?material="+vMaterial+
//                              "&tipo_transporte="+vTipoTransp+"&veiculo_placa="+vPlaca+"&cacamba_numero="+
            //                 vCacamba+"&pessoa[email]="+vEmail, caminho);
            this.logerro.Grava("{\"material\": \"" + vMaterial + "\",\"tipo_transporte\": \"" + vTipoTransp + "\",\"veiculo_placa\": \"" + vPlaca
                    + "\",\"cacamba_numero\": \"" + vCacamba
                    + "\",\"pessoa[email]\": \"" + vEmail + "\"}", caminho);

            // For POST only - START
            con.setDoOutput(true);
            OutputStream os = con.getOutputStream();

            //os.write(("Content-Disposition: form-data; "+"{\"material\": \""+vMaterial+"\",\"tipo_transporte\": \""+vTipoTransp+"\",\"veiculo_placa\": \""+vPlaca+
            //       "\",\"cacamba_numero\": \""+vCacamba+
            //       "\",\"pessoa[email]\": \""+vEmail+"\"}").getBytes("UTF-8"));
            os.write(("{").getBytes("UTF-8"));
            os.write(("				\"mode\": \"formdata\",").getBytes("UTF-8"));
            os.write(("  		\"formdata\": [").getBytes("UTF-8"));
            os.write(("				{").getBytes("UTF-8"));
            os.write(("				\"key\": \"material\",").getBytes("UTF-8"));
            os.write(("				\"value\": \"" + vMaterial + "\"").getBytes("UTF-8"));
            os.write(("				},").getBytes("UTF-8"));
            os.write(("				{").getBytes("UTF-8"));
            os.write(("				\"key\": \"tipo_transporte\",").getBytes("UTF-8"));
            os.write(("				\"value\": \"" + vTipoTransp + "\"").getBytes("UTF-8"));
            os.write((" 			},").getBytes("UTF-8"));
            os.write(("			        {").getBytes("UTF-8"));
            os.write(("				\"key\": \"veiculo_placa\",").getBytes("UTF-8"));
            os.write(("				\"value\": \"" + vPlaca + "\"").getBytes("UTF-8"));
            os.write(("			        },").getBytes("UTF-8"));
            os.write(("			        {").getBytes("UTF-8"));
            os.write(("				\"key\": \"cacamba_numero\",").getBytes("UTF-8"));
            os.write(("				\"value\": \"" + vCacamba + "\"").getBytes("UTF-8"));
            os.write(("  			},").getBytes("UTF-8"));
            os.write(("		        	{").getBytes("UTF-8"));
            os.write(("				\"key\": \"pessoa[email]\",").getBytes("UTF-8"));
            os.write(("				\"value\": \"" + vEmail + "\"").getBytes("UTF-8"));
            os.write(("			        }").getBytes("UTF-8"));
            os.write(("		     ]").getBytes("UTF-8"));
            os.write(("	}").getBytes("UTF-8"));
            /*
            os.write(("tipo_transporte="+vTipoTransp).getBytes("UTF-8"));
            os.write(("veiculo_placa="+vPlaca).getBytes("UTF-8"));
            os.write(("cacamba_numero="+vCacamba).getBytes("UTF-8"));
            os.write(("pessoa[email]="+vEmail).getBytes("UTF-8"));
             */
            this.logerro.Grava(os.toString(), caminho);

            os.flush();
            os.close();
            // For POST only - END

            int responseCode = con.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) { //success
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject().getAsJsonObject("response");
                this.logerro.Grava(response.toString(), caminho);
                vRetorno = response.toString();//jsonObject.get("ctr").getAsString();
                //String tokenId = jsonObject.get("token").getAsString();

            } else {
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getErrorStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

                // print result
                this.logerro.Grava(response.toString(), caminho);
                vRetorno = response.toString();
            }
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do WS" + e.getMessage(), caminho);
        }
        //vRetorno = "A resposta é: " + vResultado;
    }

    public void solicitarServicoSLUN(String vToken, String vMaterial, String vIDObra, String vCodVeiculo, String vCacamba, String vCodGerador, String vGuia, String vSerie) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\solicitarServicoSLUN.txt";
        String vTokenSLU = "";
        //vEmail = "<EMAIL>";
        try {

            URL url = new URL("https://api.slu.df.gov.br/rcc/ctr/");
            String vArquivo;
            vArquivo = "$headers = New-Object \"System.Collections.Generic.Dictionary[[String],[String]]\"\n"
                    + "$headers.Add(\"Authorization\", \"Bearer" + vToken + "\")\n"
                    + "\n"
                    + "$headers.Add(\"Content-Type\", \"text/plain\")\n"
                    + "\n"
                    + "$body = \"{\n"
                    + "`n  `\"idResiduoPredominante`\": " + vMaterial + ",\n"
                    + "`n  `\"idGerador`\": " + vCodGerador + ",\n"
                    + "`n  `\"idVeiculo`\": " + vCodVeiculo + ",\n"
                    + "`n  `\"idCacamba`\": " + vCacamba + ",\n"
                    + "`n  `\"idObra`\": " + vIDObra + "\n"
                    + "`n}\"\n"
                    + "\n"
                    + "$response = Invoke-RestMethod 'https://api.slu.df.gov.br/rcc/ctr' -Method 'POST' -Headers $headers -Body $body\n"
                    + "$response | ConvertTo-Json";
            String scriptPath = "c:\\Clientes\\ReqCtr.ps1";
            File file = new File(scriptPath);
            if (file.exists()) {
                file.delete();
            }
            FileWriter vArq = new FileWriter(scriptPath);
            PrintWriter gravarArq = new PrintWriter(vArq);

            gravarArq.printf(vArquivo);
            vArq.close();

            ProcessBuilder processBuilder = new ProcessBuilder("powershell.exe", "-File", scriptPath);

            // Redireciona a saída do processo para ler a resposta
            processBuilder.redirectErrorStream(true);

            // Inicia o processo
            Process process = processBuilder.start();

            // Lê a saída do processo
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                vRetornoCtr = vRetornoCtr + line;
            }
            this.logerro.Grava(vRetornoCtr, caminho);
            vRetornoCtr = vRetornoCtr.replace("null", "");
            vRetornoCtr = vRetornoCtr.replace("{    \"count\":  1,    \"rows\":  [                 ", "");
            vRetornoCtr = vRetornoCtr.replace("             ]}", "");
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into SLUIntegra(Sequencia, Guia, Serie, TipoOperacao, ParamOperacao, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from SLUIntegra), \n"
                    + vGuia + ", \n"
                    + "'" + vSerie + "', \n"
                    + "'solicitarServicoSLUN', \n"
                    + "'" + vArquivo.replace("'", "") + "', \n"
                    + "'" + vRetornoCtr.replace("'", "") + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATECOVISAO"));
            SQLPdrLog.insert();

            this.logerro.Grava("RetornoLimpo:" + vRetornoCtr, caminho);
            String vSQLExec = "";
            String vSQL;
            String vCodCidade = "5300108";
            String vStatus = "1";
            Consulta SQLPdr;
            Persistencia dbpadrao;
            dbpadrao = this.pool.getConexao("SATECOVISAO");
            if (!vRetornoCtr.contains("Error") && !vRetornoCtr.contains("error") && !vRetornoCtr.contains("atrelada")) {
                try {
                    JsonParser jsonParser = new JsonParser();
                    JsonObject jsonObject = jsonParser.parse(vRetornoCtr).getAsJsonObject();

                    if (!jsonObject.get("id").getAsString().equals(null)) {
                        vRetornoCtr = jsonObject.get("id").getAsString();
                        //Grava na tabela CTR
                        try {
                            vSQLExec = "insert into Ctr(Guia, Serie, CodCidade, CTR, Data_Emis, Hora_Emis, Status, Operador, Dt_Alter, Hr_Alter) Values ( \n"
                                    + vGuia + ", \n"
                                    + "'" + vSerie + "'" + ", \n"
                                    + "'" + vCodCidade + "', \n"
                                    + vRetornoCtr + ", \n"
                                    + "'" + getDataAtual("SQL") + "', \n"
                                    + "'" + getDataAtual("HORA") + "',\n"
                                    + vStatus + ", \n"
                                    + "'SATSERVER', \n"
                                    + "'" + getDataAtual("SQL") + "', \n"
                                    + "'" + getDataAtual("HORA") + "')\n";
                            SQLPdr = new Consulta(vSQLExec, dbpadrao);
                            SQLPdr.insert();
                        } catch (Exception e) {
                            logexecucao.Grava("Erro SQL: " + vSQLExec, caminho);
                            throw new Exception("CTR.Alocar - " + e.getMessage() + "\r\n"
                                    + vSQLExec);
                        }
                        SQLPdr.close();
                        alocarServicoSLUUN(vToken, vRetornoCtr, vIDObra, vCodVeiculo, vCacamba);
                    } else {
                        vRetornoCtr = vRetornoCtr;
                    }
                } catch (JSONException e) {
                    this.logerro.Grava(e.toString(), caminho);
                }
            } else {
                vSQLLog = "Insert into SLUIntegra(Sequencia, Guia, Serie, TipoOperacao, ParamOperacao, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from SLUIntegra), \n"
                        + vGuia + ", \n"
                        + "'" + vSerie + "', \n"
                        + "'solicitarServicoSLUN', \n"
                        + "'" + vArquivo.replace("'", "") + "', \n"
                        + "'" + "CACAMBA ATRELADA A OUTRO CTR" + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATECOVISAO"));
                SQLPdrLog.insert();

            }
            this.logerro.Grava("Parametros:" + "CTR=" + vCacamba + " REsposta" + vRetornoCtr, caminho);
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do WS" + e.getMessage(), caminho);
        }

    }

    public void buscarPessoaSLU(String vToken, String vCPFCNPJ) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\buscarPessoaSLU.txt";

        try {
            URL url = new URL("https://rcc.slu.df.gov.br/api/v2/pessoa/findbycpfcnpj");

            HttpURLConnection con = (HttpURLConnection) url.openConnection();

            con.setRequestMethod("GET");
            con.setRequestProperty("Authorization", "Bearer " + vToken);
            //con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("Accept", "application/json");
            con.setDoOutput(true);
            String vDataHora = "", vDataHoraEnvio = "", vObs = "";
            vDataHora = DataAtual.getDataAtual("SQL") + " " + DataAtual.getDataAtual("HORASEGUNDOS");
            vDataHoraEnvio = DataAtual.getDataAtual("SQL") + " " + DataAtual.getDataAtual("HORASEGUNDOS");

            String jsonInputString = "cpfCnpj=" + vCPFCNPJ + "&perfis=0";
            try ( OutputStream os = con.getOutputStream()) {
                byte[] input = jsonInputString.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            try ( BufferedReader br = new BufferedReader(
                    new InputStreamReader(con.getInputStream(), "utf-8"))) {
                StringBuilder response = new StringBuilder();
                String responseLine = null;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                String vCTRresult = "";
                String vRet = response.toString();
                try {
                    JsonParser jsonParser = new JsonParser();
                    JsonObject jsonObject = jsonParser.parse(vRet).getAsJsonObject();
                    this.logerro.Grava(vRet, caminho);
                    if (!jsonObject.get("CpfCNPJ").getAsString().equals(null)) {
                        vRetornoPessoa = "CPF_CNPJ:" + jsonObject.get("CpfCNPJ").getAsString();
                        vRetornoPessoa = "NomeRazaoSocial:" + jsonObject.get("nomeRazaoSocial").getAsString();
                    } else {
                        vRetornoPessoa = vRet;
                    }
                } catch (JSONException e) {
                    this.logerro.Grava(e.toString(), caminho);
                }
            }
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do WS" + e.getMessage(), caminho);
        }

    }

    public void transportarServicoSLU(String vToken, String vCTR, String vPlaca, String vCNPJ) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\transportarServicoSLU.txt";
        String vResultado = "", vTokenUtalk = "";

        try {

        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do JS" + e.getMessage(), caminho);
        }
        vRetorno = "A resposta é: " + vResultado;
    }

    public void alocarServicoSLUUN(String vToken, String vCTR, String vLocal, String vCodVeiculo, String vCacamba) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {

        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\alocarSLUUN.txt";
        String vTokenSLU = "";
        //vEmail = "<EMAIL>";
        try {

            URL url = new URL("https://api.slu.df.gov.br/rcc/ctr/");
            String vArquivo;
            vArquivo = "$headers = New-Object \"System.Collections.Generic.Dictionary[[String],[String]]\"\n"
                    + "$headers.Add(\"Authorization\", \"Bearer " + vToken + "\")\n"
                    + "$headers.Add(\"Content-Type\", \"text/plain\")\n"
                    + "\n"
                    + "$body = \"{\n"
                    + "`n  `\"idVeiculo`\": " + vCodVeiculo + ",\n"
                    + "`n  `\"idCacamba`\": " + vCacamba + "\n"
                    + "`n}\"\n"
                    + "\n"
                    + "$response = Invoke-RestMethod 'https://api.slu.df.gov.br/rcc/ctr/alocar_cacamba/" + vCTR + "' -Method 'PUT' -Headers $headers -Body $body\n"
                    + "$response | ConvertTo-Json";
            String scriptPath = "c:\\Clientes\\ReqAloc.ps1";
            File file = new File(scriptPath);
            if (file.exists()) {
                file.delete();
            }
            FileWriter vArq = new FileWriter(scriptPath);
            PrintWriter gravarArq = new PrintWriter(vArq);

            gravarArq.printf(vArquivo);
            vArq.close();

            ProcessBuilder processBuilder = new ProcessBuilder("powershell.exe", "-File", scriptPath);

            // Redireciona a saída do processo para ler a resposta
            processBuilder.redirectErrorStream(true);

            // Inicia o processo
            Process process = processBuilder.start();

            // Lê a saída do processo
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                vRetornoAlocar = vRetornoAlocar + line;
            }
            this.logerro.Grava("Alocar:" + vRetornoAlocar, caminho);
            vRetornoAlocar = vRetornoAlocar.replace("null", "");
            vRetornoAlocar = vRetornoAlocar.replace("{    \"count\":  1,    \"rows\":  [                 ", "");
            vRetornoAlocar = vRetornoAlocar.replace("             ]}", "");
            this.logerro.Grava("RetornoLimpo Alocar:" + vRetornoAlocar, caminho);
            try {
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(vRetornoAlocar).getAsJsonObject();

                if (!jsonObject.get("success").getAsString().equals(null)) {
                    vRetornoAlocar = jsonObject.get("success").getAsString();
                } else {
                    vRetornoAlocar = vRetornoAlocar;
                }
            } catch (JSONException e) {
                this.logerro.Grava(e.toString(), caminho);
            }

            this.logerro.Grava("Parametros:" + "CTR=" + vCacamba + " REsposta" + vRetornoCtr, caminho);
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do WS" + e.getMessage(), caminho);
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/cisensores")
    public Response cisensores(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\cisensores.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));
        String vToken;
        String vCodEquip;
        String vMarca;
        String vStatus;

        vToken = (String) parametros.getOrDefault("token", null);
        vCodEquip = (String) parametros.getOrDefault("codequip", null);
        vMarca = (String) parametros.getOrDefault("marca", null);
        vStatus = (String) parametros.getOrDefault("status", null);

        if (vToken.equals("")) {
            vToken = "B8489FF7205E3217B4E043D70FF9848B";
        }
        logexecucao.Grava("parametros: token=" + vToken
                + ";codequip=" + vCodEquip
                + ";marca=" + vMarca
                + ";status=" + vStatus, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnEnvio = new JSONObject();
        JSONArray vjnArray = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vCodEquip == null || vCodEquip.equals("")) {
                throw new StatusClientePstServException("Cofre Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vRetorno = "", vDt_envio = "", vHr_envio = "", vDt_retorno = "",
                    vHr_retorno = "", vBancoDados = "";
            Consulta qTmpX;
            vBancoDados = qToken.getBancoDados();

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }

            vSQL = "Select * from CiSensores \n"
                    + " where CodEquip = " + vCodEquip
                    + "   and Marca = '" + vMarca + "'";
            try {
                qTmpX = new Consulta(vSQL, dbpadrao);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                throw new Exception("cisensores - " + e.getMessage() + "\r\n"
                        + vSQL);
            }

            int vConta = 0;

            while (qTmpX.Proximo()) {
                vConta++;
                vjnEnvio = new JSONObject();
                vStatus = qTmpX.getString("Status");
                vjnEnvio.put("status", vStatus);

                vjnArray.put(vjnEnvio);
            }
            if (vConta == 0) {
                vjnEnvio.put("resposta", "NAO HA DADOS");
                vjnArray.put(vjnEnvio);
            }
            try {
                vjnRetorno.put("gtve", vjnArray);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("cisensores - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/cistatus")
    public Response cistatus(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\cistatus.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));
        String vToken;
        String vCodEquip;
        String vMarca;
        Integer vSequencia;
        Integer vSeqNew = 0;
        String vStatus = "", vData = "", vHora = "",
                vTransacao = "", vRetorno = "", vDt_Status = "", vHr_Status = "";

        vToken = (String) parametros.getOrDefault("token", null);
        vCodEquip = (String) parametros.getOrDefault("codequip", null);
        vMarca = (String) parametros.getOrDefault("marca", null);
        vTransacao = (String) parametros.getOrDefault("Transacao", null);
        vSequencia = (Integer) parametros.getOrDefault("Sequencia", null);
        vStatus = "PD";

        if (vToken.equals("")) {
            vToken = "B8489FF7205E3217B4E043D70FF9848B";
        }
        logexecucao.Grava("parametros: token=" + vToken
                + ";codequip=" + vCodEquip
                + ";marca=" + vMarca
                + ";status=" + vStatus, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnEnvio = new JSONObject();
        JSONArray vjnArray = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vCodEquip == null || vCodEquip.equals("")) {
                throw new StatusClientePstServException("Cofre Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vDt_envio = "", vHr_envio = "", vDt_retorno = "",
                    vHr_retorno = "", vBancoDados = "";
            Consulta qTmpX;
            vBancoDados = qToken.getBancoDados();

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }
            int vConta = 0;
            if (vSequencia == 0) {
                Consulta SQLPdr;
                Consulta qTmpX2;
                vSQLExec = "insert into CIStatus(Sequencia, CodEquip, Marca, Data, Hora, Transacao, Retorno, Status, Dt_Status, Hr_Status) Values(\n"
                        + "Select Max(Sequencia) from CIStatus, \n"
                        + "'" + vCodEquip + "', \n"
                        + "'" + vMarca + "', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "', \n"
                        + "'" + vTransacao + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'PD', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')\n";
                SQLPdr = new Consulta(vSQLExec, dbpadrao);
                SQLPdr.insert();
                SQLPdr.Close();

                vSQL = "Select Sequencia from CIStatus \n"
                        + " where CodEquip = " + vCodEquip
                        + "   and Marca = '" + vMarca + "'"
                        + "   and Situacao = 'PD'";
                try {
                    qTmpX2 = new Consulta(vSQL, dbpadrao);
                    qTmpX2.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("cisensores - " + e.getMessage() + "\r\n"
                            + vSQL);
                }

                while (qTmpX2.Proximo()) {
                    vConta++;
                    vjnEnvio = new JSONObject();
                    vStatus = qTmpX2.getString("Sequencia");
                    vjnEnvio.put("sequencia", vStatus);
                    vjnArray.put(vjnEnvio);
                    vSeqNew = qTmpX2.getInt("Sequencia");
                }
            } else {
                vSQL = "Select * from CIStatus \n"
                        + " where CodEquip = " + vCodEquip
                        + "   and Marca = '" + vMarca + "'";
                try {
                    qTmpX = new Consulta(vSQL, dbpadrao);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("cisensores - " + e.getMessage() + "\r\n"
                            + vSQL);
                }
                while (qTmpX.Proximo()) {
                    vConta++;
                    vjnEnvio = new JSONObject();
                    vStatus = qTmpX.getString("Status");
                    vjnEnvio.put("status", vStatus);

                    vjnArray.put(vjnEnvio);
                }
            }
            if (vConta == 0) {
                vjnEnvio.put("resposta", "NAO HA DADOS");
                vjnArray.put(vjnEnvio);
            }
            try {
                vjnRetorno.put("gtve", vjnArray);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("cisensores - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    //Unirest.setTimeouts(0, 0);
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/enviarmensagensws")
    public Response enviarmensagensws(String param) throws UnsupportedEncodingException, IOException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vServico = (String) parametros.getOrDefault("servico", null);
        String vToken = (String) parametros.getOrDefault("token", null);
        String vDestino = (String) parametros.getOrDefault("destino", null);
        String vMensagem = (String) parametros.getOrDefault("mensagem", null);
        String vArquivo = (String) parametros.getOrDefault("arquivo", null);

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnNumero = new JSONObject();

        try {
            if (vServico.equals("whatsapp")) {
                enviarMsgWhatsapp(vToken, vDestino, vArquivo, vMensagem);
                String vStatus = vRetorno;
                vjnNumero.put("Telefone", vDestino);
                vjnRetorno.put("resposta", vRetorno);
            }

        } finally {
            //vjnRetorno.put("resposta", vjnPessoaFoto);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/consultaPonto")
    public String consultaPonto(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\consultaPonto.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vMatr = (String) parametros.getOrDefault("matr", null); //Obrigatorio
        String vToken = (String) parametros.getOrDefault("token", null);
        //String vPW = (String) parametros.getOrDefault("pw", null);
        String vDtIni = (String) parametros.getOrDefault("dataini", null);
        String vDtFim = (String) parametros.getOrDefault("datafim", null);
        logexecucao.Grava("parametros: Matr=" + vMatr + ";Token=" + vToken + ";dataini=" + vDtIni + ";datafinal= " + vDtFim, caminho);
        Persistencia dbpadrao, dbsatellite;
        String vArqLogo;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "";
        String vPathWeb = "";
        String vPathLocal = "";
        String vPathFotoPonto = "";
        String vPathFotoPontoLocal = "";
        String vData = "";

        try {
            //Validando se a matrícula informada não está vazia
            if (vMatr == null || vMatr.equals("")) {
                throw new StatusClientePstServException("MatriculaInvalida");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            String vBancoDados = qToken.getBancoDados();
            dbpadrao = this.pool.getConexao(vBancoDados);
            Consulta qTmpX;
            vArqLogo = getLogo(dbpadrao.getEmpresa(), "0");

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }

            if (vDtIni.equals("") || vDtFim.length() != 8) {
                vData = "20220201"; //vData = getDataAtual("SQL");
            } else {
                vData = vDtIni;
            }

            vPathWeb = "https://mobile.sasw.com.br:9091/satellite/fotos/" + vBancoDados + "/_F_ID/";
            vPathLocal = "C:\\xampp\\htdocs\\satellite\\fotos\\" + vBancoDados + "\\_F_ID\\";
            vPathFotoPonto = "https://mobile.sasw.com.br:9091/satellite/fotos/" + vBancoDados
                    + "/ponto/" + vData + "/";
            vPathFotoPontoLocal = "C:\\xampp\\htdocs\\satellite\\fotos\\" + vBancoDados + "\\ponto\\" + vData + "\\";
            vMatr.replace(".0", "");
            if (vMatr.length() == 1) {
                vMatr = "0000000" + vMatr;
            } else if (vMatr.length() == 2) {
                vMatr = "000000" + vMatr;
            } else if (vMatr.length() == 3) {
                vMatr = "00000" + vMatr;
            } else if (vMatr.length() == 4) {
                vMatr = "0000" + vMatr;
            } else if (vMatr.length() == 5) {
                vMatr = "000" + vMatr;
            } else if (vMatr.length() == 6) {
                vMatr = "00" + vMatr;
            } else if (vMatr.length() == 7) {
                vMatr = "0" + vMatr;
            }

            try {

                vSQL = " Select Pessoa.Codigo, Pessoa.Nome, Pessoa.Matr,  \n"
                        + " PessoaDet.FaceID, PessoaDet.Foto1, PessoaDet.Foto2, PessoaDet.Foto3, RH_Horas.Data DtBatida,  \n"
                        + "RH_Horas.Hora1,\n"
                        + "RH_Horas.Hora2,\n"
                        + "RH_Horas.Hora3,\n"
                        + "RH_Horas.Hora4, \n"
                        + " Pessoa.Fone1, Pessoa.Email, Funcion.Cargo, Cargos.Descricao, PstServ.Secao, PstServ.Local, \n"
                        + " Filiais.Descricao FilialDesc, FaceAutentica.Codigo ChaveBio, RHHorario.Descricao DescHorario, \n"
                        + "Filiais.RazaoSocial, RH_Ctrl.*, Filiais.Endereco EnderecoFilial, Filiais.Bairro BairroFil, \n"
                        + "Filiais.Cidade CidadeFil, Filiais.UF UFFil, Filiais.CEP CEPFil, Filiais.CNPJ CNPJFil, Funcion.CodFil, \n"
                        + " RH_Horas.Situacao SituacaoHR, RH_horas.HSdiurnas, RH_horas.HSNoturnas, RH_horas.HsIntraj, \n"
                        + " Isnull(RH_horas.HE50,0) HE50D, RH_horas.HE100 HE100D, RH_horas.HE50I, RH_horas.HE100I \n"
                        + "from Pessoa \n"
                        + " Left Join PessoaDet on PessoaDet.Codigo = Pessoa.Codigo \n"
                        + " Left Join Funcion on Funcion.Matr = Pessoa.Matr \n"
                        + " Left Join PstServ on PstServ.Secao =  Funcion.Secao \n"
                        + "                  and PstServ.CodFil = Funcion.CodFil \n"
                        + " Left Join Cargos on Cargos.Cargo = Funcion.Cargo \n"
                        + " Left Join Filiais on Filiais.Codfil = Funcion.CodFil "
                        + " Left Join RH_Horas on RH_Horas.Matr = Funcion.Matr \n"
                        + "                   and RH_Horas.data >= '" + vData + "' \n"
                        + "                   and RH_Horas.data <= '" + vDtFim + "' \n"
                        + " Left Join FaceAutentica on  FaceAutentica.CodPessoa = Pessoa.Codigo \n"
                        + " Left Join RHHorario on RHHorario.Codigo = Funcion.Horario \n"
                        + "                     and  RHHorario.CodFil = Funcion.CodFil \n"
                        + " Left Join RH_Ctrl on RH_Ctrl.Matr = Funcion.Matr \n"
                        + "                   and RH_Ctrl.CodFil = Funcion.CodFil \n"
                        + " where Pessoa.matr = " + vMatr + "\n"
                        + "                   and RH_Ctrl.Dt_Ini >= '" + vDtIni + "' \n"
                        + "                   and RH_Ctrl.Dt_Fim <= '" + vDtFim + "' \n"
                        + "                   and Dt_UltAcPortal is not null "
                        + " order by DtBatida ";
                try {
                    qTmpX = new Consulta(vSQL, dbpadrao);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("FuncionPessoa.obterFotoPw - " + e.getMessage() + "\r\n"
                            + vSQL);
                }
                JSONObject vjnPessoaFoto;
                JSONArray vjnPessoaData = new JSONArray();
                int vConta = 0;
                //if (qTmpX.getString("Matr") != "") {
                //.Proximo();
                //qTmpX.                
                while (qTmpX.Proximo()) {
                    vConta = ++vConta;

                    if (!qTmpX.getString("Matr").equals(null)) {

                        try {
                            vjnPessoaFoto = new JSONObject();
                            vjnPessoaFoto.put("matr", qTmpX.getString("Matr"));
                            vjnPessoaFoto.put("nome", qTmpX.getString("Nome"));
                            vjnPessoaFoto.put("cargo", qTmpX.getString("Cargo"));
                            vjnPessoaFoto.put("cargodescricao", qTmpX.getString("Descricao"));
                            vjnPessoaFoto.put("filialdesc", qTmpX.getString("FilialDesc"));
                            vjnPessoaFoto.put("secao", qTmpX.getString("Secao"));
                            vjnPessoaFoto.put("posto", qTmpX.getString("Local"));
                            vjnPessoaFoto.put("fone", qTmpX.getString("Fone1"));
                            vjnPessoaFoto.put("email", qTmpX.getString("Email"));
                            vjnPessoaFoto.put("data", qTmpX.getString("DtBatida"));
                            vjnPessoaFoto.put("arqlogo", vArqLogo);
                            vjnPessoaFoto.put("chavebio", "");

                            if (!qTmpX.getString("Hora1").equals("")) {
                                vjnPessoaFoto.put("hora1", qTmpX.getString("Hora1"));
                                File vFotoBatida1 = new File(vPathFotoPontoLocal + vMatr + "_1.jpg");
                                if (vFotoBatida1.exists()) {
                                    vjnPessoaFoto.put("fotobatida1", vPathFotoPonto + vMatr + "_1.jpg");
                                } else {
                                    vjnPessoaFoto.put("fotobatida1", "");
                                }
                            } else {
                                vjnPessoaFoto.put("hora1", "");
                            }

                            if (!qTmpX.getString("Hora2").equals("")) {
                                vjnPessoaFoto.put("hora2", qTmpX.getString("Hora2"));
                                File vFotoBatida2 = new File(vPathFotoPontoLocal + vMatr + "_2.jpg");
                                if (vFotoBatida2.exists()) {
                                    vjnPessoaFoto.put("fotobatida2", vPathFotoPonto + vMatr + "_2.jpg");
                                } else {
                                    vjnPessoaFoto.put("fotobatida2", "");
                                }
                            } else {
                                vjnPessoaFoto.put("hora2", "");
                            }

                            if (!qTmpX.getString("Hora3").equals("")) {
                                vjnPessoaFoto.put("hora3", qTmpX.getString("Hora3"));
                                File vFotoBatida3 = new File(vPathFotoPontoLocal + vMatr + "_3.jpg");
                                if (vFotoBatida3.exists()) {
                                    vjnPessoaFoto.put("fotobatida3", vPathFotoPonto + vMatr + "_3.jpg");
                                } else {
                                    vjnPessoaFoto.put("fotobatida3", "");
                                }
                            } else {
                                vjnPessoaFoto.put("hora3", "");
                            }

                            if (!qTmpX.getString("Hora4").equals("")) {
                                vjnPessoaFoto.put("hora4", qTmpX.getString("Hora4"));
                                File vFotoBatida4 = new File(vPathFotoPontoLocal + vMatr + "_4.jpg");
                                if (vFotoBatida4.exists()) {
                                    vjnPessoaFoto.put("fotobatida4", vPathFotoPonto + vMatr + "_4.jpg");
                                } else {
                                    vjnPessoaFoto.put("fotobatida4", "");
                                }
                            } else {
                                vjnPessoaFoto.put("hora4", "");
                            }
                            if (qTmpX.getString("Foto1").equals("") || qTmpX.getString("Foto2").equals("")) {
                                vjnPessoaFoto.put("nfoto1", "NAO REGISTRADO");
                                vjnPessoaFoto.put("nfoto2", "NAO REGISTRADO");
                                vjnPessoaFoto.put("nfoto3", "NAO REGISTRADO");
                                vjnPessoaFoto.put("faceid", "NAO REGISTRADO");
                                vjnPessoaFoto.put("imgfoto", "NAO REGISTRADO");
                            } else {
                                vjnPessoaFoto.put("nfoto1", qTmpX.getString("Foto1"));

                                vjnPessoaFoto.put("nfoto2", qTmpX.getString("Foto2"));
                                vjnPessoaFoto.put("nfoto3", qTmpX.getString("Foto3"));
                                vjnPessoaFoto.put("faceid", vPathWeb + qTmpX.getString("Foto1"));
                                vjnPessoaFoto.put("imgfoto", vPathLocal + qTmpX.getString("Foto1"));
                            }
                            vjnPessoaFoto.put("codigo", qTmpX.getString("Codigo"));
                            vjnPessoaFoto.put("deschorario", qTmpX.getString("deschorario"));
                            vjnPessoaFoto.put("horastrab", qTmpX.getString("HorasTrab"));
                            vjnPessoaFoto.put("horasextr", qTmpX.getString("HorasExtr"));
                            vjnPessoaFoto.put("hsneg", qTmpX.getString("HsNeg"));
                            vjnPessoaFoto.put("he50", qTmpX.getString("HE50"));
                            vjnPessoaFoto.put("he100", qTmpX.getString("HE100"));
                            vjnPessoaFoto.put("adnot", qTmpX.getString("AdNot"));
                            vjnPessoaFoto.put("hsnotred", qTmpX.getString("HsNotRed"));
                            vjnPessoaFoto.put("diastrab", qTmpX.getString("DiasTrab"));
                            vjnPessoaFoto.put("diasfolga", qTmpX.getString("DiasFolga"));
                            vjnPessoaFoto.put("faltas", qTmpX.getString("Faltas"));
                            vjnPessoaFoto.put("hsprojecao", qTmpX.getString("HSProjecao"));
                            vjnPessoaFoto.put("enderecofilial", qTmpX.getString("EnderecoFilial"));
                            vjnPessoaFoto.put("bairrofil", qTmpX.getString("bairrofil"));
                            vjnPessoaFoto.put("cidadefil", qTmpX.getString("cidadefil"));
                            vjnPessoaFoto.put("uffil", qTmpX.getString("UFFil"));
                            vjnPessoaFoto.put("cepfil", qTmpX.getString("cepfil"));
                            vjnPessoaFoto.put("cnpjfil", qTmpX.getString("cnpjfil"));
                            vjnPessoaFoto.put("codfil", qTmpX.getString("codfil"));
                            vjnPessoaFoto.put("razaosocial", qTmpX.getString("RazaoSocial"));
                            vjnPessoaFoto.put("diasfertrab", qTmpX.getString("DiasFerTrab"));
                            vjnPessoaFoto.put("intraj", qTmpX.getString("Intraj"));
                            vjnPessoaFoto.put("diasfolga", qTmpX.getString("DiasFolga"));
                            vjnPessoaFoto.put("diastrab", qTmpX.getString("DiasTrab"));
                            vjnPessoaFoto.put("faltasjust", qTmpX.getString("FaltasJust"));
                            vjnPessoaFoto.put("atmedico", qTmpX.getString("AtMedico"));
                            vjnPessoaFoto.put("faltasabon", qTmpX.getString("FaltasAbon"));
                            vjnPessoaFoto.put("diasferias", qTmpX.getString("DiasFerias"));
                            vjnPessoaFoto.put("diasronda", qTmpX.getString("DiasRonda"));
                            vjnPessoaFoto.put("diaschsup", qTmpX.getString("DiasCHSup"));
                            vjnPessoaFoto.put("descdsr", qTmpX.getString("DescDSR"));
                            vjnPessoaFoto.put("situacao", qTmpX.getString("SituacaoHR"));
                            vjnPessoaFoto.put("hsdiurnas", qTmpX.getString("HSdiurnas"));
                            vjnPessoaFoto.put("hsnoturnas", qTmpX.getString("HSNoturnas"));
                            vjnPessoaFoto.put("hsintraj", qTmpX.getString("hsintraj"));
                            vjnPessoaFoto.put("he50d", qTmpX.getString("HE50D"));
                            vjnPessoaFoto.put("he100d", qTmpX.getString("HE100D"));
                            vjnPessoaFoto.put("he50i", qTmpX.getString("HE50I"));
                            vjnPessoaFoto.put("he100i", qTmpX.getString("HE100I"));
                            vjnPessoaFoto.put("pathlocal", vPathLocal);
                            vjnPessoaFoto.put("pathweb", vPathWeb);

                            vjnPessoaData.put(vjnPessoaFoto);
                        } catch (Exception e) {
                            logexecucao.Grava("Erro geração Retorno Json", caminho);
                            throw new Exception("FuncionPessoa.obterFotoPw - " + e.getMessage() + "\r\n");
                        }
                    }

                    vjnRetorno.put("funcion", vjnPessoaData);
                    //                  pstServJO.put("funcion", funcionJA);                

                }
                qTmpX.Close();
                if (vConta == 0) {
                    vjnRetorno.put("matr", "0");
                    vjnRetorno.put("nome", "USUARIO OU SENHA INCORRETA");
                    vjnRetorno.put("nfoto1", "NAO REGISTRADO");
                    vjnRetorno.put("nfoto2", "NAO REGISTRADO");
                    vjnRetorno.put("nfoto3", "NAO REGISTRADO");
                    vjnRetorno.put("codigo", "0");
                    vjnRetorno.put("funcion", vjnPessoaData);
                }
            } catch (Exception e) {
                logexecucao.Grava("Erro try principal " + e.getMessage(), caminho);
                throw new Exception("FuncionPessoa.obterFotoPw - " + e.getMessage() + "\r\n"
                        + vSQL);

            }
        } finally {
            vRetorno = vjnRetorno.toString();
            return vRetorno;
            /*Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();*/
        }
    }

    @GET
    @Path("/xmlGTVE/{token}/{chavegtve}/")
    @Produces(MediaType.APPLICATION_JSON)
    public Response xmlGTVEs(@QueryParam("token") String vToken, @QueryParam("empresa") String vChaveGTVE) {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\xmlGTVE.txt";
        // TODO ler body em json
        //String param;
        //Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        //String vToken = (String) parametros.getOrDefault("token", null);
        //String vChaveGTVE = (String) parametros.getOrDefault("chavegtve", null);
        if (vToken.equals("")) {
            vToken = "B8489FF7205E3217B4E043D70FF9848B";
        }
        logexecucao.Grava("parametros: token=" + vToken + ";chaveGTVE=" + vChaveGTVE, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnEnvio = new JSONObject();
        JSONArray vjnArray = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vChaveGTVE == null || vChaveGTVE.equals("")) {
                throw new StatusClientePstServException("Chave Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vCNPJ = "", vGuia = "", vSerie = "", vCodCidade = "", vDt_GTVE = "",
                    vRetorno = "", vDt_envio = "", vHr_envio = "", vDt_retorno = "",
                    vHr_retorno = "", vFatIDGTV = "", vOpenIDFilial = "", vXMLretorno = "",
                    vBancoDados = "";
            Consulta qTmpX;
            vBancoDados = qToken.getBancoDados();

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }

            vSQL = "Select top 1 XMLGTVE.CNPJ, XMLGTVE.Guia, XMLGTVE.Serie, "
                    + "XMLGTVE.CodCidade, XMLGTVE.Dt_GTVE, XMLGTVE.Hr_GTVE, XMLGTVE.ChaveGTVE, \n"
                    + "Substring(Substring(XMLGTVE.XML_Retorno, PATINDEX('%<xMotivo>%', XMLGTVE.XML_Retorno)+09, PATINDEX('%</xMotivo>%' , XMLGTVE.XML_Retorno)-10),1,(PATINDEX('%</xMotivo>%', XMLGTVE.XML_Retorno)-PATINDEX('%<xMotivo>%', XMLGTVE.XML_Retorno))-9) Retorno, \n"
                    + "XMLGTVE.Dt_Envio, XMLGTVE.Hr_Envio, \n"
                    + "XMLGTVE.Dt_retorno, XMLGTVE.Hr_retorno, XMLGTVE.FatIDGTV, \n"
                    + "XMLGTVE.OPenIDFilial, \n"
                    + "REPLACE(REPLACE(substring(XMLGTVE.xml_retorno,1, Len(XMLGTVE.xml_retorno)) , CHAR(13), ''), CHAR(10), '') XMLRetorno from XMLGTVE \n"
                    + " where XMLGTVE.ChaveGTVE = '" + vChaveGTVE + "'"
                    + "   or XMLGTVE.XML_Retorno like '%" + vChaveGTVE + "%'";
            try {
                qTmpX = new Consulta(vSQL, dbpadrao);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                throw new Exception("xmlGTVE - " + e.getMessage() + "\r\n"
                        + vSQL);
            }

            int vConta = 0;

            while (qTmpX.Proximo()) {
                vConta++;
                vjnEnvio = new JSONObject();
                vCNPJ = qTmpX.getString("CNPJ");
                vGuia = qTmpX.getBigDecimal("Guia").toString().replace(".0", "");
                vSerie = qTmpX.getString("Serie");
                vCodCidade = qTmpX.getString("CodCidade").replace(".0", "");
                vDt_GTVE = qTmpX.getString("Dt_Gtve");
                vRetorno = qTmpX.getString("Hr_Gtve");
                vDt_envio = qTmpX.getString("Dt_Envio");
                vHr_envio = qTmpX.getString("Hr_Envio");
                vDt_retorno = qTmpX.getString("Dt_retorno");
                vHr_retorno = qTmpX.getString("Hr_retorno");
                vFatIDGTV = qTmpX.getString("FatIDGTV");
                vOpenIDFilial = qTmpX.getString("OpenIDFilial");
                vXMLretorno = qTmpX.getString("XMLRetorno");

                vjnEnvio.put("cnpj", vCNPJ);
                vjnEnvio.put("guia", vGuia);
                vjnEnvio.put("serie", vSerie);
                vjnEnvio.put("codcidade", vCodCidade);
                vjnEnvio.put("dt_gtve", vDt_GTVE);
                vjnEnvio.put("retorno", vRetorno);
                vjnEnvio.put("dt_envio", vDt_envio);
                vjnEnvio.put("hr_envio", vHr_envio);
                vjnEnvio.put("dt_retorno", vDt_retorno);
                vjnEnvio.put("hr_retorno", vHr_retorno);
                vjnEnvio.put("fatidgtv", vFatIDGTV);
                vjnEnvio.put("openidfilial", vOpenIDFilial);
                vjnEnvio.put("xmlretorno", vXMLretorno);

                vjnArray.put(vjnEnvio);
            }
            if (vConta == 0) {
                vjnEnvio.put("resposta", "NAO HA DADOS");
                vjnArray.put(vjnEnvio);
            }
            try {
                vjnRetorno.put("gtve", vjnArray);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("XMLGTVE - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    //Unirest.setTimeouts(0, 0);
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/integraSLU")
    public Response integraSLU(String param) throws UnsupportedEncodingException, IOException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vMaterial = (String) parametros.getOrDefault("material", null);
        String vTipoTransp = (String) parametros.getOrDefault("tipotransp", null);
        String vPlaca = (String) parametros.getOrDefault("placa", null);
        String vCacamba = (String) parametros.getOrDefault("cacamba", null);
        String vEmailCliente = (String) parametros.getOrDefault("emailcliente", null);
        String vComando = (String) parametros.getOrDefault("comando", null);
        String vCtrCli = (String) parametros.getOrDefault("ctr", null);
        String vCPFCNPJ = (String) parametros.getOrDefault("cpfcnpj", null);
        String vLocal = (String) parametros.getOrDefault("local", null);
        String vCEP = (String) parametros.getOrDefault("cep", null);
        String vGuiaParam = (String) parametros.getOrDefault("guia", null);
        String vSerieParam = (String) parametros.getOrDefault("serie", null);

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnNumero = new JSONObject();

        try {
            String vNumRet = "";
            String vLogin = "<EMAIL>";
            String vPW = "Ecovisao@2019";
            String vEmail = "<EMAIL>";
            String vToken = "";
            obterTokenSLU(vLogin, vPW, vEmail);
            vToken = vRetorno;
            vjnRetorno.put("resposta", vToken);
            if (vComando.equals("SOLICITAR")) {
                buscarGeradorSLUGet(vToken, vCPFCNPJ, vGuiaParam, vSerieParam);
                if (vRetornoPessoa != null) {
                    buscarObraSLUGet(vToken, vRetornoPessoa, vCEP, vGuiaParam, vSerieParam);
                }
                buscarVeiculoSLUGet(vToken, vPlaca, vGuiaParam, vSerieParam);
                buscarCacambaSLUGet(vToken, vCacamba, vGuiaParam, vSerieParam);
                if (!vRetornoObra.equals("000") && !vRetornoObra.contains("Error") && !vRetornoObra.contains("error")) {
                    solicitarServicoSLUN(vToken, vMaterial, vRetornoObra, vRetornoVeiculo, vRetornoCacamba, vRetornoPessoa, vGuiaParam, vSerieParam);
                    String vCtr = "Retorno" + vRetorno
                            + "-Retorno Gerador:" + vRetornoPessoa
                            + "-Retorno Obra:" + vRetornoObra
                            + "-Retorno Veiculo:" + vRetornoVeiculo
                            + "-Retorno Cacamba:" + vRetornoCacamba
                            + "-Retorno CTR:" + vRetornoCtr;
                    vjnNumero.put("CTR", vCtr);
                } else {
                    vRetornoCtr = "0";
                    //String vCtr = "Retorno" + vRetorno
                    String vCtr = "Retorno Gerador:" + vRetornoPessoa
                            + "-Retorno Obra:" + vRetornoObra
                            + "-Retorno Veiculo:" + vRetornoVeiculo
                            + "-Retorno Cacamba:" + vRetornoCacamba
                            + "-Retorno CTR:" + vRetornoCtr;
                    vjnNumero.put("CTR", vCtr);
                }

                vEndeOK = false;
                vjnRetorno.put("CTR", vRetornoCtr);
            }
            if (vComando.equals("ALOCAR")) {
                alocarServicoSLUUN(vToken, vRetornoCtr, vRetornoObra, vRetornoVeiculo, vRetornoCacamba);
                String vCtr = vRetorno;
                vjnRetorno.put("CTR", vCtr);
            }
            if (vComando.equals("PESSOA")) {
                buscarGeradorSLUGet(vToken, vCPFCNPJ, vGuiaParam, vSerieParam);
                buscarObraSLUGet(vToken, vRetornoPessoa, vCEP, vGuiaParam, vSerieParam);
                if (!vRetornoPessoa.contains("{    \"count\":  0,    \"rows\":  [") && !vRetornoPessoa.contains("error")) {
                    buscarVeiculoSLUGet(vToken, vPlaca, vGuiaParam, vSerieParam);
                    buscarCacambaSLUGet(vToken, vCacamba, vGuiaParam, vSerieParam);
                    //solicitarServicoSLUN(vToken, vMaterial, vRetornoObra, vRetornoVeiculo, vRetornoCacamba, vRetornoPessoa);
                    if (!vRetornoObra.equals("000") && !vRetornoObra.contains("Error") && !vRetornoObra.contains("error")) {
                        solicitarServicoSLUN(vToken, vMaterial, vRetornoObra, vRetornoVeiculo, vRetornoCacamba, vRetornoPessoa, vGuiaParam, vSerieParam);
                        String vCtr = "Retorno" + vRetorno
                                + "-Retorno Gerador:" + vRetornoPessoa
                                + "-Retorno Obra:" + vRetornoObra
                                + "-Retorno Veiculo:" + vRetornoVeiculo
                                + "-Retorno Cacamba:" + vRetornoCacamba
                                + "-Retorno Ctr:" + vRetornoCtr;
                        vjnNumero.put("CTR", vCtr);
                        vRetorno = vCtr;
                    } else {
                        vRetornoCtr = "0";
                        String vCtr
                                = //"Retorno" + vRetorno
                                "-Retorno Gerador:" + vRetornoPessoa + " \n"
                                + "-Retorno Obra:" + vRetornoObra + " \n"
                                + "-Retorno Veiculo:" + vRetornoVeiculo + " \n"
                                + "-Retorno Cacamba:" + vRetornoCacamba + " \n"
                                + "-Retorno Ctr:" + vRetornoCtr + " \n";
                        vjnNumero.put("CTR", vCtr);
                    }
                }
            }

        } finally {
            //vjnRetorno.put("resposta", vjnPessoaFoto);
            //vRetorno = Response.serverError().toString();

            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    public void buscarGeradorSLUGet(String vToken, String vCPFCNPJ, String vGuia, String vSerie) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\buscarPessoaSLUGet.txt";
        String vTokenSLU = "";
        String vSQLLog = "";
        try {

            //URL url = new URL("https://rcc.slu.df.gov.br/api/v2/pessoa/findbycpfcnpj?cpfCnpj="+vCPFCNPJ+"&perfis=0");
            URL url = new URL("https://api.slu.df.gov.br/rcc/gerador/" + vCPFCNPJ + "?");
            //Gravar Arquivo Request
            String vArquivo;
            vArquivo = "$headers = New-Object \"System.Collections.Generic.Dictionary[[String],[String]]\"\n"
                    + "$headers.Add(\"Authorization\", \"Bearer" + vToken + "\")\n"
                    + "\n"
                    + "$response = Invoke-RestMethod 'https://api.slu.df.gov.br/rcc/gerador/" + vCPFCNPJ + "?' -Method 'GET' -Headers $headers\n"
                    + "$response | ConvertTo-Json";
            String scriptPath = "c:\\Clientes\\ReqGerador.ps1";
            File file = new File(scriptPath);
            if (file.exists()) {
                file.delete();
            }
            FileWriter vArq = new FileWriter(scriptPath);
            PrintWriter gravarArq = new PrintWriter(vArq);

            gravarArq.printf(vArquivo);
            vArq.close();

            ProcessBuilder processBuilder = new ProcessBuilder("powershell.exe", "-File", scriptPath);

            // Redireciona a saída do processo para ler a resposta
            processBuilder.redirectErrorStream(true);

            // Inicia o processo
            Process process = processBuilder.start();

            // Lê a saída do processo
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            Consulta SQLPdrLog;
            while ((line = reader.readLine()) != null) {
                vRetornoPessoa = vRetornoPessoa + line;
            }
            this.logerro.Grava(vRetornoCtr, caminho);
            vRetornoPessoa = vRetornoPessoa.replace("null", "");
            vRetornoPessoa = vRetornoPessoa.replace("{    \"count\":  1,    \"rows\":  [                 ", "");
            vRetornoPessoa = vRetornoPessoa.replace("{    \"count\":  2,    \"rows\":  [                 ", "");
            vRetornoPessoa = vRetornoPessoa.replace("{    \"count\":  3,    \"rows\":  [                 ", "");
            vRetornoPessoa = vRetornoPessoa.replace("{    \"count\":  4,    \"rows\":  [                 ", "");
            vRetornoPessoa = vRetornoPessoa.replace("             ]}", "");
            vSQLLog = "Insert into SLUIntegra(Sequencia, Guia, Serie, TipoOperacao, ParamOperacao, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from SLUIntegra), \n"
                    + vGuia + ", \n"
                    + "'" + vSerie + "', \n"
                    + "'buscarGeradorSLUGet', \n"
                    + "'" + vArquivo.replace("'", "") + "', \n"
                    + "'" + vRetornoPessoa + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATECOVISAO"));
            SQLPdrLog.insert();
            if (!vRetornoPessoa.contains("Error") && !vRetornoPessoa.contains("error") && !vRetornoPessoa.contains("\"count\":  0,")) {
                try {
                    JsonParser jsonParser = new JsonParser();
                    JsonObject jsonObject = jsonParser.parse(vRetornoPessoa).getAsJsonObject();

                    if (!jsonObject.get("idPerfil").getAsString().equals(null)) {
                        vRetornoPessoa = jsonObject.get("idPerfil").getAsString();
                    } else {
                        vRetornoPessoa = vRetornoPessoa;
                    }
                } catch (JSONException e) {
                    this.logerro.Grava(e.toString(), caminho);
                }
            } else {
                vSQLLog = "Insert into SLUIntegra(Sequencia, Guia, Serie, TipoOperacao, ParamOperacao, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from SLUIntegra), \n"
                        + vGuia + ", \n"
                        + "'" + vSerie + "', \n"
                        + "'buscarGeradorSLUGet', \n"
                        + "'" + vArquivo.replace("'", "") + "', \n"
                        + "'" + "SEM GERADOR CADASTRADO" + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATECOVISAO"));
                SQLPdrLog.insert();
            }
            this.logerro.Grava("Parametros:" + "cpfCnpj=" + vCPFCNPJ + " REsposta" + vRetornoPessoa, caminho);

            // For POST only - START
            // For POST only - END            
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do WS" + e.getMessage() + " SQL: " + vSQLLog, caminho);
        }
    }

    public void buscarObraSLUGet(String vToken, String vIdGerador, String vCEP, String vGuia, String vSerie) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\buscarObraSLUGet.txt";
        String vTokenSLU = "";

        try {

            //URL url = new URL("https://rcc.slu.df.gov.br/api/v2/pessoa/findbycpfcnpj?cpfCnpj="+vCPFCNPJ+"&perfis=0");
            URL url = new URL("https://api.slu.df.gov.br/rcc/obra?idGerador=" + vIdGerador + "&+cep=" + vCEP + "&limit=100&offset=0");
            //Gravar Arquivo Request
            String vArquivo;
            vArquivo = "$headers = New-Object \"System.Collections.Generic.Dictionary[[String],[String]]\"\n"
                    + "$headers.Add(\"Authorization\", \"Bearer" + vToken + "\")\n"
                    + "\n"
                    + "$response = Invoke-RestMethod 'https://api.slu.df.gov.br/rcc/obra?idGerador=" + vIdGerador + "&+cep=" + vCEP + "&limit=10000&offset=0' -Method 'GET' -Headers $headers\n"
                    + "$response | ConvertTo-Json";
            String scriptPath = "c:\\Clientes\\ReqObra.ps1";
            File file = new File(scriptPath);
            if (file.exists()) {
                file.delete();
            }
            FileWriter vArq = new FileWriter(scriptPath);
            PrintWriter gravarArq = new PrintWriter(vArq);

            gravarArq.printf(vArquivo);
            vArq.close();

            ProcessBuilder processBuilder = new ProcessBuilder("powershell.exe", "-File", scriptPath);

            // Redireciona a saída do processo para ler a resposta
            processBuilder.redirectErrorStream(true);

            // Inicia o processo
            Process process = processBuilder.start();

            // Lê a saída do processo
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                vRetornoObra = vRetornoObra + line;
            }
            this.logerro.Grava("Parametros:" + vRetornoObra, caminho);
            Consulta SQLPdrLog;
            String vSQLLog;
            if (!vRetornoObra.contains("Error") && !vRetornoObra.contains("error") && !vRetornoObra.contains("\"count\":  0,")) {
                vRetornoObra = vRetornoObra.replace("null{", "{");
                String vTag = vRetornoObra.substring(0, 49);
                //vRetornoObra = vRetornoObra.replace(vTag, "");
                //vRetornoObra = vRetornoObra.replace("{    \"count\":  1,    \"rows\":  [                 ", "");
                //vRetornoObra = vRetornoObra.replace("{    \"count\":  2,    \"rows\":  [                 ", "");
                //vRetornoObra = vRetornoObra.replace("{    \"count\":  7,    \"rows\":  [                 ", "");
                //vRetornoObra = vRetornoObra.replace("{    \"count\":  56,    \"rows\":  [                 ", "");
                //vRetornoObra = vRetornoObra.replace("             ]}", "");
                vRetornoObra = vRetornoObra.replace("\"duracaoFinal\":  ,", "\"duracaoFinal\":  '',");
                vRetornoObra = vRetornoObra.replace("\"duracaoInicial\":  ,", "\"duracaoInicial\": '' ,");
                vRetornoObra = vRetornoObra.replace("\"complemento\":  ,                     ", "\"complemento\": '' ,                     ");

                vSQLLog = "Insert into SLUIntegra(Sequencia, Guia, Serie, TipoOperacao, ParamOperacao, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from SLUIntegra), \n"
                        + vGuia + ", \n"
                        + "'" + vSerie + "', \n"
                        + "'buscarObraSLUGet', \n"
                        + "'" + vArquivo.replace("'", "") + "', \n"
                        + "'" + vRetornoObra + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATECOVISAO"));
                SQLPdrLog.insert();

                try {
                    JSONObject vJnObrasGer = new JSONObject(vRetornoObra);
                    JSONArray vJnObraInd = vJnObrasGer.getJSONArray("rows");
                    int vTam = vJnObraInd.length();
                    for (int i = 0; i < vTam; i++) {
                        JSONObject vBuscaJn = vJnObraInd.getJSONObject(i);

                        if (vBuscaJn.getString("cep").equals(vCEP)) {
                            vRetornoObra = "";
                            vEndeOK = true;
                            vRetornoObra = Integer.toString(vBuscaJn.getInt("id"));
                        } else if (vEndeOK == false) {
                            vRetornoObra = "";
                            vRetornoObra = "000";
                        }
                    }
                } catch (JSONException e) {
                    this.logerro.Grava(e.toString(), caminho);
                }
            } else {
                vRetornoObra = "";
                vRetornoObra = "000";
                vSQLLog = "Insert into SLUIntegra(Sequencia, Guia, Serie, TipoOperacao, ParamOperacao, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from SLUIntegra), \n"
                        + vGuia + ", \n"
                        + "'" + vSerie + "', \n"
                        + "'buscarObraSLUGet', \n"
                        + "'" + vArquivo.replace("'", "") + "', \n"
                        + "'" + "SEM OBRA CADASTRADA" + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATECOVISAO"));
                SQLPdrLog.insert();
            }
            this.logerro.Grava("Parametros:" + "IDGerador=" + vIdGerador + " REsposta" + vRetornoObra + " CEP:" + vCEP, caminho);

            // For POST only - START
            // For POST only - END            
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do WS" + e.getMessage(), caminho);
            this.logerro.Grava("Parametros:" + "IDGerador=" + vIdGerador + " REsposta:" + vRetornoObra + " CEP:" + vCEP, caminho);
        }
    }

    public void buscarVeiculoSLUGet(String vToken, String vPlaca, String vGuia, String vSerie) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\buscarVeiculoSLUGet.txt";
        String vTokenSLU = "";

        try {

            //URL url = new URL("https://rcc.slu.df.gov.br/api/v2/pessoa/findbycpfcnpj?cpfCnpj="+vCPFCNPJ+"&perfis=0");
            URL url = new URL("https://api.slu.df.gov.br/rcc/veiculo/" + vPlaca + "?");

            String vArquivo;
            vArquivo = "$headers = New-Object \"System.Collections.Generic.Dictionary[[String],[String]]\"\n"
                    + "$headers.Add(\"Authorization\", \"Bearer" + vToken + "\")\n"
                    + "\n"
                    + "$response = Invoke-RestMethod 'https://api.slu.df.gov.br/rcc/veiculo/" + vPlaca + "?' -Method 'GET' -Headers $headers\n"
                    + "$response | ConvertTo-Json";
            String scriptPath = "c:\\Clientes\\ReqVeiculo.ps1";
            File file = new File(scriptPath);
            if (file.exists()) {
                file.delete();
            }
            FileWriter vArq = new FileWriter(scriptPath);
            PrintWriter gravarArq = new PrintWriter(vArq);

            gravarArq.printf(vArquivo);
            vArq.close();

            ProcessBuilder processBuilder = new ProcessBuilder("powershell.exe", "-File", scriptPath);

            // Redireciona a saída do processo para ler a resposta
            processBuilder.redirectErrorStream(true);

            // Inicia o processo
            Process process = processBuilder.start();

            // Lê a saída do processo
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                vRetornoVeiculo = vRetornoVeiculo + line;
            }

            vRetornoVeiculo = vRetornoVeiculo.replace("null", "");
            vRetornoVeiculo = vRetornoVeiculo.replace("{    \"count\":  1,    \"rows\":  [                 ", "");
            vRetornoVeiculo = vRetornoVeiculo.replace("             ]}", "");
            vRetornoVeiculo = vRetornoVeiculo.replace("\"cor\":  ,", "\"cor\":'',");
            vRetornoVeiculo = vRetornoVeiculo.replace("\"renavam\":  ,", "\"cor\":'',");
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into SLUIntegra(Sequencia, Guia, Serie, TipoOperacao, ParamOperacao, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from SLUIntegra), \n"
                    + vGuia + ", \n"
                    + "'" + vSerie + "', \n"
                    + "'buscarVeiculoSLUGet', \n"
                    + "'" + vArquivo.replace("'", "") + "', \n"
                    + "'" + vRetornoVeiculo + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATECOVISAO"));
            SQLPdrLog.insert();
            try {
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(vRetornoVeiculo).getAsJsonObject();

                if (!jsonObject.get("id").getAsString().equals(null)) {
                    vRetornoVeiculo = jsonObject.get("id").getAsString();
                } else {
                    vRetornoVeiculo = vRetornoVeiculo;
                }
            } catch (JSONException e) {
                this.logerro.Grava(e.toString(), caminho);
            }

            this.logerro.Grava("Parametros:" + "Placa=" + vPlaca + " REsposta" + vRetornoVeiculo, caminho);
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do WS" + e.getMessage(), caminho);
        }
    }

    public void buscarCacambaSLUGet(String vToken, String vCacamba, String vGuia, String vSerie) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\buscarCAcambaSLUGet.txt";
        String vTokenSLU = "";

        try {

            //URL url = new URL("https://rcc.slu.df.gov.br/api/v2/pessoa/findbycpfcnpj?cpfCnpj="+vCPFCNPJ+"&perfis=0");
            URL url = new URL("https://api.slu.df.gov.br/rcc/cacamba/" + vCacamba + "?");
            String vArquivo;
            vArquivo = "$headers = New-Object \"System.Collections.Generic.Dictionary[[String],[String]]\"\n"
                    + "$headers.Add(\"Authorization\", \"Bearer" + vToken + "\")\n"
                    + "\n"
                    + "$response = Invoke-RestMethod 'https://api.slu.df.gov.br/rcc/cacamba/" + vCacamba + "?' -Method 'GET' -Headers $headers\n"
                    + "$response | ConvertTo-Json";
            String scriptPath = "c:\\Clientes\\ReqCacamba.ps1";
            File file = new File(scriptPath);
            if (file.exists()) {
                file.delete();
            }
            FileWriter vArq = new FileWriter(scriptPath);
            PrintWriter gravarArq = new PrintWriter(vArq);

            gravarArq.printf(vArquivo);
            vArq.close();

            ProcessBuilder processBuilder = new ProcessBuilder("powershell.exe", "-File", scriptPath);

            // Redireciona a saída do processo para ler a resposta
            processBuilder.redirectErrorStream(true);

            // Inicia o processo
            Process process = processBuilder.start();

            // Lê a saída do processo
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                vRetornoCacamba = vRetornoCacamba + line;
            }

            this.logerro.Grava("Entrou:" + "Cacamba=" + vCacamba + " REsposta" + vRetornoCacamba, caminho);
            vRetornoCacamba = vRetornoCacamba.replace("null", "");
            vRetornoCacamba = vRetornoCacamba.replace("{    \"count\":  1,    \"rows\":  [                 ", "");
            vRetornoCacamba = vRetornoCacamba.replace("             ]}", "");
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into SLUIntegra(Sequencia, Guia, Serie, TipoOperacao, ParamOperacao, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from SLUIntegra), \n"
                    + vGuia + ", \n"
                    + "'" + vSerie + "', \n"
                    + "'buscarCacambaSLUGet', \n"
                    + "'" + vArquivo.replace("'", "") + "', \n"
                    + "'" + vRetornoCacamba + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATECOVISAO"));
            SQLPdrLog.insert();

            try {
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(vRetornoCacamba).getAsJsonObject();

                if (!jsonObject.get("id").getAsString().equals(null)) {
                    vRetornoCacamba = jsonObject.get("id").getAsString();
                } else {
                    vRetornoCacamba = vRetornoCacamba;
                }
            } catch (JSONException e) {
                this.logerro.Grava(e.toString(), caminho);
            }
            this.logerro.Grava("Parametros:" + "Cacamba=" + vCacamba + " REsposta" + vRetornoCacamba, caminho);
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do WS" + e.getMessage(), caminho);
        }
    }

    public String obterTokenSankhya(String vToken, String vAppKey, String vUser, String vPassword) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\obterTpkenSankhya.txt";
        String vTokenSLU = "";
        vToken = "5460c740-b47d-48f8-ba2e-4c1cd59cf39a";
        vAppKey = "2817151b-c016-4ff3-9e51-7a6aeae4b5d9";
        vUser = "<EMAIL>";
        vPassword = "teste123";
        // Definir a URL do Web Service                        

        try {
            // Comando PowerShell que você deseja executar            
            String vArquivo;
            vArquivo = "$headers = New-Object \"System.Collections.Generic.Dictionary[[String],[String]]\"\n"
                    + "$headers.Add(\"token\", \"5460c740-b47d-48f8-ba2e-4c1cd59cf39a\")\n"
                    + "$headers.Add(\"appkey\", \"2817151b-c016-4ff3-9e51-7a6aeae4b5d9\")\n"
                    + "$headers.Add(\"username\", \"<EMAIL>\")\n"
                    + "$headers.Add(\"password\", \"teste123\")\n"
                    + "$headers.Add(\"Authorization\", \"Bearer bearerToken\")\n"
                    + "\n"
                    + "$response = Invoke-RestMethod 'https://api.sankhya.com.br/login' -Method 'POST' -Headers $headers\n"
                    + "$response | ConvertTo-Json";

            // Caminho para o arquivo do script PowerShell
            String scriptPath = "C:\\Clientes\\executar.ps1";
            File file = new File(scriptPath);
            if (file.exists()) {
                file.delete();
            }
            FileWriter vArq = new FileWriter(scriptPath);
            PrintWriter gravarArq = new PrintWriter(vArq);

            gravarArq.printf(vArquivo);
            vArq.close();
            // Cria o ProcessBuilder com o comando para executar o script
            ProcessBuilder processBuilder = new ProcessBuilder("powershell.exe", "-File", scriptPath);

            // Redireciona a saída do processo para ler a resposta
            processBuilder.redirectErrorStream(true);

            // Inicia o processo
            Process process = processBuilder.start();

            // Lê a saída do processo
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                vRetorno = vRetorno + line;
            }

            vRetorno = vRetorno.replace("null{  ", "{  ");
            try {
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(vRetorno).getAsJsonObject();
                this.logerro.Grava(vRetorno, caminho);
                if (!jsonObject.get("bearerToken").getAsString().equals(null)) {
                    vRetorno = jsonObject.get("bearerToken").getAsString();
                } else {
                    vRetorno = vRetorno;
                }
            } catch (JSONException e) {
                this.logerro.Grava(e.toString(), caminho);
            }
            // Aguarda o término do processo
            int exitCode = process.waitFor();
            //vRetorno = vRetorno + " - Processo encerrado com código de saída: " + exitCode;

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return vRetorno;
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/consultaTesCustodia")
    public String consultaTesCustodia(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\consultaTesCustodia.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vCodFil = (String) parametros.getOrDefault("codfil", null); //Obrigatorio
        String vToken = (String) parametros.getOrDefault("token", null);
        //String vPW = (String) parametros.getOrDefault("pw", null);
        String vDtIni = (String) parametros.getOrDefault("dataini", null);
        String vDtFim = (String) parametros.getOrDefault("datafim", null);
        logexecucao.Grava("parametros: codfil=" + vCodFil + ";Token=" + vToken + ";dataini=" + vDtIni + ";datafinal=" + vDtFim, caminho);
        Persistencia dbpadrao, dbsatellite;
        String vArqLogo;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "";
        String vData = "";

        try {
            //Validando se a matrícula informada não está vazia
            if (vCodFil == null || vCodFil.equals("")) {
                throw new StatusClientePstServException("filial_invalida");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            String vBancoDados = qToken.getBancoDados();
            dbpadrao = this.pool.getConexao(vBancoDados);
            Consulta qTmpX;
            vArqLogo = getLogo(dbpadrao.getEmpresa(), "0");

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\//.txt";
            }

            if (vDtIni.equals("") || vDtFim.length() != 8) {
                vData = "20231201"; //vData = getDataAtual("SQL");
            } else {
                vData = vDtIni;
            }

            try {

                vSQL = "Select TesCustodia.CodFil, TesFecha.DtFecha, Clientes.NRed, GuiasPend, SaldoPend, GuiasEmRec, SaldoEmRec, \n"
                        + " TesCustodia.SaldoRec*(Select top 1 isnull(Valor,0) from TesMoedas \n"
                        + " Left Join TesCustodiaME TesCustME on TesCustME.CodCli = TEsCustodia.CodCli and TEsCustME.CodMoeda = TesMoedas.CodMoeda \n"
                        + " where TesCustME.CodCli = TEsCustodia.CodCli and TesCustME.CodFil = TesCustodia.CodFil ) ValorConv,  \n"
                        + " SaldoRec, SaldoCxF, Case when SaldoRec-SaldoCxF > 0 then SaldoRec-SaldoCxF else 0 end SaldoFisicoTes, TesCustodia.CodCli from TesCustodia \n"
                        + " left Join TesFecha on  Tesfecha.CodFil = TesCustodia.CodFil \n"
                        + "                    and TesFecha.CodCli = TesCustodia.CodCli \n"
                        + " Left Join Clientes on  Clientes.Codigo = TesCustodia.CodCli \n"
                        + "                    and Clientes.CodFil = TesCustodia.CodFil \n"
                        + " where TesCustodia.CodFil = " + vCodFil;
                try {
                    qTmpX = new Consulta(vSQL, dbpadrao);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("TesCustodia - " + e.getMessage() + "\r\n"
                            + vSQL);
                }
                JSONObject vjnTesCustodia;
                JSONArray vjnTesCustodiaData = new JSONArray();
                int vConta = 0;
                //if (qTmpX.getString("Matr") != "") {
                //.Proximo();
                //qTmpX.                
                while (qTmpX.Proximo()) {
                    vConta = ++vConta;

                    if (!qTmpX.getString("CodFil").equals(null)) {
                        try {
                            vjnTesCustodia = new JSONObject();
                            vjnTesCustodia.put("codfil", qTmpX.getString("CodFil"));
                            vjnTesCustodia.put("dtfecha", qTmpX.getString("DtFecha"));
                            vjnTesCustodia.put("nred", qTmpX.getString("NRed"));
                            vjnTesCustodia.put("guiaspend", qTmpX.getString("GuiasPend"));
                            vjnTesCustodia.put("saldopend", qTmpX.getString("SaldoPend"));
                            vjnTesCustodia.put("guiasemrec", qTmpX.getString("GuiasEmRec"));
                            vjnTesCustodia.put("saldoemrec", qTmpX.getString("SaldoEmRec"));
                            vjnTesCustodia.put("valorconv", qTmpX.getString("ValorConv"));
                            vjnTesCustodia.put("saldorec", qTmpX.getString("SaldoRec"));
                            vjnTesCustodia.put("saldocxf", qTmpX.getString("SaldoCxF"));
                            vjnTesCustodia.put("saldofisicotes", qTmpX.getString("SaldoFisicoTes"));
                            vjnTesCustodia.put("codcli", qTmpX.getString("CodCli"));
                            vjnTesCustodia.put("arqlogo", vArqLogo);

                            vjnTesCustodiaData.put(vjnTesCustodia);
                        } catch (Exception e) {
                            logexecucao.Grava("Erro geração Retorno Json", caminho);
                            throw new Exception("TesCustodia - " + e.getMessage() + "\r\n");
                        }
                    }

                    vjnRetorno.put("tescustodia", vjnTesCustodiaData);
                    //                  pstServJO.put("funcion", funcionJA);                

                }
                qTmpX.Close();
                if (vConta == 0) {
                    vjnRetorno.put("codfil", "0");
                    vjnRetorno.put("resposta", "SUCURSAL NO ENCONTRADA");
                    vjnRetorno.put("tescustodia", vjnTesCustodiaData);
                }
            } catch (Exception e) {
                logexecucao.Grava("Erro try principal " + e.getMessage(), caminho);
                throw new Exception("TesCustodia - " + e.getMessage() + "\r\n"
                        + vSQL);

            }
        } finally {
            vRetorno = vjnRetorno.toString();
            return vRetorno;
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/consultaTesCustodiaData")
    public String consultaTesCustodiaData(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\consultaTesCustodiaData.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vCodFil = (String) parametros.getOrDefault("codfil", null); //Obrigatorio
        String vCodTes = (String) parametros.getOrDefault("codtes", null); //Obrigatorio
        String vToken = (String) parametros.getOrDefault("token", null);
        //String vPW = (String) parametros.getOrDefault("pw", null);
        String vDtIni = (String) parametros.getOrDefault("dataini", null);
        String vDtFim = (String) parametros.getOrDefault("datafim", null);
        logexecucao.Grava("parametros: codfil=" + vCodFil + ";codtes=" + vCodTes + ";Token=" + vToken + ";dataini=" + vDtIni + ";datafim= " + vDtFim, caminho);
        Persistencia dbpadrao, dbsatellite;
        String vArqLogo;
        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "";
        String vData = "";

        try {
            //Validando se a matrícula informada não está vazia
            if (vCodFil == null || vCodFil.equals("")) {
                throw new StatusClientePstServException("filial_invalida");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            String vBancoDados = qToken.getBancoDados();

            String vSQLDiaSem;
            dbpadrao = this.pool.getConexao(vBancoDados);
            Consulta qTmpX;
            //vArqLogo = getLogo(dbpadrao.getEmpresa(), "0");

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\consultaTesCustodiaData.txt";
            }

            if (vDtIni.equals("") || vDtFim.length() != 8) {
                vData = "20231201"; //vData = getDataAtual("SQL");
            } else {
                vData = vDtIni;
            }

            try {

                vSQLDiaSem = " Case when DatePart(dw, Data) = 1 then 'Domingo' "
                        + "      when DatePart(dw, Data) = 2 then 'Segunda' "
                        + "      when DatePart(dw, Data) = 3 then 'Terça  ' "
                        + "      when DatePart(dw, Data) = 4 then 'Quarta ' "
                        + "         when DatePart(dw, Data) = 5 then 'Quinta ' "
                        + "         when DatePart(dw, Data) = 6 then 'Sexta  ' "
                        + "         when DatePart(dw, Data) = 7 then 'Sábado ' "
                        + "    else '' "
                        + "    end as DiaSem, ";
                vSQL = "Select top 100 TesCustCC.CodFil, TesCustCC.Data, Clientes.Nred, " + vSQLDiaSem + "Sum(TesCustCC.ValorDecl) ValorDecl, Sum(TesCustCC.Entradas) Entradas, Sum(TesCustCC.Saidas) Saidas, \n"
                        + " Sum(TesCustCC.Saldo)*(Select top 1 isnull(Valor,0) from TesMoedas "
                        + " Left Join TesCustodiaME TesCustME on TesCustME.CodCli = '" + vCodTes + "' and TEsCustME.CodMoeda = TesMoedas.CodMoeda "
                        + " where TesCustME.CodCli = '" + vCodTes + "' and TesCustME.CodFil = " + vCodFil + " ) ValorConv,  "
                        + " Sum(TesCustCC.DifMaior) DifMaior, Sum(TesCustCC.DifMenor) DifMenor, Sum(TesCustCC.Saldo) Saldo  from TesCustCC "
                        + " Left Join Clientes on Clientes.Codigo = TesCustCC.CodCli "
                        + "                   and Clientes.CodFil = TesCustCC.CodFil "
                        + " where TesCustCC.CodFil = " + vCodFil
                        + "   and TesCustCC.CodCli = '" + vCodTes + "' \n"
                        //+ "   änd TesCustCC.Data >= "+vData
                        + " Group by TesCustCC.CodFil, TesCustCC.Data, Clientes.Nred "
                        + " order by Data Desc ";
                try {
                    qTmpX = new Consulta(vSQL, dbpadrao);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("TesCustodiaData - " + e.getMessage() + "\r\n"
                            + vSQL);
                }
                JSONObject vjnTesCustodia;
                JSONArray vjnTesCustodiaData = new JSONArray();
                int vConta = 0;

                while (qTmpX.Proximo()) {
                    vConta = ++vConta;

                    if (!qTmpX.getString("CodFil").equals(null)) {
                        try {
                            vjnTesCustodia = new JSONObject();
                            vjnTesCustodia.put("diasem", qTmpX.getString("DiaSem"));
                            vjnTesCustodia.put("data", qTmpX.getString("Data"));
                            vjnTesCustodia.put("valordecl", qTmpX.getString("ValorDecl"));
                            vjnTesCustodia.put("entradas", qTmpX.getString("Entradas"));
                            vjnTesCustodia.put("saidas", qTmpX.getString("Saidas"));
                            vjnTesCustodia.put("valorconv", qTmpX.getString("ValorConv"));
                            vjnTesCustodia.put("difmaior", qTmpX.getString("DifMaior"));
                            vjnTesCustodia.put("difmenor", qTmpX.getString("DifMenor"));
                            vjnTesCustodia.put("saldo", qTmpX.getString("Saldo"));
                            vjnTesCustodia.put("nred", qTmpX.getString("Nred"));
                            vjnTesCustodiaData.put(vjnTesCustodia);
                        } catch (Exception e) {
                            logexecucao.Grava("Erro geração Retorno Json", caminho);
                            throw new Exception("TesCustidiaData - " + e.getMessage() + "\r\n");
                        }
                    }

                    vjnRetorno.put("tescustodiadata", vjnTesCustodiaData);
                    //                  pstServJO.put("funcion", funcionJA);                

                }
                qTmpX.Close();
                if (vConta == 0) {
                    vjnRetorno.put("codfil", "0");
                    vjnRetorno.put("resposta", "TESORERIA NO ENCONTRADA EN ESTA FECHA");
                    vjnRetorno.put("tescustodiadata", vjnTesCustodiaData);
                }
            } catch (Exception e) {
                logexecucao.Grava("Erro try principal " + e.getMessage(), caminho);
                throw new Exception("TesCustodiaData - " + e.getMessage() + "\r\n"
                        + vSQL);

            }
        } finally {
            vRetorno = vjnRetorno.toString();
            return vRetorno;
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/consultaTesCustodiaContas")
    public String consultaTesCustodiaContas(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\consultaTesCustodiaContas.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vCodFil = (String) parametros.getOrDefault("codfil", null); //Obrigatorio
        String vCodTes = (String) parametros.getOrDefault("codtes", null); //Obrigatorio
        String vToken = (String) parametros.getOrDefault("token", null);
        //String vPW = (String) parametros.getOrDefault("pw", null);
        String vDtIni = (String) parametros.getOrDefault("dataini", null);
        String vDtFim = (String) parametros.getOrDefault("datafim", null);
        logexecucao.Grava("parametros: codfil=" + vCodFil + ";codtes=" + vCodTes + ";Token=" + vToken + ";dataini=" + vDtIni + ";datafim= " + vDtFim, caminho);
        Persistencia dbpadrao, dbsatellite;
        String vArqLogo;
        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "";
        String vData = "";

        try {
            //Validando se a matrícula informada não está vazia
            if (vCodFil == null || vCodFil.equals("")) {
                throw new StatusClientePstServException("filial_invalida");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            String vBancoDados = qToken.getBancoDados();

            String vSQLDiaSem;
            dbpadrao = this.pool.getConexao(vBancoDados);
            Consulta qTmpX;
            //vArqLogo = getLogo(dbpadrao.getEmpresa(), "0");

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\consultaTesCustodiaData.txt";
            }

            if (vDtIni.equals("") || vDtFim.length() != 8) {
                vData = "20231201"; //vData = getDataAtual("SQL");
            } else {
                vData = vDtIni;
            }

            try {

                vSQL = "Select TesCustCC.CodFil, TesCustCC.ContaTes, TesContas.Descricao, ValorDecl, Entradas, Saidas, DifMaior, DifMenor, Saldo  from TesCustCC \n"
                        + " left Join TesContas on TesContas.Codigo = TesCustCC.ContaTes \n"
                        + " where TesCustCC.CodFil = " + vCodFil + "\n"
                        + "   and TesCustCC.CodCli = '" + vCodTes + "'\n"
                        + "   and TesCustCC.Data   = '" + vData + "'\n"
                        + "   and ValorDecl+Entradas+Saidas > 0 \n"
                        + " order by ContaTes ";
                try {
                    qTmpX = new Consulta(vSQL, dbpadrao);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("TesCustodiaConta- " + e.getMessage() + "\r\n"
                            + vSQL);
                }
                JSONObject vjnTesCustodia;
                JSONArray vjnTesCustodiaData = new JSONArray();
                int vConta = 0;

                while (qTmpX.Proximo()) {
                    vConta = ++vConta;

                    if (!qTmpX.getString("CodFil").equals(null)) {
                        try {
                            vjnTesCustodia = new JSONObject();
                            vjnTesCustodia.put("contates", qTmpX.getString("ContaTes"));
                            vjnTesCustodia.put("descricao", qTmpX.getString("Descricao"));
                            vjnTesCustodia.put("valordecl", qTmpX.getString("ValorDecl"));
                            vjnTesCustodia.put("entradas", qTmpX.getString("Entradas"));
                            vjnTesCustodia.put("saidas", qTmpX.getString("Saidas"));
                            vjnTesCustodia.put("difmaior", qTmpX.getString("DifMaior"));
                            vjnTesCustodia.put("difmenor", qTmpX.getString("DifMenor"));
                            vjnTesCustodia.put("saldo", qTmpX.getString("Saldo"));
                            vjnTesCustodiaData.put(vjnTesCustodia);
                        } catch (Exception e) {
                            logexecucao.Grava("Erro geração Retorno Json", caminho);
                            throw new Exception("TesCustidiaContas - " + e.getMessage() + "\r\n");
                        }
                    }

                    vjnRetorno.put("tescustodiacointa", vjnTesCustodiaData);
                    //                  pstServJO.put("funcion", funcionJA);                

                }
                qTmpX.Close();
                if (vConta == 0) {
                    vjnRetorno.put("codfil", "0");
                    vjnRetorno.put("resposta", "TESORERIA NO ENCONTRADA EN ESTA FECHA");
                    vjnRetorno.put("tescustodiaconta", vjnTesCustodiaData);
                }
            } catch (Exception e) {
                logexecucao.Grava("Erro try principal " + e.getMessage(), caminho);
                throw new Exception("TesCustodiaConta - " + e.getMessage() + "\r\n"
                        + vSQL);

            }
        } finally {
            vRetorno = vjnRetorno.toString();
            return vRetorno;
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/integraSankhya")
    public String integraSankhya(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\integraSankhya.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vCodigo = (String) parametros.getOrDefault("codigo", null); //Obrigatorio
        String vCodfil = (String) parametros.getOrDefault("codfil", null);
        String vToken = (String) parametros.getOrDefault("token", null);
        String vIntegracao = (String) parametros.getOrDefault("integracao", null);

        logexecucao.Grava("parametros: Codigo=" + vCodigo + ";CodFil=" + vCodfil + "token=" + vToken, caminho);
        Persistencia dbpadrao, dbsatellite;
        String vArqLogo;

        JSONObject vjnRetorno = new JSONObject();

        if (vToken.equals("")) {
            vToken = "";
        }

        try {
            //Validando se a matrícula informada não está vazia
            if (vCodigo == null || vCodigo.equals("")) {
                throw new StatusClientePstServException("CodigoInvalido");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            String vBancoDados = qToken.getBancoDados();
            dbpadrao = this.pool.getConexao(vBancoDados);
            Consulta qTmpX;

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }
            String vSQL = "";
            String vTpPessoa = "J";
            String vNRed = "";
            String vCodCid = "";
            String vCPFCNPJ = "";
            String vEmailCl = "";
            String vEndCli = "";
            String vTelefone = "";
            String vComplemento = "";
            String vCEP = "";
            String vCidade = "";
            String vCodParc = "";
            String vData = "";
            String vCodOper = "";
            String vTpVenda = "";
            String vCodVenda = "";
            String vCodEmp = "";
            String vTipoMov = "";
            String vCodProd = "";
            String vValor = "";
            String vTokenDB = "";
            String vNF = "";
            String vPraca = "";

            try {

                if (vIntegracao.equals("clientes")) {
                    vSQL = " Select * from Clientes \n"
                            + " where Clientes.Codigo = '" + vCodigo + "'\n"
                            + "   and Clientes.CodFil = " + vCodfil + " \n";
                } else {
                    vSQL = " Select NFiscal.*, NFItens.* from NFiscal \n"
                            + " Left Join NFItens on NFItens.Numero = NFiscal.Numero"
                            + "                  and NFItens.Praca = NFiscal.Praca "
                            + " where NFiscal.Numero = '" + vCodigo + "'\n"
                            + "   and NFiscal.praca = " + vCodfil + " \n";
                }
                try {
                    qTmpX = new Consulta(vSQL, dbpadrao);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("Integracao.sankhya - " + e.getMessage() + "\r\n"
                            + vSQL);
                }
                JSONObject vjnClientes;
                JSONArray vjnClientesGer = new JSONArray();
                int vConta = 0;
                //if (qTmpX.getString("Matr") != "") {
                //.Proximo();
                //qTmpX.                
                while (qTmpX.Proximo()) {
                    vConta = ++vConta;
                    if (vIntegracao.equals("clientes")) {
                        if (!qTmpX.getString("Codigo").equals(null)) {

                            try {

                                vNRed = qTmpX.getString("Nred");
                                vCidade = qTmpX.getString("Cidade");
                                if (!qTmpX.getString("CGC").equals("")) {
                                    vCPFCNPJ = qTmpX.getString("CGC");
                                } else {
                                    vCPFCNPJ = qTmpX.getString("CPF");
                                }
                                vEmailCl = qTmpX.getString("Email");
                                vEndCli = qTmpX.getString("Ende");
                                vTelefone = qTmpX.getString("Fone1");
                                vComplemento = qTmpX.getString("EndCob");
                                vCEP = qTmpX.getString("Cep");
                                obterTokenSankhya(vToken, vToken, vCEP, vTpPessoa);
                                if (vIntegracao.equals("clientes")) {
                                    integraClientesSankhya(vRetorno, vTpPessoa, vNRed, vCidade, vCPFCNPJ, vEmailCl, vEndCli, vTelefone, vComplemento, vCEP, vToken, vCodigo, vCodfil);
                                } else {
                                    integraPedidoSankhya(vRetorno, vCodParc, vData, vCodOper, vTpVenda, vCodVenda, vCodEmp, vTipoMov, vCodProd, vValor, vTokenDB, vNF, vPraca);
                                }
                                vjnClientes = new JSONObject();
                                vjnClientes.put("Retorno", vRetorno);

                                vjnClientesGer.put(vjnClientesGer);
                            } catch (Exception e) {
                                logexecucao.Grava("Erro geração Retorno Json", caminho);
                                throw new Exception("FuncionPessoa.obterFotoPw - " + e.getMessage() + "\r\n");
                            }
                        }
                    } else {
                        obterTokenSankhya(vToken, vToken, vCEP, vTpPessoa);
                        vCodParc = "15";
                        vData = qTmpX.getString("Data");
                        vCodOper = "1718";
                        vTpVenda = "34";
                        vCodVenda = "0";
                        vCodEmp = "15";
                        vTipoMov = "V";
                        vCodProd = "6";
                        vValor = qTmpX.getString("Valor");//qTmpX.getString("Valor");
                        vTokenDB = vToken;
                        vNF = qTmpX.getString("Numero");
                        vPraca = qTmpX.getString("Praca");
                        integraPedidoSankhya(vRetorno, vCodParc, vData, vCodOper, vTpVenda, vCodVenda, vCodEmp, vTipoMov, vCodProd, vValor, vTokenDB, vNF, vPraca);
                    }
                    vjnRetorno.put("Retorno", vjnClientesGer);
                    //                  pstServJO.put("funcion", funcionJA);                

                }
                qTmpX.Close();
                if (vConta == 0) {
                    vjnRetorno.put("codigo", "0");
                    vjnRetorno.put("mesagem", "CLINTE NAO CADASTRADO");
                    vjnRetorno.put("retorno", vjnClientesGer);
                }
            } catch (Exception e) {
                logexecucao.Grava("Erro try principal " + e.getMessage(), caminho);
                throw new Exception("FuncionPessoa.obterFotoPw - " + e.getMessage() + "\r\n"
                        + vSQL);

            }
        } finally {
            vRetorno = vjnRetorno.toString();
            return vRetorno;
            /*Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();*/
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/integraEGuardian")
    public Response integraEGuardian(String param) throws UnsupportedEncodingException, IOException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vCodCli = (String) parametros.getOrDefault("codcli", null);
        String vNRed = (String) parametros.getOrDefault("nred", null);
        String vEnde = (String) parametros.getOrDefault("ende", null);
        String vCidade = (String) parametros.getOrDefault("cidade", null);
        String vCep = (String) parametros.getOrDefault("cep", null);
        String vUF = (String) parametros.getOrDefault("uf", null);
        String vFone = (String) parametros.getOrDefault("fone", null);
        String vData = (String) parametros.getOrDefault("data", null);
        String vDtAlter = (String) parametros.getOrDefault("data", null);
        String vCNPJ = (String) parametros.getOrDefault("cnpj", null);
        String vEmail = (String) parametros.getOrDefault("email", null);
        String vCNPJTransp = (String) parametros.getOrDefault("cnpjtransp", null);
        String vValor = (String) parametros.getOrDefault("valor", null);

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnNumero = new JSONObject();

        try {
            cadastraClienteEguard(vCodCli, vNRed, vEnde, vCidade, vCep, vUF, vFone, vData, vDtAlter, vCNPJ, vEmail);
            if (vRetornoEGuardian.contains("Integrada/Atualizada")) {
                cadastraMovFinEguard(vCodCli, vNRed, vEnde, vCidade, vCep, vUF, vFone, vData, vDtAlter, vCNPJ, vEmail, vValor, vCNPJTransp);
                vRetorno = vRetornoEGuardian;
            }
        } finally {
            //vjnRetorno.put("resposta", vjnPessoaFoto);
            vRetorno = Response.serverError().toString();

            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/autenticarUsuario")
    public Response autenticarUsuario(String param) throws UnsupportedEncodingException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\logautenticarUsuario.txt";
        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vEmail = (String) parametros.getOrDefault("email", null); //Obrigatorio
        String vToken = (String) parametros.getOrDefault("token", null);
        String vPW = (String) parametros.getOrDefault("pw", null);
        logexecucao.Grava("parametros: email=" + vEmail + ";Token=" + vToken + ";PW=" + vPW, caminho);
        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "", vTokenGerado = "", vCodPessoaBD = "";
        String vPathWeb = "";
        String vPathLocal = "";
        String vPathFotoPonto = "";
        String vPathFotoPontoLocal = "";

        try {
            //Validando se a matrícula informada não está vazia
            if (vEmail == null || vEmail.equals("")) {
                throw new StatusClientePstServException("EmailInvalido");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            dbsatellite.FechaConexao();

            String vBancoDados = "";

            Consulta qTmpX;

            String vData = getDataAtual("SQL");
            String vFace = "ECAF";

            try {

                String vSQLAdic = "(Select Top 1 Codigo from tokens where BancoDados = PessoaLogin.BancoDados and DtValid > Getdate()) Token ";
                vSQL = " Select Paramet.Sequencia, Pessoa.Codigo CodPessoa, Pessoa.Nome,  \n"
                        + " PessoaDet.FaceID, PessoaDet.Foto1, PessoaDet.Foto2, PessoaDet.Foto3, \n"
                        + " Pessoa.Fone1, Pessoa.Email, PessoaLogin.BancoDados, PessoaLogin.CodPessoaBD, \n"
                        + " Paramet.Nome_Empr, Paramet.Filial_Pdr, \n"
                        + vSQLAdic
                        + "from Pessoa \n"
                        + " Left Join PessoaDet on PessoaDet.Codigo = Pessoa.Codigo \n"
                        + "                     and PessoaDet.Ordem = (Select Max(Ordem) From PessoaDet PD where PD.Codigo = Pessoa.Codigo)"
                        + " Left Join PessoaLogin on PessoaLogin.Codigo = Pessoa.Codigo "
                        + " Left Join Paramet on Paramet.Path = PessoaLogin.BancoDados "
                        + " where Pessoa.Email = '" + vEmail + "' \n"
                        + "   and Pessoa.PWWeb = '" + vPW + "' \n"
                        + " Order By Paramet.Path, Paramet.Filial_Pdr \n";
                try {
                    qTmpX = new Consulta(vSQL, dbsatellite);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("AutenticarUsuario - " + e.getMessage() + "\r\n"
                            + vSQL);
                }

                JSONObject vjnEnvio;
                JSONArray vjnArray = new JSONArray();
                JSONObject vjnEnvioP;
                JSONArray vjnArrayP = new JSONArray();
                int vConta = 0;
                String vBDx = "";
                String vPath = "";
                String vSequencia = "";
                String vCodPessoa = "";
                String vTokenAut = "";
                while (qTmpX.Proximo()) {
                    vConta = ++vConta;
                    if (!qTmpX.getString("Sequencia").equals(vSequencia)) {
                        vPath = qTmpX.getString("BancoDados");
                        vSequencia = qTmpX.getString("Sequencia");

                        vBDx = qTmpX.getString("BancoDados");
                        vCodPessoa = qTmpX.getString("CodPessoa");
                        vCodPessoaBD = qTmpX.getString("CodPessoaBD");
                        vTokenAut = qTmpX.getString("Token");

                        if ((!qTmpX.getString("CodPessoa").equals(null))
                                && (vConta == 1)) {
                            try {
                                vjnEnvio = new JSONObject();
                                vjnEnvio.put("codpessoa", vCodPessoa);
                                vjnEnvio.put("nome", qTmpX.getString("Nome"));
                                vjnEnvio.put("fone", qTmpX.getString("Fone1"));
                                vjnEnvio.put("email", qTmpX.getString("Email"));

                                vjnEnvio.put("bancodados", vBDx);
                                vjnEnvio.put("nome_empr", qTmpX.getString("Nome_Empr"));
                                vjnEnvio.put("filial_pdr", qTmpX.getString("Filial_Pdr"));
                                vjnEnvio.put("codpessoabd", vCodPessoaBD);
                                vjnEnvio.put("token", vTokenAut);
                                vjnArray.put(vjnEnvio);

                            } catch (Exception e) {
                                logexecucao.Grava("Erro geração Retorno Json", caminho);
                                throw new Exception("AutenticarUsuario - " + e.getMessage() + "\r\n");
                            }
                        } else if (vConta > 1) {
                            vjnEnvio = new JSONObject();
                            vjnEnvio.put("codpessoa", vCodPessoa);
                            vjnEnvio.put("nome", qTmpX.getString("Nome"));
                            vjnEnvio.put("fone", qTmpX.getString("Fone1"));
                            vjnEnvio.put("email", qTmpX.getString("Email"));
                            vjnEnvio.put("bancodados", vBDx);
                            vjnEnvio.put("codpessoabd", vCodPessoaBD);
                            vjnEnvio.put("pathlocal", vPathLocal);
                            vjnEnvio.put("pathweb", vPathWeb);
                            vjnEnvio.put("bancodados", vBDx);
                            vjnEnvio.put("nome_empr", qTmpX.getString("Nome_Empr"));
                            vjnEnvio.put("filial_pdr", qTmpX.getString("Filial_Pdr"));
                            vjnEnvio.put("codpessoabd", vCodPessoaBD);
                            vjnEnvio.put("token", qTmpX.getString("Token"));
                            vjnArray.put(vjnEnvio);
                        }
                        vjnRetorno.put("login", vjnArray);
                    }
                }
                if (vConta == 1) {
                    //Gerar token no Banco Central - Somente 1 PessoaLogin
                    TOKENSDao tokensDao = new TOKENSDao();
                    TOKENS vGerToken = new TOKENS();
                    vGerToken.setBancoDados(vBDx);
                    vGerToken.setModulo("SATMOB");
                    vGerToken.setChave("WS");
                    vGerToken.setData(getDataAtual("SQL"));
                    vGerToken.setHora(getDataAtual("HORA"));
                    vTokenGerado = tokensDao.gerarToken(vGerToken, dbsatellite);
                    TOKENS qToken = this.tokenDao.obterToken(vTokenGerado, dbsatellite);
                    dbpadrao = this.pool.getConexao(qToken.getBancoDados());
                    vSQL = "Select Pessoa.Matr, Funcion.Cargo, Funcion.Secao, Cargos.Descricao CargoDesc, \n"
                            + " PstServ.Secao, PstServ.Local, Filiais.CodFil, Filiais.Descricao FilialDesc \n"
                            + " from Pessoa "
                            + " Left Join Funcion on Funcion.Matr = Pessoa.Matr \n"
                            + " Left Join PstServ on PstServ.Secao =  Funcion.Secao \n"
                            + "                  and PstServ.CodFil = Funcion.CodFil \n"
                            + " Left Join Cargos on Cargos.Cargo = Funcion.Cargo "
                            + " Left Join Filiais on Filiais.Codfil = Funcion.CodFil \n"
                            + " where Pessoa.Codigo = " + vCodPessoaBD;
                    Consulta qTmpX2 = new Consulta(vSQL, dbpadrao);
                    qTmpX2.select();
                    vjnEnvioP = new JSONObject();
                    while (qTmpX2.Proximo()) {
                        if (!qTmpX2.getString("Matr").equals(null)) {
                            vjnEnvioP.put("token", vTokenGerado);
                            vjnEnvioP.put("matr", qTmpX2.getString("Matr"));
                            vjnEnvioP.put("codfil", qTmpX2.getString("Codfil"));
                            vjnEnvioP.put("filialdesc", qTmpX2.getString("FilialDesc"));
                            vjnEnvioP.put("cargo", qTmpX2.getString("Cargo"));
                            vjnEnvioP.put("cargodesc", qTmpX2.getString("CargoDesc"));
                            vjnEnvioP.put("secao", qTmpX2.getString("Secao"));
                            vjnEnvioP.put("posto", qTmpX2.getString("Local"));
                        } else {
                            vjnEnvioP.put("matr", "0");
                        }
                        vjnArrayP.put(vjnEnvioP);
                    }
                    qTmpX2.Close();
                    dbpadrao.FechaConexao();
                }
                qTmpX.Close();
                if (vConta == 0) {
                    vjnEnvio = new JSONObject();
                    vjnEnvio.put("codpessoa", "0");
                    vjnEnvio.put("nome", "USUARIO OU SENHA INCORRETA");
                    vjnEnvio.put("nfoto1", "NAO REGISTRADO");
                    vjnEnvio.put("nfoto2", "NAO REGISTRADO");
                    vjnEnvio.put("nfoto3", "NAO REGISTRADO");
                    vjnEnvio.put("codigo", "0");
                    vjnArray.put(vjnEnvio);
                    vjnRetorno.put("login", vjnArray);
                }
            } catch (Exception e) {
                logexecucao.Grava("Erro try principal " + e.getMessage(), caminho);
                throw new Exception("AutenticarPessoa - " + e.getMessage() + "\r\n"
                        + vSQL);

            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @GET
    @Path("/integraCICF")
    @Produces(MediaType.APPLICATION_JSON)
    public Response integraCICF(@QueryParam("token") String vToken, @QueryParam("empresa") String vEmpresa, @QueryParam("dataini") String vDataIni, @QueryParam("datafim") String vDataFim) {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\IntegraCofres\\"
                + "\\integraCICF.txt";
        // TODO ler body em json
        //String param;
        //Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        //String vToken = (String) parametros.getOrDefault("token", null);
        //String vChaveGTVE = (String) parametros.getOrDefault("chavegtve", null);
        if (vToken.equals("")) {
            vToken = "C0574EE251E7EB197650B332CEE4750C";
        }
        logexecucao.Grava("parametros: token=" + vToken + ";empresa=" + vEmpresa, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnEnvio = new JSONObject();
        JSONArray vjnArray = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vEmpresa == null || vEmpresa.equals("")) {
                throw new StatusClientePstServException("Empresa Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "", vSQLExec = "", vBancoDados = "";
            String vCodCofre = "";
            String vData = "";
            String vDiaSem = "";
            String vFeriado = "";
            String vVlrRecAntesCorte = "";
            String vHrRecAntesCorte = "";
            String vVlrRecAposCorte = "";
            String vHrRecAposCorte = "";
            String vCredD0Rec = "";
            String vCredD0RecAposCorte = "";
            String vCredD1Rec = "";
            String vCredD0 = "";
            String vCredD1 = "";
            String vCredProxDU = "";
            String vCredDiaAnt = "";
            String vCredDiaD0Rec = "";
            String vCredCorte = "";
            String vVlrTotalCred = "";
            String vVlrTotalRec = "";
            String vSaldoFisTotal = "";
            String vSaldoFisCred = "";
            String vSaldoFisCst = "";
            String vDataStr = "";
            String vDiaSemana = "";
            Consulta qTmpX;
            vBancoDados = qToken.getBancoDados();

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }

            vSQL = "Select TesCofresRes.*,  \n"
                    + "Case    when DiaSem = 1 then 'Domingo  '   \n"
                    + "        when DiaSem = 2 then 'Segunda  '   \n"
                    + "        when DiaSem = 3 then 'Terça    '   \n"
                    + "        when DiaSem = 4 then 'Quarta   '   \n"
                    + "        when DiaSem = 5 then 'Quinta   '   \n"
                    + "        when DiaSem = 6 then 'Sexta    '   \n"
                    + "        when DiaSem = 7 then 'Sabado   ' else '' end DiaSemana from TesCofresRes  \n"
                    + "Left Join Clientes on  Clientes.CodCofre = TesCofresRes.CodCofre\n"
                    + "where Data = '10/19/2023'   \n"
                    + "  and TesCofresRes.CodCofre in (Select Clientes.CodCofre  From PessoaCliAut\n"
                    + "           Left Join Clientes  on PessoaCliAut.CodCli = Clientes.Codigo\n"
                    + "                              and PessoaCliAut.CodFil = Clientes.CodFil\n"
                    + "           Left Join Pessoa on Pessoa.Codigo = PessoaCliAut.Codigo\n"
                    + "           Where Clientes.CodFil >= 1    \n"
                    + "             and  Pessoa.Email =  '<EMAIL>')\n"
                    + "order by TesCofresRes.Data Desc ";
            try {
                qTmpX = new Consulta(vSQL, dbpadrao);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                throw new Exception("integraCICF - " + e.getMessage() + "\r\n"
                        + vSQL);
            }

            int vConta = 0;

            while (qTmpX.Proximo()) {
                vConta++;
                vjnEnvio = new JSONObject();
                vCodCofre = qTmpX.getString("CodCofre");
                vData = qTmpX.getString("Data");
                vDiaSem = qTmpX.getString("DiaSem");
                vFeriado = qTmpX.getString("Feriado");
                vVlrRecAntesCorte = qTmpX.getString("VlrRecAntesCorte");
                vHrRecAntesCorte = qTmpX.getString("HrRecAntesCorte");
                vVlrRecAposCorte = qTmpX.getString("VlrRecAposCorte ");
                vHrRecAposCorte = qTmpX.getString("HrRecAposCorte");
                vCredD0Rec = qTmpX.getString("CredD0Rec");
                vCredD0RecAposCorte = qTmpX.getString("CredD0RecAposCorte");
                vCredD1Rec = qTmpX.getString("CredD1Rec");
                vCredD0 = qTmpX.getString("CredD0");
                vCredD1 = qTmpX.getString("CredD1");
                vCredProxDU = qTmpX.getString("CredProxDU");
                vCredDiaAnt = qTmpX.getString("CredDiaAnt");
                vCredDiaD0Rec = qTmpX.getString("CredDiaD0Rec");
                vCredCorte = qTmpX.getString("CredCorte");
                vVlrTotalCred = qTmpX.getString("VlrTotalCred");
                vVlrTotalRec = qTmpX.getString("VlrTotalRec");
                vSaldoFisTotal = qTmpX.getString("SaldoFisTotal");
                vSaldoFisCred = qTmpX.getString("SaldoFisCred");
                vSaldoFisCst = qTmpX.getString("SaldoFisCst");
                vDataStr = qTmpX.getString("DataStr");
                vDiaSemana = qTmpX.getString("DiaSemana");

                vjnEnvio.put("CodCofre", vCodCofre);
                vjnEnvio.put("Data", vData);
                vjnEnvio.put("DiaSem", vDiaSem);
                vjnEnvio.put("Feriado", vFeriado);
                vjnEnvio.put("VlrRecAntesCorte", vVlrRecAntesCorte);
                vjnEnvio.put("HrRecAntesCorte", vHrRecAntesCorte);
                vjnEnvio.put("VlrRecAposCorte", vVlrRecAposCorte);
                vjnEnvio.put("HrRecAposCorte", vHrRecAposCorte);
                vjnEnvio.put("CredD0Rec", vCredD0Rec);
                vjnEnvio.put("CredD0RecAposCorte", vCredD0RecAposCorte);
                vjnEnvio.put("CredD1Rec", vCredD1Rec);
                vjnEnvio.put("CredD0", vCredD0);
                vjnEnvio.put("CredD1", vCredD1);
                vjnEnvio.put("CredProxDU", vCredProxDU);
                vjnEnvio.put("CredDiaAnt", vCredDiaAnt);
                vjnEnvio.put("CredDiaD0Rec", vCredDiaD0Rec);
                vjnEnvio.put("CredCorte", vCredCorte);
                vjnEnvio.put("VlrTotalCred", vVlrTotalCred);
                vjnEnvio.put("VlrTotalRec", vVlrTotalRec);
                vjnEnvio.put("SaldoFisTotal", vSaldoFisTotal);
                vjnEnvio.put("SaldoFisCred", vSaldoFisCred);
                vjnEnvio.put("SaldoFisCst", vSaldoFisCst);
                vjnEnvio.put("DataStr", vDataStr);
                vjnEnvio.put("DiaSemana", vDiaSemana);

                vjnArray.put(vjnEnvio);
            }
            if (vConta == 0) {
                vjnEnvio.put("resposta", "NAO HA DADOS");
                vjnArray.put(vjnEnvio);
            }
            try {
                vjnRetorno.put("CofresRes", vjnArray);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("XMLGTVE - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @GET
    @Path("/ecoConsultaCliente")
    @Produces(MediaType.APPLICATION_JSON)
    public Response ecoConsultaCliente(@QueryParam("token") String vToken, @QueryParam("empresa") String vEmpresa, @QueryParam("fone") String vFone) {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\IntegraCofres\\"
                + "\\ecoConsultaCliente.txt";
        // TODO ler body em json
        //String param;
        //Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        //String vToken = (String) parametros.getOrDefault("token", null);
        //String vChaveGTVE = (String) parametros.getOrDefault("chavegtve", null);
        if (vToken.equals("")) {
            vToken = "C0574EE251E7EB197650B332CEE4750C";
        }
        logexecucao.Grava("parametros: token=" + vToken + ";empresa=" + vEmpresa + ";fone=" + vFone, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnEnvio = new JSONObject();
        JSONArray vjnArray = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vEmpresa == null || vEmpresa.equals("")) {
                throw new StatusClientePstServException("Empresa Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "", vSQLExec = "", vBancoDados = "";
            String vCEP = "";
            String vNome = "";
            String vEndereco = "";
            String vBairro = "";
            String vCidade = "";
            String vUF = "";
            String vObras = "";
            String vCNPJCPF = "";
            String vTempoDias = "";
            String vCacamba = "";
            String vDtUltMov = "";
            String vNomeObra = "";
            Consulta qTmpX;
            
            vBancoDados = qToken.getBancoDados();
            if (vFone == null){
                vFone = "";
            }

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }
            int vConta = 0;
            if (!vFone.equals("")) {
                vSQL = " SELECT Clientes.nred, Clientes.latitude, Clientes.longitude,Clientes.Ende, Clientes.Bairro, clientes.CGC, Clientes.CPF, \n"
                        + " Clientes.Cidade, Clientes.Estado, Clientes.CEP, Funcion.Nome, DATEDIFF(day,DtUltMov,convert(date,Getdate()))  Tempo_Dias, "
                        + " convert(date,CtrOperEquip.DtUltMov) DtUltMov_date,  CtrOperEquip.*,  \n"
                        + " Case when Isnull(CliFat.Limite,0) <= 0 then 10 else Isnull(CliFat.Limite,0) end Limite, "
                        + " Case when Isnull(Clientes.Limite,0) <= 0 then 10 else Isnull(Clientes.Limite,0) end LimiteLocal, Pedido.Solicitante, "
                        + " CliFat.NRed NredFat, FORMAT(DATEADD(day, Case when Isnull(CliFat.Limite,0) <= 0 then 10 else Isnull(CliFat.Limite,0) end, convert(date,CtrOperEquip.DtUltMov)), 'dd/MM/yyyy') dtPrevistoColeta, FORMAT(convert(date,CtrOperEquip.DtUltMov), 'dd/MM/yyyy') dtEntrega"
                        + " FROM CtrOperEquip \n"
                        + " LEFT JOIN Clientes ON Clientes.codigo = CtrOperEquip.codcli1 \n"
                        + "                  AND Clientes.codfil = CtrOperEquip.codfil \n"
                        + " LEFT JOIN Rt_Guias  on Rt_Guias.Guia = CtrOperEquip.Guia \n"
                        + "                    and Rt_Guias.Serie = CtrOperEquip.Serie \n"
                        + " LEFT JOIN Os_Vig  on OS_Vig.OS = Rt_Guias.OS \n"
                        + "                  and OS_Vig.CodFil = CtrOperEquip.CodFil \n"
                        + " LEFT JOIN Clientes CliFat  on CliFat.Codigo = OS_Vig.CliFat \n"
                        + "                          and CliFat.CodFil = OS_Vig.CodFil \n"
                        + " LEFT JOIN Pedido  on Pedido.SeqRota = CtrOperEquip.SeqRota \n"
                        + "                  and Pedido.Parada = CtrOperEquip.Parada \n"
                        + " LEFT JOIN Escala  on Escala.SeqRota = CtrOperEquip.SeqRota \n"
                        + " LEFT JOIN Funcion  on Funcion.Matr  = Escala.MatrMot"
                        + " WHERE "
                        + " CtrOperEquip.codfil = 1 and "
                        + " ((Clientes.Fone1 = '" + vFone + "' and Clientes.Fone1 <> '') or \n"
                        + "  (Clientes.Fone2 = '" + vFone + "' and Clientes.Fone2 <> ''))";
                try {
                    qTmpX = new Consulta(vSQL, dbpadrao);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("ecoConsultaCliente - " + e.getMessage() + "\r\n"
                            + vSQL);
                }

                while (qTmpX.Proximo()) {
                    vConta++;
                    vjnEnvio = new JSONObject();
                    vNome = qTmpX.getString("Nome");
                    vEndereco = qTmpX.getString("Ende");
                    vBairro = qTmpX.getString("Bairro");
                    vCidade = qTmpX.getString("Cidade");
                    vUF = qTmpX.getString("Estado");
                    vCacamba = qTmpX.getString("IDEquip");
                    vTempoDias = qTmpX.getString("TempoDias");
                    vNomeObra = qTmpX.getString("Nred");
                    vDtUltMov = qTmpX.getString("DtUltMov_Date");
                    if (!qTmpX.getString("CPF").equals("")) {
                        vCNPJCPF = qTmpX.getString("CPF");
                    } else {
                        vCNPJCPF = qTmpX.getString("CGC");
                    }
                    vObras = "";
                    vCEP = qTmpX.getString("CEP");
                    vjnEnvio.put("CNPJCPF", vCNPJCPF);
                    vjnEnvio.put("Nome", vNome);
                    vjnEnvio.put("Endereco", vEndereco);
                    vjnEnvio.put("Bairro", vBairro);
                    vjnEnvio.put("Cidade", vCidade);
                    vjnEnvio.put("UF", vUF);
                    vjnEnvio.put("CEP", vCEP);
                    vjnEnvio.put("TempoDias", vTempoDias);
                    vjnEnvio.put("Cacamba", vCacamba);
                    vjnEnvio.put("DtUltMov", vDtUltMov);
                    vjnEnvio.put("Obra", vNomeObra);
                    vjnArray.put(vjnEnvio);
                }
            }            
            if (vConta == 0) {
               String vNredCliente = "";
                vSQL = " Select * from Clientes where  ((Clientes.Fone1 = '" + vFone + "' and Clientes.Fone1 <> '') or \n"
                        + "  (Clientes.Fone2 = '" + vFone + "' and Clientes.Fone2 <> ''))";
                try {
                    qTmpX = new Consulta(vSQL, dbpadrao);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("ecoConsultaCliente - " + e.getMessage() + "\r\n"
                            + vSQL);
                }

                while (qTmpX.Proximo()) {
                    vConta++;
                    vNredCliente = qTmpX.getString("Nred");
                }
                if (vConta == 0){
                   vjnEnvio.put("resposta", "TELEFONE INEXISTENTE! CLIENTE NAO CADASTRADO");
                   vjnArray.put(vjnEnvio);
                }else{
                   vjnEnvio.put("resposta", "Nome:"+vNredCliente);
                   vjnEnvio.put("resposta", "Fone:"+vFone);                   
                   vjnArray.put(vjnEnvio);
                }
            }
            try {
                vjnRetorno.put("Obras", vjnArray);
                vjnRetorno.put("Total", Integer.toString(vConta));
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("Eco OBRAS - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @GET
    @Path("/ecoConsultaFatura")
    @Produces(MediaType.APPLICATION_JSON)
    public Response ecoConsultaFatura(@QueryParam("token") String vToken, @QueryParam("empresa") String vEmpresa, @QueryParam("cpfcnpj") String vCPFCNPJ, @QueryParam("dataini") String vDataIni, @QueryParam("datafim") String vDataFim) {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\IntegraCofres\\"
                + "\\ecoConsultaFatura.txt";
        // TODO ler body em json
        //String param;
        //Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        //String vToken = (String) parametros.getOrDefault("token", null);
        //String vChaveGTVE = (String) parametros.getOrDefault("chavegtve", null);
        if (vToken.equals("")) {
            vToken = "C0574EE251E7EB197650B332CEE4750C";
        }
        logexecucao.Grava("parametros: token=" + vToken + ";empresa=" + vEmpresa + ";cpfcnpj=" + vCPFCNPJ+ ";dataini=" + vDataIni+ ";datafim=" + vDataFim, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnEnvio = new JSONObject();
        JSONArray vjnArray = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vEmpresa == null || vEmpresa.equals("")) {
                throw new StatusClientePstServException("Empresa Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "", vSQLExec = "", vBancoDados = "";
            String vDatavenc = "";
            String vValor = "";            
            String vCaminho = "";
            String vSQLAtraso = "";
            String vSQLFornec = "";
            String vCampos = "";
            String vDtVenc = "";
           
            String vTabelaJoins = "";
            
            Consulta qTmpX;
            
            vBancoDados = qToken.getBancoDados();
            if (vCPFCNPJ == null){
                vCPFCNPJ = "";
            }

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }
            int vConta = 0;  
            if (!vCPFCNPJ.equals("")) {
                                  vSQLAtraso = "Case "+
                 " when CReceber.Situacao = '"+"OK"+"' then Cast(Cast(CReceber.DtPagto As DateTime) - Cast(CReceber.DtVenc As DateTime) as Integer)"+
                 " when  CReceber.Situacao = '"+"PD"+"' and  Cast(CReceber.DtVenc As DateTime) < Cast(Convert(Char(08), getDate(), 112) as DateTime) "+
                 "  then Cast(Cast(Convert(Char(08), getDate(), 112) as DateTime) - Cast(CReceber.DtVenc As DateTime) as Integer) "+
                 " else 0 "+
                 " end ";
   vSQLFornec = "Select top 1 Fornec.Empresa from CReceberCobranca "+
                 " left join Fornec on Fornec.Codigo = CReceberCobranca.CodForn "+
                 " where CReceberCobranca.SeqReceber = CReceber.Sequencia "+
                 " order by bordero Desc ";
   vCampos = " Select Cast(CReceber.DtVenc as dateTime) DtVenc, Cast(CReceber.DtPrevPg as dateTime) DtPrevPg, Cast(CReceber.DtPagto as dateTime) DtPagto, "+
              " Cast(CReceber.Dt_Alter as dateTime) Dt_Alter, CReceber.*, Vendas.Rota, Vendas.Data DtVenda, Clientes.NRed ClienteNRed, Clientes.CNPJ, Filiais.Descricao FilialDesc, "+
              " TiposTit.Descricao TipoTitDesc, FormasPgto.Descricao FormaPgtoDesc, NFiscal.Data DtEmissao, CReceber.NossoNumero, CReceber.TCobran, NFiscal.NFERetorno, "+
              " TCobran.Descricao TCobranDesc, ContasFin.Descricao ContaFinDesc, ContasFin.Natureza, "+
              " Case "+
              "  When ContasFin.Natureza = 1 then 'Créditos' "+
              "  When ContasFin.Natureza = 2 then 'Débitos'"+
              "  When ContasFin.Natureza = 3 then 'Receitas'"+
              "  When ContasFin.Natureza = 4 then 'Despesas'"+
              "  When ContasFin.Natureza = 5 then 'Bancos' end NaturezaDesc, "+
              "CCusto.Descricao CCustoDesc, Cast(DtPagto as Datetime) DtPagto1, NFiscal.CodReceber, "+
              vSQLAtraso+" Atraso, CReceber.SitCob, OCItens.MotivoDesc, OCItens.Detalhes, CReceber.Bordero, ("+vSQLFornec+") EscCobranca, "+
              "NFiscal.Valor ValorBruto from CReceber ";
   vTabelaJoins =  " Left join ClientesEag Clientes   on  (CReceber.CodCli     = Clientes.Codigo  )  "+
                   " Left join Filiais                on  (CReceber.CodFil     = Filiais.CodFil   )  "+
                   " Left join TiposTit               on  (CReceber.TipoTit    = TiposTit.Codigo  )  "+
                   " Left join FormasPgto             on  (CReceber.FormaPgto  = FormasPgto.Codigo)  "+
                   " Left join ContasFin              on  (CReceber.ContaFin   = ContasFin.Codigo )  "+
                   " Left join CCusto                 on  (CReceber.CCusto     = CCusto.CCusto    )  "+
                   " left join Vendas                 on   Vendas.Numero       = CReceber.Pedido     "+
                   " left join OCItens                on   OCItens.CodTitulo   = CReceber.Sequencia  "+
                   " left join NFiscal                on   NFiscal.Numero      = CReceber.NF         "+
                   "                                  and  NFiscal.Praca       = CReceber.Praca      "+
                   " left join TCobran                on   TCobran.Codigo      = CReceber.TCobran    ";
     String vStrCPFCNPJ = "";
     if(vCPFCNPJ.length() == 11){
         vStrCPFCNPJ = "CPF = '";
     }else{
         vStrCPFCNPJ = "CNPJ = '";
     }
       vSQL = vCampos+" "+vTabelaJoins +
              " where CReceber.DtVenc >= '"+vDataIni+"'"+
              "   and CReceber.DtVenc <= '"+vDataFim+"'"+
              "   and Clientes."+vStrCPFCNPJ+vCPFCNPJ+"'"+
              "   and CReceber.DtPagto is null";

                try {
                    qTmpX = new Consulta(vSQL, dbpadrao);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("ecoConsultaCliente - " + e.getMessage() + "\r\n"
                            + vSQL);
                }

                while (qTmpX.Proximo()) {
                    vConta++;
                    vjnEnvio = new JSONObject();
                    vValor = qTmpX.getString("Valor");
                    vCaminho = "NAO LOCALIZADO";
                    vDtVenc = qTmpX.getString("DtVenc");
                    
                    vjnEnvio.put("CPFCNPJ", vCPFCNPJ);
                    vjnEnvio.put("Valor", vValor);
                    vjnEnvio.put("Data", vDtVenc);                    
                    vjnEnvio.put("Link", "");                    
                    vjnArray.put(vjnEnvio);
                }
            }
            if (vConta == 0) {
                vjnEnvio.put("resposta", "NAO HA DADOS");
                vjnArray.put(vjnEnvio);
            }
            try {
                vjnRetorno.put("Faturas", vjnArray);
                vjnRetorno.put("Total", Integer.toString(vConta));
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("Eco OBRAS - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }
    
    @GET
    @Path("/ecoConsultaClienteCad")
    @Produces(MediaType.APPLICATION_JSON)
    public Response ecoConsultaClienteCad(@QueryParam("token") String vToken, @QueryParam("empresa") String vEmpresa, @QueryParam("cpfcnpj") String vCPFCNPJ) {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\IntegraCofres\\"
                + "\\ecoConsultaClienteCad.txt";
        // TODO ler body em json
        //String param;
        //Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        //String vToken = (String) parametros.getOrDefault("token", null);
        //String vChaveGTVE = (String) parametros.getOrDefault("chavegtve", null);
        if (vToken.equals("")) {
            vToken = "C0574EE251E7EB197650B332CEE4750C";
        }
        logexecucao.Grava("parametros: token=" + vToken + ";empresa=" + vEmpresa + ";cpfcnpj=" + vCPFCNPJ, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnEnvio = new JSONObject();
        JSONArray vjnArray = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vEmpresa == null || vEmpresa.equals("")) {
                throw new StatusClientePstServException("Empresa Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "", vSQLExec = "", vBancoDados = "";
            String vCEP = "";
            String vNome = "";
            String vEndereco = "";
            String vBairro = "";
            String vCidade = "";
            String vUF = "";
            String vObras = "";
            String vCNPJCPF = "";
            String vTempoDias = "";
            String vCacamba = "";
            String vDtUltMov = "";
            String vNomeObra = "";
            Consulta qTmpX;
            vBancoDados = qToken.getBancoDados();

            if (vCPFCNPJ == null){
                vCPFCNPJ = "";
            } 
            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }

            int vConta = 0;
            if (!vCPFCNPJ.equals("")) {
                vSQL = " SELECT Clientes.nred, Clientes.latitude, Clientes.longitude,Clientes.Ende, Clientes.Bairro, clientes.CGC, Clientes.CPF, \n"
                        + " Clientes.Cidade, Clientes.Estado, Clientes.CEP, Funcion.Nome, DATEDIFF(day,DtUltMov,convert(date,Getdate()))  Tempo_Dias, "
                        + " convert(date,CtrOperEquip.DtUltMov) DtUltMov_date,  CtrOperEquip.*,  \n"
                        + " Case when Isnull(CliFat.Limite,0) <= 0 then 10 else Isnull(CliFat.Limite,0) end Limite, "
                        + " Case when Isnull(Clientes.Limite,0) <= 0 then 10 else Isnull(Clientes.Limite,0) end LimiteLocal, Pedido.Solicitante, "
                        + " CliFat.NRed NredFat, FORMAT(DATEADD(day, Case when Isnull(CliFat.Limite,0) <= 0 then 10 else Isnull(CliFat.Limite,0) end, convert(date,CtrOperEquip.DtUltMov)), 'dd/MM/yyyy') dtPrevistoColeta, FORMAT(convert(date,CtrOperEquip.DtUltMov), 'dd/MM/yyyy') dtEntrega"
                        + " FROM CtrOperEquip \n"
                        + " LEFT JOIN Clientes ON Clientes.codigo = CtrOperEquip.codcli1 \n"
                        + "                  AND Clientes.codfil = CtrOperEquip.codfil \n"
                        + " LEFT JOIN Rt_Guias  on Rt_Guias.Guia = CtrOperEquip.Guia \n"
                        + "                    and Rt_Guias.Serie = CtrOperEquip.Serie \n"
                        + " LEFT JOIN Os_Vig  on OS_Vig.OS = Rt_Guias.OS \n"
                        + "                  and OS_Vig.CodFil = CtrOperEquip.CodFil \n"
                        + " LEFT JOIN Clientes CliFat  on CliFat.Codigo = OS_Vig.CliFat \n"
                        + "                          and CliFat.CodFil = OS_Vig.CodFil \n"
                        + " LEFT JOIN Pedido  on Pedido.SeqRota = CtrOperEquip.SeqRota \n"
                        + "                  and Pedido.Parada = CtrOperEquip.Parada \n"
                        + " LEFT JOIN Escala  on Escala.SeqRota = CtrOperEquip.SeqRota \n"
                        + " LEFT JOIN Funcion  on Funcion.Matr  = Escala.MatrMot"
                        + " WHERE "
                        + " CtrOperEquip.codfil = 1 and "
                        + " (Clientes.CPF = '" + vCPFCNPJ + "'  or \n"
                        + "  Clientes.CGC = '" + vCPFCNPJ + "')";
                try {
                    qTmpX = new Consulta(vSQL, dbpadrao);
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("ecoConsultaCliente - " + e.getMessage() + "\r\n"
                            + vSQL);
                }

                while (qTmpX.Proximo()) {
                    vConta++;
                    vjnEnvio = new JSONObject();
                    vNome = qTmpX.getString("Nome");
                    vEndereco = qTmpX.getString("Ende");
                    vBairro = qTmpX.getString("Bairro");
                    vCidade = qTmpX.getString("Cidade");
                    vUF = qTmpX.getString("Estado");
                    vCacamba = qTmpX.getString("IDEquip");
                    vTempoDias = qTmpX.getString("TempoDias");
                    vNomeObra = qTmpX.getString("Nred");
                    vDtUltMov = qTmpX.getString("DtUltMov_Date");
                    if (!qTmpX.getString("CPF").equals("")) {
                        vCNPJCPF = qTmpX.getString("CPF");
                    } else {
                        vCNPJCPF = qTmpX.getString("CGC");
                    }
                    vObras = "";
                    vCEP = qTmpX.getString("CEP");
                    vjnEnvio.put("CNPJCPF", vCNPJCPF);
                    vjnEnvio.put("Nome", vNome);
                    vjnEnvio.put("Endereco", vEndereco);
                    vjnEnvio.put("Bairro", vBairro);
                    vjnEnvio.put("Cidade", vCidade);
                    vjnEnvio.put("UF", vUF);
                    vjnEnvio.put("CEP", vCEP);
                    vjnEnvio.put("TempoDias", vTempoDias);
                    vjnEnvio.put("Cacamba", vCacamba);
                    vjnEnvio.put("DtUltMov", vDtUltMov);
                    vjnEnvio.put("Obra", vNomeObra);
                    vjnArray.put(vjnEnvio);
                }
            }
            if (vConta == 0) {
                vjnEnvio.put("resposta", "CLIENTE NAO LOCALIZADO");
                vjnArray.put(vjnEnvio);
            }
            try {
                vjnRetorno.put("Obras", vjnArray);
                vjnRetorno.put("Total", Integer.toString(vConta));
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("Eco OBRAS - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    public String integraClientesSankhya(String vToken, String vTpPessoa, String vNRed, String vCodCid, String vCPFCNPJ, String vEmailCli,
            String vEndCli, String vTelefone, String vComplemento, String vCEP, String vTokenDB, String vCodCli, String vCodfil) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException, Exception {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\integraClientesSankhya.txt";
        // Definir a URL do Web Service                        

        vCodCid = "1499";

        try {
            // Comando PowerShell que você deseja executar            
            String vArquivo;
            vArquivo = "$headers = New-Object \"System.Collections.Generic.Dictionary[[String],[String]]\"\n"
                    + "$headers.Add(\"Authorization\", \"Bearer " + vToken + "\")\n"
                    + "$headers.Add(\"Content-Type\", \"application/json\")\n"
                    + "\n"
                    + "$body = \"{  `\"serviceName`\":`\"CRUDServiceProvider.saveRecord`\",\n"
                    + "`n   `\"requestBody`\":{\n"
                    + "`n      `\"dataSet`\":{\n"
                    + "`n         `\"rootEntity`\":`\"Parceiro`\",\n"
                    + "`n         `\"includePresentationFields`\":`\"S`\",\n"
                    + "`n         `\"dataRow`\":{\n"
                    + "`n            `\"localFields`\":{\n"
                    + "`n               `\"TIPPESSOA`\":{\n"
                    + "`n                  `\"`$`\":`\"" + vTpPessoa + "`\"\n"
                    + "`n               },               \n"
                    + "`n               `\"NOMEPARC`\":{\n"
                    + "`n                  `\"`$`\":`\"" + vNRed + "`\"\n"
                    + "`n               },               \n"
                    + "`n               `\"CODCID`\":{\n"
                    + "`n                  `\"`$`\":`\"" + vCodCid + "`\"\n"
                    + "`n               },               \n"
                    + "`n               `\"ATIVO`\":{\n"
                    + "`n                  `\"`$`\":`\"S`\"\n"
                    + "`n               },\n"
                    + "`n                `\"CLIENTE`\":{\n"
                    + "`n                    `\"`$`\":`\"S`\"\n"
                    + "`n                },\n"
                    + "`n                `\"CLASSIFICMS`\":{\n"
                    + "`n                    `\"`$`\":`\"C`\"\n"
                    + "`n                },\n"
                    + "`n                `\"CGC_CPF`\":{\n"
                    + "`n                    `\"`$`\":`\"" + vCPFCNPJ + "`\"\n"
                    + "`n                },\n"
                    + "`n                `\"EMAILCTE`\":{\n"
                    + "`n                    `\"`$`\":`\"" + vEmailCli + "`\"\n"
                    + "`n                },\n"
                    + "`n                `\"AD_ENDERECO_REL`\":{\n"
                    + "`n                    `\"`$`\":`\"" + vEndCli + "`\"\n"
                    + "`n                },\n"
                    + "`n                `\"TELEFONE`\":{\n"
                    + "`n                    `\"`$`\":`\"" + vTelefone + "`\"\n"
                    + "`n                },\n"
                    + "`n                `\"COMPLEMENTO`\":{\n"
                    + "`n                    `\"`$`\":`\"" + vComplemento + "`\"\n"
                    + "`n                },\n"
                    + "`n                `\"CEP`\":{\n"
                    + "`n                    `\"`$`\":`\"" + vCEP + "`\"\n"
                    + "`n                }\n"
                    + "`n                \n"
                    + "`n            }\n"
                    + "`n         }, `\"entity`\":{\n"
                    + "`n            `\"fieldset`\":{\n"
                    + "`n               `\"list`\":`\"CODPARC,TIPPESSOA,NOMEPARC,CODCID,ATIVO,CLIENTE,CLASSIFICMS,CGC_CPF,EMAILCTE,AD_ENDERECO_REL,TELEFONE, COMPLEMENTO, CEP`\"\n"
                    + "`n            }\n"
                    + "`n         }\n"
                    + "`n      }\n"
                    + "`n   }\n"
                    + "`n}\"\n"
                    + "\n"
                    + "$response = Invoke-RestMethod 'https://api.sankhya.com.br/gateway/v1/mge/service.sbr?serviceName=CRUDServiceProvider.saveRecord&outputType=json' -Method 'POST' -Headers $headers -Body $body\n"
                    + "$response | ConvertTo-Json";

            // Caminho para o arquivo do script PowerShell
            String scriptPath = "C:\\Clientes\\clientes.ps1";

            File file = new File(scriptPath);
            if (file.exists()) {
                file.delete();
            }
            FileWriter vArq = new FileWriter(scriptPath);
            PrintWriter gravarArq = new PrintWriter(vArq);

            gravarArq.printf(vArquivo);
            vArq.close();

            // Cria o ProcessBuilder com o comando para executar o script
            ProcessBuilder processBuilder = new ProcessBuilder("powershell.exe", "-File", scriptPath);

            // Redireciona a saída do processo para ler a resposta
            processBuilder.redirectErrorStream(true);

            // Inicia o processo
            Process process = processBuilder.start();

            // Lê a saída do processo
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            vRetorno = "";
            while ((line = reader.readLine()) != null) {
                vRetorno = vRetorno + line;
            }

            Persistencia dbpadrao, dbsatellite;

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vTokenDB, dbsatellite);

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            String vBancoDados = qToken.getBancoDados();
            dbpadrao = this.pool.getConexao(vBancoDados);
            Consulta qTmpX = null;

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }
            vRetorno = vRetorno.replace("null", "");

            String vSQLExec = "";
            Consulta SQLPdr;
            try {
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(vRetorno).getAsJsonObject();
                this.logerro.Grava(vRetorno, caminho);
                if (vRetorno.contains("statusMessage")) {
                    if (!jsonObject.get("statusMessage").getAsString().equals(null)) {
                        vRetorno = jsonObject.get("statusMessage").getAsString();
                        vRetorno = vRetorno.replace("'", " ");
                        vSQLExec = "Update Clientes Set \n"
                                + "InterfExt = '" + vRetorno + "' \n"
                                + " where Codigo = '" + vCodCli + "' \n"
                                + "   and Codfil = " + vCodfil;
                        SQLPdr = new Consulta(vSQLExec, dbpadrao);
                        SQLPdr.update();
                    } else {
                        vRetorno = vRetorno;
                    }
                    vRetorno = jsonObject.get("transactionid").getAsString();
                    vRetorno = vRetorno.replace("'", " ");
                    vSQLExec = "Update Clientes Set \n"
                            + "InterfExt = '" + vRetorno + "' \n"
                            + " where Codigo = '" + vCodCli + "' \n"
                            + "   and Codfil = " + vCodfil;
                    SQLPdr = new Consulta(vSQLExec, dbpadrao);
                    SQLPdr.update();
                } else {
                }
            } catch (JSONException e) {
                this.logerro.Grava(e.toString(), caminho);
            }
            // Aguarda o término do processo
            int exitCode = process.waitFor();
            //vRetorno = vRetorno + " - Processo encerrado com código de saída: " + exitCode;

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return vRetorno;
    }

    public String integraPedidoSankhya(String vToken, String vCodParc, String vData, String vCodOper, String vTpVenda, String vCodVenda,
            String vCodEmp, String vTipoMov, String vCodProd, String vValor, String vTokenDB, String vNF, String vPraca) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException, Exception {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\integraPedidoSankhya.txt";
        // Definir a URL do Web Service                                
        try {
            // Comando PowerShell que você deseja executar            
            String vArquivo;
            vArquivo = "$headers = New-Object \"System.Collections.Generic.Dictionary[[String],[String]]\"\n"
                    + "$headers.Add(\"Authorization\", \"Bearer " + vToken + "\")\n"
                    + "$headers.Add(\"Content-Type\", \"application/json\")\n"
                    + "\n"
                    + "$body = \"{\n"
                    + "`n    `\"serviceName`\": `\"CACSP.incluirNota`\",\n"
                    + "`n    `\"requestBody`\": {\n"
                    + "`n        `\"nota`\": {\n"
                    + "`n            `\"cabecalho`\": {\n"
                    + "`n                `\"NUNOTA`\": {},\n"
                    + "`n                `\"CODPARC`\": {\n"
                    + "`n                    `\"`$`\": `\"3`\"\n"
                    + "`n                },\n"
                    + "`n                `\"DTNEG`\": {\n"
                    + "`n                    `\"`$`\": `\"03/07/2023`\"\n"
                    + "`n                },\n"
                    + "`n                `\"CODTIPOPER`\": {\n"
                    + "`n                    `\"`$`\": `\"1718`\"\n"
                    + "`n                },\n"
                    + "`n                `\"CODTIPVENDA`\": {\n"
                    + "`n                    `\"`$`\": `\"34`\"\n"
                    + "`n                },\n"
                    + "`n                `\"CODVEND`\": {\n"
                    + "`n                    `\"`$`\": `\"0`\"\n"
                    + "`n                },\n"
                    + "`n                `\"CODEMP`\": {\n"
                    + "`n                    `\"`$`\": `\"15`\"\n"
                    + "`n                },\n"
                    + "`n                `\"TIPMOV`\": {\n"
                    + "`n                    `\"`$`\": `\"V`\"\n"
                    + "`n                },\n"
                    + "`n                `\"CODNAT`\": {\n"
                    + "`n                    `\"`$`\": `\"10101002`\"\n"
                    + "`n                },\n"
                    + "`n                `\"CODCENCUS`\": {\n"
                    + "`n                    `\"`$`\": `\"10600200`\"\n"
                    + "`n                },\n"
                    + "`n                `\"SERIE`\": {\n"
                    + "`n                    `\"`$`\": `\"14`\"\n"
                    + "`n                }\n"
                    + "`n            },\n"
                    + "`n            `\"itens`\": {\n"
                    + "`n                `\"INFORMARPRECO`\": `\"True`\",\n"
                    + "`n                `\"item`\": [\n"
                    + "`n                    {\n"
                    + "`n                        `\"NUNOTA`\": {},\n"
                    + "`n                        `\"CODPROD`\": {\n"
                    + "`n                            `\"`$`\": `\"6`\"\n"
                    + "`n                        },\n"
                    + "`n                        `\"QTDNEG`\": {\n"
                    + "`n                            `\"`$`\": `\"1`\"\n"
                    + "`n                        },\n"
                    + "`n                        `\"CODLOCALORIG`\": {\n"
                    + "`n                            `\"`$`\": `\"0`\"\n"
                    + "`n                        },\n"
                    + "`n                        `\"CODVOL`\": {\n"
                    + "`n                            `\"`$`\": `\"SV`\"\n"
                    + "`n                        },\n"
                    + "`n                        `\"PERCDESC`\": {\n"
                    + "`n                            `\"`$`\": `\"0`\"\n"
                    + "`n                        },\n"
                    + "`n                        `\"VLRUNIT`\": {\n"
                    + "`n                            `\"`$`\": `\"81.75`\"\n"
                    + "`n                        }\n"
                    + "`n                    }\n"
                    + "`n                ]\n"
                    + "`n            }\n"
                    + "`n        }\n"
                    + "`n    }\n"
                    + "`n}\"\n"
                    + "\n"
                    + "$response = Invoke-RestMethod 'https://api.sankhya.com.br/gateway/v1/mgecom/service.sbr?serviceName=CACSP.incluirNota&outputType=json ' -Method 'POST' -Headers $headers -Body $body\n"
                    + "$response | ConvertTo-Json";

            // Caminho para o arquivo do script PowerShell
            String scriptPath = "C:\\Clientes\\pedidos.ps1";

            File file = new File(scriptPath);
            if (file.exists()) {
                file.delete();
            }
            FileWriter vArq = new FileWriter(scriptPath);
            PrintWriter gravarArq = new PrintWriter(vArq);

            gravarArq.printf(vArquivo);
            vArq.close();

            // Cria o ProcessBuilder com o comando para executar o script
            ProcessBuilder processBuilder = new ProcessBuilder("powershell.exe", "-File", scriptPath);

            // Redireciona a saída do processo para ler a resposta
            processBuilder.redirectErrorStream(true);

            // Inicia o processo
            Process process = processBuilder.start();

            // Lê a saída do processo
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            vRetorno = "";
            while ((line = reader.readLine()) != null) {
                vRetorno = vRetorno + line;
            }

            Persistencia dbpadrao, dbsatellite;

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vTokenDB, dbsatellite);

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            String vBancoDados = qToken.getBancoDados();
            dbpadrao = this.pool.getConexao(vBancoDados);
            Consulta qTmpX = null;

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }
            vRetorno = vRetorno.replace("null", "");

            String vSQLExec = "";
            Consulta SQLPdr;
            try {
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(vRetorno).getAsJsonObject();
                this.logerro.Grava(vRetorno, caminho);
                if (vRetorno.contains("NUNOTA")) {
                    if (!jsonObject.get("NUNOTA").getAsString().equals(null)) {
                        vRetorno = jsonObject.get("NUNOTA").getAsString();
                        vRetorno = vRetorno.replace("'", " ");
                        vSQLExec = "Update NFiscal Set \n"
                                + "OBS = '" + vRetorno + "', \n"
                                + "NFEChave = '" + jsonObject.get("transactionid").getAsString() + "', \n"
                                + " where Numero = '" + vNF + "' \n"
                                + "   and praca = " + vPraca;
                        SQLPdr = new Consulta(vSQLExec, dbpadrao);
                        SQLPdr.update();
                    } else {
                        vRetorno = vRetorno;
                    }
                    vRetorno = jsonObject.get("transactionid").getAsString();
                    vRetorno = vRetorno.replace("'", " ");
                    vSQLExec = "Update Clientes Set \n"
                            + "InterfExt = '" + vRetorno + "' \n"
                            + " where Codigo = '" + vNF + "' \n"
                            + "   and Codfil = " + vPraca;
                    SQLPdr = new Consulta(vSQLExec, dbpadrao);
                    SQLPdr.update();
                } else {
                }
            } catch (JSONException e) {
                this.logerro.Grava(e.toString(), caminho);
            }
            // Aguarda o término do processo
            int exitCode = process.waitFor();
            //vRetorno = vRetorno + " - Processo encerrado com código de saída: " + exitCode;

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        return vRetorno;
    }

    public void cadastraClienteEguard(String vCodCli, String vNRed, String vEnde, String vCidade, String vCep, String vUF, String vFone, String vData, String vDtAlter, String vCNPJ, String vEmail) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\cadastraClienteEguard.txt";

        //vEmail = "<EMAIL>";
        try {

            URL url = new URL("http://************:9598/EGUARDIAN_API_INTEG_CLIENTES/CadastroClientes");
            String vArquivo;
            vArquivo = "$headers = New-Object \"System.Collections.Generic.Dictionary[[String],[String]]\"\n"
                    + "$headers.Add(\"Content-Type\", \"application/json\")\n"
                    + "\n"
                    + "$body = \"{\n"
                    + "`n\n"
                    + "`n    `\"Empresa`\": `\"EGUARDIAN`\",\n"
                    + "`n\n"
                    + "`n    `\"CdCliente`\": `" + vCodCli + "`,\n"
                    + "`n\n"
                    + "`n    `\"Clientes`\": {\n"
                    + "`n        `\"DeCliente`\": `\"" + vNRed + "`\",\n"
                    + "`n        \n"
                    + "`n        `\"CdTpCliente`\": `\"PF`\",\n"
                    + "`n        \n"
                    + "`n        `\"DeEndereco`\": `\"" + vEnde + "`\",\n"
                    + "`n        \n"
                    + "`n        `\"DeCidade`\": `\"" + vCidade + "`\",\n"
                    + "`n        \n"
                    + "`n        \n"
                    + "`n        `\"DeEstado`\": `\"" + vUF + "`\",\n"
                    + "`n        `\"DePais`\": `\"BRASIL`\",\n"
                    + "`n        `\"CdCep`\": `\"" + vCep + "`\",\n"
                    + "`n\n"
                    + "`n        `\"DeEnderecoRes`\": `\"" + vEnde + "`\",\n"
                    + "`n\n"
                    + "`n        `\"DeCidadeRes`\": `\"" + vCidade + "`\",\n"
                    + "`n\n"
                    + "`n        `\"DeEstadoRes`\": `\"" + vUF + "`\",\n"
                    + "`n\n"
                    + "`n        `\"DePaisRes`\": `\"BRASIL`\",\n"
                    + "`n\n"
                    + "`n        `\"CdCepRes`\": `\"" + vCep + "`\",\n"
                    + "`n\n"
                    + "`n        `\"DeEnderecoCml`\": `\"" + vEnde + "`\",\n"
                    + "`n\n"
                    + "`n        `\"DeCidadeCml`\": `\"" + vCidade + "`\",\n"
                    + "`n\n"
                    + "`n        `\"DeEstadoCml`\": `\"" + vUF + "`\",\n"
                    + "`n\n"
                    + "`n        `\"DePaisCml`\": `\"BRASIL`\",\n"
                    + "`n\n"
                    + "`n        `\"CdCepCml`\": `\"" + vCep + "`\",\n"
                    + "`n\n"
                    + "`n        `\"DeFone1`\": `\"" + vFone + "`\",\n"
                    + "`n\n"
                    + "`n        `\"DeFone2`\": `\"`\",\n"
                    + "`n\n"
                    + "`n        `\"DtAberturaRel`\": `\"" + vData + "`\",\n"
                    + "`n\n"
                    + "`n        `\"CicCpf`\": `\"" + vCNPJ + "`\",\n"
                    + "`n\n"
                    + "`n        `\"DsGrupoCliente`\": `\"SATELLITE`\",\n"
                    + "`n\n"
                    + "`n        `\"DtDesativacao`\": `\"1900-01-01`\",\n"
                    + "`n\n"
                    + "`n        `\"FlFundoInvest`\": false,\n"
                    + "`n\n"
                    + "`n        `\"FlCliEventual`\": false,\n"
                    + "`n\n"
                    + "`n        `\"DeResponsCadastro`\": `\"`\",\n"
                    + "`n\n"
                    + "`n        `\"DeConfCadastro`\": `\"`\",\n"
                    + "`n\n"
                    + "`n        `\"CdRisco`\": 1,\n"
                    + "`n\n"
                    + "`n        `\"CdNaic`\": `\"`\",\n"
                    + "`n\n"
                    + "`n        `\"DeLinhaNegocio`\": `\"`\",\n"
                    + "`n\n"
                    + "`n        `\"FlCadastroProc`\": false,\n"
                    + "`n\n"
                    + "`n        `\"FlNaoResidente`\": true,\n"
                    + "`n\n"
                    + "`n        `\"FlGrandesFortunas`\": false,\n"
                    + "`n\n"
                    + "`n        `\"DePaisSede`\": `\"`\",\n"
                    + "`n\n"
                    + "`n        `\"DeSitCadastro`\": `\"`\",\n"
                    + "`n\n"
                    + "`n        `\"FlBloqueado`\": false,\n"
                    + "`n\n"
                    + "`n        `\"CdRiscoInerente`\": 1,\n"
                    + "`n\n"
                    + "`n        `\"DtConstituicao`\": `\"1900-01-01`\",\n"
                    + "`n\n"
                    + "`n        `\"IpEletronico`\": `\"`\",\n"
                    + "`n\n"
                    + "`n        `\"DeEmail`\": `\"" + vEmail + "`\",\n"
                    + "`n\n"
                    + "`n        `\"FlRelacionamentoTerceiros`\": false,\n"
                    + "`n\n"
                    + "`n        `\"FlAdminCartoes`\": false,\n"
                    + "`n\n"
                    + "`n        `\"FlEmpresaTrust`\": false,\n"
                    + "`n\n"
                    + "`n        `\"FlFacilitadoraPagto`\": false,\n"
                    + "`n\n"
                    + "`n        `\"CdNatJuridica`\": `\"`\",\n"
                    + "`n\n"
                    + "`n        `\"FlEmpregulada`\": false,\n"
                    + "`n\n"
                    + "`n        `\"DsRamoAtv`\": `\"`\",\n"
                    + "`n\n"
                    + "`n        `\"DtUltAlteracao`\": `\"" + vDtAlter + "`\",\n"
                    + "`n\n"
                    + "`n    }\n"
                    + "`n\n"
                    + "`n}\"\n"
                    + "\n"
                    + "$response = Invoke-RestMethod 'http://************:9598/EGUARDIAN_API_INTEG_CLIENTES/CadastroClientes' -Method 'POST' -Headers $headers -Body $body\n"
                    + "$response | ConvertTo-Json";
            String scriptPath = "c:\\Clientes\\GravaCli.ps1";
            File file = new File(scriptPath);
            if (file.exists()) {
                file.delete();
            }
            FileWriter vArq = new FileWriter(scriptPath);
            PrintWriter gravarArq = new PrintWriter(vArq);

            gravarArq.printf(vArquivo);
            vArq.close();

            ProcessBuilder processBuilder = new ProcessBuilder("powershell.exe", "-File", scriptPath);

            // Redireciona a saída do processo para ler a resposta
            processBuilder.redirectErrorStream(true);

            // Inicia o processo
            Process process = processBuilder.start();

            // Lê a saída do processo
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                vRetornoEGuardian = vRetornoEGuardian + line;
            }

            try {
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(vRetornoEGuardian).getAsJsonObject();

            } catch (JSONException e) {
                this.logerro.Grava(e.toString(), caminho);
            }

            this.logerro.Grava("Parametros:" + "CTR=" + vRetornoEGuardian + " REsposta" + vRetornoEGuardian, caminho);
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do WS" + e.getMessage(), caminho);
        }

    }

    public void cadastraMovFinEguard(String vCodCli, String vNRed, String vEnde, String vCidade, String vCep, String vUF, String vFone, String vData, String vDtAlter, String vCNPJ, String vEmail, String vValor, String vCNPJTransp) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\cadastraClienteEguard.txt";

        //vEmail = "<EMAIL>";
        try {

            URL url = new URL("http://************:9598/EGUARDIAN_API_INTEG_MOVIMENTACOES/IntegracaoMovimentacoes/IntegracaoMovimentacaoViewIntegMovfin");
            String vArquivo;
            vArquivo = "$headers = New-Object \"System.Collections.Generic.Dictionary[[String],[String]]\"\r\n"
                    + "$headers.Add(\"Content-Type\", \"application/json\")\r\n"
                    + "\r\n"
                    + "$body = \"{`\"Empresa`\":`\"EGUARDIAN`\",\r\n"
                    + "`n`\"Movimentacoes`\": {\r\n"
                    + "`n                `\"CdIdentificacao`\": `\"" + vCNPJ + "`\",\r\n"
                    + "`n                `\"CdVeicLegal`\":5,\r\n"
                    + "`n                `\"CdAgencia`\":`\"6`\",\r\n"
                    + "`n                `\"CdAgenciaMovto`\":`\"1`\",\r\n"
                    + "`n                `\"CdConta`\":1,\r\n"
                    + "`n                `\"CdCliente`\":`\"" + vCNPJ + "`\",\r\n"
                    + "`n                `\"DtMovimenta`\":`\"" + vData + "`\",\r\n"
                    + "`n                `\"DthrMovimenta`\":`\"" + vData + "`\",\r\n"
                    + "`n                `\"CdMoeda`\":`\"R`$`\",\r\n"
                    + "`n                `\"VlOperacao`\":" + vValor + ",\r\n"
                    + "`n                `\"TpDebCred`\":`\"d`\",\r\n"
                    + "`n                `\"CdForma`\":`\"transferencia`\",\r\n"
                    + "`n                `\"DeContraparte`\":`\"contraparte MF110`\",\r\n"
                    + "`n                `\"DeBancoContra`\":`\"1`\",\r\n"
                    + "`n                `\"DeAgenciaContra`\":`\"02`\",\r\n"
                    + "`n                `\"CdContaContra`\":`\"0003`\",\r\n"
                    + "`n                `\"CdPaisContra`\":`\"Brasil`\",\r\n"
                    + "`n                `\"CdIspbEmissor`\" : `\"-1`\",\r\n"
                    + "`n                `\"DeOrigemOpe`\":`\"MF110`\",\r\n"
                    + "`n                `\"CdProduto`\":`\"01`\",\r\n"
                    + "`n                `\"DeFinalidade`\":`\"Fin MF110`\",\r\n"
                    + "`n                `\"CpfCnpjContra`\":`\"" + vCNPJTransp + "`\",\r\n"
                    + "`n                `\"DsCidadeContra`\":`\"c`\",\r\n"
                    + "`n                `\"DsCompHistorico`\":`\"c`\",\r\n"
                    + "`n                `\"VlRendimento`\":0.00,\r\n"
                    + "`n                `\"DtPrevLiquidacao`\":`\"" + vData + "`\",\r\n"
                    + "`n                `\"CdIspbEmissor`\":2345,\r\n"
                    + "`n                `\"NrCheque`\":`\"1`\",\r\n"
                    + "`n                `\"DeTpDoctoContra`\":`\"1`\",\r\n"
                    + "`n                `\"NrDoctoContra`\":`\"1`\",\r\n"
                    + "`n                `\"DeExecutor`\":`\"1`\",\r\n"
                    + "`n                `\"CpfExecutor`\":`\"1`\",\r\n"
                    + "`n                `\"NrPassaporteExec`\":`\"1`\",\r\n"
                    + "`n                `\"FlIofCarencia`\":0,\r\n"
                    + "`n                `\"CdNatOperacao`\":`\"1`\",\r\n"
                    + "`n                `\"DeOrdenante`\":`\"Ord. MF110`\",\r\n"
                    + "`n                `\"UfMovto`\":`\"" + vUF + "`\",\r\n"
                    + "`n                `\"NrPos`\":`\"" + vUF + "`\",\r\n"
                    + "`n                `\"DsCidadePos`\":`\"" + vUF + "`\",\r\n"
                    + "`n                `\"DsCanal`\":`\"" + vUF + "`\",\r\n"
                    + "`n                `\"DsOrigemRecurso`\":`\"" + vUF + "`\",\r\n"
                    + "`n                `\"DsDestinoRecurso`\":`\"" + vUF + "`\",\r\n"
                    + "`n                `\"CdProvisionamento`\":`\"" + vUF + "`\",\r\n"
                    + "`n                `\"DsAmbienteNeg`\":`\"" + vUF + "`\"\r\n"
                    + "`n                }\r\n"
                    + "`n}\"\r\n"
                    + "\r\n"
                    + "$response = Invoke-RestMethod 'http://************:9598/EGUARDIAN_API_INTEG_MOVIMENTACOES/IntegracaoMovimentacoes/IntegracaoMovimentacaoViewIntegMovfin' -Method 'POST' -Headers $headers -Body $body\r\n"
                    + "$response | ConvertTo-Json";
            String scriptPath = "c:\\Clientes\\GravaMov.ps1";
            File file = new File(scriptPath);
            if (file.exists()) {
                file.delete();
            }
            FileWriter vArq = new FileWriter(scriptPath);
            PrintWriter gravarArq = new PrintWriter(vArq);

            gravarArq.printf(vArquivo);
            vArq.close();

            ProcessBuilder processBuilder = new ProcessBuilder("powershell.exe", "-File", scriptPath);

            // Redireciona a saída do processo para ler a resposta
            processBuilder.redirectErrorStream(true);

            // Inicia o processo
            Process process = processBuilder.start();

            // Lê a saída do processo
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            vRetornoEGuardian = "";
            while ((line = reader.readLine()) != null) {
                vRetornoEGuardian = vRetornoEGuardian + line;
            }

            try {
                //JsonParser jsonParser = new JsonParser();
                //JsonObject jsonObject = jsonParser.parse(vRetornoEGuardian).getAsJsonObject();
                if (vRetornoEGuardian.contains("Sucesso")) {
                    vRetorno = "SUCESSO";
                } else if (vRetornoEGuardian.contains("ja existe")) {
                    vRetorno = "JAEXISTE";
                } else {
                    vRetorno = vRetornoEGuardian;
                }

            } catch (JSONException e) {
                this.logerro.Grava(e.toString(), caminho);
                vRetorno = vRetornoEGuardian;
            }

            this.logerro.Grava("Parametros:" + "Eaguardian=" + vRetornoEGuardian + " REsposta" + vRetornoEGuardian, caminho);
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do WS" + e.getMessage(), caminho);
        }

    }

    public void enviarMsgWhatsapp(String vToken, String vTelefone, String vArquivo, String vMensagem) throws NoSuchMethodException, FileNotFoundException, ScriptException, MalformedURLException, IOException {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc"
                + "\\enviarMsgWhatsapp.txt";

        try {

            String vRet = "";
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            okhttp3.MediaType mediaType = okhttp3.MediaType.parse("text/plain");
            logexecucao.Grava("Comando Enviado: https://apinew.socialhub.pro/api/sendMessage", caminho);
            if (vArquivo.equals("")) {
                RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                        .addFormDataPart("api_token", vToken)
                        .addFormDataPart("phone", vTelefone)
                        .addFormDataPart("message", vMensagem)
                        .build();
                Request request = new Request.Builder()
                        .url("https://apinew.socialhub.pro/api/sendMessage")
                        .method("POST", body)
                        .build();
                okhttp3.Response response = client.newCall(request).execute();
                String vCTRresult = "";
                vRet = response.body().string();
            } else {
                RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                        .addFormDataPart("api_token", vToken)
                        .addFormDataPart("phone", vTelefone)
                        .addFormDataPart("file", vArquivo)
                        .addFormDataPart("message", vMensagem)
                        .build();
                Request request = new Request.Builder()
                        .url("https://apinew.socialhub.pro/api/sendMessage")
                        .method("POST", body)
                        // .addHeader("Content-Type", "application/json")
                        //.addHeader("Accept", "application/json")
                        //.addHeader("Authorization", vToken)
                        .build();
                okhttp3.Response response = client.newCall(request).execute();
                String vCTRresult = "";
                vRet = response.body().string();
            }
            try {
                vRetorno = vRet;
            } catch (JSONException e) {
                this.logerro.Grava(e.toString(), caminho);
            }
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do WS" + e.getMessage(), caminho);
        }

    }

}

/* ScriptEngineManager factory = new ScriptEngineManager(); // Permite escolher linguagens diferentes da tradicional Java
        ScriptEngine engine = factory.getEngineByName("JavaScript"); //Escolhemos a linguagem que será utilizada, nesse caso é o JavaScript
        Invocable invocable = (Invocable) engine;
        String vImgX = "{FotoSrvN1: " + vFotoSrvN1X + "),"
                + "FotoSrvS1: " + vFotoSrvS1X + "),"
                + "FotoN1: " + vFotoN1X + "),"
                + "FotoS1: " + vFotoN1X + ")";
        //logexecucao.Grava("Montagem vImgX:" + vImgX, caminho);
        String vResultado = "";
        try {
            engine.eval(new FileReader("C:\\xampp\\htdocs\\satellite\\js\\saslibraryRec.js"));
            vResultado = (String) invocable.invokeFunction("comparaFoto", vImgX);
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do JS" + e.getMessage(), caminho);            
        }

            vSQL = "Select TMktdet.Sequencia, TMktdet.Andamento, TMktdet.Data, TMktdet.Hora, TMktdet.TipoCont, TMktdet.CodPessoa, \n" +
                   "TMktdet.Historico, TMktdet.Detalhes,  Pessoa.Nome, Pessoa.email, Pessoa.PWweb, PstServ.Local, Contatos.Nome NomeContato, TMktdet.Situacao, \n"+
                   " TMktdet.Ciente, tMKTDet.CodCont from TMktdet \n" +
                   "Left Join Contatos on Contatos.Codigo = TMKtDet.CodCont\n " +
                   "Left Join Clientes on Clientes.Codigo = Contatos.Codcli\n " +
                   "                  and Clientes.Codfil = Contatos.CodFil\n " +
                   "Left Join PStServ on PstServ.Codcli = Clientes.Codigo\n " +
                   "                 and PStServ.CodFil = Clientes.CodFil\n " +
                   "Left Join Pessoa  on Pessoa.Codigo = TMKtDet.CodPessoa \n "+
                   " Where TMKtDet.CodPessoa = " +vCodPessoa+
                   "   and Pessoa.PWWeb = '"+vPW+"'";


 */
