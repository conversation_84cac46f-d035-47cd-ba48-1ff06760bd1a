<?xml version="1.0" encoding="UTF-8"?><!--
        *** GENERATED FROM jax-ws.xml - DO NOT EDIT !                             ***
        *** TO MODIFY wsimport options USE Web Service node -> Edit WS Attributes ***
        *** TO CHANGE TARGETS GENERATED TO jaxws-build.xml COPY THOSE             ***
        *** TARGETS TO ../build.xml AND MODIFY THAT FILE INSTEAD                  ***

        --><project xmlns:xalan="http://xml.apache.org/xslt" xmlns:webproject2="http://www.netbeans.org/ns/web-project/2" xmlns:jaxws="http://www.netbeans.org/ns/jax-ws/1">
    <!--
                ===================
                JAX-WS WSGEN SECTION
                ===================
            -->
    <target name="wsgen-init" depends="init, -do-compile">
        <mkdir dir="${build.generated.sources.dir}/jax-ws/resources/"/>
        <mkdir dir="${build.classes.dir}"/>
        <mkdir dir="${build.classes.dir}/META-INF"/>
        <property name="j2ee.platform.wsgen.classpath" value="${libs.jaxws21.classpath}"/>
        <taskdef name="wsgen" classname="com.sun.tools.ws.ant.WsGen">
            <classpath path="${java.home}/../lib/tools.jar:${build.classes.dir}:${j2ee.platform.wsgen.classpath}:${javac.classpath}"/>
        </taskdef>
    </target>
    <target name="wsgen-faceid" depends="wsgen-init">
        <copy todir="${build.classes.dir}/META-INF">
            <fileset dir="${webinf.dir}" includes="wsit-br.com.sasw.satwebservice.faceid.faceid.xml"/>
        </copy>
        <wsgen sourcedestdir="${build.generated.sources.dir}/jax-ws" resourcedestdir="${build.generated.sources.dir}/jax-ws/resources/" destdir="${build.generated.sources.dir}/jax-ws" verbose="true" keep="true" genwsdl="true" sei="br.com.sasw.satwebservice.faceid.faceid" xendorsed="true">
            <classpath path="${java.home}/../lib/tools.jar:${build.classes.dir}:${j2ee.platform.wsgen.classpath}:${javac.classpath}"/>
        </wsgen>
    </target>
    <!--
                ===================
                JAX-WS WSIMPORT SECTION
                ===================
            -->
</project>
