/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.executasvc;

import br.com.sasw.pacotesuteis.sasbeans.TOKENS;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 *
 * <AUTHOR>
 */
public class ValidarToken {

    public static void validarExistencia(TOKENS token) throws StatusClientePstServException {
        try{
            if(token == null) throw new StatusClientePstServException();
            if(token.getCodigo() == null) throw new StatusClientePstServException();
            if(token.getCodigo().equals("")) throw new StatusClientePstServException();
        } catch (Exception e){
            throw new StatusClientePstServException("TokenInvalido");
        }
    }

    /**
     * TOKENS.dtValid deve estar no formato 'yyyyMMdd'
     * @param token
     * @throws StatusClientePstServException 
     */
    public static void validarValidade(TOKENS token) throws StatusClientePstServException {
        try{
            if(token.getDtValid() == null) throw new StatusClientePstServException();
            if(token.getDtValid().equals("")) throw new StatusClientePstServException();
            
            LocalDateTime validadeToken = LocalDateTime.parse(token.getDtValid(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime dataAtual = LocalDateTime.parse(getDataAtual("SQL-L") + " " + getDataAtual("HORASEGUNDOS"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            
            if(dataAtual.isAfter(validadeToken)) throw new StatusClientePstServException();
        } catch (Exception e){
            throw new StatusClientePstServException("TokenExpirado");
        }
    }
    
    /**
     * Valida se o token informado se refere à matrícula também informada
     * @param token
     * @param secao
     * @throws StatusClientePstServException 
     */
    public static void validarSecao(TOKENS token, String secao) throws StatusClientePstServException {
        try{
            if(!secao.replace(".0", "").equals(token.getChave().replace("SECAO - ", "").replace(".0",""))) throw new StatusClientePstServException();
        } catch (Exception e){
            throw new StatusClientePstServException("TokenSecaoInvalida");
        }
    }
}
