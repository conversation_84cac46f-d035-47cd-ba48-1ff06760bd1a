/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.dao.entity;

import br.com.sasw.satwebservice.bradesconovo.utils.BradescoConstants;

/**
 *
 * <AUTHOR>
 */
public class BradescoCertificateEntity {

    private String empresa;
    private String pathCertificadoProd;
    private String pathCertificadoHom;
    private String BancoDeDados;
    private String chaveTokenHom;
    private String chaveCoinHom;
    private String chaveTokenProd;
    private String chaveCoinProd;

    public BradescoCertificateEntity() {
    }


    public String getChaveTokenHom() {
        return chaveTokenHom;
    }

    public void setChaveTokenHom(String chaveTokenHom) {
        this.chaveTokenHom = chaveTokenHom;
    }

    public String getChaveCoinHom() {
        return chaveCoinHom;
    }

    public void setChaveCoinHom(String chaveCoinHom) {
        this.chaveCoinHom = chaveCoinHom;
    }

    public String getChaveTokenProd() {
        return chaveTokenProd;
    }

    public String getChaveToken(String ambiente) {
        if (BradescoConstants.AMBIENTE_PRODUCAO.equals(ambiente)) {
            return chaveTokenProd;
        } else {
            return chaveTokenHom;
        }
    }

    public void setChaveTokenProd(String chaveTokenProd) {
        this.chaveTokenProd = chaveTokenProd;
    }

    public String getChaveCoinProd() {
        return chaveCoinProd;
    }

    public void setChaveCoinProd(String chaveCoinProd) {
        this.chaveCoinProd = chaveCoinProd;
    }

    public String getEmpresa() {
        return empresa;
    }

    public void setEmpresa(String empresa) {
        this.empresa = empresa;
    }

    public String getPathCertificado(String ambiente) {
        if (BradescoConstants.AMBIENTE_PRODUCAO.equals(ambiente)) {
            return pathCertificadoProd;
        } else {
            return pathCertificadoHom;
        }
    }

    public String getChaveCoin(String ambiente) {
        if (BradescoConstants.AMBIENTE_PRODUCAO.equals(ambiente)) {
            return chaveCoinProd;
        } else {
            return chaveCoinHom;
        }
    }

    public String getBancoDeDados() {
        return BancoDeDados;
    }

    public void setBancoDeDados(String BancoDeDados) {
        this.BancoDeDados = BancoDeDados;
    }

    public String getPathCertificadoHom() {
        return pathCertificadoHom;
    }

    public void setPathCertificadoHom(String pathCertificadoHom) {
        this.pathCertificadoHom = pathCertificadoHom;
    }

    public String getPathCertificadoProd() {
        return pathCertificadoProd;
    }

    public void setPathCertificadoProd(String pathCertificadoProd) {
        this.pathCertificadoProd = pathCertificadoProd;
    } 
    
}
