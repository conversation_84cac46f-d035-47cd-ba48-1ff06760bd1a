/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.importacao.rotas.models;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
public class VolumesGTVe {

    private String lacre;
    private Integer especie;
    private String moeda;
    private BigDecimal valor;

    public String getLacre() {
        return lacre;
    }

    public void setLacre(String lacre) {
        this.lacre = lacre;
    }

    public Integer getEspecie() {
        return especie;
    }

    public void setEspecie(Integer especie) {
        this.especie = especie;
    }

    public String getMoeda() {
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }    

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }
}
