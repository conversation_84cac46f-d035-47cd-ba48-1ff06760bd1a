/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.controller;

import br.com.sasw.satwebservice.bradesconovo.dto.request.RecolhimentosRequest;
import br.com.sasw.satwebservice.bradesconovo.service.BradescoTokenService;
import br.com.sasw.satwebservice.bradesconovo.service.ConsultaMovimentacaoService;
import br.com.sasw.satwebservice.bradesconovo.service.ListaPontoAtendimentoService;
import br.com.sasw.satwebservice.bradesconovo.service.RetornoAtendimentoBancoService;
import com.google.gson.Gson;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.UriInfo;

/**
 *
 * <AUTHOR> Silva
 */
@Path("/nova-integracao-bradesco/")
public class NovaIntegracaoBradescoController {
    
    private ListaPontoAtendimentoService listaPontoAtendimentoService = new ListaPontoAtendimentoService();
    private ConsultaMovimentacaoService consultaMovimentacaoService = new ConsultaMovimentacaoService();
    private BradescoTokenService bradescoTokenService = new BradescoTokenService();
    private RetornoAtendimentoBancoService retornoAtendimentoBancoService = new RetornoAtendimentoBancoService();
    private Gson gson = new Gson();
    
    @Context
    private UriInfo uriInfo;
    
    /*
     Token
    */
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/token")
    public String getToken(@QueryParam("empresa") String empresa, 
            @QueryParam("ambiente") String ambiente)  {
        return bradescoTokenService.getTokenFromEmpresa(empresa, ambiente);
    }
    
    /*
     Api conciliacao-integrada - lista-ponto-atendimento
    */
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/lista-ponto-atendimento/personalizados")
    public String getPontoAtendimentoPersonalidados(@QueryParam("empresa") String empresa, 
            @QueryParam("token") String token, @QueryParam("cnpj") String cnpj, 
            @QueryParam("page") String page, @QueryParam("size") String size, 
            @QueryParam("sort") String sort, @QueryParam("ambiente") String ambiente)  {
        
        String query = uriInfo.getRequestUri().getQuery();
        return listaPontoAtendimentoService.getPontoAtendimentoPersonalidados(
                empresa, cnpj, token, page, size, sort, query, ambiente);
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/lista-ponto-atendimento/massificados")
    public String getPontoAtendimentoMassificados(@QueryParam("empresa") String empresa, 
            @QueryParam("token") String token, @QueryParam("cnpj") String cnpj, 
            @QueryParam("page") String page, @QueryParam("size") String size, 
            @QueryParam("sort") String sort, @QueryParam("ambiente") String ambiente)  {
        
        String query = uriInfo.getRequestUri().getQuery();
        return listaPontoAtendimentoService.getPontoAtendimentoMassificados(empresa, cnpj, token, page, size, sort, query, ambiente);
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/lista-ponto-atendimento/cofres")
    public String getPontoAtendimentoCofres(@QueryParam("empresa") String empresa, 
            @QueryParam("token") String token, @QueryParam("cnpj") String cnpj, 
            @QueryParam("page") String page, @QueryParam("size") String size, 
            @QueryParam("sort") String sort, @QueryParam("ambiente") String ambiente)  {
        
        String query = uriInfo.getRequestUri().getQuery();
        return listaPontoAtendimentoService.getPontoAtendimentoCofres(empresa, 
                cnpj, token, page, size, sort, query, ambiente);
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/lista-ponto-atendimento/agencias")
    public String getPontoAtendimentoAgencias(@QueryParam("empresa") String empresa, 
            @QueryParam("token") String token, @QueryParam("cnpj") String cnpj, 
            @QueryParam("page") String page, @QueryParam("size") String size, 
            @QueryParam("sort") String sort, @QueryParam("ambiente") String ambiente)  {
        
        String query = uriInfo.getRequestUri().getQuery();
        return listaPontoAtendimentoService.getPontoAtendimentoAgencias(empresa, 
                cnpj, token, page, size, sort, query, ambiente);
    }
    
    /*
     Api conciliacao-integrada - consulta-movimentacao
    */
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/consulta-movimentacao/interbancarios/tomados")
    public String getConsultaMovintacaoInterbancariosTomados(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token,
            @QueryParam("cnpj") String cnpj, 
            @QueryParam("situacaoPedido") String situacaoPedido, 
            @QueryParam("dataAtendimentoInicio") String dataAtendimentoInicio,
            @QueryParam("dataAtendimentoFim") String dataAtendimentoFim, 
            @QueryParam("page") String page, @QueryParam("size") String size, 
            @QueryParam("sort") String sort, @QueryParam("ambiente") String ambiente)  {
        
        String query = uriInfo.getRequestUri().getQuery();
        return consultaMovimentacaoService.getConsultaMovintacaoInterbancariosTomados(
                empresa, cnpj, token, situacaoPedido, dataAtendimentoInicio, 
                dataAtendimentoFim, page, size, sort, query, ambiente);
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/consulta-movimentacao/interbancarios/cedidos")
    public String getConsultaMovintacaoInterbancariosCedidos(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token,
            @QueryParam("cnpj") String cnpj, 
            @QueryParam("situacaoPedido") String situacaoPedido, 
            @QueryParam("dataAtendimentoInicio") String dataAtendimentoInicio,
            @QueryParam("dataAtendimentoFim") String dataAtendimentoFim, 
            @QueryParam("page") String page, @QueryParam("size") String size, 
            @QueryParam("sort") String sort, @QueryParam("ambiente") String ambiente)  {
        
        String query = uriInfo.getRequestUri().getQuery();
        return consultaMovimentacaoService.getConsultaMovintacaoInterbancariosCedidos(
                empresa, cnpj, token, situacaoPedido, dataAtendimentoInicio, 
                dataAtendimentoFim, page, size, sort, query, ambiente);
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/consulta-movimentacao/entrebases")
    public String getConsultaMovintacaoEntrebases(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token,
            @QueryParam("cnpj") String cnpj, 
            @QueryParam("situacaoPedido") String situacaoPedido, 
            @QueryParam("dataAtendimentoInicio") String dataAtendimentoInicio,
            @QueryParam("dataAtendimentoFim") String dataAtendimentoFim, 
            @QueryParam("page") String page, @QueryParam("size") String size, 
            @QueryParam("sort") String sort, @QueryParam("ambiente") String ambiente)  {
        String query = uriInfo.getRequestUri().getQuery();
        return consultaMovimentacaoService.getConsultaMovintacaoEntrebases(
                empresa, cnpj, token, situacaoPedido, dataAtendimentoInicio, 
                dataAtendimentoFim, page, size, sort, query, ambiente);
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/consulta-movimentacao/clientes/suprimentos/massificados")
    public String getConsultaMovimentacaoClientesSuprimentosMassificados(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token,
            @QueryParam("cnpj") String cnpj, 
            @QueryParam("situacaoPedido") String situacaoPedido, 
            @QueryParam("dataAtendimentoInicio") String dataAtendimentoInicio,
            @QueryParam("dataAtendimentoFim") String dataAtendimentoFim, 
            @QueryParam("page") String page, @QueryParam("size") String size, 
            @QueryParam("sort") String sort, @QueryParam("ambiente") String ambiente)  {
        String query = uriInfo.getRequestUri().getQuery();
        return consultaMovimentacaoService.getConsultaMovimentacaoClientesSuprimentosMassificados(
                empresa, cnpj, token, situacaoPedido, dataAtendimentoInicio, 
                dataAtendimentoFim, page, size, sort, query, ambiente);
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/consulta-movimentacao/clientes/suprimentos/personalizados")
    public String getConsultaMovimentacaoClientesSuprimentosPersonalizados(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token,
            @QueryParam("cnpj") String cnpj, 
            @QueryParam("situacaoPedido") String situacaoPedido, 
            @QueryParam("dataAtendimentoInicio") String dataAtendimentoInicio,
            @QueryParam("dataAtendimentoFim") String dataAtendimentoFim, 
            @QueryParam("page") String page, @QueryParam("size") String size, 
            @QueryParam("sort") String sort, @QueryParam("ambiente") String ambiente)  {
        String query = uriInfo.getRequestUri().getQuery();
        return consultaMovimentacaoService.getConsultaMovimentacaoClientesSuprimentosPersonalizados(
                empresa, cnpj, token, situacaoPedido, dataAtendimentoInicio, 
                dataAtendimentoFim, page, size, sort, query, ambiente);
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/consulta-movimentacao/custodiantes")
    public String getConsultaMovimentacaoCustodiantes(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token,
            @QueryParam("cnpj") String cnpj, 
            @QueryParam("situacaoPedido") String situacaoPedido, 
            @QueryParam("dataAtendimentoInicio") String dataAtendimentoInicio,
            @QueryParam("dataAtendimentoFim") String dataAtendimentoFim, 
            @QueryParam("page") String page, @QueryParam("size") String size, 
            @QueryParam("sort") String sort, @QueryParam("ambiente") String ambiente)  {
        String query = uriInfo.getRequestUri().getQuery();
        return consultaMovimentacaoService.getConsultaMovimentacaoCustodiantes(
                empresa, cnpj, token, situacaoPedido, dataAtendimentoInicio, 
                dataAtendimentoFim, page, size, sort, query, ambiente);
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/consulta-movimentacao/agencias")
    public String getConsultaMovimentacaoAgencias(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token,
            @QueryParam("cnpj") String cnpj, 
            @QueryParam("situacaoPedido") String situacaoPedido, 
            @QueryParam("dataAtendimentoInicio") String dataAtendimentoInicio,
            @QueryParam("dataAtendimentoFim") String dataAtendimentoFim, 
            @QueryParam("page") String page, @QueryParam("size") String size, 
            @QueryParam("sort") String sort, @QueryParam("ambiente") String ambiente)  {
        String query = uriInfo.getRequestUri().getQuery();
        return consultaMovimentacaoService.getConsultaMovimentacaoAgencias(
                empresa, cnpj, token, situacaoPedido, dataAtendimentoInicio, 
                dataAtendimentoFim, page, size, sort, query, ambiente);
    }
    
        
    /*
     Api Retorno Atendimento
    */
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/retorno-atendimento-banco/clientes/cofres/creditos")
    public String getRetornoAntendimentoClienteCofreCredito(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token, 
            @QueryParam("cnpjEmpresa") String cnpjEmpresa,
            @QueryParam("senhaConexao") String senhaConexao,
            @QueryParam("numeroPedido") String numeroPedido,
            @QueryParam("cnpjCliente") String cnpjCliente,
            @QueryParam("numeroSequencialCliente") String numeroSequencialCliente,
            @QueryParam("cnpjPontoCliente") String cnpjPontoCliente,
            @QueryParam("agenciaCredito") String agenciaCredito,
            @QueryParam("contaCredito") String contaCredito,
            @QueryParam("digitoContaCredito") String digitoContaCredito,
            @QueryParam("dataOperacao") String dataOperacao,
            @QueryParam("valorOperacao") String valorOperacao,
            @QueryParam("numeroCofreInteligenteOrigem") String numeroCofreInteligenteOrigem,
            @QueryParam("numeroOCT") String numeroOCT,
            @QueryParam("nomeRemetenteOCT") String nomeRemetenteOCT, 
            @QueryParam("ambiente") String ambiente)  {
        String query = uriInfo.getRequestUri().getQuery();
        return retornoAtendimentoBancoService.getRetornoAntendimentoClienteCofreCredito(empresa,                               // empresa
                    token,                               // token
                    cnpjEmpresa,                        // cnpjEmpresa
                    numeroPedido,                       // numeroPedido
                    cnpjCliente,                        // cnpjCliente
                    numeroSequencialCliente,            // numeroSequencialCliente
                    cnpjPontoCliente,                   // cnpjPontoCliente
                    agenciaCredito,                     // agenciaCredito
                    contaCredito,                       // contaCredito
                    digitoContaCredito,                 // digitoContaCredito
                    dataOperacao,                       // dataOperacao
                    valorOperacao,                      // valorOperacao
                    numeroCofreInteligenteOrigem,       // numeroCofreInteligenteOrigem
                    numeroOCT,                          // numeroOCT
                    nomeRemetenteOCT,                   // nomeRemetenteOCT
                    query,                              // query
                    ambiente                );
    }
    
    @DELETE
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/retorno-atendimento-banco/clientes/cofres/creditos")
    public String deleteRetornoAntendimentoClienteCofreCredito(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token, 
            @QueryParam("cnpjEmpresa") String cnpjEmpresa,
            @QueryParam("numeroPedido") String numeroPedido,
            @QueryParam("cnpjCliente") String cnpjCliente,
            @QueryParam("numeroSequencialCliente") String numeroSequencialCliente,
            @QueryParam("cnpjPontoCliente") String cnpjPontoCliente,
            @QueryParam("agenciaCredito") String agenciaCredito,
            @QueryParam("contaCredito") String contaCredito,
            @QueryParam("digitoContaCredito") String digitoContaCredito,
            @QueryParam("dataOperacao") String dataOperacao,
            @QueryParam("valorOperacao") String valorOperacao,
            @QueryParam("numeroCofreInteligenteOrigem") String numeroCofreInteligenteOrigem,
            @QueryParam("numeroOCT") String numeroOCT,
            @QueryParam("nomeRemetenteOCT") String nomeRemetenteOCT, 
            @QueryParam("ambiente") String ambiente)  {
        String query = uriInfo.getRequestUri().getQuery();
        return retornoAtendimentoBancoService.deleteRetornoAntendimentoClienteCofreCredito(empresa,                               // empresa
                    token,                               // token
                    cnpjEmpresa,                        // cnpjEmpresa
                    numeroPedido,                       // numeroPedido
                    cnpjCliente,                        // cnpjCliente
                    numeroSequencialCliente,            // numeroSequencialCliente
                    cnpjPontoCliente,                   // cnpjPontoCliente
                    agenciaCredito,                     // agenciaCredito
                    contaCredito,                       // contaCredito
                    digitoContaCredito,                 // digitoContaCredito
                    dataOperacao,                       // dataOperacao
                    valorOperacao,                      // valorOperacao
                    numeroCofreInteligenteOrigem,       // numeroCofreInteligenteOrigem
                    numeroOCT,                          // numeroOCT
                    nomeRemetenteOCT,                   // nomeRemetenteOCT
                    query,                              // query
                    ambiente                );
    }
    
    
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/retorno-atendimento-banco/clientes/cofres/recolhimentos")
    public String getRetornoAntendimentoClienteCofreRecolhimento(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token, 
            String json, @QueryParam("ambiente") String ambiente)  {
        System.out.println(json);
        String query = uriInfo.getRequestUri().getQuery();
        RecolhimentosRequest request = gson.fromJson(json, RecolhimentosRequest.class);
        return retornoAtendimentoBancoService.
                getRetornoAntendimentoClienteCofreRecolhimento(empresa, token, 
                        request, query, ambiente);   

    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/retorno-atendimento-banco/clientes/cofres/recolhimentos_delete")
    public String deleteRetornoAntendimentoClienteCofreRecolhimento(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token, 
            String json, @QueryParam("ambiente") String ambiente)  {
        String query = uriInfo.getRequestUri().getQuery();
        RecolhimentosRequest request = gson.fromJson(json, RecolhimentosRequest.class);
        return retornoAtendimentoBancoService.
                deleteRetornoAntendimentoClienteCofreRecolhimento(empresa, token, 
                        request, query, ambiente);
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/retorno-atendimento-banco/clientes/cofres/sinistros")
    public String getConsultaMovimentacaoClienteCofreSinistro(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token, 
            @QueryParam("cnpj") String cnpj, 
            @QueryParam("numeroPedido") String numeroPedido,
            @QueryParam("dataOperacao") String dataOperacao, 
            @QueryParam("valorOperacao") String valorOperacao, 
            @QueryParam("numeroCofreInteligenteOrigem") String numeroCofreInteligenteOrigem, 
            @QueryParam("ambiente") String ambiente)  {
        
        String query = uriInfo.getRequestUri().getQuery();
        return retornoAtendimentoBancoService.
                getConsultaMovimentacaoClienteCofreSinistro(empresa, token, 
                        cnpj, numeroPedido, dataOperacao, valorOperacao, 
                        numeroCofreInteligenteOrigem, query, ambiente);
    }
    
    @DELETE
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/retorno-atendimento-banco/clientes/cofres/sinistros")
    public String deleteConsultaMovimentacaoClienteCofreSinistro(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token, 
            @QueryParam("cnpj") String cnpj, 
            @QueryParam("numeroPedido") String numeroPedido,
            @QueryParam("dataOperacao") String dataOperacao, 
            @QueryParam("valorOperacao") String valorOperacao, 
            @QueryParam("numeroCofreInteligenteOrigem") String numeroCofreInteligenteOrigem, 
            @QueryParam("ambiente") String ambiente)  {
        
        String query = uriInfo.getRequestUri().getQuery();
        return retornoAtendimentoBancoService.
                deleteConsultaMovimentacaoClienteCofreSinistro(empresa, token, 
                        cnpj, numeroPedido, dataOperacao, valorOperacao, 
                        numeroCofreInteligenteOrigem, query, ambiente);
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/retorno-atendimento-banco/clientes/cofres/transferencias")
    public String getConsultaMovimentacaoClienteCofreTransferencias(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token, 
            @QueryParam("cnpjEmpresaDestino") String cnpjEmpresaDestino,
            @QueryParam("numeroPedido") String numeroPedido, 
            @QueryParam("cnpjPontoCliente") String cnpjPontoCliente, 
            @QueryParam("dataOperacao") String dataOperacao, 
            @QueryParam("valorOperacao") String valorOperacao, 
            @QueryParam("numeroCofreInteligenteOrigem") String numeroCofreInteligenteOrigem,
            @QueryParam("numeroCofreInteligenteDestino") String numeroCofreInteligenteDestino, 
            @QueryParam("cnpjEmpresaProcessadora") String cnpjEmpresaProcessadora,
            @QueryParam("cnpjTransportadoraOrigem")  String cnpjTransportadoraOrigem, 
            @QueryParam("ambiente") String ambiente)  {
        
        String query = uriInfo.getRequestUri().getQuery();
        return retornoAtendimentoBancoService.
                getConsultaMovimentacaoClienteCofreTransferencias(empresa, 
                        token, cnpjEmpresaDestino,numeroPedido, cnpjPontoCliente, 
                        dataOperacao, valorOperacao, numeroCofreInteligenteOrigem, 
                        numeroCofreInteligenteDestino, cnpjEmpresaProcessadora, 
                        cnpjTransportadoraOrigem, query, ambiente);
    }
    
    @DELETE
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/conciliacao-integrada/retorno-atendimento-banco/clientes/cofres/transferencias")
    public String deleteConsultaMovimentacaoClienteCofreTransferencias(
            @QueryParam("empresa") String empresa, @QueryParam("token") String token, 
            @QueryParam("cnpjEmpresaDestino") String cnpjEmpresaDestino,
            @QueryParam("numeroPedido") String numeroPedido, 
            @QueryParam("cnpjPontoCliente") String cnpjPontoCliente, 
            @QueryParam("dataOperacao") String dataOperacao, 
            @QueryParam("valorOperacao") String valorOperacao, 
            @QueryParam("numeroCofreInteligenteOrigem") String numeroCofreInteligenteOrigem,
            @QueryParam("numeroCofreInteligenteDestino") String numeroCofreInteligenteDestino, 
            @QueryParam("cnpjEmpresaProcessadora") String cnpjEmpresaProcessadora,
            @QueryParam("cnpjTransportadoraOrigem")  String cnpjTransportadoraOrigem, 
            @QueryParam("ambiente") String ambiente)  {
        
        String query = uriInfo.getRequestUri().getQuery();
        return retornoAtendimentoBancoService.
                deleteConsultaMovimentacaoClienteCofreTransferencias(empresa, 
                        token, cnpjEmpresaDestino,numeroPedido, cnpjPontoCliente, 
                        dataOperacao, valorOperacao, numeroCofreInteligenteOrigem, 
                        numeroCofreInteligenteDestino, cnpjEmpresaProcessadora, 
                        cnpjTransportadoraOrigem, query, ambiente);
    }
}
