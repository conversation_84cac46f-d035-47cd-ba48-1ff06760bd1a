<!DOCTYPE html>
<html>
    <head>
        <title>CT-E @ide.nCT</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
        <style>
            @CHARSET "UTF-8";
            *{font-size: 8px; font-family: courier; line-height: 1px;}
            table{ width: 100%; border-spacing:0;}
            th{ border: 1px solid black; text-align: center; font-size: 10px; 
                -moz-border-radius-topleft: 5px; -moz-border-radius-topright: 5px; 
                -webkit-border-top-left-radius: 5px; -webkit-border-top-right-radius: 5px;
                font-family: courier;}
            hr{ display: block;margin-top: 4px;margin-bottom: 4px;margin-left: auto;margin-right: auto;border-style: dotted;border-width: 1px;}
            td{ border: 1px solid black; padding-left: 7px; padding-bottom: 2px;}
            .canto_esquerdo_superior {-moz-border-radius-topleft: 5px; -webkit-border-top-left-radius: 5px;}
            .canto_direito_superior{-moz-border-radius-topright: 5px; -webkit-border-top-right-radius: 5px; }
            .canto_esquerdo_inferior{-moz-border-radius-bottomleft: 5px; -webkit-border-bottom-left-radius: 5px;}
            .canto_direito_inferior{-moz-border-radius-bottomright: 5px; -webkit-border-bottom-right-radius: 5px;}
            .sem_borda_esquerda{border-left: none;}
            .sem_borda_direita{border-right: none;}
            .sem_borda_inferior{border-bottom: none;}
            .sem_borda_superior{border-top: none;}
            .texto_superior{vertical-align: text-top;}
            .texto_inferior{ vertical-align: bottom; }
            .texto_centro{text-align: center;}
            .texto_direita{text-align: right; padding-right: 10px; }
            .largura10{ width: 10%; }
            .largura15{ width: 15%; }
            .largura20{ width: 20%; }
            .largura25{ width: 25%; }
            .largura33{ width: 33%; }
            .altura70px{ height: 70px; }
            .altura50px{ height: 50px; }
            .negrito{ font-weight: bold; }
            .fonte7{ font-size: 7px; font-family: courier;}
            .fonte8{ font-size: 8px; font-family: courier;}
            .fonte10{ font-size: 10px; font-family: courier;}
            .fonte14{ font-size: 14px; font-family: courier;}
            .fonte16{ font-size: 16px; font-family: courier;}
            .linha{ line-height: 6px; }
            .linha6{ line-height: 6px; }
            .linha8{ line-height: 8px; }
            .DACTE-OS{ font-size: 16px; font-weight: bold; font-family: courier;}            
            .divCode{ float: left; height: 0.7in; }
            .bar1 {border-left: 0.75px solid black;}
            .bar2 {border-left: 1.5px solid black;}
            .bar3 {border-left: 2.25px solid black;}
            .bar4 {border-left: 3px solid black;}
            .space0{ margin-right: 0px;}
            .space1{ margin-right: 0.75px;}
            .space2{ margin-right: 1.5px;}
            .space3{ margin-right: 2.25px;}
            .space4{ margin-right: 3px;}
        </style>
        <script>
            // ===> INICIO DO CODIGO
            var BARS = [ 212222, 222122, 222221, 121223, 121322, 131222, 122213,
                122312, 132212, 221213, 221312, 231212, 112232, 122132, 122231,
                113222, 123122, 123221, 223211, 221132, 221231, 213212, 223112,
                312131, 311222, 321122, 321221, 312212, 322112, 322211, 212123,
                212321, 232121, 111323, 131123, 131321, 112313, 132113, 132311,
                211313, 231113, 231311, 112133, 112331, 132131, 113123, 113321,
                133121, 313121, 211331, 231131, 213113, 213311, 213131, 311123,
                311321, 331121, 312113, 312311, 332111, 314111, 221411, 431111,
                111224, 111422, 121124, 121421, 141122, 141221, 112214, 112412,
                122114, 122411, 142112, 142211, 241211, 221114, 413111, 241112,
                134111, 111242, 121142, 121241, 114212, 124112, 124211, 411212,
                421112, 421211, 212141, 214121, 412121, 111143, 111341, 131141,
                114113, 114311, 411113, 411311, 113141, 114131, 311141, 411131,
                211412, 211214, 211232, 23311120 ], START_BASE = 38, STOP = 106; 

            var fromType = {
                A : function(charCode) {
                    if (charCode >= 0 && charCode < 32)
                        return charCode + 64;  
                    if (charCode >= 32 && charCode < 96)
                        return charCode - 32;
                    return charCode;
                },
                B : function(charCode) {
                    if (charCode >= 32 && charCode < 128)
                        return charCode - 32;
                    return charCode;
                },
                C : function(charCode) {
                    return charCode;
                }
            };

            function code128(code, allSpace, barcodeType) {
                if (barcodeType == undefined)
                    barcodeType = code128Detect(code);
                if (allSpace == undefined)
                    allSpace = 180;  
                if (barcodeType == 'C' && code.length % 2 == 1)
                    code = '0' + code;

                var a = parseBarcode(code, barcodeType);
                var b = a.join('');
                var sb = [];
                var codeBarSpace = 2;
                //alert(b + " - " + b.length);  

                for ( var pos = 0; pos < b.length; pos += 2) {
                    sb.push(' <div class="divCode bar' + b.charAt(pos) + ' space' + b.charAt(pos + 1) + '"></div>');
                    codeBarSpace = ( codeBarSpace + (parseInt(b.charAt(pos + 1)) + parseInt(b.charAt(pos))) );
                }

                var margin = (parseInt(allSpace)-codeBarSpace)/2;
                return '<div>' + sb.join('') + '</div>';
            }

            function code128Detect(code) {
                if (/^[0-9]+$/.test(code))
                    return 'C';
                if (/[a-z]/.test(code))
                    return 'B';
                return 'A';
            }

            function parseBarcode(barcode, barcodeType) {
                var bars = [];
                bars.add = function(nr) {
                    var nrCode = BARS[nr];
                    this.check = this.length == 0 ? nr : this.check + nr * this.length;
                    this.push(nrCode || format("UNDEFINED: %1->%2", nr, nrCode));
                };

                bars.add(START_BASE + barcodeType.charCodeAt(0));

                for ( var i = 0; i < barcode.length; i++) {
                    var code = barcodeType == 'C' ? +barcode.substr(i++, 2) : barcode .charCodeAt(i); converted = fromType[barcodeType](code);  
                    if (isNaN(converted) || converted<0 || converted>106)
                        throw new Error( format("Unrecognized character (%1) at position %2 in code '%3'.", code, i, barcode));
                    bars.add(converted);
                }

                bars.push(BARS[bars.check % 103], BARS[STOP]);
                return bars;
            }
        </script>    
    </head>
    <body>
        <table>
            <tr>
                <th colspan="4" class="sem_borda_inferior fonte8">
                    DECLARO QUE RECEBI OS VOLUMES DESTE CONHECIMENTO EM PERFEITO ESTADO PELO QUE DOU POR CUMPRIDO O PRESENTE CONTRATO DE TRANSPORTE
                </th>
            </tr>
            <tr>
                <td class="sem_borda_inferior sem_borda_direita texto_superior largura25">
                   <span class="fonte7">NOME</span>
                </td>
                <td rowspan="2" class="sem_borda_direita largura25 texto_inferior texto_centro">
                   <span class="fonte7">ASSINATURA&nbsp;/&nbsp;CARIMBO</span>
                </td>
                <td class="sem_borda_inferior texto_centro largura25">
                   <span class="fonte7">TÉRMINO&nbsp;DA&nbsp;PRESTÇÃO&nbsp;-&nbsp;DATA/HORA</span>
                   <br>
                   <span class="fonte8">____________ &nbsp; &nbsp; ____________</span>  
                </td>
                <td rowspan="2" class="canto_direito_inferior sem_borda_esquerda largura25 ">
                    <div style="width: 100%; font-size: 11px; font-family: courier" class="texto_centro texto_superior">
                        CT-E&nbsp;OS
                    </div>
                    <span class="fonte7">NRO.&nbsp;DOCUMENTO</span>
                    <br>
                    <span class="fonte8">@ide.nCT</span> 
                    <br>
                    <span class="fonte7">SÉRIE</span>
                    <br>
                    <span class="fonte8">@ide.serie</span> 
                </td>
            </tr>
            <tr>
                <td class="canto_esquerdo_inferior sem_borda_direita texto_superior">
                    <span class="fonte7">RG</span>
                </td>
                <td class="sem_borda_superior texto_centro">
                    <span class="fonte7">INICIO&nbsp;DE&nbsp;PRESTAÇÃO&nbsp;-&nbsp;DATA/HORA</span>
                   <br>
                   <span class="fonte8">____________ &nbsp; &nbsp; ____________</span> 
                </td>
            </tr>
        </table>
        
        <hr>
        
        <table>
            <tr>
                <td rowspan="3" class="canto_esquerdo_superior canto_esquerdo_inferior sem_borda_direita largura10"> 
                    <img src="@IMAGEM" alt="" width="100"> 
                </td>
                <td rowspan="3" colspan="2" class="canto_direito_superior canto_direito_inferior sem_borda_esquerda texto_superior linha6"> 
                    <span class="fonte7">IDENTIFICAÇÃO&nbsp;DO&nbsp;EMITENTE</span>
                    <span class="negrito fonte10">
                        <br>
                        <br>
                        <span class="fonte8">@emit.xNome</span> 
                        <br>
                        <span class="fonte8">@emit.CNPJ</span> 
                        <br>
                        <span class="fonte8">@emit.IE</span>
                        <br>
                        <span class="fonte8">@emit.enderEmit.xLgr</span>,&nbsp;<span class="fonte8">@emit.enderEmit.nro</span>, <span class="fonte8">@emit.enderEmit.xBairro</span>, <span class="fonte8">@emit.enderEmit.xMun</span>&nbsp;-&nbsp;<span class="fonte8">@emit.enderEmit.UF</span> 
                        <br>
                        <span class="fonte7">CEP:</span>&nbsp;<span class="fonte8">@emit.enderEmit.CEP</span> 
                    </span>
                </td>
                <td colspan="4" class="canto_esquerdo_superior canto_esquerdo_inferior canto_direito_superior canto_direito_inferior texto_centro linha6">
                    <span class="DACTE-OS" >DACTE-OS</span>
                    <br><br>
                    <span class="fonte8">DOCUMENTO AUXILIAR DO CONHECIMENTO DE TRANSPORTE ELETRÔNICO PARA OUTROS SERVIÇOS</span>
                </td>
                <td class="canto_esquerdo_superior canto_esquerdo_inferior canto_direito_superior canto_direito_inferior texto_centro">
                    <span class="fonte7">MODAL</span>
                    <br>
                    <span class="fonte14">
                        @ide.modal
                    </span>
                </td>
            </tr>
            <tr>
                <td class="canto_esquerdo_superior canto_esquerdo_inferior sem_borda_direita texto_superior linha8">
                    <span class="fonte7">MODELO</span>
                    <br>
                    <span class="fonte8">@ide.mod</span> 
                </td>
                <td class=" texto_superior linha8" style="width: 50px; ">
                    <span class="fonte7">SÉRIE</span>
                    <br>
                    <span class="fonte8">@ide.serie</span> 
                </td>
                <td class="sem_borda_esquerda texto_superior linha8">
                    <span class="fonte7">NÚMERO</span>
                    <br>
                    <span class="fonte8">@ide.nCT</span> 
                </td>
                <td colspan="2" class="canto_direito_superior canto_direito_inferior sem_borda_esquerda texto_superior linha8">
                    <span class="fonte7">DATA&nbsp;E&nbsp;HORA&nbsp;DE&nbsp;EMISSÃO</span>
                    <br>
                    <span class="fonte8">@ide.dhEmi</span> 
                </td>
            </tr>
            <tr>			
                <td colspan="5" class="canto_esquerdo_superior canto_direito_superior sem_borda_inferior texto_centro">                
                    <script>                               
                        window.document.write(code128('@infCte', '273', 'A'));
                    </script>				
                </td>	
            </tr>
            <tr>
                <td rowspan="2" colspan="2" class="canto_esquerdo_superior canto_esquerdo_inferior sem_borda_direita texto_superior largura15 linha6">
                    <span class="fonte7">TIPO&nbsp;DO&nbsp;CT-E</span>
                    <br>
                    <br>
                    <span class="fonte8">@ide.tpCTe</span> 
                </td>
                <td rowspan="2" class="canto_direito_superior canto_direito_inferior texto_superior largura15 linha6">
                    <span class="fonte7">TIPO&nbsp;DO&nbsp;SERVIÇO</span>
                    <br>
                    <br>
                    <span class="fonte8">@ide.tpServ</span>   <span class="fonte8">@ide.natOp</span> 
                </td>
                <td colspan="5" class="canto_esquerdo_inferior canto_direito_inferior texto_centro linha6">
                    <span class="fonte8">Chave de acesso para consulta de autenticidade no site www.cte.fazenda.gov.br ou da Sefaz Autorizada.</span>
                    <br>
                    <span class="fonte8">@infCte</span> 
                </td>
            </tr>
            <tr>
                <td colspan="5" class="canto_direito_superior canto_esquerdo_superior texto_centro altura50px linha">
                    <span class="fonte8">Consulta de autenticidade no portal nacional do CT-e, no site da Sefaz Autorizada, ou em http://www.cte.fazenda.gov.br/portal</span> 
                </td>
            </tr>
            <tr>
                <td colspan="3" class="canto_direito_superior canto_esquerdo_superior canto_direito_inferior canto_esquerdo_inferior texto_superior linha8">
                    <span class="fonte7">CFOP&nbsp;-&nbsp;NATUREZA&nbsp;DA&nbsp;PRESTAÇÃO</span>
                    <br>
                    <span class="fonte8">@ide.CFOP&nbsp;-&nbsp;@ide.natOp</span> 
                </td>
                <td colspan="5" class="canto_direito_inferior canto_esquerdo_inferior sem_borda_superior texto_superior linha8">
                    <span class="fonte7">Protocolo&nbsp;de&nbsp;Autorização&nbsp;de&nbsp;Uso</span>
                    <br>
                    <span class="fonte8">@autUSOXX</span> 
                 </td>
            </tr>
        </table>
        
        <table>
            <tr>
                <td class="canto_direito_superior canto_esquerdo_superior canto_direito_inferior canto_esquerdo_inferior texto_superior largura33 linha8">
                    <span class="fonte7">INÍCIO&nbsp;DA&nbsp;PRESTAÇÃO</span>
                    <br>
                    <span class="fonte8">@ide.xMunIni - @ide.UFIni<br></span> 
                </td>
                <td class="canto_direito_superior canto_esquerdo_superior canto_direito_inferior canto_esquerdo_inferior texto_superior largura33 linha8">
                    <span class="fonte7">PERCURSO&nbsp;DO&nbsp;VEÍCULO</span>
                    <br>                    
                </td>
                <td class="canto_direito_superior canto_esquerdo_superior canto_direito_inferior canto_esquerdo_inferior texto_superior largura33 linha8">
                   <span class="fonte7">TÉRMINO&nbsp;DA&nbsp;PRESTAÇÃO</span>
                   <br>
                   <span class="fonte8">@ide.xMunFim - @ide.UFFim<br></span> 
                </td>
            </tr>
        </table>
        <table>
            <tr>
                <td class="canto_esquerdo_superior canto_esquerdo_inferior sem_borda_direita linha6">
                    <span class="fonte7">TOMADOR&nbsp;DO&nbsp;SERVICO:&nbsp;</span> <span class="fonte8">@toma.xNome</span>
                    <br>
                    <span class="fonte7">ENDEREÇO:&nbsp;</span> <span class="fonte8">@toma.enderToma.xLgr&nbsp;-&nbsp;@toma.enderToma.nro&nbsp;-&nbsp;@toma.enderToma.xBairro&nbsp;</span> 
                    <br>
                    <span class="fonte7">CNPJ/CPF:&nbsp;</span> <span class="fonte8">@toma.CNPJ</span>
                </td>
                <td class="sem_borda_esquerda sem_borda_direita linha6">
                    <span class="fonte7">MUNICÍPIO:&nbsp;</span> <span class="fonte8">@toma.enderToma.xMun</span>
                    <br>
                    <span class="fonte7">FONE:&nbsp;</span> <span class="fonte8">@toma.fone</span>
                    <br>
					<span class="fonte7">IE:&nbsp;</span> <span class="fonte8">@toma.IE</span>
                </td>
                <td class="canto_direito_superior canto_direito_inferior sem_borda_esquerda linha6">
                    <span class="fonte7">CEP:&nbsp;</span> <span class="fonte8">@toma.enderToma.CEP</span> 
                    <br>
                    <span class="fonte7">PAÍS:&nbsp;</span> <span class="fonte8">@toma.enderToma.xPais</span>
                    <br>
                    <br>
                </td>
            </tr>
        </table>	
        <table>
            <tr>
                <th colspan="2">
                    INFORMAÇÕES&nbsp;DA&nbsp;PRESTAÇÃO&nbsp;DO&nbsp;SERVIÇO
                </th>
            </tr>
            <tr>
                <td class="canto_esquerdo_inferior sem_borda_superior sem_borda_direita texto_superior largura10">
                    <span class="fonte7">QUANTIDADE</span>
                    <br>
                    <br>
                    <br>
                    <br>
                </td>
                <td class="canto_direito_inferior sem_borda_superior texto_superior">
                    <span class="fonte7">DESCRIÇÃO&nbsp;DO&nbsp;SERVIÇO&nbsp;PRESTADO</span>
                    <br>
                    <span class="fonte8">@infCTeNorm.infServico.xDescServ</span> 
                    <br>
                    <br>
                </td>
            </tr>
        </table>
        <table>
            <tr>
                <th colspan="9" class="sem_borda_inferior">
                    COMPONENTES&nbsp;DO&nbsp;VALOR&nbsp;DA&nbsp;PRESTAÇÃO&nbsp;DE&nbsp;SERVIÇO
                </th>
            </tr>
            <tr>
                <td  rowspan="2" class="canto_esquerdo_inferior sem_borda_direita texto_superior largura10 linha6">
                    <span class="fonte7">NOME</span>
                    <br>
                    <span class="fonte8">@vPrest.Comp.xNome</span> 
                </td>
                <td  rowspan="2" class="sem_borda_esquerda sem_borda_direita texto_superior texto_direita largura10 linha6">
                    <span class="fonte7">VALOR</span>
                    <br>
                    <span class="fonte8">@vPrest.Comp.vComp</span> 
                </td>
                <td  rowspan="2" class="sem_borda_direita texto_superior largura10 linha6">
                    <span class="fonte7">NOME</span>
                    <br>
                    <span class="fonte8">@vPrest.Comp.xNome</span> 
                </td>
                <td  rowspan="2" class="sem_borda_esquerda sem_borda_direita texto_superior texto_direita largura10 linha6">
                    <span class="fonte7">VALOR</span>
                    <br>
                    <span class="fonte8">@vPrest.Comp.vComp</span> 
                </td>
                <td  rowspan="2" class="sem_borda_direita texto_superior largura10 linha6">
                    <span class="fonte7">NOME</span>
                    <br>
                    <span class="fonte8">@vPrest.Comp.xNome</span> 
                </td>
                <td  rowspan="2" class="sem_borda_esquerda sem_borda_direita texto_superior texto_direita largura10 linha6">
                    <span class="fonte7">VALOR</span>
                    <br>
                    <span class="fonte8">@vPrest.Comp.vComp</span> 
                </td>
                <td  rowspan="2" class="sem_borda_direita texto_superior largura10 linha6">
                    <span class="fonte7">NOME</span>
                    <br>
                    <span class="fonte8">@vPrest.Comp.xNome</span> 
                </td>
                <td  rowspan="2" class="sem_borda_esquerda sem_borda_direita texto_superior texto_direita largura10 linha6">
                    <span class="fonte7">VALOR</span>
                    <br>
                    <span class="fonte8">@vPrest.Comp.vComp</span> 
                </td>
                <td class=" texto_superior largura20 linha8">
                    <span class="fonte7">VALOR&nbsp;TOTAL&nbsp;DO&nbsp;SERVIÇO</span>
                    <br>
                    <span class="fonte8">@vPrest.vTPrest</span>
                </td>
            </tr>
            <tr>
                <td class="sem_borda_superior canto_direito_inferior texto_superior linha8">
                    <span class="fonte7">VALOR&nbsp;A&nbsp;RECEBER </span>
                    <br>
                    <span class="fonte8">@vPrest.vRec</span> 
                </td>
            </tr>
        </table>
        <table>
            <tr>
                <th colspan="5" class="sem_borda_inferior">
                    INFORMAÇÕES&nbsp;RELATIVAS&nbsp;AO&nbsp;IMPOSTO
                </th>
            </tr>
            <tr>
                <td class="sem_borda_direita canto_esquerdo_inferior texto_superior largura20 linha8">
                    <span class="fonte7">CLASSIFICAÇÃO&nbsp;TRIBUTÁRIA&nbsp;DO&nbsp;SERVIÇO</span>
                    <br>
                    <span class="fonte8">@imp.ICMS.ICMS00.CST</span> <!-- 00 - ICMS Normal -->
                </td>
                <td class="sem_borda_direita texto_superior largura20 linha8">
                    <span class="fonte7">BASE&nbsp;DE&nbsp;CÁLCULO</span>
                    <br>
                    <span class="fonte8">@imp.ICMS.ICMS00.vBC</span> 
                </td>
                <td class="sem_borda_direita texto_superior largura20 linha8">
                    <span class="fonte7">AL.&nbsp;ICMS</span>
                    <br>
                    <span class="fonte8">@imp.ICMS.ICMS00.pICMS</span> 
                </td>
                <td class="sem_borda_direita texto_superior largura20 linha8">
                    <span class="fonte7">VALOR&nbsp;ICMS</span>
                    <br>
                    <span class="fonte8">@imp.ICMS.ICMS00.vICMS</span> 
                </td>
                <td class="canto_direito_inferior texto_superior largura20 linha8">
                    <span class="fonte7">&nbsp;</span>
                    <br>
                </td>
            </tr>
        </table>
        <table>
            <tr>
                <th>
                    OBSERVAÇÕES
                </th>
            </tr>
            <tr>
                <td class="canto_direito_inferior canto_esquerdo_inferior sem_borda_superior texto_centro altura50px">
                    @compl.xObs
                </td>
            </tr>
        </table>
        <table>
            <tr>
                <th colspan="3">
                    SEGURO&nbsp;DA&nbsp;VIAGEM
                </th>
            </tr>
            <tr>
                <td class="canto_esquerdo_inferior sem_borda_superior sem_borda_direita texto_superior largura33 linha8">
                    <span class="fonte7">RESPONSÁVEL</span>
                    <br>
                    <br>
                </td>
                <td class="sem_borda_superior sem_borda_direita texto_superior largura33 linha8">
                    <span class="fonte7">NOME&nbsp;DA&nbsp;SEGURADORA</span>
                    <br>
                    <br>
                </td>
                <td class="canto_direito_inferior sem_borda_superior texto_superior largura33 linha8">
                    <span class="fonte7">NÚMERO&nbsp;DA&nbsp;APÓLICE</span>
                    <br>
                    <br>
                </td>
            </tr>
        </table>
        <table>
            <tr>
                <th colspan="5">
                    INFORMAÇÕES&nbsp;ESPECÍFICAS&nbsp;DO&nbsp;MODAL&nbsp;RODOVIÁRIO
            </tr>
            <tr>
                <td class="canto_esquerdo_inferior sem_borda_superior sem_borda_direita texto_superior largura20 linha8">
                    <span class="fonte7">TERMO&nbsp;DE&nbsp;AUTORIZAÇÃO&nbsp;DE&nbsp;FRETAMENTO</span>
                    <br>       
                    <br>             
                </td>
                <td class="sem_borda_superior sem_borda_direita texto_superior largura20 linha8">
                    <span class="fonte7">N.&nbsp;DE&nbsp;REGISTRO&nbsp;ESTADUAL</span>
                    <br>       
                    <br>           
                </td>
                <td class="sem_borda_superior sem_borda_direita texto_superior largura20 linha8">
                    <span class="fonte7">PLACA&nbsp;DO&nbsp;VEÍCULO</span>
                    <br>
                    <br>
                </td>
                <td class="sem_borda_superior sem_borda_direita texto_superior largura20 linha8">
                    <span class="fonte7">RENAVAM&nbsp;DO&nbsp;VEÍCULO</span>
                    <br>     
                    <br>               
                </td>
                <td class="canto_direito_inferior sem_borda_superior texto_superior largura20 linha8">
                    <span class="fonte7">CNPJ/CPF</span>
                    <br>   
                    <br>                 
                </td>
            </tr>
        </table>
        <table>
            <tr>
                <th class="canto_esquerdo_superior canto_direito_superior">
                    USO&nbsp;EXCLUSIVO&nbsp;DO&nbsp;EMISSOR&nbsp;DO&nbsp;CT-e
                </th>
                <th class="canto_esquerdo_superior canto_direito_superior">
                    RESERVADO&nbsp;AO&nbsp;FISCO
                </th>
            </tr>
            <tr>
                <td class="canto_esquerdo_inferior canto_direito_inferior sem_borda_superior">
                    <br>
                    <br>
                    <br>
                    <br>
                </td>
                <td class="canto_esquerdo_inferior canto_direito_inferior sem_borda_superior">
                    <br>
                    <br>
                    <br>
                    <br>
                </td>
            </tr>
        </table>
    </body>
</html>