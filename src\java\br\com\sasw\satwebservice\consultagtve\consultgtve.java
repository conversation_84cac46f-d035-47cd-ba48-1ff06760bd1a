/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.consultagtve;

import Arquivo.ArquivoLog;
import Dados.Consulta;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasDaos.FiliaisDao;
import br.com.sasw.pacotesuteis.sasbeans.TOKENS;
import br.com.sasw.pacotesuteis.sasdaos.TOKENSDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.satwebservice.executasvc.ValidarToken;
import br.com.sasw.satwebservice.messages.Messages;
import java.sql.ResultSet;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
@Path("/consultagtve/")
public class consultgtve {

    private final ArquivoLog logerro;
    private ArquivoLog logexecucao;
    private ArquivoLog arquivohtml;
    private final SasPoolPersistencia pool;
    private String caminho;
    private String caminhoweb;
    private final Messages messages;
    private final FiliaisDao filiaisDao;
    private final TOKENSDao tokenDao;
    private ResultSet vResultadoConsulta;
    private String vRetorno;
    private String vRetornoPessoa;

    public consultgtve() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ConsultaGTVE"
                + "\\erro.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        messages = new Messages();

        filiaisDao = new FiliaisDao();
        tokenDao = new TOKENSDao();
    }

    
    @GET
    @Path("/xmlgtve")
    @Produces(MediaType.APPLICATION_JSON)    
    public Response xmlGTVEs(@QueryParam("token") String vToken, @QueryParam("chavegtve") String vChaveGTVE){
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\consultagtve"
                + "\\xmlGTVE.txt";
        // TODO ler body em json
        //String param;
        //Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        //String vToken = (String) parametros.getOrDefault("token", null);
        //String vChaveGTVE = (String) parametros.getOrDefault("chavegtve", null);

        if (vToken.equals("")) {
            vToken = "B8489FF7205E3217B4E043D70FF9848B";
        }
        logexecucao.Grava("parametros: token=" + vToken + ";chaveGTVE=" + vChaveGTVE, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnEnvio = new JSONObject();
        JSONArray vjnArray = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vChaveGTVE == null || vChaveGTVE.equals("")) {
                throw new consultgtveException("Chave Inexistente.");
            }

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "";
            String vSQLExec = "", vCNPJ = "", vGuia = "", vSerie = "", vCodCidade = "", vDt_GTVE = "",
                    vRetorno = "", vDt_envio = "", vHr_envio = "", vDt_retorno = "",
                    vHr_retorno = "", vFatIDGTV = "", vOpenIDFilial = "", vXMLretorno = "",
                    vBancoDados = "", vVersao = "", vnGuia = "", vCStat = "", vMotivo = "", vDtRecbto= "";                
            
            Consulta qTmpX;
            vBancoDados = qToken.getBancoDados();

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }

            vSQL = "Select top 1 XMLGTVE.CNPJ, XMLGTVE.Guia, XMLGTVE.Serie, "
                    + "XMLGTVE.CodCidade, XMLGTVE.Dt_GTVE, XMLGTVE.Hr_GTVE, XMLGTVE.ChaveGTVE, \n"
                    + "Substring(Substring(XMLGTVE.XML_Retorno, PATINDEX('%<xMotivo>%', XMLGTVE.XML_Retorno)+09, PATINDEX('%</xMotivo>%' , XMLGTVE.XML_Retorno)-10),1,(PATINDEX('%</xMotivo>%', XMLGTVE.XML_Retorno)-PATINDEX('%<xMotivo>%', XMLGTVE.XML_Retorno))-9) Retorno, \n"
                    + "Substring(Substring(XMLGTVE.XML_Retorno, PATINDEX('%<cStat>%', XMLGTVE.XML_Retorno)+07, PATINDEX('%</cStat>%' , XMLGTVE.XML_Retorno)-08),1,(PATINDEX('%</cStat>%', XMLGTVE.XML_Retorno)-PATINDEX('%<cStat>%', XMLGTVE.XML_Retorno))-7) cStat, \n"
                    + "Substring(Substring(XMLGTVE.XML_Retorno, PATINDEX('%<dhRecbto>%', XMLGTVE.XML_Retorno)+10, PATINDEX('%</dhRecbto>%' , XMLGTVE.XML_Retorno)-11),1,(PATINDEX('%</dhRecbto>%', XMLGTVE.XML_Retorno)-PATINDEX('%<dhRecbto>%', XMLGTVE.XML_Retorno))-10) dhRecbto, \n"                    
                    + "XMLGTVE.Dt_Envio, XMLGTVE.Hr_Envio, \n"
                    + "XMLGTVE.Dt_retorno, XMLGTVE.Hr_retorno, XMLGTVE.FatIDGTV, \n"
                    + "XMLGTVE.OPenIDFilial, \n"
                    + "REPLACE(REPLACE(substring(XMLGTVE.xml_retorno,1, Len(XMLGTVE.xml_retorno)) , CHAR(13), ''), CHAR(10), '') XMLRetorno from XMLGTVE \n"
                    + " where XMLGTVE.Dt_Envio >= GetDate()-3 and (XMLGTVE.ChaveGTVE = '" + vChaveGTVE + "'"
                    + "   or XMLGTVE.XML_Envio like '%" + vChaveGTVE + "%')";
            try {
                qTmpX = new Consulta(vSQL, dbpadrao);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                throw new Exception("xmlGTVE - " + e.getMessage() + "\r\n"
                        + vSQL);
            }

            int vConta = 0;

            while (qTmpX.Proximo()) {
                vConta++;
                vjnEnvio = new JSONObject();
                vCNPJ = qTmpX.getString("CNPJ");
                vGuia = qTmpX.getBigDecimal("Guia").toString().replace(".0", "");;
                vSerie = qTmpX.getString("Serie");
                vCodCidade = qTmpX.getString("CodCidade").replace(".0", "");
                vDt_GTVE = qTmpX.getString("Dt_Gtve");
                vRetorno = qTmpX.getString("Hr_Gtve");
                vDt_envio = qTmpX.getString("Dt_Envio");
                vHr_envio = qTmpX.getString("Hr_Envio");
                vDt_retorno = qTmpX.getString("Dt_retorno");
                vHr_retorno = qTmpX.getString("Hr_retorno");
                vFatIDGTV = qTmpX.getString("FatIDGTV");
                vOpenIDFilial = qTmpX.getString("OpenIDFilial");
                vXMLretorno = qTmpX.getString("XMLRetorno");
                vVersao = "3.00";
                vnGuia = qTmpX.getBigDecimal("Guia").toString().replace(".0", "");;
                vCStat = qTmpX.getString("cStat");
                vMotivo = qTmpX.getString("Retorno"); 
                vDtRecbto = qTmpX.getString("dhRecbto");                 
                if (vDtRecbto == null){
                    vDtRecbto = vDt_retorno;
                }

                vjnEnvio.put("cnpj", vCNPJ);
                vjnEnvio.put("guia", vGuia);
                vjnEnvio.put("serie", vSerie);
                vjnEnvio.put("codcidade", vCodCidade);
                vjnEnvio.put("dt_gtve", vDt_GTVE);
                vjnEnvio.put("retorno", vRetorno);
                vjnEnvio.put("dt_envio", vDt_envio);
                vjnEnvio.put("hr_envio", vHr_envio);
                vjnEnvio.put("dt_retorno", vDt_retorno);
                vjnEnvio.put("hr_retorno", vHr_retorno);
                vjnEnvio.put("fatidgtv", vFatIDGTV);
                vjnEnvio.put("openidfilial", vOpenIDFilial);
                vjnEnvio.put("versao", vVersao);
                vjnEnvio.put("chCTe", vChaveGTVE);
                vjnEnvio.put("nCTe", vnGuia);
                vjnEnvio.put("cStat", vCStat);
                vjnEnvio.put("xMotivo", vMotivo);
                vjnEnvio.put("dhRecbto", vDtRecbto);                
                vjnEnvio.put("xmlretorno", vXMLretorno);

                vjnArray.put(vjnEnvio);
            }
            if (vConta == 0) {
                vjnEnvio.put("resposta", "NAO HA DADOS");
                vjnArray.put(vjnEnvio);
            }
            try {
                vjnRetorno.put("gtve", vjnArray);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("XMLGTVE - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

}

/* ScriptEngineManager factory = new ScriptEngineManager(); // Permite escolher linguagens diferentes da tradicional Java
        ScriptEngine engine = factory.getEngineByName("JavaScript"); //Escolhemos a linguagem que será utilizada, nesse caso é o JavaScript
        Invocable invocable = (Invocable) engine;
        String vImgX = "{FotoSrvN1: " + vFotoSrvN1X + "),"
                + "FotoSrvS1: " + vFotoSrvS1X + "),"
                + "FotoN1: " + vFotoN1X + "),"
                + "FotoS1: " + vFotoN1X + ")";
        //logexecucao.Grava("Montagem vImgX:" + vImgX, caminho);
        String vResultado = "";
        try {
            engine.eval(new FileReader("C:\\xampp\\htdocs\\satellite\\js\\saslibraryRec.js"));
            vResultado = (String) invocable.invokeFunction("comparaFoto", vImgX);
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do JS" + e.getMessage(), caminho);            
        }

            vSQL = "Select TMktdet.Sequencia, TMktdet.Andamento, TMktdet.Data, TMktdet.Hora, TMktdet.TipoCont, TMktdet.CodPessoa, \n" +
                   "TMktdet.Historico, TMktdet.Detalhes,  Pessoa.Nome, Pessoa.email, Pessoa.PWweb, PstServ.Local, Contatos.Nome NomeContato, TMktdet.Situacao, \n"+
                   " TMktdet.Ciente, tMKTDet.CodCont from TMktdet \n" +
                   "Left Join Contatos on Contatos.Codigo = TMKtDet.CodCont\n " +
                   "Left Join Clientes on Clientes.Codigo = Contatos.Codcli\n " +
                   "                  and Clientes.Codfil = Contatos.CodFil\n " +
                   "Left Join PStServ on PstServ.Codcli = Clientes.Codigo\n " +
                   "                 and PStServ.CodFil = Clientes.CodFil\n " +
                   "Left Join Pessoa  on Pessoa.Codigo = TMKtDet.CodPessoa \n "+
                   " Where TMKtDet.CodPessoa = " +vCodPessoa+
                   "   and Pessoa.PWWeb = '"+vPW+"'";


 */
