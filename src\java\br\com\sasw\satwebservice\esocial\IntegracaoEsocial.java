package br.com.sasw.satwebservice.esocial;

import java.util.logging.Level;
import java.util.logging.Logger;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.UriInfo;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

/**
 * REST Web Service
 *
 * <AUTHOR>
 */
@Path("integracao-esocial")
public class IntegracaoEsocial {

    @Context
    private UriInfo context;

    public IntegracaoEsocial() {
    }

    @POST
    public String send(String body,
            @QueryParam("id") String id,
            @QueryParam("empresa") String empresa,
            @QueryParam("nrInscEmpregador") String nrInscEmpregador,
            @QueryParam("tpInscEmpregador") String tpInscEmpregador,
            @QueryParam("nrInscTransmissor") String nrInscTransmissor,
            @QueryParam("tpInscTransmissor") String tpInscTransmissor,
            @QueryParam("idGrupo") String idGrupo) {

                try {
            String uri = "http://localhost:8081/envio-esocial?id=${id}&empresa=${empresa}&nrInscEmpregador=${nrInscEmpregador}&tpInscEmpregador=${tpInscEmpregador}&nrInscTransmissor=${nrInscTransmissor}&tpInscTransmissor=${tpInscTransmissor}&idGrupo=${idGrupo}";
            uri = uri.replace("${id}", id);
            uri = uri.replace("${empresa}", empresa);
            uri = uri.replace("${nrInscEmpregador}", nrInscEmpregador);
            uri = uri.replace("${tpInscEmpregador}",     tpInscEmpregador);
            uri = uri.replace("${nrInscTransmissor}", nrInscTransmissor);
            uri = uri.replace("${tpInscTransmissor}", tpInscTransmissor);
            uri = uri.replace("${idGrupo}", idGrupo);

            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();
                    
            HttpPost httpPost = new HttpPost(uri);
            httpPost.setEntity(new StringEntity(body));
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(IntegracaoEsocial.class.getName()).log(Level.INFO, "ERRO:", ex);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }        
        return null;
    }
}
