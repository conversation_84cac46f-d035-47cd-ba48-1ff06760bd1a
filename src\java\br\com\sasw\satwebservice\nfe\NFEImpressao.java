/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.nfe;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import br.com.sasw.pacotesuteis.sasbeans.XMLNFE;
import br.com.sasw.pacotesuteis.sasdaos.XMLNFEDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.util.HashMap;
import java.util.Map;
import javax.ws.rs.core.Response;

/**
 *
 * <AUTHOR>
 */
public class NFEImpressao {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;

    public NFEImpressao() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\NFe\\"
                + getDataAtual("SQL") + "\\erro.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    public Response impressao(String chave) {
        Map retorno = new HashMap<>();
        try {
//             Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao("SATEXCEL");
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido");
            }

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\NFe\\"
                    + getDataAtual("SQL") + "\\impressao.txt";

            this.logerro.Grava(chave, this.caminho);

            XMLNFEDao xmlNFEDao = new XMLNFEDao();
            XMLNFE xmlNFE = xmlNFEDao.buscarChaveXMLNFE(chave, persistencia);
            retorno.put("xmlNFE", xmlNFE);

        } catch (Exception e) {
            // Salvando em log o que foi mandado
            retorno.clear();
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);
        } finally {

            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(retorno)
                    .build();
        }
    }
}
