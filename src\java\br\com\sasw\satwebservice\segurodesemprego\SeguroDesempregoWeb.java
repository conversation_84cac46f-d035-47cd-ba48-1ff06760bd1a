package br.com.sasw.satwebservice.segurodesemprego;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.pacotesuteis.utilidades.Trace;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "SeguroDesempregoWeb", urlPatterns = {"/SeguroDesempregoWeb"})
public class SeguroDesempregoWeb extends HttpServlet {
    
    private ArquivoLog logerro;
    private SasPoolPersistencia pool;
    
    /**
     * Cria a pool de persistencias na criação da servlet
     */
    @Override
    public void init(){
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        request.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter resp = response.getWriter();
                    
        //colocar em variáveis tudo que vier nos parametros
        String resposta = "<?xml version=\"1.0\"?>";
        String param = request.getParameter("param").toUpperCase();
        String sCodPessoa = request.getParameter("codpessoa");
        String senha = request.getParameter("senha");
        BigDecimal codfil = new BigDecimal(request.getParameter("codfil"));
        String codmovfp = request.getParameter("codmovfp");
        String dtdemis = request.getParameter("dtdemis");
         
        String dataAtual = request.getParameter("dataAtual");
        String horaAtual = request.getParameter("horaAtual");
        if(null == dataAtual || dataAtual.equals("")) dataAtual = DataAtual.getDataAtual("SQL");
        if(null == horaAtual || horaAtual.equals("")) horaAtual = DataAtual.getDataAtual("HORA");
        
        logerro = new ArquivoLog();
        try {
            // Gera o trace com os valores da requisição
            long tStart = 0, tEnd;
            Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), sCodPessoa,  param, logerro);
            tStart = System.currentTimeMillis();
            
            //Cria a persistencia
            Persistencia persistencia = pool.getConexao(param);
            
            ArquivoSeguroDesemprego asd = new ArquivoSeguroDesemprego();
            String arquivo = asd.GerarSeguroDesemprego(codfil, codmovfp, DataAtual.inverteData(dtdemis), persistencia);
            resposta += Xmls.tag("arquivo", arquivo);
            resp.print(resposta);
            persistencia.FechaConexao();
            
            // Calcula o tempo gasto pelo servidor entre criar a persistencia e enviar a respota de volta ao android e gera o trace disso.
            tEnd = System.currentTimeMillis();
            NumberFormat formatter = new DecimalFormat("#0.00");
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Tempo gasto: "
                    + formatter.format((tEnd - tStart) / 1000.0)+" segundos.\r\n", sCodPessoa, param, logerro); 
        } catch (Exception e) {
            Trace.gerarTrace(getServletContext(), this.getServletName(),"Falha - "+ e.getMessage(), sCodPessoa, param, logerro);
            resp.print("<?xml version=\"1.0\"?>" + Xmls.tag("resp", "0") + Xmls.tag("erro", e.getMessage()));
        }
    }

    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
