/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.consultaci;

import Arquivo.ArquivoLog;
import Dados.Consulta;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasDaos.FiliaisDao;
import br.com.sasw.pacotesuteis.sasbeans.TOKENS;
import br.com.sasw.pacotesuteis.sasdaos.TOKENSDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.satwebservice.executasvc.ValidarToken;
import br.com.sasw.satwebservice.messages.Messages;
import java.sql.ResultSet;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
@Path("/consultaci/")
public class consultci {

    private final ArquivoLog logerro;
    private ArquivoLog logexecucao;
    private ArquivoLog arquivohtml;
    private final SasPoolPersistencia pool;
    private String caminho;
    private String caminhoweb;
    private final Messages messages;
    private final FiliaisDao filiaisDao;
    private final TOKENSDao tokenDao;
    private ResultSet vResultadoConsulta;
    private String vRetorno;
    private String vRetornoPessoa;

    public consultci() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ConsultaCI"
                + "\\erro.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        messages = new Messages();

        filiaisDao = new FiliaisDao();
        tokenDao = new TOKENSDao();
    }

    
    @GET
    @Path("/integraCICF")
    @Produces(MediaType.APPLICATION_JSON)    
    public Response integraCICF(@QueryParam("token") String vToken, @QueryParam("empresa") String vEmpresa, @QueryParam("dataini") String vDataIni, @QueryParam("datafim") String vDataFim) {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\IntegraCofres\\"
                + "\\integraCICF.txt";
        // TODO ler body em json
        //String param;
        //Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        //String vToken = (String) parametros.getOrDefault("token", null);
        //String vChaveGTVE = (String) parametros.getOrDefault("chavegtve", null);
        if (vToken.equals("")) {
            vToken = "C0574EE251E7EB197650B332CEE4750C";
        }
        logexecucao.Grava("parametros: token=" + vToken + ";empresa=" + vEmpresa+
                "parametros: dataini=" + vDataIni + ";datafim=" + vDataFim, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnEnvio = new JSONObject();
        JSONArray vjnArray = new JSONArray();

        try {
            //Validando se a matrícula informada não está vazia
            if (vEmpresa == null || vEmpresa.equals("")) {
                throw new consultciException("Empresa Inexistente.");
            }

            logexecucao.Grava("Inicia Conexao Central", caminho);
            
            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);
            
            logexecucao.Grava("Passou Token", caminho);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "", vSQLExec = "", vBancoDados = "";
            String vCodCofre = "";
            String vData = "";
            String vDiaSem = "";
            String vFeriado = "";
            String vVlrRecAntesCorte = "";
            String vHrRecAntesCorte = "";
            String vVlrRecAposCorte = "";
            String vHrRecAposCorte = "";
            String vCredD0Rec = "";
            String vCredD0RecAposCorte = "";
            String vCredD1Rec = "";
            String vCredD0 = "";
            String vCredD1 = "";
            String vCredProxDU = "";
            String vCredDiaAnt = "";
            String vCredDiaD0Rec = "";
            String vCredCorte = "";
            String vVlrTotalCred = "";
            String vVlrTotalRec = "";
            String vSaldoFisTotal = "";
            String vSaldoFisCred = "";
            String vSaldoFisCst = "";
            String vDataStr = "";
            String vDiaSemana = "";
            String vCNPJ = "";
            String vRazaoSocial = "";
            String vHrUltAtua = "";
            Consulta qTmpX;
            vBancoDados = qToken.getBancoDados();

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }
            String vSQLHR;
            vSQLHR  = "Select top 1 TesCofresMov.Hora from TesCofresMov "+
              " where TesCofresMov.CodCofre = Clientes.CodCofre "+
              "   and TesCofresMov.Data     <=  getDate() "+
              "   and TesCofresMov.CodCliente <> '0'"+
              " order by TesCofresMov.Data Desc ";

            vSQL = "Select TesCofresRes.*,  \n"
                    +vSQLHR+" HrUltAtua, "
                    + "Case    when DiaSem = 1 then 'Domingo  '   \n"
                    + "        when DiaSem = 2 then 'Segunda  '   \n"
                    + "        when DiaSem = 3 then 'Terça    '   \n"
                    + "        when DiaSem = 4 then 'Quarta   '   \n"
                    + "        when DiaSem = 5 then 'Quinta   '   \n"
                    + "        when DiaSem = 6 then 'Sexta    '   \n"
                    + "        when DiaSem = 7 then 'Sabado   ' else '' end DiaSemana, Clientes.Nome, Clientes.CGC CNPJ from TesCofresRes  \n"
                    + "Left Join Clientes on  Clientes.CodCofre = TesCofresRes.CodCofre\n"
                    + "where Data = '10/19/2023'   \n"
                    + "  and TesCofresRes.CodCofre in (Select Clientes.CodCofre  From PessoaCliAut\n"
                    + "           Left Join Clientes  on PessoaCliAut.CodCli = Clientes.Codigo\n"
                    + "                              and PessoaCliAut.CodFil = Clientes.CodFil\n"
                    + "           Left Join Pessoa on Pessoa.Codigo = PessoaCliAut.Codigo\n"
                    + "           Where Clientes.CodFil >= 1    \n"
                    + "             and  Pessoa.Email =  '<EMAIL>')\n"
                    + "             and TesCofresRes.Data >= '"+vDataIni+"'"
                    + "             and TesCofresRes.Data <= '"+vDataFim+"'"
                    + "order by TesCofresRes.Data Desc ";
            try {
                qTmpX = new Consulta(vSQL, dbpadrao);
                qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                throw new Exception("integraCICF - " + e.getMessage() + "\r\n"
                        + vSQL);
            }

            int vConta = 0;

            while (qTmpX.Proximo()) {
                vConta++;
                vjnEnvio = new JSONObject();
                vCodCofre = qTmpX.getString("CodCofre");
                vData = qTmpX.getString("Data");
                vDiaSem = qTmpX.getString("DiaSem");
                vFeriado = qTmpX.getString("Feriado");
                vVlrRecAntesCorte = qTmpX.getString("VlrRecAntesCorte");
                vHrRecAntesCorte = qTmpX.getString("HrRecAntesCorte");
                vVlrRecAposCorte = qTmpX.getString("VlrRecAposCorte");
                vHrRecAposCorte = qTmpX.getString("HrRecAposCorte");
                vCredD0Rec = qTmpX.getString("CredD0Rec");
                vCredD0RecAposCorte = qTmpX.getString("CredD0RecAposCorte");
                vCredD1Rec = qTmpX.getString("CredD1Rec");
                vCredD0 = qTmpX.getString("CredD0");
                vCredD1 = qTmpX.getString("CredD1");
                vCredProxDU = qTmpX.getString("CredProxDU");
                vCredDiaAnt = qTmpX.getString("CredDiaAnt");
                vCredDiaD0Rec = qTmpX.getString("CredDiaD0Rec");
                vCredCorte = qTmpX.getString("CredCorte");
                vVlrTotalCred = qTmpX.getString("VlrTotalCred");
                vVlrTotalRec = qTmpX.getString("VlrTotalRec");
                vSaldoFisTotal = qTmpX.getString("SaldoFisTotal");
                vSaldoFisCred = qTmpX.getString("SaldoFisCred");
                vSaldoFisCst = qTmpX.getString("SaldoFisCst");
                vDataStr = qTmpX.getString("DataStr");
                vDiaSemana = qTmpX.getString("DiaSemana");
                vRazaoSocial = qTmpX.getString("Nome");
                vCNPJ = qTmpX.getString("CNPJ");
                vHrUltAtua = qTmpX.getString("HrUltAtua");

                vjnEnvio.put("CodCofre", vCodCofre);
                vjnEnvio.put("Data", vData);
                vjnEnvio.put("DiaSem", vDiaSem);
                vjnEnvio.put("Feriado", vFeriado);
                vjnEnvio.put("VlrRecAntesCorte", vVlrRecAntesCorte);
                vjnEnvio.put("HrRecAntesCorte", vHrRecAntesCorte);
                vjnEnvio.put("VlrRecAposCorte", vVlrRecAposCorte);
                vjnEnvio.put("HrRecAposCorte", vHrRecAposCorte);
                vjnEnvio.put("CredD0Rec", vCredD0Rec);
                vjnEnvio.put("CredD0RecAposCorte", vCredD0RecAposCorte);
                vjnEnvio.put("CredD1Rec", vCredD1Rec);
                vjnEnvio.put("CredD0", vCredD0);
                vjnEnvio.put("CredD1", vCredD1);
                vjnEnvio.put("CredProxDU", vCredProxDU);
                vjnEnvio.put("CredDiaAnt", vCredDiaAnt);
                vjnEnvio.put("CredDiaD0Rec", vCredDiaD0Rec);
                vjnEnvio.put("CredCorte", vCredCorte);
                vjnEnvio.put("VlrTotalCred", vVlrTotalCred);
                vjnEnvio.put("VlrTotalRec", vVlrTotalRec);
                vjnEnvio.put("SaldoFisTotal", vSaldoFisTotal);
                vjnEnvio.put("SaldoFisCred", vSaldoFisCred);
                vjnEnvio.put("SaldoFisCst", vSaldoFisCst);
                vjnEnvio.put("DataStr", vDataStr);
                vjnEnvio.put("DiaSemana", vDiaSemana);
                vjnEnvio.put("RazaoSocial", vRazaoSocial);
                vjnEnvio.put("CNPJ", vCNPJ);
                vjnEnvio.put("HrUltAtua", vHrUltAtua);


                vjnArray.put(vjnEnvio);
            }
            if (vConta == 0) {
                vjnEnvio.put("resposta", "NAO HA DADOS");
                vjnArray.put(vjnEnvio);
            }
            try {
                vjnRetorno.put("CofresRes", vjnArray);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("GUIA - " + e.getMessage() + "\r\n"
                );
            }
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
        }
    }

}

/* ScriptEngineManager factory = new ScriptEngineManager(); // Permite escolher linguagens diferentes da tradicional Java
        ScriptEngine engine = factory.getEngineByName("JavaScript"); //Escolhemos a linguagem que será utilizada, nesse caso é o JavaScript
        Invocable invocable = (Invocable) engine;
        String vImgX = "{FotoSrvN1: " + vFotoSrvN1X + "),"
                + "FotoSrvS1: " + vFotoSrvS1X + "),"
                + "FotoN1: " + vFotoN1X + "),"
                + "FotoS1: " + vFotoN1X + ")";
        //logexecucao.Grava("Montagem vImgX:" + vImgX, caminho);
        String vResultado = "";
        try {
            engine.eval(new FileReader("C:\\xampp\\htdocs\\satellite\\js\\saslibraryRec.js"));
            vResultado = (String) invocable.invokeFunction("comparaFoto", vImgX);
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do JS" + e.getMessage(), caminho);            
        }

            vSQL = "Select TMktdet.Sequencia, TMktdet.Andamento, TMktdet.Data, TMktdet.Hora, TMktdet.TipoCont, TMktdet.CodPessoa, \n" +
                   "TMktdet.Historico, TMktdet.Detalhes,  Pessoa.Nome, Pessoa.email, Pessoa.PWweb, PstServ.Local, Contatos.Nome NomeContato, TMktdet.Situacao, \n"+
                   " TMktdet.Ciente, tMKTDet.CodCont from TMktdet \n" +
                   "Left Join Contatos on Contatos.Codigo = TMKtDet.CodCont\n " +
                   "Left Join Clientes on Clientes.Codigo = Contatos.Codcli\n " +
                   "                  and Clientes.Codfil = Contatos.CodFil\n " +
                   "Left Join PStServ on PstServ.Codcli = Clientes.Codigo\n " +
                   "                 and PStServ.CodFil = Clientes.CodFil\n " +
                   "Left Join Pessoa  on Pessoa.Codigo = TMKtDet.CodPessoa \n "+
                   " Where TMKtDet.CodPessoa = " +vCodPessoa+
                   "   and Pessoa.PWWeb = '"+vPW+"'";


 */
