<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by <EMAIL> (PROCERGS) -->
<xs:schema xmlns="http://www.portalfiscal.inf.br/cte" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="cteTiposBasico_v3.00.xsd"/>
	<xs:element name="aquav">
		<xs:annotation>
			<xs:documentation>Informações do modal Aquaviário</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="vPrest" type="TDec_1302">
					<xs:annotation>
						<xs:documentation>Valor da Prestação Base de Cálculo do AFRMM</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="vAFRMM" type="TDec_1302">
					<xs:annotation>
						<xs:documentation>AFRMM (Adicional de Frete para Renovação da Marinha Mercante)</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="xNavio">
					<xs:annotation>
						<xs:documentation>Identificação do Navio </xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="TString">
							<xs:maxLength value="60"/>
							<xs:minLength value="1"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="balsa" minOccurs="0" maxOccurs="3">
					<xs:annotation>
						<xs:documentation>Grupo de informações das balsas</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="xBalsa">
								<xs:annotation>
									<xs:documentation>Identificador da Balsa</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="TString">
										<xs:minLength value="1"/>
										<xs:maxLength value="60"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="nViag" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Número da Viagem</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:whiteSpace value="preserve"/>
							<xs:pattern value="[1-9]{1}[0-9]{0,9}"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="direc">
					<xs:annotation>
						<xs:documentation>Direção</xs:documentation>
						<xs:documentation>Preencher com: N-Norte, L-Leste, S-Sul, O-Oeste  </xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:whiteSpace value="preserve"/>
							<xs:enumeration value="N"/>
							<xs:enumeration value="S"/>
							<xs:enumeration value="L"/>
							<xs:enumeration value="O"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="irin">
					<xs:annotation>
						<xs:documentation>Irin do navio sempre deverá ser informado</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:whiteSpace value="preserve"/>
							<xs:maxLength value="10"/>
							<xs:minLength value="1"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="detCont" minOccurs="0" maxOccurs="unbounded">
					<xs:annotation>
						<xs:documentation>Grupo de informações de detalhamento dos conteiners 
(Somente para Redespacho Intermediário e Serviço Vinculado a Multimodal)</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="nCont" type="TContainer">
								<xs:annotation>
									<xs:documentation>Identificação do Container</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="lacre" minOccurs="0" maxOccurs="3">
								<xs:annotation>
									<xs:documentation>Grupo de informações dos lacres dos cointainers da qtde da carga </xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="nLacre">
											<xs:annotation>
												<xs:documentation>Lacre</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TString">
													<xs:minLength value="1"/>
													<xs:maxLength value="20"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="infDoc" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Informações dos documentos dos conteiners</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:choice>
										<xs:element name="infNF" maxOccurs="unbounded">
											<xs:annotation>
												<xs:documentation>Informações das NF</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="serie">
														<xs:annotation>
															<xs:documentation>Série</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:minLength value="1"/>
																<xs:maxLength value="3"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="nDoc">
														<xs:annotation>
															<xs:documentation>Número </xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:minLength value="1"/>
																<xs:maxLength value="20"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="unidRat" type="TDec_0302_0303" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Unidade de medida rateada (Peso,Volume)</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="infNFe" maxOccurs="unbounded">
											<xs:annotation>
												<xs:documentation>Informações das NFe</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="chave" type="TChNFe">
														<xs:annotation>
															<xs:documentation>Chave de acesso da NF-e</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="unidRat" type="TDec_0302_0303" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Unidade de medida rateada (Peso,Volume)</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:choice>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="tpNav" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Tipo de Navegação</xs:documentation>
						<xs:documentation>Preencher com: 
						0 - Interior;
						1 - Cabotagem</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:whiteSpace value="preserve"/>
							<xs:enumeration value="0"/>
							<xs:enumeration value="1"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
