/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.crash;

import Arquivo.ArquivoLog;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.UriInfo;

/**
 * REST Web Service
 *
 * <AUTHOR>
 */
@Path("/ws-crash/crashes/")
public class RecebeErros {

    @Context
    private UriInfo context;

    /**
     * Creates a new instance of RecebeErros
     */
    public RecebeErros() {

    }

    @PUT
    @Path("/{aplicativo}/{filename}/")
    public void uploadFile(@PathParam("aplicativo") String aplicativo, @PathParam("filename") String filename, String input) throws IOException {
        try {
            String path = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Crash\\"
                    + aplicativo + "\\"
                    + getDataAtual("SQL") + "\\";
            File file = new File(path);
            if (!file.exists()) {
                file.mkdirs();
            }
            BufferedWriter writer = new BufferedWriter(new FileWriter(path + filename + ".json"));
            writer.write(input);
            writer.close();
        } catch (Exception e) { 
            String caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Crash\\Erro\\"
                    + getDataAtual("SQL") + "\\log.txt";
            ArquivoLog logerro = new ArquivoLog();
            logerro.Grava(e.getMessage(), caminho);
        }
    }
}
