/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.nfe;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.NFiscal;
import br.com.sasw.pacotesuteis.sasbeans.TOKENS;
import br.com.sasw.pacotesuteis.sasdaos.TOKENSDao;
import br.com.sasw.pacotesuteis.sasdaos.XMLNFEDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.satwebservice.batidaponto.ValidarToken.validarValidade;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServlet;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;

/**
 *
 * <AUTHOR>
 */
@Path("/ws-nfe-dashboard/")
public class NFEDashboard extends HttpServlet {

    private @Context
    HttpHeaders httpHeaders;
    private @Context
    UriInfo uriInfo;

    private final Gson gson;
    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private final String caminho;
    private final TOKENSDao tokensDao;

    /**
     * Creates a new instance of ImportacaoClientes
     */
    public NFEDashboard() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        tokensDao = new TOKENSDao();
        gson = new GsonBuilder().create();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/{empresa}")
    public Response funcion(@PathParam("empresa") String empresa) {
        try {
            this.persistencia = this.pool.getConexao(empresa.toUpperCase());

            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }

            List<NFiscal> listaNotas = new ArrayList<>();

            XMLNFEDao xmlNfeDao = new XMLNFEDao();
            listaNotas = xmlNfeDao.buscarDahsboard(this.persistencia);
            
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(new Gson().toJson(listaNotas).toString())
                    .build();
        } catch (Exception e) {
            return Response
                    .status(Response.Status.UNAUTHORIZED)
                    .type("application/json")
                    .entity(gson.toJson(e.getMessage()))
                    .build();
        }
    }

    private String getParam() throws Exception {

        if (this.httpHeaders.getRequestHeader("authorization") == null || this.httpHeaders.getRequestHeader("authorization").isEmpty()) {
            this.logerro.Grava("Authorization faltando\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Authorization missing");
        }

        String bearer = null;

        for (String b : this.httpHeaders.getRequestHeader("authorization")) {
            if (b.contains("Bearer ")) {
                bearer = b;
                break;
            }
        }

        if (bearer == null) {
            this.logerro.Grava("Authorization Bearer faltando\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Authorization Bearer missing");
        }

        String codigo = bearer.replace("Bearer ", "");
        this.persistencia = this.pool.getConexao("SATELLITE");
        TOKENS token = this.tokensDao.obterToken(codigo, this.persistencia);

        if (token == null) {
            this.logerro.Grava("token inválido: " + codigo + "\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Invalid token: " + codigo);
        }

        if (!token.getModulo().equals("INTEGRACAO")) {
            this.logerro.Grava("token inválido: " + codigo + " modulo(" + token.getModulo() + ")\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Invalid token: " + codigo);
        }

        if (!token.getChave().equals("WS")) {
            this.logerro.Grava("token inválido: " + codigo + " chave(" + token.getChave() + ")\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Invalid token: " + codigo);
        }

        try {
            validarValidade(token);
        } catch (Exception e) {
            this.logerro.Grava("token expirado: " + codigo + " validade(" + token.getDtValid() + ")\r\n" + this.uriInfo.getPath(), this.caminho);
            throw new Exception("Expired token: " + codigo);
        }

        return token.getBancoDados();
    }
}
