/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.nfe.deserializers;

import br.com.sasw.satwebservice.nfe.exception.NFeException;
import br.com.sasw.satwebservice.nfe.models.EnvioModel;
import br.com.sasw.satwebservice.nfe.models.ItensModel;
import br.com.sasw.satwebservice.nfe.models.RejeicaoModel;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 *
 * <AUTHOR>
 */
public class NFeEnvioDeserializer implements JsonDeserializer<EnvioModel> {

    @Override
    public EnvioModel deserialize(JsonElement json, Type typeOfT,
            JsonDeserializationContext context) throws NFeException {
        final JsonObject jsonObject = json.getAsJsonObject();

        EnvioModel envio = new EnvioModel();

        try {
            envio.setAcao(jsonObject.get("acao").getAsInt());
            if (envio.getAcao() > 2) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.acao")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.acao", jsonObject.get("acao").toString())));
        }

//        try {
//            if(envio.getAcao() == 2){
//                envio.setChave(jsonObject.get("chave").getAsString());
//                if (envio.getChave().length() > 80) {
//                    throw new Exception();
//                }
//            }
//        } catch (Exception e) {
//            if (e instanceof NullPointerException) {
//                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.chave")));
//            }
//            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.chave", jsonObject.get("chave").toString())));
//        }

        try {
            envio.setOrgao(jsonObject.get("orgao").getAsInt());
            if (envio.getOrgao() > 99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.orgao")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.orgao", jsonObject.get("orgao").toString())));
        }

        try {
            envio.setNatOp(jsonObject.get("natOp").getAsString());
            if (envio.getNatOp().length() > 60) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.natOp")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.natOp", jsonObject.get("natOp").toString())));
        }

        try {
            envio.setIndPag(jsonObject.get("indPag").getAsInt());
            if (envio.getIndPag() > 2) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.indPag")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.indPag", jsonObject.get("indPag").toString())));
        }

        try {
            envio.setMod(jsonObject.get("mod").getAsString());
            if (envio.getMod().length() > 2) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.mod")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.mod", jsonObject.get("mod").toString())));
        }

        try {
            envio.setSerie(jsonObject.get("serie").getAsInt());
            if (envio.getSerie() > 999) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.serie")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.serie", jsonObject.get("serie").toString())));
        }

        try {
            envio.setnRPS(jsonObject.get("nRPS").getAsString());
            if (envio.getnRPS().length() > 20) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.nRPS")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.nRPS", jsonObject.get("nRPS").toString())));
        }

        try {
            envio.setDatahemi(jsonObject.get("datahemi").getAsString());
            if (envio.getDatahemi().length() > 25) {
                throw new Exception();
            }
            
            LocalDateTime.parse(envio.getDatahemi(), DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX"));
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.datahemi")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.datahemi", jsonObject.get("datahemi").toString())));
        }

        try {
            envio.setDataSaiEnt(jsonObject.get("dataSaiEnt").getAsString());
            if (envio.getDataSaiEnt().length() > 10) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.dataSaiEnt")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.dataSaiEnt", jsonObject.get("dataSaiEnt").toString())));
        }

        try {
            envio.setHoraSaiEnt(jsonObject.get("HoraSaiEnt").getAsString());
            if (envio.getHoraSaiEnt().length() > 8) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.HoraSaiEnt")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.HoraSaiEnt", jsonObject.get("HoraSaiEnt").toString())));
        }

        try {
            envio.setTpNF(jsonObject.get("tpNF").getAsInt());
            if (envio.getTpNF() > 1) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.tpNF")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.tpNF", jsonObject.get("tpNF").toString())));
        }

        try {
            envio.setCodcidadeicms(jsonObject.get("codcidadeicms").getAsInt());
            if (envio.getCodcidadeicms() > 9999999) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.codcidadeicms")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.codcidadeicms", jsonObject.get("codcidadeicms").toString())));
        }

        try {
            envio.setTipoImpressao(jsonObject.get("tipoImpressao").getAsInt());
            if (envio.getTipoImpressao() > 4) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.tipoImpressao")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.tipoImpressao", jsonObject.get("tipoImpressao").toString())));
        }

        try {
            envio.setCnpjemissor(jsonObject.get("cnpjemissor").getAsString());
            if (envio.getCnpjemissor().length() > 14) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.cnpjemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.cnpjemissor", jsonObject.get("cnpjemissor").toString())));
        }

        try {
            envio.setRazaosocialemissor(jsonObject.get("razaosocialemissor").getAsString());
            if (envio.getRazaosocialemissor().length() > 60) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.razaosocialemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.razaosocialemissor", jsonObject.get("razaosocialemissor").toString())));
        }

        try {
            envio.setFantasiaemissor(jsonObject.get("fantasiaemissor").getAsString());
            if (envio.getFantasiaemissor().length() > 40) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.fantasiaemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.fantasiaemissor", jsonObject.get("fantasiaemissor").toString())));
        }

        try {
            envio.setEnderecoemissor(jsonObject.get("enderecoemissor").getAsString());
            if (envio.getEnderecoemissor().length() > 60) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.enderecoemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.enderecoemissor", jsonObject.get("enderecoemissor").toString())));
        }

        try {
            envio.setNroemissor(jsonObject.get("nroemissor").getAsInt());
            if (envio.getNroemissor() > 99999) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.nroemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.nroemissor", jsonObject.get("nroemissor").toString())));
        }
        
        try {
            envio.setBairroemissor(jsonObject.get("bairroemissor").getAsString());
            if (envio.getBairroemissor().length() > 25) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.bairroemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.bairroemissor", jsonObject.get("bairroemissor").toString())));
        }

        try {
            envio.setCidadeemissor(jsonObject.get("cidadeemissor").getAsString());
            if (envio.getCidadeemissor().length() > 25) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.cidadeemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.cidadeemissor", jsonObject.get("cidadeemissor").toString())));
        }

        try {
            envio.setUfemissor(jsonObject.get("ufemissor").getAsString());
            if (envio.getUfemissor().length() > 2) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.ufemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.ufemissor", jsonObject.get("ufemissor").toString())));
        }

        try {
            envio.setCodcidadeemissor(jsonObject.get("codcidadeemissor").getAsInt());
            if (envio.getCodcidadeemissor() > 9999999) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.codcidadeemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.codcidadeemissor", jsonObject.get("codcidadeemissor").toString())));
        }

        try {
            envio.setCepemissor(jsonObject.get("cepemissor").getAsString());
            if (envio.getCepemissor().length() > 8) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.cepemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.cepemissor", jsonObject.get("cepemissor").toString())));
        }

        try {
            envio.setPaisemissor(jsonObject.get("paisemissor").getAsInt());
            if (envio.getPaisemissor() > 9999) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.paisemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.paisemissor", jsonObject.get("paisemissor").toString())));
        }

        try {
            envio.setTelefoneemissor(jsonObject.get("telefoneemissor").getAsString());
            if (envio.getTelefoneemissor().length() > 11) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.telefoneemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.telefoneemissor", jsonObject.get("telefoneemissor").toString())));
        }

        try {
            envio.setEmailemissor(jsonObject.get("emailemissor").getAsString());
            if (envio.getEmailemissor().length() > 80) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.emailemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.emailemissor", jsonObject.get("emailemissor").toString())));
        }

        try {
            envio.setIeemissor(jsonObject.get("ieemissor").getAsString());
            if (envio.getIeemissor().length() > 14) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.ieemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.ieemissor", jsonObject.get("ieemissor").toString())));
        }

        try {
            envio.setImemissor(jsonObject.get("imemissor").getAsString());
            if (envio.getImemissor().length() > 15) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.imemissor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.imemissor", jsonObject.get("imemissor").toString())));
        }

        try {
            envio.setCNAE(jsonObject.get("CNAE").getAsInt());
            if (envio.getCNAE() > 9999999) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.CNAE")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.CNAE", jsonObject.get("CNAE").toString())));
        }

        try {
            envio.setCRT(jsonObject.get("CRT").getAsInt());
            if (envio.getCRT() > 3) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.CRT")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.CRT", jsonObject.get("CRT").toString())));
        }

        try {
            envio.setCnpjdest(jsonObject.get("cnpjdest").getAsString());
            if (envio.getCnpjdest().length() > 14) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                // Se for nulo, não faz nada;
            } else {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.cnpjdest", jsonObject.get("cnpjdest").toString())));
            }
        }

        try {
            envio.setCpfdest(jsonObject.get("cpfdest").getAsString());
            if (envio.getCpfdest().length() > 11) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                // Se for nulo, não faz nada;
            } else {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.cpfdest", jsonObject.get("cpfdest").toString())));
            }
        }

        try {
            envio.setRazaosocialdest(jsonObject.get("razaosocialdest").getAsString());
            if (envio.getRazaosocialdest().length() > 60) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.razaosocialdest")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.razaosocialdest", jsonObject.get("razaosocialdest").toString())));
        }

        try {
            envio.setFantasiadest(jsonObject.get("fantasiadest").getAsString());
            if (envio.getFantasiadest().length() > 40) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.fantasiadest")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.fantasiadest", jsonObject.get("fantasiadest").toString())));
        }

        try {
            envio.setEnderecodest(jsonObject.get("enderecodest").getAsString());
            if (envio.getEnderecodest().length() > 60) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.enderecodest")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.enderecodest", jsonObject.get("enderecodest").toString())));
        }

        try {
            if(!jsonObject.get("nrodest").getAsString().equals("")){
                envio.setNrodest(jsonObject.get("nrodest").getAsInt());
                if (envio.getNrodest()> 99999) {
                    throw new Exception();
                }
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.nrodest")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.nrodest", jsonObject.get("nrodest").toString())));
        }
        
        try {
            envio.setBairrodest(jsonObject.get("bairrodest").getAsString());
            if (envio.getBairrodest().length() > 25) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.bairrodest")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.bairrodest", jsonObject.get("bairrodest").toString())));
        }

        try {
            envio.setCidadedest(jsonObject.get("cidadedest").getAsString());
            if (envio.getCidadedest().length() > 25) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.cidadedest")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.cidadedest", jsonObject.get("cidadedest").toString())));
        }

        try {
            envio.setUfdest(jsonObject.get("ufdest").getAsString());
            if (envio.getUfdest().length() > 2) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.ufdest")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.ufdest", jsonObject.get("ufdest").toString())));
        }

        try {
            envio.setCodcidadedest(jsonObject.get("codcidadedest").getAsInt());
            if (envio.getCodcidadedest() > 9999999) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.codcidadedest")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.codcidadedest", jsonObject.get("codcidadedest").toString())));
        }

        try {
            envio.setCepdest(jsonObject.get("cepdest").getAsString());
            if (envio.getCepdest().length() > 8) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.cepdest")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.cepdest", jsonObject.get("cepdest").toString())));
        }

        try {
            envio.setTelefonedest(jsonObject.get("telefonedest").getAsString());
            if (envio.getTelefonedest().length() > 11) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.telefonedest")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.telefonedest", jsonObject.get("telefonedest").toString())));
        }

        try {
            envio.setEmaildest(jsonObject.get("emaildest").getAsString());
            if (envio.getEmaildest().length() > 80) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.emaildest")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.emaildest", jsonObject.get("emaildest").toString())));
        }

        try {
            envio.setIedest(jsonObject.get("iedest").getAsString());
            if (envio.getIedest().length() > 20) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.iedest")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.iedest", jsonObject.get("iedest").toString())));
        }

        try {
            envio.setObservacao(jsonObject.get("observacao").getAsString());
            if (envio.getObservacao().length() > 2000) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                // Se for nulo, não faz nada;
            } else {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.observacao", jsonObject.get("observacao").toString())));
            }
        }

        try {
            JsonArray itensJsonArray = jsonObject.getAsJsonArray("Itens");
            JsonObject itensJsonObject;
            Gson gsonNFe = new GsonBuilder()
                    .registerTypeAdapter(ItensModel.class, new ItensDeserializer())
                    .create();
            for (int i = 0; i < itensJsonArray.size(); i++) {
                itensJsonObject = itensJsonArray.get(i).getAsJsonObject();
                envio.getItens().add(gsonNFe.fromJson(itensJsonObject, ItensModel.class));
            }
        } catch (Exception e) {
            if (e instanceof NFeException) {
                NFeException.NFeError nFeError = ((NFeException) e).getNfeError();
                nFeError.setNfe(jsonObject);
                throw new NFeException(e.getMessage(), nFeError);
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens")));
        }

        return envio;
    }
}
