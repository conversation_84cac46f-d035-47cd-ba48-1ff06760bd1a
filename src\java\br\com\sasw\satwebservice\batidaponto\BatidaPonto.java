/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.batidaponto;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.RHPonto;
import SasDaos.FiliaisDao;
import SasDaos.FuncionDao;
import SasDaos.PessoaDao;
import SasDaos.RHPontoDao;
import br.com.sasw.pacotesuteis.sasbeans.TOKENS;
import br.com.sasw.pacotesuteis.sasdaos.TOKENSDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.Logos.getLogo;
import static br.com.sasw.satwebservice.Utilidades.obterParametros;
import br.com.sasw.satwebservice.messages.Messages;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
@Path("/ws-batida/")
public class BatidaPonto {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private String caminho;
    private final Messages messages;
    private final FiliaisDao filiaisDao;
    private final FuncionDao funcionDao;
    private final PessoaDao pessoaDao;
    private final RHPontoDao rhPontoDao;
    private final TOKENSDao tokenDao;

    /**
     * Creates a new instance of BatidaPonto
     */
    public BatidaPonto() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\BatidaPonto\\"
                + getDataAtual("SQL") + "\\erro.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        messages = new Messages();

        filiaisDao = new FiliaisDao();
        funcionDao = new FuncionDao();
        pessoaDao = new PessoaDao();
        rhPontoDao = new RHPontoDao();
        tokenDao = new TOKENSDao();
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/")
    public Response obterBatida(String param) throws UnsupportedEncodingException {

        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String matricula = (String) parametros.getOrDefault("matr", null);
        String token = (String) parametros.getOrDefault("token", null);
        String senha = (String) parametros.getOrDefault("pw", null);

        Persistencia persistencia, satellite;

        JSONObject retorno = new JSONObject();

        try {
            // Validando se a matrícula informada não está vazia
            if (matricula == null || matricula.equals("")) {
                throw new BatidaPontoException("MatriculaInvalida");
            }

            // Validando se a matrícula informada é numérica
            try {
                matricula = matricula.replace(".0", "");
                Long.parseLong(matricula);
            } catch (Exception validarMatricula) {
                throw new BatidaPontoException("MatriculaInvalida");
            }

            satellite = this.pool.getConexao("SATELLITE");
            TOKENS infoToken = this.tokenDao.obterToken(token, satellite);

//            messages.setIdioma(alguma consulta pra buscar o idioma depois de reconhecer o parâmetro)
            satellite.FechaConexao();

            ValidarToken.validarExistencia(infoToken);
            ValidarToken.validarValidade(infoToken);
            ValidarToken.validarMatricula(infoToken, matricula);

            persistencia = this.pool.getConexao(infoToken.getBancoDados());

            if (persistencia != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\BatidaPonto\\"
                        + getDataAtual("SQL") + "\\" + infoToken.getBancoDados() + "\\.txt";
            }

            // Validando senha
            if (!senha.equals("access-sup") || !infoToken.getChave().contains("SUPERVISOR")) {
                try {
                    if (!this.pessoaDao.validarSenha(matricula, senha, persistencia)) {
                        if (!senha.equals("access-sup")) {
                            throw new BatidaPontoException("SenhaInvalida");
                        } else {
                            retorno.put("resumo", "ND");
                            return Response
                                    .status(Response.Status.OK)
                                    .type("application/json")
                                    .entity(retorno.toString())
                                    .build();
                        }
                    }
                } catch (BatidaPontoException e) {
                    if (!senha.equals("access-sup")) {
                        throw new BatidaPontoException("SenhaInvalida");
                    } else {
                        retorno.put("resumo", "ND");
                        return Response
                                .status(Response.Status.OK)
                                .type("application/json")
                                .entity(retorno.toString())
                                .build();
                    }
                } catch (Exception e) {
                    if (!senha.equals("access-sup")) {
                        throw new BatidaPontoException("SenhaInvalida");
                    } else {
                        retorno.put("resumo", "ND");
                        return Response
                                .status(Response.Status.OK)
                                .type("application/json")
                                .entity(retorno.toString())
                                .build();
                    }
                }
            }

            // Strings e formatação da página do relatório
            JSONObject relatorioJO = new JSONObject();
            relatorioJO.put("titulo", this.messages.getMessage("RelatorioBatidas"));
            relatorioJO.put("data", this.messages.getData(infoToken.getData(), "yyyyMMdd"));
            relatorioJO.put("texto_data", this.messages.getMessage("Data"));
            relatorioJO.put("entrada", this.messages.getMessage("Entrada"));
            relatorioJO.put("saida", this.messages.getMessage("Saida"));
            relatorioJO.put("hora", this.messages.getMessage("Hora"));
            relatorioJO.put("local", this.messages.getMessage("Local"));
            relatorioJO.put("distancia", this.messages.getMessage("Distancia"));
            relatorioJO.put("cliquefoto", this.messages.getMessage("CliqueVisualizarFoto"));
            relatorioJO.put("carga", this.messages.getMessage("CargaHoraria"));
            relatorioJO.put("intervalo", this.messages.getMessage("Intervalo"));
            relatorioJO.put("irregular", this.messages.getMessage("Irregular"));
            retorno.put("relatorio", relatorioJO);

            // Buscando informações do funcionário
            Funcion funcion = this.funcionDao.buscarFuncion(matricula, persistencia);

            JSONObject funcionJO = new JSONObject();
            funcionJO.put("nome", funcion.getNome());
            funcionJO.put("matr", funcion.getMatr().toBigInteger().toString());
            funcionJO.put("contrato", funcion.getContrato());
            retorno.put("funcion", funcionJO);

            Filiais filial = this.filiaisDao.buscarFilial(funcion.getCodFil().toString(), persistencia);
            JSONObject filialJO = new JSONObject();
            filialJO.put("razaosocial", filial.getRazaoSocial());
            filialJO.put("fone", messages.getTelefone(filial.getFone()));
            filialJO.put("endereco", filial.getEndereco());
            filialJO.put("logo", getLogo(infoToken.getBancoDados(), filial.getCodFil().toBigInteger().toString()));
            retorno.put("filial", filialJO);

            String hora1 = "", hora2 = "", hora3 = "", hora4 = "";
            LocalDateTime batida1 = null, batida2 = null, batida3 = null, batida4 = null;
            List<RHPonto> batidas = this.rhPontoDao.listaTodosPontos(infoToken.getData(), funcion.getMatr().toString(),
                    "", funcion.getCodFil().toString(), persistencia);
            JSONArray batidasJA = new JSONArray();
            JSONObject batidaJO;
            for (int i = batidas.size() - 1; i >= 0; i--) {
                switch (batidas.get(i).getBatida().intValue()) {
                    case 1:
                        batida1 = LocalDateTime.parse(batidas.get(i).getDtBatida() + batidas.get(i).getHora(), DateTimeFormatter.ofPattern("yyyyMMddHH:mm"));
                        hora1 = batidas.get(i).getHora();
                        break;
                    case 2:
                        batida2 = LocalDateTime.parse(batidas.get(i).getDtBatida() + batidas.get(i).getHora(), DateTimeFormatter.ofPattern("yyyyMMddHH:mm"));
                        hora2 = batidas.get(i).getHora();
                        break;
                    case 3:
                        batida3 = LocalDateTime.parse(batidas.get(i).getDtBatida() + batidas.get(i).getHora(), DateTimeFormatter.ofPattern("yyyyMMddHH:mm"));
                        hora3 = batidas.get(i).getHora();
                        break;
                    case 4:
                        batida4 = LocalDateTime.parse(batidas.get(i).getDtBatida() + batidas.get(i).getHora(), DateTimeFormatter.ofPattern("yyyyMMddHH:mm"));
                        hora4 = batidas.get(i).getHora();
                        break;
                }

                batidaJO = new JSONObject();
                batidaJO.put("batida", batidas.get(i).getBatida());
                batidaJO.put("distancia", batidas.get(i).getDistancia());
                batidaJO.put("foto", batidas.get(i).getFoto());
                batidaJO.put("funcionario", batidas.get(i).getFuncionario());
                batidaJO.put("hora", this.messages.getHora(batidas.get(i).getHora(), "HH:mm"));
                batidaJO.put("local", batidas.get(i).getLocal());
                batidaJO.put("latitude", batidas.get(i).getLatitude());
                batidaJO.put("longitude", batidas.get(i).getLongitude());
                batidasJA.put(batidaJO);
            }
            retorno.put("batidas", batidasJA);

            String intervalo = "", carga = "", irregular = "";

            if (batida1 != null
                    && batida2 != null
                    && batida3 != null
                    && batida4 != null) {
                Duration carga1 = Duration.between(batida3, batida4);
                Duration carga2 = Duration.between(batida1, batida2);
                long cargaTotal = carga1.toMillis() + carga2.toMillis();

                carga = String.format("%02d:%02d",
                        TimeUnit.MILLISECONDS.toHours(cargaTotal),
                        TimeUnit.MILLISECONDS.toMinutes(cargaTotal)
                        - TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(cargaTotal)));

                long intervaloTotal = Duration.between(batida2, batida3).toMillis();

                intervalo = String.format("%02d:%02d",
                        TimeUnit.MILLISECONDS.toHours(intervaloTotal),
                        TimeUnit.MILLISECONDS.toMinutes(intervaloTotal)
                        - TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(intervaloTotal)));
            } else if (batida1 != null
                    && batida2 != null
                    && batida3 != null) {
                long cargaTotal = Duration.between(batida1, batida2).toMillis();

                carga = String.format("%02d:%02d",
                        TimeUnit.MILLISECONDS.toHours(cargaTotal),
                        TimeUnit.MILLISECONDS.toMinutes(cargaTotal)
                        - TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(cargaTotal)));

                long intervaloTotal = Duration.between(batida2, batida3).toMillis();

                intervalo = String.format("%02d:%02d",
                        TimeUnit.MILLISECONDS.toHours(intervaloTotal),
                        TimeUnit.MILLISECONDS.toMinutes(intervaloTotal)
                        - TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(intervaloTotal)));

                irregular = "1";
            } else if (batida1 != null
                    && batida2 != null) {
                long cargaTotal = Duration.between(batida1, batida2).toMillis();

                carga = String.format("%02d:%02d",
                        TimeUnit.MILLISECONDS.toHours(cargaTotal),
                        TimeUnit.MILLISECONDS.toMinutes(cargaTotal)
                        - TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(cargaTotal)));
            } else if (batida1 != null) {
                irregular = "1";
            }

            JSONObject resumoJO = new JSONObject();
            resumoJO.put("carga", carga);
            resumoJO.put("intervalo", intervalo);
            resumoJO.put("irregular", irregular);

            resumoJO.put("hora1", this.messages.getHora(hora1, "HH:mm"));
            resumoJO.put("hora2", this.messages.getHora(hora2, "HH:mm"));
            resumoJO.put("hora3", this.messages.getHora(hora3, "HH:mm"));
            resumoJO.put("hora4", this.messages.getHora(hora4, "HH:mm"));

            retorno.put("resumo", resumoJO);

            this.logerro.Grava(retorno.toString(2), this.caminho);
        } catch (BatidaPontoException e) {
            retorno = new JSONObject();
            retorno.put("erro", this.messages.getMessage(e.getMessage()));
            this.logerro.Grava(this.messages.getMessage(e.getMessage()), this.caminho);
        } catch (Exception e) {
            retorno = new JSONObject();
            retorno.put("erro", this.messages.getMessage("OcorreuUmErro"));
            this.logerro.Grava(e.getMessage(), this.caminho);
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(retorno.toString())
                    .build();
        }
    }
}
