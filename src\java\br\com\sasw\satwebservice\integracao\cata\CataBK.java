/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.integracao.cata;

import Arquivo.ArquivoLog;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.UriInfo;
import org.json.JSONObject;
import org.json.XML;

/**
 *
 * <AUTHOR>
 */

@Path("ws-cata-bk")
public class CataBK {
    
    private final ArquivoLog logerro;
    private final String caminho;
    
    @Context
    private UriInfo context;
    
    public CataBK(){
        //localhost:8080/SatWebService/api/ws-cata?username=corpvs.integration&password=Cata1234&machineId=CCO000532&data=2018-11-01
        logerro = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Integracao" +
                "\\Cata\\" + DataAtual.getDataAtual("SQL") + ".txt";
    }
    
    /**
     * Retrieves representation of an instance of br.com.sasw.integracao.cata.Cata
     * @param username
     * @param password
     * @param machineId
     * @param dataInicio
     * @param horaInicio
     * @param horaFim
     * @param dataFim
     * @return an instance of java.lang.String
     */
    @GET
    @Produces(MediaType.APPLICATION_XML)
    public String getXml(@QueryParam("username")String username, @QueryParam("password")String password,
            @QueryParam("machineId")String machineId, 
            @QueryParam("dataInicio")String dataInicio, @QueryParam("horaInicio")String horaInicio,
            @QueryParam("dataFim")String dataFim, @QueryParam("horaFim")String horaFim) {
        
        this.logerro.Grava(gerarLogURL(username, password, machineId, dataInicio, horaInicio, dataFim, horaFim), caminho);
        String retorno = null;
        String authorization = identificacao(username,password);
        if(authorization != null) {
            retorno = movimentacoes(authorization, machineId, dataInicio, horaInicio, dataFim, horaFim);
        }
        if(retorno == null) retorno = "{\"transactions\":\"\"}";
        
        JSONObject json = new JSONObject(retorno);
        this.logerro.Grava(XML.toString(json,"resp"), caminho);
        return XML.toString(json,"resp");
//        return retorno;
    }
    
    private String gerarLogURL(String username, String password, String machineId, String dataInicio, String horaInicio, String dataFim, String horaFim){
        StringBuilder log = new StringBuilder();
        log.append("username:   ").append(username).append("\n");
        log.append("password:   ").append(password).append("\n");
        log.append("machineId:  ").append(machineId).append("\n");
        log.append("dataInicio: ").append(dataInicio).append("\n");
        log.append("horaInicio: ").append(horaInicio).append("\n");
        log.append("dataFim:    ").append(dataFim).append("\n");
        log.append("horaFim:    ").append(horaFim);
        return log.toString();
    }
    
    private String movimentacoes(String tokenId, String machineId, String dataInicio, String horaInicio, String dataFim, String horaFim){
        String transactions = null;
        try{
            StringBuilder url = new StringBuilder("https://clientapi.catamoeda.com.br/api/v1/client/Transaction?");
//            if(machineId != null) url.append("Filter.ByMachineId=").append(machineId).append("&");
            if(machineId != null) url.append("Filter.ByMachineSerial=").append(machineId).append("&");
            if(dataInicio != null){
                url.append("Filter.ByDateRange.Start=").append(dataInicio);
                if(horaInicio != null) url.append("T").append(horaInicio);
                url.append("&");
            }
            if(dataFim != null){
                url.append("Filter.ByDateRange.End=").append(dataFim);
                if(horaFim != null) url.append("T").append(horaFim);
                url.append("&");
            }
            
            URL obj = new URL(url.toString());
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();
            
            con.setRequestProperty("Authorization", tokenId);
            con.setRequestProperty("Method", "GET");


            int responseCode = con.getResponseCode();
            
            if (responseCode     == HttpURLConnection.HTTP_OK) { //success
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                }
                in.close();

                transactions = response.toString();
            } else { 
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getErrorStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                }
                in.close();

                // print result
                this.logerro.Grava(response.toString(), caminho);
            }
        } catch(IOException e){
            this.logerro.Grava(e.getMessage(), caminho);
        }
        return transactions;
    }
    
    private String identificacao(String username, String password){
        String tokenId = null;
        try{
            URL obj = new URL("https://identification.catamoeda.com.br/api/IdentificationService/login");
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();
            
            con.setRequestProperty("Content-Type", "application/json;");
            con.setRequestProperty("Accept", "application/json");
            con.setRequestProperty("Method", "POST");
            
            // For POST only - START
            con.setDoOutput(true);
            OutputStream os = con.getOutputStream();
            os.write(("{\"username\": \""+username+"\",\"password\": \""+password+"\"}").getBytes("UTF-8"));
            os.flush();
            os.close();
            // For POST only - END

            int responseCode = con.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) { //success
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                }
                in.close();

                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject().getAsJsonObject("token");
                tokenId = jsonObject.get("id").getAsString();
                
            } else { 
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getErrorStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                        response.append(inputLine);
                }
                in.close();

                // print result
                this.logerro.Grava(response.toString(), caminho);
            }
        } catch(JsonSyntaxException | IOException e){
            this.logerro.Grava(e.getMessage(), caminho);
        }
        return tokenId;
    }
    
    /**
     * PUT method for updating or creating an instance of Cata
     * @param content representation for the resource
     */
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    public void putJson(String content) {
    }
}
