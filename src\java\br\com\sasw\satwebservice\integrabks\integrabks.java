/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.integrabks;

import Arquivo.ArquivoLog;
import Dados.Consulta;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasDaos.FiliaisDao;
import br.com.sasw.pacotesuteis.sasbeans.TOKENS;
import br.com.sasw.pacotesuteis.sasdaos.TOKENSDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.satwebservice.executasvc.ValidarToken;
import br.com.sasw.satwebservice.messages.Messages;
import java.sql.ResultSet;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.json.JSONArray;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
@Path("/integrabks/")
public class integrabks {

    private final ArquivoLog logerro;
    private ArquivoLog logexecucao;
    private ArquivoLog arquivohtml;
    private final SasPoolPersistencia pool;
    private String caminho;
    private String caminhoweb;
    private final Messages messages;
    private final FiliaisDao filiaisDao;
    private final TOKENSDao tokenDao;
    private ResultSet vResultadoConsulta;
    private String vRetorno;
    private String vRetornoPessoa;
    private Object vStrInvoice;

    public integrabks() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ConsultaCI"
                + "\\erro.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        messages = new Messages();

        filiaisDao = new FiliaisDao();
        tokenDao = new TOKENSDao();
    }

    @GET
    @Path("/integraCustomer")
    @Produces(MediaType.APPLICATION_JSON)
    public Response integraCustomer(@QueryParam("token") String vToken, @QueryParam("empresa") String vEmpresa, @QueryParam("dataini") String vDataIni, @QueryParam("datafim") String vDataFim) {
        logexecucao = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ExecutaSvc\\integrabks\\"
                + "\\integraCustomer.txt";
        // TODO ler body em json
        //String param;
        //Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        //String vToken = (String) parametros.getOrDefault("token", null);
        //String vChaveGTVE = (String) parametros.getOrDefault("chavegtve", null);
        if (vToken.equals("")) {
            vToken = "C0574EE251E7EB197650B332CEE4750C";
        }
        logexecucao.Grava("parametros: token=" + vToken + ";empresa=" + vEmpresa
                + "parametros: dataini=" + vDataIni + ";datafim=" + vDataFim, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnEnvio = new JSONObject();
        JSONArray vjnArray = new JSONArray();
        String vStr = "";

        try {
            //Validando se a matrícula informada não está vazia
            if (vEmpresa == null || vEmpresa.equals("")) {
                throw new integrabksException("Empresa Inexistente.");
            }

            logexecucao.Grava("Inicia Conexao Central", caminho);

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            logexecucao.Grava("Passou Token", caminho);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "", vSQLExec = "", vBancoDados = "";
            String vCodCofre = "";
            String vData = "";
            String vDiaSem = "";
            String vFeriado = "";
            String vVlrRecAntesCorte = "";
            String vHrRecAntesCorte = "";
            String vVlrRecAposCorte = "";
            String vHrRecAposCorte = "";
            String vCredD0Rec = "";
            String vCredD0RecAposCorte = "";
            String vCredD1Rec = "";
            String vCredD0 = "";
            String vCredD1 = "";
            String vCredProxDU = "";
            String vCredDiaAnt = "";
            String vCredDiaD0Rec = "";
            String vCredCorte = "";
            String vVlrTotalCred = "";
            String vVlrTotalRec = "";
            String vSaldoFisTotal = "";
            String vSaldoFisCred = "";
            String vSaldoFisCst = "";
            String vDataStr = "";
            String vDiaSemana = "";
            String vCNPJ = "";
            String vRazaoSocial = "";
            String vHrUltAtua = "";
            Consulta qTmpX;
            vBancoDados = qToken.getBancoDados();

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\integrabks\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }
            String vSQLHR;
            vSQLHR = "Select top 1 TesCofresMov.Hora from TesCofresMov "
                    + " where TesCofresMov.CodCofre = Clientes.CodCofre "
                    + "   and TesCofresMov.Data     <=  getDate() "
                    + "   and TesCofresMov.CodCliente <> '0'"
                    + " order by TesCofresMov.Data Desc ";

            vSQL = "Select TesCofresRes.*,  \n"
                    + vSQLHR + " HrUltAtua, "
                    + "Case    when DiaSem = 1 then 'Domingo  '   \n"
                    + "        when DiaSem = 2 then 'Segunda  '   \n"
                    + "        when DiaSem = 3 then 'Terça    '   \n"
                    + "        when DiaSem = 4 then 'Quarta   '   \n"
                    + "        when DiaSem = 5 then 'Quinta   '   \n"
                    + "        when DiaSem = 6 then 'Sexta    '   \n"
                    + "        when DiaSem = 7 then 'Sabado   ' else '' end DiaSemana, Clientes.Nome, Clientes.CGC CNPJ from TesCofresRes  \n"
                    + "Left Join Clientes on  Clientes.CodCofre = TesCofresRes.CodCofre\n"
                    + "where Data = '10/19/2023'   \n"
                    + "  and TesCofresRes.CodCofre in (Select Clientes.CodCofre  From PessoaCliAut\n"
                    + "           Left Join Clientes  on PessoaCliAut.CodCli = Clientes.Codigo\n"
                    + "                              and PessoaCliAut.CodFil = Clientes.CodFil\n"
                    + "           Left Join Pessoa on Pessoa.Codigo = PessoaCliAut.Codigo\n"
                    + "           Where Clientes.CodFil >= 1    \n"
                    + "             and  Pessoa.Email =  '<EMAIL>')\n"
                    + "             and TesCofresRes.Data >= '" + vDataIni + "'"
                    + "             and TesCofresRes.Data <= '" + vDataFim + "'"
                    + "order by TesCofresRes.Data Desc ";
            try {
                //    qTmpX = new Consulta(vSQL, dbpadrao);
                //    qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                throw new Exception("integrabks - " + e.getMessage() + "\r\n"
                        + vSQL);
            }

            int vConta = 0;

            //while (qTmpX.Proximo()) {
            vConta++;
            vjnEnvio = new JSONObject();
            vStr =  "{"
                    + "	["
                    + "	   {"
                    + "	      \"CUSTOMER_NAME\": \"PIZZARIA E PARRILLA VERDEMAR LTDA\","
                    + "	      \"CUST_TAX_REFERENCE\": \"*********\","
                    + "	      \"INSERT_UPDATE_FLAG\": \"I\","
                    + "       \"ORIG_SYSTEM_CUSTOMER_REF\": \"*********\","
                    + "	      \"GLOBAL_ATTRIBUTE1\": \"55\","
                    + "	      \"GLOBAL_ATTRIBUTE2\": \"2\","
                    + "	      \"GLOBAL_ATTRIBUTE3\": \"*********\","
                    + "	      \"GLOBAL_ATTRIBUTE4\": \"0011\","
                    + "	      \"GLOBAL_ATTRIBUTE5\": \"99\","
                    + "	      \"GLOBAL_ATTRIBUTE6\": \"47350001070\","
                    + "	      \"GLOBAL_ATTRIBUTE7\": \"ISENTO\","
                    + "	      \"GLOBAL_ATTRIBUTE8\": \"CONTRIBUINTE\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE2\": \"2\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE3\": \"*********\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE4\": \"0011\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE5\": \"99\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE6\": \"47350001070\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE7\": \"ISENTO\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE8\": \"CONTRIBUINTE\","
                    + "	      \"OPERATING_UNIT\": \"Transpar Operating Unit\","
                    + "	      \"ORIG_SYSTEM_ADDRESS_REF\": \"*********001199\","
                    + "	      \"ADDRESS1\": \"R GUAICUI\","
                    + "	      \"ADDRESS2\": \"700\","
                    + "	      \"ADDRESS3\": \"\","
                    + "	      \"ADDRESS4\": \"LUXEMBURGO\","
                    + "	      \"CITY\": \"BELO HORIZONTE\","
                    + "	      \"STATE\": \"MG\","
                    + "	      \"POSTAL_CODE\": \"30380342\","
                    + "	      \"COUNTRY\": \"BR\","
                    + "	      \"BILL_TO_ORIG_ADDRESS_REF\": \"\","
                    + "	      \"SITE_USE_CODE\": \"BILL_TO\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE13\": \"1\","
                    + "	      \"CUSTOMER_STATUS\": \"A\","
                    + "	      \"CUSTOMER_TYPE\": \"R\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE12\": \"N\","
                    + "	      \"GLOBAL_ATTRIBUTE_CATEGORY\": \"JL.BR.ARXCUDCI.CUSTOMERS\","
                    + "	      \"PRIMARY_SITE_USE_FLAG\": \"N\","
                    + "	      \"CREDIT_HOLD\": \"N\","
                    + "	      \"CUSTOMER_PROFILE_CLASS_NAME\": \"DEFAULT\","
                    + "	      \"GLOBAL_ADD_ATTRIBUTE_CATEGORY\": \"JL.BR.ARXCUDCI.Additional\","
                    + "	      },"
                    + "	      {"
                    + "	      \"CUSTOMER_NAME\": \"AUTO POSTO DA TORRE LTDA\","
                    + "	      \"CUST_TAX_REFERENCE\": \"*********\","
                    + "	      \"INSERT_UPDATE_FLAG\": \"U\","
                    + "	      \"ORIG_SYSTEM_CUSTOMER_REF\": \"*********\","
                    + "	      \"GLOBAL_ATTRIBUTE1\": \"55\","
                    + "	      \"GLOBAL_ATTRIBUTE2\": \"2\","
                    + "	      \"GLOBAL_ATTRIBUTE3\": \"*********\","
                    + "	      \"GLOBAL_ATTRIBUTE4\": \"0001\","
                    + "	      \"GLOBAL_ATTRIBUTE5\": \"08\","
                    + "	      \"GLOBAL_ATTRIBUTE6\": \"244166458118\","
                    + "	      \"GLOBAL_ATTRIBUTE7\": \"757527\","
                    + "	      \"GLOBAL_ATTRIBUTE8\": \"CONTRIBUINTE\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE2\": \"2\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE3\": \"*********\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE4\": \"0001\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE5\": \"08\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE6\": \"244166458118\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE7\": \"757527\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE8\": \"CONTRIBUINTE\","
                    + "	      \"OPERATING_UNIT\": \"BGS Operating Unit\","
                    + "	      \"ORIG_SYSTEM_ADDRESS_REF\": \"*********000108\","
                    + "	      \"ADDRESS1\": \"AV ANDRADE NEVES\","
                    + "	      \"ADDRESS2\": \"2321\","
                    + "	      \"ADDRESS3\": \"\","
                    + "	      \"ADDRESS4\": \"CASTELO\","
                    + "	      \"CITY\": \"CAMPINAS\","
                    + "	      \"STATE\": \"SP\","
                    + "	      \"POSTAL_CODE\": \"13070001\","
                    + "	      \"COUNTRY\": \"BR\","
                    + "	      \"BILL_TO_ORIG_ADDRESS_REF\": \"\","
                    + "	      \"SITE_USE_CODE\": \"BILL_TO\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE13\": \"1\","
                    + "	      \"CUSTOMER_STATUS\": \"A\","
                    + "	      \"CUSTOMER_TYPE\": \"R\","
                    + "	      \"GLOBAL_ADDRESS_ATTRIBUTE12\": \"N\","
                    + "	      \"GLOBAL_ATTRIBUTE_CATEGORY\": \"JL.BR.ARXCUDCI.CUSTOMERS\","
                    + "	      \"PRIMARY_SITE_USE_FLAG\": \"N\","
                    + "	      \"CREDIT_HOLD\": \"N\","
                    + "	      \"CUSTOMER_PROFILE_CLASS_NAME\": \"DEFAULT\","
                    + "	      \"GLOBAL_ADD_ATTRIBUTE_CATEGORY\": \"JL.BR.ARXCUDCI.Additional\","
                    + "	      }"
                    + "	]"
                    + "}";

            /*
                    + "	      {"
                    + "			'CUSTOMER_NAME': 'BANCO CITIBANK S A',\n"
                    + "			'CUST_TAX_REFERENCE': '*********',\n"
                    + "			'INSERT_UPDATE_FLAG': 'U',\n"
                    + "			'ORIG_SYSTEM_CUSTOMER_REF': '*********',\n"
                    + "			'GLOBAL_ATTRIBUTE1': '55',\n"
                    + "			'GLOBAL_ATTRIBUTE2': '2',\n"
                    + "			'GLOBAL_ATTRIBUTE3': '*********',\n"
                    + "			'GLOBAL_ATTRIBUTE4': '0001',\n"
                    + "			'GLOBAL_ATTRIBUTE5': '80',\n"
                    + "			'GLOBAL_ATTRIBUTE6': '',\n"
                    + "			'GLOBAL_ATTRIBUTE7': '********',\n"
                    + "			'GLOBAL_ATTRIBUTE8': 'NAO CONTRIBUINTE',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE2': '2',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE3': '*********',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE4': '0001',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE5': '80',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE6': '',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE7': '********',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE8': 'NAO CONTRIBUINTE',\n"
                    + "			'OPERATING_UNIT': 'BGS Operating Unit',\n"
                    + "			'ORIG_SYSTEM_ADDRESS_REF': '*********000180',\n"
                    + "			'ADDRESS1': 'AV PAULISTA',\n"
                    + "			'ADDRESS2': '1111',\n"
                    + "			'ADDRESS3': '2 ANDAR PARTE',\n"
                    + "			'ADDRESS4': 'BELA VISTA',\n"
                    + "			'CITY': 'SAO PAULO',\n"
                    + "			'STATE': 'SP',\n"
                    + "			'POSTAL_CODE': '01311920',\n"
                    + "			'COUNTRY': 'BR',\n"
                    + "			'BILL_TO_ORIG_ADDRESS_REF': '',\n"
                    + "			'SITE_USE_CODE': 'BILL_TO',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE13': '9',\n"
                    + "			'CUSTOMER_STATUS': 'A',\n"
                    + "			'CUSTOMER_TYPE': 'R',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE12': 'N',\n"
                    + "			'GLOBAL_ATTRIBUTE_CATEGORY': 'JL.BR.ARXCUDCI.CUSTOMERS',\n"
                    + "			'PRIMARY_SITE_USE_FLAG': 'N',\n"
                    + "			'CREDIT_HOLD': 'N',\n"
                    + "			'CUSTOMER_PROFILE_CLASS_NAME': 'DEFAULT',\n"
                    + "			'GLOBAL_ADD_ATTRIBUTE_CATEGORY': 'JL.BR.ARXCUDCI.Additional',\n"
                    + "			'STG_SOURCE_LEGACY': 'Portal',\n"
                    + "			'STG_ATTRIBUTE1': 'BR'\n"
                    + "		},\n"
                    + "		{\n"
                    + "			'CUSTOMER_NAME': 'ITAU UNIBANCO SA',\n"
                    + "			'CUST_TAX_REFERENCE': '*********',\n"
                    + "			'INSERT_UPDATE_FLAG': 'U',\n"
                    + "			'ORIG_SYSTEM_CUSTOMER_REF': '*********',\n"
                    + "			'GLOBAL_ATTRIBUTE1': '55',\n"
                    + "			'GLOBAL_ATTRIBUTE2': '2',\n"
                    + "			'GLOBAL_ATTRIBUTE3': '*********',\n"
                    + "			'GLOBAL_ATTRIBUTE4': '0332',\n"
                    + "			'GLOBAL_ATTRIBUTE5': '90',\n"
                    + "			'GLOBAL_ATTRIBUTE6': '',\n"
                    + "			'GLOBAL_ATTRIBUTE7': '2163004',\n"
                    + "			'GLOBAL_ATTRIBUTE8': 'NAO CONTRIBUINTE',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE2': '2',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE3': '*********',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE4': '0332',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE5': '90',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE6': '',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE7': '2163004',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE8': 'NAO CONTRIBUINTE',\n"
                    + "			'OPERATING_UNIT': 'Transpar Operating Unit',\n"
                    + "			'ORIG_SYSTEM_ADDRESS_REF': '*********033290',\n"
                    + "			'ADDRESS1': 'AV BARAO DO RIO BRANCO',\n"
                    + "			'ADDRESS2': '2334',\n"
                    + "			'ADDRESS3': '',\n"
                    + "			'ADDRESS4': 'CENTRO',\n"
                    + "			'CITY': 'JUIZ DE FORA',\n"
                    + "			'STATE': 'MG',\n"
                    + "			'POSTAL_CODE': '36016310',\n"
                    + "			'COUNTRY': 'BR',\n"
                    + "			'BILL_TO_ORIG_ADDRESS_REF': '*********033290',\n"
                    + "			'SITE_USE_CODE': 'SHIP_TO',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE13': '9',\n"
                    + "			'CUSTOMER_STATUS': 'A',\n"
                    + "			'CUSTOMER_TYPE': 'R',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE12': 'N',\n"
                    + "			'GLOBAL_ATTRIBUTE_CATEGORY': 'JL.BR.ARXCUDCI.CUSTOMERS',\n"
                    + "			'PRIMARY_SITE_USE_FLAG': 'N',\n"
                    + "			'CREDIT_HOLD': 'N',\n"
                    + "			'CUSTOMER_PROFILE_CLASS_NAME': 'DEFAULT',\n"
                    + "			'GLOBAL_ADD_ATTRIBUTE_CATEGORY': 'JL.BR.ARXCUDCI.Additional',\n"
                    + "			'STG_SOURCE_LEGACY': 'Portal',\n"
                    + "			'STG_ATTRIBUTE1': 'BR'\n"
                    + "\n"
                    + "		},\n"
                    + "		{\n"
                    + "			'CUSTOMER_NAME': 'COOPERATIVA CENTRAL DE CREDITO AILOS',\n"
                    + "			'CUST_TAX_REFERENCE': '*********',\n"
                    + "			'INSERT_UPDATE_FLAG': 'U',\n"
                    + "			'ORIG_SYSTEM_CUSTOMER_REF': '*********',\n"
                    + "			'GLOBAL_ATTRIBUTE1': '55',\n"
                    + "			'GLOBAL_ATTRIBUTE2': '2',\n"
                    + "			'GLOBAL_ATTRIBUTE3': '*********',\n"
                    + "			'GLOBAL_ATTRIBUTE4': '0001',\n"
                    + "			'GLOBAL_ATTRIBUTE5': '29',\n"
                    + "			'GLOBAL_ATTRIBUTE6': '',\n"
                    + "			'GLOBAL_ATTRIBUTE7': '73883',\n"
                    + "			'GLOBAL_ATTRIBUTE8': 'NAO CONTRIBUINTE',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE2': '2',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE3': '*********',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE4': '0001',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE5': '29',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE6': '',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE7': '73883',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE8': 'NAO CONTRIBUINTE',\n"
                    + "			'OPERATING_UNIT': 'BSL Operating Unit',\n"
                    + "			'ORIG_SYSTEM_ADDRESS_REF': '*********000129',\n"
                    + "			'ADDRESS1': 'R GENERAL OSORIO',\n"
                    + "			'ADDRESS2': '1180',\n"
                    + "			'ADDRESS3': '',\n"
                    + "			'ADDRESS4': 'VELHA',\n"
                    + "			'CITY': 'BLUMENAU',\n"
                    + "			'STATE': 'SC',\n"
                    + "			'POSTAL_CODE': '89041002',\n"
                    + "			'COUNTRY': 'BR',\n"
                    + "			'BILL_TO_ORIG_ADDRESS_REF': '*********000129',\n"
                    + "			'SITE_USE_CODE': 'SHIP_TO',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE13': '9',\n"
                    + "			'CUSTOMER_STATUS': 'A',\n"
                    + "			'CUSTOMER_TYPE': 'R',\n"
                    + "			'GLOBAL_ADDRESS_ATTRIBUTE12': 'N',\n"
                    + "			'GLOBAL_ATTRIBUTE_CATEGORY': 'JL.BR.ARXCUDCI.CUSTOMERS',\n"
                    + "			'PRIMARY_SITE_USE_FLAG': 'N',\n"
                    + "			'CREDIT_HOLD': 'N',\n"
                    + "			'CUSTOMER_PROFILE_CLASS_NAME': 'DEFAULT',\n"
                    + "			'GLOBAL_ADD_ATTRIBUTE_CATEGORY': 'JL.BR.ARXCUDCI.Additional',\n"
                    + "			'STG_SOURCE_LEGACY': 'Portal',\n"
                    + "			'STG_ATTRIBUTE1': 'BR'\n"
                    + "		}\n"
                    + "	]\n"
                    + "}";
            */
            /* Tratamento antigo de aspa
            char vAspa = '"';
            vStr = vStr.replace("\n", "");
            vStr = vStr.replace("\t", "");
            vStr = vStr.replace("\\", "");            
            //vStr = vStr.replace("'", Character.toString(vAspa));  
            */
            
            /*
            vjnEnvio.put("RESPOSTA", vStr);

            //vjnArray.put(vjnEnvio);
            //}
            if (vConta == 0) {
                vjnEnvio.put("resposta", "NAO HA DADOS");
                vjnArray.put(vjnEnvio);
            }
            try {
                vjnRetorno.put("Retorno", vjnArray);
            } catch (Exception e) {
                logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                throw new Exception("Resposta - " + e.getMessage() + "\r\n"
                );
            }
            */
        } finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vStr)   // era isso: vjnRetorno.toString()
                //    .entity(vjnRetorno.toString())
                    .build();
        }
    }

    @GET
    @Path("/integraInvoice")
    @Produces(MediaType.APPLICATION_JSON)
    
    public Response integraInvoice(@QueryParam("token") String vToken, @QueryParam("empresa") String vEmpresa, @QueryParam("dataini") String vDataIni, @QueryParam("datafim") String vDataFim) throws Exception {
        logexecucao = new ArquivoLog();
                                caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Integracao\\integrabks\\"
                + "\\integraInvoice.txt";
        // TODO ler body em json
        //String param;
        //Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        //String vToken = (String) parametros.getOrDefault("token", null);
        //String vChaveGTVE = (String) parametros.getOrDefault("chavegtve", null);
        if (vToken.equals("")) {
            vToken = "C0574EE251E7EB197650B332CEE4750C";
        }
        logexecucao.Grava("parametros: token=" + vToken + ";empresa=" + vEmpresa
                + "parametros: dataini=" + vDataIni + ";datafim=" + vDataFim, caminho);

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();
        JSONObject vjnEnvio = new JSONObject();
        JSONArray vjnArray = new JSONArray();
        String vStrInvoice = "";

        try {
            //Validando se a matrícula informada não está vazia
            if (vEmpresa == null || vEmpresa.equals("")) {
                throw new integrabksException("Empresa Inexistente.");
            }

            logexecucao.Grava("Inicia Conexao Central", caminho);

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENS qToken = this.tokenDao.obterToken(vToken, dbsatellite);

            logexecucao.Grava("Passou Token", caminho);

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());
            String vSQL = "", vSQLExec = "", vBancoDados = "";
            String vCodCofre = "";
            String vData = "";
            String vDiaSem = "";
            String vFeriado = "";
            String vVlrRecAntesCorte = "";
            String vHrRecAntesCorte = "";
            String vVlrRecAposCorte = "";
            String vHrRecAposCorte = "";
            String vCredD0Rec = "";
            String vCredD0RecAposCorte = "";
            String vCredD1Rec = "";
            String vCredD0 = "";
            String vCredD1 = "";
            String vCredProxDU = "";
            String vCredDiaAnt = "";
            String vCredDiaD0Rec = "";
            String vCredCorte = "";
            String vVlrTotalCred = "";
            String vVlrTotalRec = "";
            String vSaldoFisTotal = "";
            String vSaldoFisCred = "";
            String vSaldoFisCst = "";
            String vDataStr = "";
            String vDiaSemana = "";
            String vCNPJ = "";
            String vRazaoSocial = "";
            String vHrUltAtua = "";
            Consulta qTmpX;
            vBancoDados = qToken.getBancoDados();

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\integrabks\\"
                        + getDataAtual("SQL") + "\\" + vBancoDados + "\\.txt";
            }
            String vSQLHR;
            vSQLHR = "Select top 1 TesCofresMov.Hora from TesCofresMov "
                    + " where TesCofresMov.CodCofre = Clientes.CodCofre "
                    + "   and TesCofresMov.Data     <=  getDate() "
                    + "   and TesCofresMov.CodCliente <> '0'"
                    + " order by TesCofresMov.Data Desc ";

            vSQL = "Select TesCofresRes.*,  \n"
                    + vSQLHR + " HrUltAtua, "
                    + "Case    when DiaSem = 1 then 'Domingo  '   \n"
                    + "        when DiaSem = 2 then 'Segunda  '   \n"
                    + "        when DiaSem = 3 then 'Terça    '   \n"
                    + "        when DiaSem = 4 then 'Quarta   '   \n"
                    + "        when DiaSem = 5 then 'Quinta   '   \n"
                    + "        when DiaSem = 6 then 'Sexta    '   \n"
                    + "        when DiaSem = 7 then 'Sabado   ' else '' end DiaSemana, Clientes.Nome, Clientes.CGC CNPJ from TesCofresRes  \n"
                    + "Left Join Clientes on  Clientes.CodCofre = TesCofresRes.CodCofre\n"
                    + "where Data = '10/19/2023'   \n"
                    + "  and TesCofresRes.CodCofre in (Select Clientes.CodCofre  From PessoaCliAut\n"
                    + "           Left Join Clientes  on PessoaCliAut.CodCli = Clientes.Codigo\n"
                    + "                              and PessoaCliAut.CodFil = Clientes.CodFil\n"
                    + "           Left Join Pessoa on Pessoa.Codigo = PessoaCliAut.Codigo\n"
                    + "           Where Clientes.CodFil >= 1    \n"
                    + "             and  Pessoa.Email =  '<EMAIL>')\n"
                    + "             and TesCofresRes.Data >= '" + vDataIni + "'"
                    + "             and TesCofresRes.Data <= '" + vDataFim + "'"
                    + "order by TesCofresRes.Data Desc ";
            try {
                //    qTmpX = new Consulta(vSQL, dbpadrao);
                //    qTmpX.select();
            } catch (Exception e) {
                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                throw new Exception("integraCICF - " + e.getMessage() + "\r\n"
                        + vSQL);
            }

            int vConta = 0;

           // while (qTmpX.Proximo()) {
                vConta++;
                vjnEnvio = new JSONObject();

                vStrInvoice = "{"
                        + "	\"Batch_Source_Name\": \"BSL MTZ SP NFE\","
                        + "	\"Trans_Batch_Name\": \"BSL MTZ SP NFE\","
                        + "	\"Trans_Type\": \"TRANSPORTE NFS CLI\","
                        + "	\"Trans_Type_Name\": \"TRANSPORTE NFS CLI\","
                        + "	\"Trx_Number\": \"********\","
                        + "	\"Trx_Date\": \"01-08-2023\","
                        + "	\"Invoice_Currency_Code\": \"BRL\","
                        + "	\"Term\": \"Immediate\","
                        + "	\"Billing_Date\": \"01-08-2023\","
                        + "	\"Bill_To_Customer_Number\": \"002091365\","
                        + "	\"Bill_To_Site_Use\": \"002091365003209\","
                        + "	\"Ship_To_Customer_Number\": \"002091365\","
                        + "	\"Ship_To_Site_Use\": \"002091365003209\","
                        + "	\"Conversion_Date\": \"01-08-2023\","
                        + "	\"Conversion_Rate\": \"1\","
                        + "	\"Conversion_Type\": \"User\","
                        + "	\"Receipt_Method_Name\": \"\","
                        + "	\"Inventory_Item\": \"1804543676\","
                        + "	\"Warehouse\": \"26193944000116\","
                        + "	\"Header_Attribute_Category\": \"\","
                        + "	\"Header_Attribute1\": \"\","
                        + "	\"Header_Attribute3\": \"0\","
                        + "	\"Interface_Header_Context\": \"\","
                        + "	\"Interface_Header_Attribute1\": \"\","
                        + "	\"Interface_Header_Attribute2\": \"\","
                        + "    \"Interface_Header_Attribute3\": \"\","
                        + "	\"Interface_Header_Attribute4\": \"\","
                        + "	\"Interface_Header_Attribute5\": \"\","
                        + "	\"Interface_Header_Attribute6\": \"\","
                        + "	\"Interface_Header_Attribute7\": \"\","
                        + "	\"Interface_Header_Attribute8\": \"\","
                        + "	\"Interface_Header_Attribute9\": \"\","
                        + "	\"Interface_Header_Attribute10\": \"\","
                        + "	\"Interface_Line_Attribute4\": \"9\","
                        + "	\"Interface_Line_Attribute5\": \"MTZ-BSL\","
                        + "	\"Interface_Line_Attribute6\": \"UN\","
                        + "	\"Interface_Line_Attribute7\": \"08-01-2023\","
                        + "	\"Interface_Line_Attribute8\": \".\","
                        + "	\"Interface_Line_Attribute9\": \".\","
                        + "	\"Interface_Line_Attribute10\": \".\","
                        + "	\"Header_Gdf_Attr_Category\": \"JL.BR.ARXTWMAI.Additional Info\","
                        + "	\"Header_Gdf_Attribute7\": \"0\","
                        + "	\"Line_Gdf_Attr_Category\": \"JL.BR.ARXTWMAI.Additional Info\","
                        + "	\"Line_Gdf_Attribute1\": \"5949\","
                        + "	\"Line_Gdf_Attribute2\": \"99999999\","
                        + "	\"Line_Gdf_Attribute3\": \"SERVICOS\","
                        + "	\"Line_Gdf_Attribute4\": \"0\","
                        + "	\"Line_Gdf_Attribute5\": \"09\","
                        + "	\"Line_Gdf_Attribute6\": \"53\","
                        + "	\"Line_Gdf_Attribute7\": \"40\","
                        + "	\"Attribute_Category\": \"Brazil\","
                        + "	\"Gl_Date\": \"01-08-2023\","
                        + "	\"Percent\": \"100\","
                        + "	\"Interface_Line_Context\": \"TMS\","
                        + "	\"Interface_Line_Attribute2\": \"********\",	"
                        + "	\"Attribute3_Header\": \"0\","////////Q////////////////
                        + "	\"Org_Name\": \"BSL Operating Unit\","
                        + "	\"Interface_Line_Attribute1\": \"BSL MTZ SP NFE\","
                        + "	\"Portions\": ["
                        + "		{"
                        + "			\"Line_Number\": \"1\","
                        + "			\"Line_Type\": \"LINE\","
                        + "			\"Account_Class\": \"REC\","
                        + "			\"Extended_Amount\": \"10949.29\","
                        + "			\"Amount_Due_Original\": \"10949.29\","
                        + "			\"Amount_Includes_Tax_Flag\": \"N\","
                        + "			\"Tax_Rate\": \"0\","
                        + "			\"Taxable_Amount\": \"0\","
                        + "			\"Quantity_Invoiced\": \"1\","
                        + "			\"Unit_Selling_Price\": \"10949.29\","
                        + "			\"Tax_Rate_Code\": \"\","
                        + "			\"Description\": \"16.01 TRANSPORTE DE CARGA\","
                        + "			\"Distribution_Amount\": \"10949.29\","
                        + "			\"Remaining_Amount\": \"10949.29\","
                        + "			\"Revenue_Amount\": \"10949.29\","
                        + "			\"Segment1\": \"648\","
                        + "			\"Segment2\": \"000000\","
                        + "			\"Segment3\": \"0000\","
                        + "			\"Segment4\": \"115998\","
                        + "			\"Segment5\": \"00\","
                        + "			\"Segment6\": \"0000\","
                        + "			\"Segment7\": \"000\","
                        + "			\"Segment8\": \"00000\","
                        + "			\"Segment9\": \"0000\","
                        + "			\"Segment10\": \"000\","
                        + "			\"Attribute1_Line\": \"\","
                        + "			\"Attribute2_Line\": \"\","
                        + "			\"Attribute3_Line\": \"\","
                        + "			\"Attribute4_Line\": \"\","
                        + "			\"Attribute5_Line\": \"\","
                        + "			\"Attribute6_Line\": \"\","
                        + "			\"Attribute15_Line\": \"S949.03\",	"
                        + "            \"Interface_Line_Attribute3\": \"1\",	"
                        + "            \"Link_To_Line_Attribute1\": \"\",			"
                        + "			\"Link_To_Line_Attribute2\": \"\","
                        + "			\"Link_To_Line_Attribute3\": \"\","
                        + "			\"Link_To_Line_Attribute4\": \"\","
                        + "			\"Link_To_Line_Attribute5\": \"\","
                        + "			\"Link_To_Line_Attribute6\": \"\","
                        + "			\"Link_To_Line_Attribute7\": \"\","
                        + "			\"Link_To_Line_Attribute8\": \"\","
                        + "			\"Link_To_Line_Attribute9\": \"\","
                        + "			\"Link_To_Line_Attribute10\": \"\""
                        + "		},"
                        + "		{"
                        + "			\"Line_Number\": \"1\","
                        + "			\"Line_Type\": \"LINE\","
                        + "			\"Account_Class\": \"REV\","
                        + "			\"Extended_Amount\": \"10949.29\","
                        + "			\"Amount_Due_Original\": \"10949.29\","
                        + "			\"Amount_Includes_Tax_Flag\": \"N\","
                        + "			\"Tax_Rate\": \"0\","
                        + "			\"Taxable_Amount\": \"0\","
                        + "			\"Quantity_Invoiced\": \"1\","
                        + "			\"Unit_Selling_Price\": \"10949.29\","
                        + "			\"Tax_Rate_Code\": \"\","
                        + "			\"Description\": \"16.01 TRANSPORTE DE CARGA\","
                        + "			\"Distribution_Amount\": \"10949.29\","
                        + "			\"Remaining_Amount\": \"10949.29\","
                        + "			\"Revenue_Amount\": \"10949.29\","
                        + "			\"Segment1\": \"648\","
                        + "			\"Segment2\": \"640800\","
                        + "			\"Segment3\": \"0000\","
                        + "			\"Segment4\": \"400000\","
                        + "			\"Segment5\": \"00\","
                        + "			\"Segment6\": \"5400\","
                        + "			\"Segment7\": \"000\","
                        + "			\"Segment8\": \"00000\","
                        + "			\"Segment9\": \"0000\","
                        + "			\"Segment10\": \"000\","
                        + "			\"Attribute1_Line\": \"\","
                        + "			\"Attribute2_Line\": \"\","
                        + "			\"Attribute3_Line\": \"\","
                        + "			\"Attribute4_Line\": \"\","
                        + "			\"Attribute5_Line\": \"\","
                        + "			\"Attribute6_Line\": \"\","
                        + "			\"Attribute15_Line\": \"S949.03\","
                        + "			\"Interface_Line_Attribute3\": \"1\","
                        + "			\"Link_To_Line_Attribute1\": \"\","
                        + "			\"Link_To_Line_Attribute2\": \"\","
                        + "			\"Link_To_Line_Attribute3\": \"\","
                        + "			\"Link_To_Line_Attribute4\": \"\","
                        + "			\"Link_To_Line_Attribute5\": \"\","
                        + "			\"Link_To_Line_Attribute6\": \"\","
                        + "			\"Link_To_Line_Attribute7\": \"\","
                        + "			\"Link_To_Line_Attribute8\": \"\","
                        + "			\"Link_To_Line_Attribute9\": \"\","
                        + "			\"Link_To_Line_Attribute10\": \"\""
                        + "		},"
                        + "		{"
                        + "			\"Line_Number\": \"2\","
                        + "			\"Line_Type\": \"TAX\","
                        + "			\"Account_Class\": \"TAX\","
                        + "			\"Extended_Amount\": \"832.15\","
                        + "			\"Amount_Due_Original\": \"832.15\","
                        + "			\"Amount_Includes_Tax_Flag\": \"N\","
                        + "			\"Tax_Rate\": \"7.6\","
                        + "			\"Taxable_Amount\": \"832.15\","
                        + "			\"Quantity_Invoiced\": \"1\","
                        + "			\"Unit_Selling_Price\": \"832.15\","
                        + "			\"Tax_Rate_Code\": \"COFINS_PIG_7.6_C\","
                        + "			\"Description\": \"16.01 TRANSPORTE DE CARGA\","
                        + "			\"Distribution_Amount\": \"832.15\","
                        + "			\"Remaining_Amount\": \"832.15\","
                        + "			\"Revenue_Amount\": \"832.15\","
                        + "			\"Segment1\": \"648\","
                        + "			\"Segment2\": \"000000\","
                        + "			\"Segment3\": \"0000\","
                        + "			\"Segment4\": \"238005\","
                        + "			\"Segment5\": \"00\","
                        + "			\"Segment6\": \"0000\","
                        + "			\"Segment7\": \"000\","
                        + "			\"Segment8\": \"00000\","
                        + "			\"Segment9\": \"0000\","
                        + "			\"Segment10\": \"000\","
                        + "			\"Attribute1_Line\": \"\","
                        + "			\"Attribute2_Line\": \"\","
                        + "			\"Attribute3_Line\": \"\","
                        + "			\"Attribute4_Line\": \"\","
                        + "			\"Attribute5_Line\": \"\","
                        + "			\"Attribute6_Line\": \"\","
                        + "			\"Attribute15_Line\": \"S949.03\","
                        + "			\"Interface_Line_Attribute3\": \"2\","
                        + "			\"Link_To_Line_Attribute1\": \"BSL MTZ SP NFE\","
                        + "			\"Link_To_Line_Attribute2\": \"********\","
                        + "			\"Link_To_Line_Attribute3\": \"1\","
                        + "			\"Link_To_Line_Attribute4\": \"9\","
                        + "			\"Link_To_Line_Attribute5\": \"MTZ-BSL\","
                        + "			\"Link_To_Line_Attribute6\": \"UN\","
                        + "			\"Link_To_Line_Attribute7\": \"08-01-2023\","
                        + "			\"Link_To_Line_Attribute8\": \".\","
                        + "			\"Link_To_Line_Attribute9\": \".\","
                        + "			\"Link_To_Line_Attribute10\": \".\""
                        + "		},"
                        + "		{"
                        + "			\"Line_Number\": \"3\","
                        + "			\"Line_Type\": \"TAX\","
                        + "			\"Account_Class\": \"TAX\","
                        + "			\"Extended_Amount\": \"-832.15\","
                        + "			\"Amount_Due_Original\": \"-832.15\","
                        + "			\"Amount_Includes_Tax_Flag\": \"N\","
                        + "			\"Tax_Rate\": \"7.6\","
                        + "			\"Taxable_Amount\": \"-832.15\","
                        + "			\"Quantity_Invoiced\": \"1\","
                        + "			\"Unit_Selling_Price\": \"-832.15\","
                        + "			\"Tax_Rate_Code\": \"COFINS_PIG_7.6_D\","
                        + "			\"Description\": \"16.01 TRANSPORTE DE CARGA\","
                        + "			\"Distribution_Amount\": \"-832.15\","
                        + "			\"Remaining_Amount\": \"-832.15\","
                        + "			\"Revenue_Amount\": \"-832.15\","
                        + "			\"Segment1\": \"648\","
                        + "			\"Segment2\": \"640800\","
                        + "			\"Segment3\": \"0000\","
                        + "			\"Segment4\": \"402130\","
                        + "			\"Segment5\": \"00\","
                        + "			\"Segment6\": \"5400\","
                        + "			\"Segment7\": \"000\","
                        + "			\"Segment8\": \"00000\","
                        + "			\"Segment9\": \"0000\","
                        + "			\"Segment10\": \"000\","
                        + "			\"Attribute1_Line\": \"\","
                        + "			\"Attribute2_Line\": \"\","
                        + "			\"Attribute3_Line\": \"\","
                        + "			\"Attribute4_Line\": \"\","
                        + "			\"Attribute5_Line\": \"\","
                        + "			\"Attribute6_Line\": \"\","
                        + "			\"Attribute15_Line\": \"S949.03\","
                        + "			\"Interface_Line_Attribute3\": \"3\","
                        + "			\"Link_To_Line_Attribute1\": \"BSL MTZ SP NFE\","
                        + "			\"Link_To_Line_Attribute2\": \"********\","
                        + "			\"Link_To_Line_Attribute3\": \"1\","
                        + "			\"Link_To_Line_Attribute4\": \"9\","
                        + "			\"Link_To_Line_Attribute5\": \"MTZ-BSL\","
                        + "			\"Link_To_Line_Attribute6\": \"UN\","
                        + "			\"Link_To_Line_Attribute7\": \"08-01-2023\","
                        + "			\"Link_To_Line_Attribute8\": \".\","
                        + "			\"Link_To_Line_Attribute9\": \".\","
                        + "			\"Link_To_Line_Attribute10\": \".\""
                        + "		},"
                        + "		{"
                        + "			\"Line_Number\": \"4\","
                        + "			\"Line_Type\": \"TAX\","
                        + "			\"Account_Class\": \"TAX\","
                        + "			\"Extended_Amount\": \"180.66\","
                        + "			\"Amount_Due_Original\": \"180.66\","
                        + "			\"Amount_Includes_Tax_Flag\": \"N\","
                        + "			\"Tax_Rate\": \"1.65\","
                        + "			\"Taxable_Amount\": \"180.66\","
                        + "			\"Quantity_Invoiced\": \"1\","
                        + "			\"Unit_Selling_Price\": \"180.66\","
                        + "			\"Tax_Rate_Code\": \"PIS_PIG_1.65_C\","
                        + "			\"Description\": \"16.01 TRANSPORTE DE CARGA\","
                        + "			\"Distribution_Amount\": \"180.66\","
                        + "			\"Remaining_Amount\": \"180.66\","
                        + "			\"Revenue_Amount\": \"180.66\","
                        + "			\"Segment1\": \"648\","
                        + "			\"Segment2\": \"000000\","
                        + "			\"Segment3\": \"0000\","
                        + "			\"Segment4\": \"238006\","
                        + "			\"Segment5\": \"00\","
                        + "			\"Segment6\": \"0000\","
                        + "			\"Segment7\": \"000\","
                        + "			\"Segment8\": \"00000\","
                        + "			\"Segment9\": \"0000\","
                        + "			\"Segment10\": \"000\","
                        + "			\"Attribute1_Line\": \"\","
                        + "			\"Attribute2_Line\": \"\","
                        + "			\"Attribute3_Line\": \"\","
                        + "			\"Attribute4_Line\": \"\","
                        + "			\"Attribute5_Line\": \"\","
                        + "			\"Attribute6_Line\": \"\","
                        + "			\"Attribute15_Line\": \"S949.03\","
                        + "			\"Interface_Line_Attribute3\": \"4\","
                        + "			\"Link_To_Line_Attribute1\": \"BSL MTZ SP NFE\","
                        + "			\"Link_To_Line_Attribute2\": \"********\","
                        + "			\"Link_To_Line_Attribute3\": \"1\","
                        + "			\"Link_To_Line_Attribute4\": \"9\","
                        + "			\"Link_To_Line_Attribute5\": \"MTZ-BSL\","
                        + "			\"Link_To_Line_Attribute6\": \"UN\","
                        + "			\"Link_To_Line_Attribute7\": \"08-01-2023\","
                        + "			\"Link_To_Line_Attribute8\": \".\","
                        + "			\"Link_To_Line_Attribute9\": \".\","
                        + "			\"Link_To_Line_Attribute10\": \".\""
                        + "		},"
                        + "		{"
                        + "			\"Line_Number\": \"5\","
                        + "			\"Line_Type\": \"TAX\","
                        + "			\"Account_Class\": \"TAX\","
                        + "			\"Extended_Amount\": \"-180.66\","
                        + "			\"Amount_Due_Original\": \"-180.66\","
                        + "			\"Amount_Includes_Tax_Flag\": \"N\","
                        + "			\"Tax_Rate\": \"1.65\","
                        + "			\"Taxable_Amount\": \"-180.66\","
                        + "			\"Quantity_Invoiced\": \"1\","
                        + "			\"Unit_Selling_Price\": \"-180.66\","
                        + "			\"Tax_Rate_Code\": \"PIS_PIG_1.65_D\","
                        + "			\"Description\": \"16.01 TRANSPORTE DE CARGA\","
                        + "			\"Distribution_Amount\": \"-180.66\","
                        + "			\"Remaining_Amount\": \"-180.66\","
                        + "			\"Revenue_Amount\": \"-180.66\","
                        + "			\"Segment1\": \"648\","
                        + "			\"Segment2\": \"640800\","
                        + "			\"Segment3\": \"0000\","
                        + "			\"Segment4\": \"402110\","
                        + "			\"Segment5\": \"00\","
                        + "			\"Segment6\": \"5400\","
                        + "			\"Segment7\": \"000\","
                        + "			\"Segment8\": \"00000\","
                        + "			\"Segment9\": \"0000\","
                        + "			\"Segment10\": \"000\","
                        + "			\"Attribute1_Line\": \"\","
                        + "			\"Attribute2_Line\": \"\","
                        + "			\"Attribute3_Line\": \"\","
                        + "			\"Attribute4_Line\": \"\","
                        + "			\"Attribute5_Line\": \"\","
                        + "			\"Attribute6_Line\": \"\","
                        + "			\"Attribute15_Line\": \"S949.03\","
                        + "			\"Interface_Line_Attribute3\": \"5\","
                        + "			\"Link_To_Line_Attribute1\": \"BSL MTZ SP NFE\","
                        + "			\"Link_To_Line_Attribute2\": \"********\","
                        + "			\"Link_To_Line_Attribute3\": \"1\","
                        + "			\"Link_To_Line_Attribute4\": \"9\","
                        + "			\"Link_To_Line_Attribute5\": \"MTZ-BSL\","
                        + "			\"Link_To_Line_Attribute6\": \"UN\","
                        + "			\"Link_To_Line_Attribute7\": \"08-01-2023\","
                        + "			\"Link_To_Line_Attribute8\": \".\","
                        + "			\"Link_To_Line_Attribute9\": \".\","
                        + "			\"Link_To_Line_Attribute10\": \".\""
                        + "		},"
                        + "		{"
                        + "			\"Line_Number\": \"6\","
                        + "			\"Line_Type\": \"TAX\","
                        + "			\"Account_Class\": \"TAX\","
                        + "			\"Extended_Amount\": \"547.46\","
                        + "			\"Amount_Due_Original\": \"547.46\","
                        + "			\"Amount_Includes_Tax_Flag\": \"N\","
                        + "			\"Tax_Rate\": \"5.0\","
                        + "			\"Taxable_Amount\": \"547.46\","
                        + "			\"Quantity_Invoiced\": \"1\","
                        + "			\"Unit_Selling_Price\": \"547.46\","
                        + "			\"Tax_Rate_Code\": \"ISS_PIG_5_C\","
                        + "			\"Description\": \"16.01 TRANSPORTE DE CARGA\","
                        + "			\"Distribution_Amount\": \"547.46\","
                        + "			\"Remaining_Amount\": \"547.46\","
                        + "			\"Revenue_Amount\": \"547.46\","
                        + "			\"Segment1\": \"648\","
                        + "			\"Segment2\": \"000000\","
                        + "			\"Segment3\": \"0000\","
                        + "			\"Segment4\": \"238001\","
                        + "			\"Segment5\": \"00\","
                        + "			\"Segment6\": \"0000\","
                        + "			\"Segment7\": \"000\","
                        + "			\"Segment8\": \"00000\","
                        + "			\"Segment9\": \"0000\","
                        + "			\"Segment10\": \"000\","
                        + "			\"Attribute1_Line\": \"\","
                        + "			\"Attribute2_Line\": \"\","
                        + "			\"Attribute3_Line\": \"\","
                        + "			\"Attribute4_Line\": \"\","
                        + "			\"Attribute5_Line\": \"\","
                        + "			\"Attribute6_Line\": \"\","
                        + "			\"Attribute15_Line\": \"S949.03\","
                        + "			\"Interface_Line_Attribute3\": \"6\","
                        + "			\"Link_To_Line_Attribute1\": \"BSL MTZ SP NFE\","
                        + "			\"Link_To_Line_Attribute2\": \"********\","
                        + "			\"Link_To_Line_Attribute3\": \"1\","
                        + "			\"Link_To_Line_Attribute4\": \"9\","
                        + "			\"Link_To_Line_Attribute5\": \"MTZ-BSL\","
                        + "			\"Link_To_Line_Attribute6\": \"UN\","
                        + "			\"Link_To_Line_Attribute7\": \"08-01-2023\","
                        + "			\"Link_To_Line_Attribute8\": \".\","
                        + "			\"Link_To_Line_Attribute9\": \".\","
                        + "			\"Link_To_Line_Attribute10\": \".\""
                        + "		},"
                        + "		{"
                        + "			\"Line_Number\": \"7\","
                        + "			\"Line_Type\": \"TAX\","
                        + "			\"Account_Class\": \"TAX\","
                        + "			\"Extended_Amount\": \"-547.46\","
                        + "			\"Amount_Due_Original\": \"-547.46\","
                        + "			\"Amount_Includes_Tax_Flag\": \"N\","
                        + "			\"Tax_Rate\": \"5.0\","
                        + "			\"Taxable_Amount\": \"-547.46\","
                        + "			\"Quantity_Invoiced\": \"1\","
                        + "			\"Unit_Selling_Price\": \"-547.46\","
                        + "			\"Tax_Rate_Code\": \"ISS_PIG_5_D\","
                        + "			\"Description\": \"16.01 TRANSPORTE DE CARGA\","
                        + "			\"Distribution_Amount\": \"-547.46\","
                        + "			\"Remaining_Amount\": \"-547.46\","
                        + "			\"Revenue_Amount\": \"-547.46\","
                        + "			\"Segment1\": \"648\","
                        + "			\"Segment2\": \"640800\","
                        + "			\"Segment3\": \"0000\","
                        + "			\"Segment4\": \"402100\","
                        + "			\"Segment5\": \"00\","
                        + "			\"Segment6\": \"5400\","
                        + "			\"Segment7\": \"000\","
                        + "			\"Segment8\": \"00000\","
                        + "			\"Segment9\": \"0000\","
                        + "			\"Segment10\": \"000\","
                        + "			\"Attribute1_Line\": \"\","
                        + "			\"Attribute2_Line\": \"\","
                        + "			\"Attribute3_Line\": \"\","
                        + "			\"Attribute4_Line\": \"\","
                        + "			\"Attribute5_Line\": \"\","
                        + "			\"Attribute6_Line\": \"\","
                        + "			\"Attribute15_Line\": \"S949.03\","
                        + "			\"Interface_Line_Attribute3\": \"7\","
                        + "			\"Link_To_Line_Attribute1\": \"BSL MTZ SP NFE\","
                        + "			\"Link_To_Line_Attribute2\": \"********\","
                        + "			\"Link_To_Line_Attribute3\": \"1\","
                        + "			\"Link_To_Line_Attribute4\": \"9\","
                        + "			\"Link_To_Line_Attribute5\": \"MTZ-BSL\","
                        + "			\"Link_To_Line_Attribute6\": \"UN\","
                        + "			\"Link_To_Line_Attribute7\": \"08-01-2023\","
                        + "			\"Link_To_Line_Attribute8\": \".\","
                        + "			\"Link_To_Line_Attribute9\": \".\","
                        + "			\"Link_To_Line_Attribute10\": \".\""
                        + "		}"
                        + "	]"
                        + "}";

                /*        
                vjnEnvio.put("Reposta", vStr)
                vjnArray.put(vjnEnvio);
                //}
                if (vConta == 0) {
                    vjnEnvio.put("resposta", "NAO HA DADOS");
                    vjnArray.put(vjnEnvio);
                }
                try {
                    vjnRetorno.put("CofresRes", vjnArray);
                } catch (Exception e) {
                    logexecucao.Grava("Erro geração Retorno Json" + e.getMessage(), caminho);
                    throw new Exception("GUIA - " + e.getMessage() + "\r\n"
                    );
                }
                        */
            }finally {
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vStrInvoice)   // Era vjnRetorno.toString()
                    .build();
        }
        }

    }

    /* ScriptEngineManager factory = new ScriptEngineManager(); // Permite escolher linguagens diferentes da tradicional Java
        ScriptEngine engine = factory.getEngineByName("JavaScript"); //Escolhemos a linguagem que será utilizada, nesse caso é o JavaScript
        Invocable invocable = (Invocable) engine;
        String vImgX = "{FotoSrvN1: " + vFotoSrvN1X + "),"
                + "FotoSrvS1: " + vFotoSrvS1X + "),"
                + "FotoN1: " + vFotoN1X + "),"
                + "FotoS1: " + vFotoN1X + ")";
        //logexecucao.Grava("Montagem vImgX:" + vImgX, caminho);
        String vResultado = "";
        try {
            engine.eval(new FileReader("C:\\xampp\\htdocs\\satellite\\js\\saslibraryRec.js"));
            vResultado = (String) invocable.invokeFunction("comparaFoto", vImgX);
        } catch (Exception e) {
            logexecucao.Grava("Erro na chamada do JS" + e.getMessage(), caminho);            
        }

            vSQL = "Select TMktdet.Sequencia, TMktdet.Andamento, TMktdet.Data, TMktdet.Hora, TMktdet.TipoCont, TMktdet.CodPessoa, \n" +
                   "TMktdet.Historico, TMktdet.Detalhes,  Pessoa.Nome, Pessoa.email, Pessoa.PWweb, PstServ.Local, Contatos.Nome NomeContato, TMktdet.Situacao, \n"+
                   " TMktdet.Ciente, tMKTDet.CodCont from TMktdet \n" +
                   "Left Join Contatos on Contatos.Codigo = TMKtDet.CodCont\n " +
                   "Left Join Clientes on Clientes.Codigo = Contatos.Codcli\n " +
                   "                  and Clientes.Codfil = Contatos.CodFil\n " +
                   "Left Join PStServ on PstServ.Codcli = Clientes.Codigo\n " +
                   "                 and PStServ.CodFil = Clientes.CodFil\n " +
                   "Left Join Pessoa  on Pessoa.Codigo = TMKtDet.CodPessoa \n "+
                   " Where TMKtDet.CodPessoa = " +vCodPessoa+
                   "   and Pessoa.PWWeb = '"+vPW+"'";


     */
