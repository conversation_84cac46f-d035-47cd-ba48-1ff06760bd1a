/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.MultivaluedMap;

/**
 *
 * <AUTHOR>
 */
public class Utilidades {

    public static Map obterParametros(String parametro){
        Map parametros = new HashMap<String, String>();
        
       String params[] = parametro.split("\\&"), valor[];
        
        for(int i = 0; i < params.length; i++){
            valor = params[i].split("=");
            if(valor.length >= 2){
                parametros.put(valor[0], valor[1]);
            } else if(valor.length == 1){
                parametros.put(valor[0], "");
            }
            
        }
        
        return parametros;
    }
    
    public static String gerarLog(MultivaluedMap<String, String> queryParams) {
        StringBuilder log = new StringBuilder();
        for (String parametro : queryParams.keySet()) {
            log.append(parametro).append(": ").append(list2String(queryParams.get(parametro))).append("\r\n");
        }
        return log.toString();
    }

    public static String list2String(List<String> lista) {
        if (lista == null || lista.isEmpty()) {
            return "";
        } else if (lista.size() == 1) {
            return lista.get(0);
        } else {
            StringBuilder retorno = new StringBuilder();

            for (int i = 1; i < lista.size(); i++) {
                retorno.append(lista.get(i - 1)).append(";");
            }
            retorno.append(lista.get(lista.size() - 1));

            return retorno.toString();
        }
    }
}
