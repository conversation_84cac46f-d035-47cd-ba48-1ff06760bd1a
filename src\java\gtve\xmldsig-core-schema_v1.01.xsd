﻿<?xml version="1.0" encoding="utf-8"?>
<!-- ***************************************************-->
<!-- ***   Schema específico para assinaturas XML    ***-->
<!-- *** a partir de certificados do padrão (X509)   ***-->
<!-- *** ICP-Brasil - Projeto Nota Fiscal Eletrônica ***-->
<!-- ***************************************************-->
<!-- Schema for XML Signatures-->
<schema xmlns="http://www.w3.org/2001/XMLSchema" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.w3.org/2000/09/xmldsig#" elementFormDefault="qualified" attributeFormDefault="unqualified" version="0.1">
	<element name="Signature" type="ds:SignatureType"/>
	<complexType name="SignatureType">
		<sequence>
			<element name="SignedInfo" type="ds:SignedInfoType"/>
			<element name="SignatureValue" type="ds:SignatureValueType"/>
			<element name="KeyInfo" type="ds:KeyInfoType"/>
		</sequence>
		<attribute name="Id" type="ID" use="optional"/>
	</complexType>
	<complexType name="SignatureValueType">
		<simpleContent>
			<extension base="base64Binary">
				<attribute name="Id" type="ID" use="optional"/>
			</extension>
		</simpleContent>
	</complexType>
	<complexType name="SignedInfoType">
		<sequence>
			<element name="CanonicalizationMethod">
				<complexType>
					<attribute name="Algorithm" type="anyURI" use="required" fixed="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/>
				</complexType>
			</element>
			<element name="SignatureMethod">
				<complexType>
					<attribute name="Algorithm" type="anyURI" use="required" fixed="http://www.w3.org/2000/09/xmldsig#rsa-sha1"/>
				</complexType>
			</element>
			<element name="Reference" type="ds:ReferenceType"/>
		</sequence>
		<attribute name="Id" type="ID" use="optional"/>
	</complexType>
	<complexType name="ReferenceType">
		<sequence>
			<element name="Transforms" type="ds:TransformsType">
				<!-- Garante a unicidade do atributo -->
				<unique name="unique_Transf_Alg">
					<selector xpath="./*"/>
					<field xpath="@Algorithm"/>
				</unique>
			</element>
			<element name="DigestMethod">
				<complexType>
					<attribute name="Algorithm" type="anyURI" use="required" fixed="http://www.w3.org/2000/09/xmldsig#sha1"/>
				</complexType>
			</element>
			<element name="DigestValue" type="ds:DigestValueType"/>
		</sequence>
		<attribute name="Id" type="ID" use="optional"/>
		<attribute name="URI" use="required">
			<simpleType>
				<restriction base="anyURI">
					<minLength value="2"/>
				</restriction>
			</simpleType>
		</attribute>
		<attribute name="Type" type="anyURI" use="optional"/>
	</complexType>
	<complexType name="TransformsType">
		<sequence>
			<element name="Transform" type="ds:TransformType" minOccurs="2" maxOccurs="2"/>
		</sequence>
	</complexType>
	<complexType name="TransformType">
		<sequence minOccurs="0" maxOccurs="unbounded">
			<element name="XPath" type="string"/>
		</sequence>
		<attribute name="Algorithm" type="ds:TTransformURI" use="required"/>
	</complexType>
	<complexType name="KeyInfoType">
		<sequence>
			<element name="X509Data" type="ds:X509DataType"/>
		</sequence>
		<attribute name="Id" type="ID" use="optional"/>
	</complexType>
	<complexType name="X509DataType">
		<sequence>
			<element name="X509Certificate" type="base64Binary"/>
		</sequence>
	</complexType>
	<simpleType name="DigestValueType">
		<restriction base="base64Binary"/>
	</simpleType>
	<simpleType name="TTransformURI">
		<restriction base="anyURI">
			<enumeration value="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/>
			<enumeration value="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/>
		</restriction>
	</simpleType>
</schema>
