/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.integracao.digibee;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Filiais;
import SasBeans.Pedido;
import SasBeans.PedidoDN;
import SasBeans.PedidoMD;
import SasDaos.ClientesDao;
import SasDaos.FiliaisDao;
import SasDaos.OS_VigDao;
import SasDaos.PedidoDNDao;
import SasDaos.PedidoDao;
import SasDaos.PedidoMDDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.ValidadorCPF_CNPJ;
import br.com.sasw.satwebservice.integracao.digibee.beans.Clientes;
import br.com.sasw.satwebservice.integracao.digibee.beans.Pedidos;
import br.com.sasw.satwebservice.integracao.digibee.beans.envio.ClienteEnvio;
import br.com.sasw.satwebservice.integracao.digibee.beans.envio.Composicao;
import br.com.sasw.satwebservice.integracao.digibee.beans.envio.PedidoEnvio;
import br.com.sasw.satwebservice.integracao.digibee.beans.resposta.ClienteResposta;
import br.com.sasw.satwebservice.integracao.digibee.beans.resposta.PedidoResposta;
import java.math.BigDecimal;
import java.util.ArrayList;
import javax.ws.rs.Consumes;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.UriInfo;

/**
 * REST Web Service
 *
 * <AUTHOR>
 */
@Path("/ws-digibee/")
public class Digibee {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private String caminho;
    private Persistencia persistencia;

    @Context
    private UriInfo context;

    /**
     * Creates a new instance of Digibee
     */
    public Digibee() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ERRO\\"
                + "\\Digibee\\" + DataAtual.getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * PUT method for updating or creating an instance of realizarPedido
     *
     * @param empresa
     * @param pedidos
     * @return
     */
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/{empresa}/realizarPedido")
    public Pedidos<PedidoResposta> realizarPedido(@PathParam("empresa") String empresa, Pedidos<PedidoEnvio> pedidos) {
        Pedidos<PedidoResposta> respostas = new Pedidos();
        respostas.setPedidos(new ArrayList<>());
        PedidoResposta resposta;
        try {
            if (empresa.equals("SASW")) {
                empresa = "SATPRESERVE";
            }

            persistencia = pool.getConexao(empresa, "");
            if (persistencia == null) {
                throw new Exception("Empresa inválida");
            }
            caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\" + empresa + "\\"
                    + "\\Digibee\\" + DataAtual.getDataAtual("SQL") + "\\log.txt";

            ClientesDao clientesDao = new ClientesDao();
            PedidoDao pedidoDao = new PedidoDao();
            OS_VigDao os_vigDao = new OS_VigDao();
            PedidoDNDao pedidoDNDao = new PedidoDNDao();
            PedidoMDDao pedidoMDDao = new PedidoMDDao();

            SasBeans.Clientes origem = null, destino;
            SasBeans.Pedido novoPedido;
            PedidoDN pedidoDN;
            PedidoMD pedidoMD;

            String codFil, OS, numero, dataAtendimento, retorno;
            boolean existePedido;

            for (PedidoEnvio pedido : pedidos.getPedidos()) {

                resposta = new PedidoResposta();
                try {

                    if (pedido.getPedidocliente() == null) {
                        resposta.setPedidocliente("");
                        resposta.setResposta("9-pedidocliente");
                        throw new Exception("pedidocliente não informado");
                    }

                    resposta.setPedidocliente(pedido.getPedidocliente());
                    switch (pedido.getAcao()) {
                        case 1:
                            if (pedido.getTipo() == null) {
                                resposta.setResposta("9-tipo");
                                throw new Exception("tipo não informado");
                            }
                            if (!pedido.getTipo().equals("C") && !pedido.getTipo().equals("T")
                                    && !pedido.getTipo().equals("AE")
                                    && !pedido.getTipo().equals("Ai")
                                    && !pedido.getTipo().equals("AI")
                                    && !pedido.getTipo().equals("TD")
                                    && !pedido.getTipo().equals("PB")
                                    && !pedido.getTipo().equals("AG")
                                    && !pedido.getTipo().equals("IT")
                                    && !pedido.getTipo().equals("TE")
                                    && !pedido.getTipo().equals("BB")) {
                                resposta.setResposta("9-tipo");
                                throw new Exception("tipo inválido");
                            }

                            if (pedido.getTipo().equals("C")) {

                                existePedido = true;
                                try {
                                    existePedido = pedidoDao.existePedidoCliente(pedido.getPedidocliente(), persistencia);
                                } catch (Exception ePedidoCliente) {
                                    logerro.Grava(ePedidoCliente.getMessage(), caminho);
                                }

                                if (existePedido) {
                                    try {
                                        novoPedido = pedidoDao.buscaPedidoCliente(pedido.getPedidocliente(), persistencia);

                                        pedidoDao.excluiPedido(pedido.getPedidocliente(), getDataAtual("SQL"), getDataAtual("HORA"),
                                                "SatMobWeb", persistencia);

                                        resposta.setNumero(novoPedido.getNumero().toBigInteger().toString());
                                        resposta.setResposta("1");
                                    } catch (Exception eExclusaoPedido) {
                                        logerro.Grava(eExclusaoPedido.getMessage(), caminho);

                                        resposta.setResposta("9");
                                        throw new Exception("erro exclusao");
                                    }
                                } else {
                                    resposta.setResposta("9");
                                    throw new Exception("pedido não existe");
                                }
                            } else {
                                if (!pedido.getClassificacao().equals("R")
                                        && !pedido.getClassificacao().equals("V")
                                        && !pedido.getClassificacao().equals("E")
                                        && !pedido.getClassificacao().equals("A")) {
                                    resposta.setResposta("9-classificacao");
                                    throw new Exception("classificacao inválido");
                                }

                                existePedido = true;
                                try {
                                    existePedido = pedidoDao.existePedidoCliente(pedido.getPedidocliente(), persistencia);
                                } catch (Exception ePedidoCliente) {
                                    logerro.Grava(ePedidoCliente.getMessage(), caminho);
                                }

                                if (existePedido) {
                                    resposta.setResposta("10-Pedido já cadastrado");
                                    throw new Exception("10-Pedido já cadastrado");
                                }

                                if (pedido.getData() == null) {
                                    resposta.setResposta("9-data");
                                    throw new Exception("data não informada");
                                }

                                if (pedido.getIdpontoservicoorigem() == null) {
                                    resposta.setResposta("9-idpontoservicoorigem");
                                    throw new Exception("idpontoservicoorigem não informado");
                                }

                                try {
                                    if (pedido.getIdpontoservicoorigem().contains("TESOURARIA")) {
                                        origem = clientesDao.buscarTesouraria("999", "7", "006", persistencia);
                                    } else {
                                        origem = clientesDao.buscaPontoServico(pedido.getIdpontoservicoorigem(), persistencia);
                                    }
                                } catch (Exception eOrigem) {
                                    resposta.setResposta("6-Ponto de serviço de origem não encontrado");
                                    logerro.Grava(eOrigem.getMessage(), caminho);
                                    throw new Exception("6-Ponto de serviço de origem não encontrado");
                                }
                                if (origem == null) {
                                    resposta.setResposta("6-Ponto de serviço de origem não encontrado");
                                    throw new Exception("6-Ponto de serviço de origem não encontrado");
                                }
                                codFil = origem.getCodFil().toBigInteger().toString();

                                if (pedido.getHora1O() == null) {
                                    pedido.setHora1O("08:00");
//                                    resposta.setResposta("9-hora1O");
//                                    throw new Exception("hora1O não informado");
                                }

                                if (pedido.getHora2O() == null) {
                                    pedido.setHora2O("18:00");
//                                    resposta.setResposta("9-hora2O");
//                                    throw new Exception("hora2O não informado");
                                }

                                if ((pedido.getIdpontoservicodestino() == null) && (!pedido.getClassificacao().equals("A"))) {
                                    resposta.setResposta("9-idpontoservicodestino");
                                    throw new Exception("idpontoservicodestino não informado");
                                }

                                try {
                                    if (pedido.getIdpontoservicodestino().contains("TESOURARIA")) {
                                        destino = clientesDao.buscarTesouraria("999", "7", "006", persistencia);
                                    } else {
                                        destino = clientesDao.buscaPontoServico(pedido.getIdpontoservicodestino(), persistencia);
                                    }
                                } catch (Exception eDestino) {
                                    resposta.setResposta("7-Ponto de serviço de destino não encontrado");
                                    logerro.Grava(eDestino.getMessage(), caminho);
                                    throw new Exception("7-Ponto de serviço de destino não encontrado");
                                }

                                if (destino == null) {
                                    resposta.setResposta("7-Ponto de serviço de destino não encontrado");
                                    throw new Exception("7-Ponto de serviço de destino não encontrado");
                                }

                                if ((pedido.getHora1D() == null) && (!pedido.getClassificacao().equals("A"))) {
                                    pedido.setHora1D("08:00");
//                                    resposta.setResposta("9-hora1D");
//                                    throw new Exception("hora1D não informado");
                                }

                                if ((pedido.getHora2D() == null) && (!pedido.getClassificacao().equals("A"))) {
                                    pedido.setHora2D("08:00");
//                                    resposta.setResposta("9-hora2D");
//                                    throw new Exception("hora2D não informado");
                                }

                                if ((!pedido.getMoeda().equals("BRL"))
                                        && (!pedido.getMoeda().equals("USD"))
                                        && (!pedido.getMoeda().equals("EUR"))
                                        && (!pedido.getMoeda().equals("GBP"))
                                        && (!pedido.getMoeda().equals("IEN"))
                                        && (!pedido.getMoeda().equals("OUT"))) {
                                    resposta.setResposta("8");
                                    throw new Exception("8-Tipo de moeda inválido");
                                }

                                for (Composicao composicao : pedido.getComposicoes()) {
                                    if ((composicao.getCodigo() != 1)
                                            && ((composicao.getCodigo() == 2) && (!composicao.getTipo().equals("cedula")))
                                            && ((composicao.getCodigo() == 102) && (!composicao.getTipo().equals("cedula")))
                                            && (composicao.getCodigo() != 5)
                                            && ((composicao.getCodigo() == 105) && (!composicao.getTipo().equals("cedula")))
                                            && (composicao.getCodigo() != 10)
                                            && ((composicao.getCodigo() == 11) && (!composicao.getTipo().equals("cedula")))
                                            && ((composicao.getCodigo() == 20) && (!composicao.getTipo().equals("cedula")))
                                            && ((composicao.getCodigo() == 21) && (!composicao.getTipo().equals("cedula")))
                                            && ((composicao.getCodigo() == 25) && (!composicao.getTipo().equals("moeda")))
                                            && (composicao.getCodigo() != 50)
                                            && ((composicao.getCodigo() == 51) && (!composicao.getTipo().equals("cedula")))
                                            && (composicao.getCodigo() != 100)
                                            && ((composicao.getCodigo() == 101) && (!composicao.getTipo().equals("cedula")))) {
                                        resposta.setResposta("9-composicao.codigo");
                                        throw new Exception("codigo composicao invalida");
                                    }

                                    if (composicao.getQuantidade() <= 0) {
                                        resposta.setResposta("9-composicao.quantidade");
                                        throw new Exception("quantidade invalida");
                                    }

                                }

                                OS = os_vigDao.obterOS(origem.getCodigo(), destino.getCodigo(), codFil, persistencia);

                                novoPedido = new Pedido();
                                novoPedido.setCodFil(codFil);
                                novoPedido.setData(pedido.getData());
                                //novoPedido.setTipo(pedido.getTipo());
                                novoPedido.setTipo("T");
                                novoPedido.setCodCli1(origem.getCodigo());
                                novoPedido.setNRed1(origem.getNRed());
                                novoPedido.setHora1O(pedido.getHora1O());
                                novoPedido.setHora2O(pedido.getHora2O());
                                novoPedido.setCodCli2(destino.getCodigo());
                                novoPedido.setNRed2(destino.getNRed());
                                novoPedido.setHora1D(pedido.getHora1D());
                                novoPedido.setHora2D(pedido.getHora2D());
                                novoPedido.setSolicitante("");
                                novoPedido.setValor(new BigDecimal(String.valueOf(pedido.getValor())));
                                novoPedido.setObs(pedido.getObservacao());
                                novoPedido.setClassifSrv(pedido.getClassificacao());
                                novoPedido.setOperIncl("SatMobWeb");
                                novoPedido.setDt_Incl(getDataAtual("SQL"));
                                novoPedido.setHr_Incl(getDataAtual("HORA"));
                                novoPedido.setOS(OS);
                                novoPedido.setOperador("SatMobWeb");
                                novoPedido.setDt_Alter(getDataAtual("SQL"));
                                novoPedido.setHr_Alter(getDataAtual("HORA"));
                                novoPedido.setSituacao("PD");
                                novoPedido.setPedidoCliente(pedido.getPedidocliente());
                                novoPedido.setFlag_Excl("");

                                String ErroPedido = "";

                                try {
                                    numero = pedidoDao.salvarPedido(novoPedido, persistencia);
                                } catch (Exception e) {
                                    ErroPedido = e.getMessage();
                                    numero = null;
                                    logerro.Grava(e.getMessage(), caminho);
                                }

                                if (numero == null) {
                                    resposta.setResposta("Erro ao Gravar Pedido - " + ErroPedido);
                                    throw new Exception("erro insert pedido");
                                }

                                for (Composicao composicao : pedido.getComposicoes()) {
                                    if (composicao != null && composicao.getTipo() != null && composicao.getTipo().equals("moeda")) {
                                        pedidoMD = new PedidoMD();
                                        pedidoMD.setCodFil(codFil);
                                        pedidoMD.setCodigo(String.valueOf(composicao.getCodigo()));
                                        pedidoMD.setDocto(pedido.getMoeda());
                                        pedidoMD.setNumero(numero);
                                        pedidoMD.setQtde(String.valueOf(composicao.getQuantidade()));

                                        try {
                                            pedidoMDDao.deletarPedidoMD(pedidoMD, persistencia);
                                            pedidoMDDao.inserirPedidoMD(pedidoMD, persistencia);
                                        } catch (Exception ePedidoMD) {
                                            logerro.Grava(ePedidoMD.getMessage(), caminho);
                                        }
                                    } else {
                                        if (null != composicao) {
                                            pedidoDN = new PedidoDN();
                                            pedidoDN.setCodFil(codFil);
                                            pedidoDN.setCodigo(String.valueOf(composicao.getCodigo()));
                                            pedidoDN.setDocto(pedido.getMoeda());
                                            pedidoDN.setNumero(numero);
                                            pedidoDN.setQtde(String.valueOf(composicao.getQuantidade()));
                                            try {
                                                pedidoDNDao.deletarPedidoDN(pedidoDN, persistencia);
                                                pedidoDNDao.inserirPedidoDN(pedidoDN, persistencia);
                                            } catch (Exception ePedidoDN) {
                                                logerro.Grava(ePedidoDN.getMessage(), caminho);
                                            }
                                        }
                                    }
                                }

                                resposta.setNumero(numero);
                                resposta.setResposta("1-Inserido com Sucesso");
                            }
                            break;
                        case 2:
                            novoPedido = null;
                            try {
                                novoPedido = pedidoDao.obterSituacaoPedido(pedido.getPedidocliente(), persistencia);
                            } catch (Exception eBuscaPedido) {
                                logerro.Grava(eBuscaPedido.getMessage(), caminho);
                                resposta.setResposta("9");
                                throw new Exception("pedido não existe");
                            }

                            if (novoPedido == null) {
                                resposta.setResposta("9");
                                throw new Exception("pedido não existe");
                            }

                            if (novoPedido.getHoraEnt().isEmpty() && novoPedido.getStatusRec().equals("3")) {
                                resposta.setResposta("4");   //Não entregue
                            } else if (novoPedido.getStatusEnt().isEmpty()) {
                                resposta.setResposta(novoPedido.getStatusRec());
                            } else {
                                resposta.setResposta(novoPedido.getStatusEnt());
                            }
                            //retorno = "{" + " status: " + "  " + novoPedido.getSituacao() + "  ";                            
                            resposta.setRecolhimentoData(novoPedido.getDataRec());
                            resposta.setRecolhimentoHora(novoPedido.getHoraRec());
                            resposta.setRecolhimentoGtv(novoPedido.getGuiasRec());
                            resposta.setRecolhimentoValor(novoPedido.getValorRec());

                            resposta.setEntregaData(novoPedido.getDataEnt());
                            resposta.setEntregaHora(novoPedido.getHoraEnt());
                            resposta.setEntregaGtv(novoPedido.getGuiasEnt());
                            resposta.setEntregaValor(novoPedido.getValorEnt());

                            resposta.setNumero(novoPedido.getNumero().toBigInteger().toString());

                            break;
                    }

                } catch (Exception e) {
                    logerro.Grava(e.getMessage(), caminho);
                } finally {
                    if (resposta.getNumero() == null) {
                        resposta.setNumero("");
                    }
                    respostas.getPedidos().add(resposta);
                }
            }
        } catch (Exception inicializacao) {
            logerro.Grava(inicializacao.getMessage(), caminho);
        } finally {
            return respostas;
        }
    }

    /**
     * PUT method for updating or creating an instance of cliente
     *
     * @param empresa
     * @param clientes
     * @return
     */
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/{empresa}/incluirCliente")
    public Clientes<ClienteResposta> incluirCliente(@PathParam("empresa") String empresa, Clientes<ClienteEnvio> clientes) {

        Clientes<ClienteResposta> respostas = new Clientes();
        respostas.setClientes(new ArrayList<>());
        ClienteResposta resposta;

        try {
            if (empresa.equals("SASW")) {
                empresa = "SATPRESERVE";
            }

            persistencia = pool.getConexao(empresa);
            if (persistencia == null) {
                throw new Exception("Empresa inválida");
            }
            caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\" + empresa + "\\"
                    + "\\Digibee\\" + DataAtual.getDataAtual("SQL") + "\\log.txt";

            ClientesDao clientesDao = new ClientesDao();
            /**
             * 0 - não existe 1 - cliente ativo 2 - cliente inativo
             */
            int existeCliente;

            FiliaisDao filialDao = new FiliaisDao();
            Filiais filial;
            String codFil;

            String codcli, codigo, banco = "033", cpf = "", cnpj = "";

            for (ClienteEnvio cliente : clientes.getClientes()) {

                resposta = new ClienteResposta();

                try {

                    if (cliente.getIdpontoservico() == null) {
                        resposta.setIdpontoservico("");
                        resposta.setResposta("9-idpontoservico");
                        throw new Exception("idpontoservico não informado");
                    }

                    resposta.setIdpontoservico(cliente.getIdpontoservico());

                    existeCliente = clientesDao.existePontoServico(cliente.getIdpontoservico(), persistencia);

                    if (cliente.getAcao() != 1 && cliente.getAcao() != 3) {
                        resposta.setResposta("9-acao");
                        throw new Exception("acao invalida");
                    }

                    if (cliente.getCnpjfilial() == null) {
                        resposta.setResposta("9-cnpjfilial");
                        throw new Exception("cnpjfilial não informado");
                    }
                    if (!ValidadorCPF_CNPJ.ValidarCNPJ(cliente.getCnpjfilial())) {
                        resposta.setResposta("9-cnpjfilial");
                        throw new Exception("cnpjfilial invalido");
                    }

                    try {
                        filial = filialDao.buscaFilialPorCNPJ(cliente.getCnpjfilial(), persistencia);
                    } catch (Exception eFilial) {
                        logerro.Grava(eFilial.getMessage(), caminho);
                        resposta.setResposta("9-cnpjfilial");
                        throw new Exception("erro ao processar cnpjfilial");
                    }
                    if (filial.getCodFil() == null) {
                        resposta.setResposta("9-cnpjfilial");
                        throw new Exception("cnpjfilial não encontrado na base de dados");
                    }
                    codFil = filial.getCodFil().toBigInteger().toString();

                    switch (cliente.getAcao()) {
                        case 1: // Cadastro
                            switch (existeCliente) {
                                case 1:
                                    resposta.setResposta("6");
                                    throw new Exception("6-ponto de serviço já cadastrado. Inclusão não processada.");

                                // Ativar cliente
                                case 2:
                                    clientesDao.alterarSituacaoPontoServico(cliente.getIdpontoservico(), "A", persistencia);
                                    resposta.setResposta("3");
                                    break;

                                // Inserir cliente
                                default:

                                    if (cliente.getTipo() == null) {
                                        resposta.setResposta("9-tipo");
                                        throw new Exception("tipo não informado");
                                    }
                                    //if (cliente.getTipo().length() != 1
                                    if (!cliente.getTipo().equals("0")
                                            && !cliente.getTipo().equals("3")
                                            && !cliente.getTipo().equals("5")
                                            && !cliente.getTipo().equals("6")
                                            && !cliente.getTipo().equals("8")
                                            && !cliente.getTipo().equals("9")) {
                                        resposta.setResposta("9-tipo");
                                        throw new Exception("tipo invalido");
                                    }

                                    if (cliente.getTipo().equals("0")
                                            || cliente.getTipo().equals("3")
                                            || cliente.getTipo().equals("9")) {
                                        if (cliente.getAgencia() == null) {
                                            resposta.setResposta("9-agencia");
                                            throw new Exception("agencia não informada");
                                        }

                                        if (cliente.getSubagencia() == null) {
                                            resposta.setResposta("9-subagencia");
                                            throw new Exception("subagencia não informada");
                                        }
                                    }

                                    if (cliente.getCnpjcpf() == null) {
                                        cpf = null;
                                    }
                                    if (ValidadorCPF_CNPJ.ValidarCPF(cliente.getCnpjcpf())) {
                                        cpf = cliente.getCnpjcpf();
                                    }

                                    if (cliente.getCnpjcpf() == null) {
                                        cnpj = null;
                                    }
                                    if (ValidadorCPF_CNPJ.ValidarCNPJ(cliente.getCnpjcpf())) {
                                        cnpj = cliente.getCnpjcpf();
                                    }

                                    try {
                                        codcli = clientesDao.getCodCli(codFil, banco, cliente.getTipo(), persistencia);
                                    } catch (Exception eCodCli) {
                                        logerro.Grava(eCodCli.getMessage(), caminho);
                                        resposta.setResposta("9-tipo");
                                        throw new Exception("erro ao processar tipo");
                                    }

                                    codigo = banco + cliente.getTipo() + codcli;

                                    clientesDao.inserirPontoServico(codigo, codFil, cliente.getIdpontoservico(), cliente.getAgencia(),
                                            cliente.getSubagencia(), cliente.getRazaosocial(), cliente.getCep(), cliente.getEndereco(), cliente.getBairro(), cliente.getCidade(),
                                            cliente.getUf(), cnpj, cpf, cliente.getEmail(), cliente.getFone(), cliente.getFone2(), "SATMOB", DataAtual.getDataAtual("SQL"), DataAtual.getDataAtual("HORA"),  
                                            persistencia);

                                    resposta.setResposta("1");

                                    break;
                            }
                            break;

                        case 3: // Inativação
                            if (existeCliente == 0 || existeCliente == 2) {
                                resposta.setResposta("7");
                                throw new Exception("7-ponto de serviço inexistente/inativo. Desativação não processada.");
                            }

                            clientesDao.alterarSituacaoPontoServico(cliente.getIdpontoservico(), "I", persistencia);
                            resposta.setResposta("2");

                            break;
                    }

                } catch (Exception inclusao) {
                    logerro.Grava(inclusao.getMessage(), caminho);
                } finally {
                    respostas.getClientes().add(resposta);
                }
            }

        } catch (Exception inicializacao) {
            logerro.Grava(inicializacao.getMessage(), caminho);
            resposta = new ClienteResposta();
            resposta.setIdpontoservico("");
            resposta.setResposta("0");
            respostas.getClientes().add(resposta);
        } finally {
            return respostas;
        }
    }
}
