package br.com.sasw.satwebservice.bcb;

import com.google.gson.annotations.SerializedName;

/**
 *
 * <AUTHOR>
 */
public class CotacaoMoedaDia {
    
    @SerializedName(value = "paridadeCompra")
    private double paridadeCompra;
    @SerializedName(value = "paridadeVenda")
    private double paridadeVenda;
    @SerializedName(value = "cotacaoCompra")
    private double cotacaoCompra;
    @SerializedName(value = "cotacaoVenda")
    private double cotacaoVenda;
    @SerializedName(value = "dataHoraCotacao")
    private String dataHoraCotacao;
    @SerializedName(value = "tipoBoletim")
    private String tipoBoletim;

    public double getParidadeCompra() {
        return paridadeCompra;
    }

    public double getParidadeVenda() {
        return paridadeVenda;
    }

    public double getCotacaoCompra() {
        return cotacaoCompra;
    }

    public double getCotacaoVenda() {
        return cotacaoVenda;
    }

    public String getDataHoraCotacao() {
        return dataHoraCotacao;
    }

    public String getTipoBoletim() {
        return tipoBoletim;
    }

    public void setParidadeCompra(double paridadeCompra) {
        this.paridadeCompra = paridadeCompra;
    }

    public void setParidadeVenda(double paridadeVenda) {
        this.paridadeVenda = paridadeVenda;
    }

    public void setCotacaoCompra(double cotacaoCompra) {
        this.cotacaoCompra = cotacaoCompra;
    }

    public void setCotacaoVenda(double cotacaoVenda) {
        this.cotacaoVenda = cotacaoVenda;
    }

    public void setDataHoraCotacao(String dataHoraCotacao) {
        this.dataHoraCotacao = dataHoraCotacao;
    }

    public void setTipoBoletim(String tipoBoletim) {
        this.tipoBoletim = tipoBoletim;
    }
    
}
