<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by <EMAIL> (PROCERGS) -->
<xs:schema xmlns="http://www.portalfiscal.inf.br/cte" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="tiposGeralCTe_v4.00.xsd"/>
	<xs:include schemaLocation="cteTiposBasico_v4.00.xsd"/>
	<xs:simpleType name="TTermoAutFreta">
		<xs:annotation>
			<xs:documentation>Tipo Termo  de Autorização de Fretamento – TAF</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="12"/>
			<xs:pattern value="[0-9]{12}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TNroRegEstadual">
		<xs:annotation>
			<xs:documentation>Tipo Número de Registro Estadual</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="25"/>
			<xs:pattern value="[0-9]{25}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:element name="rodoOS">
		<xs:annotation>
			<xs:documentation>Informações do modal Rodoviário</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:choice>
					<xs:element name="TAF" type="TTermoAutFreta">
						<xs:annotation>
							<xs:documentation>Termo de Autorização de Fretamento – TAF</xs:documentation>
							<xs:documentation>Registro obrigatório do emitente do CT-e OS junto à ANTT, de acordo com a Resolução ANTT nº 4.777/2015</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="NroRegEstadual" type="TNroRegEstadual">
						<xs:annotation>
							<xs:documentation>Número do Registro Estadual </xs:documentation>
							<xs:documentation>Registro obrigatório do emitente do CT-e OS junto à Agência Reguladora  Estadual.					</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:choice>
				<xs:element name="veic" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Dados do Veículo</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="placa">
								<xs:annotation>
									<xs:documentation>Placa do veículo </xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="TPlaca"/>
								</xs:simpleType>
							</xs:element>
							<xs:element name="RENAVAM" minOccurs="0">
								<xs:annotation>
									<xs:documentation>RENAVAM do veículo </xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="TString">
										<xs:minLength value="9"/>
										<xs:maxLength value="11"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="prop" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Proprietário ou possuidor do Veículo.
Só preenchido quando o veículo não pertencer à empresa emitente do CT-e OS</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:choice>
											<xs:element name="CPF" type="TCpf">
												<xs:annotation>
													<xs:documentation>Número do CPF</xs:documentation>
													<xs:documentation>Informar os zeros não significativos.</xs:documentation>
												</xs:annotation>
											</xs:element>
											<xs:element name="CNPJ" type="TCnpjOpc">
												<xs:annotation>
													<xs:documentation>Número do CNPJ</xs:documentation>
													<xs:documentation>Informar os zeros não significativos.</xs:documentation>
												</xs:annotation>
											</xs:element>
										</xs:choice>
										<xs:choice>
											<xs:element name="TAF" type="TTermoAutFreta">
												<xs:annotation>
													<xs:documentation>Termo de Autorização de Fretamento – TAF</xs:documentation>
													<xs:documentation>De acordo com a Resolução ANTT nº 4.777/2015						</xs:documentation>
												</xs:annotation>
											</xs:element>
											<xs:element name="NroRegEstadual" type="TNroRegEstadual">
												<xs:annotation>
													<xs:documentation>Número do Registro Estadual </xs:documentation>
													<xs:documentation>Registro obrigatório do emitente do CT-e OS junto à Agência Reguladora  Estadual						</xs:documentation>
												</xs:annotation>
											</xs:element>
										</xs:choice>
										<xs:element name="xNome">
											<xs:annotation>
												<xs:documentation>Razão Social ou Nome do proprietário</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TString">
													<xs:maxLength value="60"/>
													<xs:minLength value="2"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:sequence minOccurs="0">
											<xs:element name="IE">
												<xs:annotation>
													<xs:documentation>Inscrição Estadual</xs:documentation>
												</xs:annotation>
												<xs:simpleType>
													<xs:restriction base="TIeDest"/>
												</xs:simpleType>
											</xs:element>
											<xs:element name="UF" type="TUf">
												<xs:annotation>
													<xs:documentation>UF</xs:documentation>
												</xs:annotation>
											</xs:element>
										</xs:sequence>
										<xs:element name="tpProp">
											<xs:annotation>
												<xs:documentation>Tipo Proprietário ou possuidor</xs:documentation>
												<xs:documentation>Preencher com:
												0-TAC – Agregado;
												1-TAC Independente; ou 
												2 – Outros.</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:whiteSpace value="preserve"/>
													<xs:enumeration value="0"/>
													<xs:enumeration value="1"/>
													<xs:enumeration value="2"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="UF" type="TUf" minOccurs="0">
								<xs:annotation>
									<xs:documentation>UF em que veículo está licenciado</xs:documentation>
									<xs:documentation>Sigla da UF de licenciamento do veículo.</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="infFretamento" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Dados do fretamento (apenas para Transporte de Pessoas)</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="tpFretamento">
								<xs:annotation>
									<xs:documentation>Tipo Fretamento</xs:documentation>
									<xs:documentation>Preencher com:
 1 - Eventual 2 - Continuo								</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="preserve"/>
										<xs:enumeration value="1"/>
										<xs:enumeration value="2"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="dhViagem" type="TDateTimeUTC" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Data e hora da viagem (Apenas para fretamento eventual)</xs:documentation>
									<xs:documentation>Formato AAAA-MM-DDTHH:MM:DD TZD</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
