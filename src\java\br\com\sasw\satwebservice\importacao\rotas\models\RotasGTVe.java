/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.importacao.rotas.models;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RotasGTVe {

    private int acao;
    private int codfil;
    private String numero;
    private String data;
    private String horaini;
    private String horafim;
    private List<TrajetosGTVe> trajetos;

    public int getAcao() {
        return acao;
    }

    public void setAcao(int acao) {
        this.acao = acao;
    }

    public int getCodfil() {
        return codfil;
    }

    public void setCodfil(int codfil) {
        this.codfil = codfil;
    }

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getHoraini() {
        return horaini;
    }

    public void setHoraini(String horaini) {
        this.horaini = horaini;
    }

    public String getHorafim() {
        return horafim;
    }

    public void setHorafim(String horafim) {
        this.horafim = horafim;
    }

    public List<TrajetosGTVe> getTrajetos() {
        if(trajetos == null){
            trajetos = new ArrayList<>();
        }
        return trajetos;
    }

    public void setTrajetos(List<TrajetosGTVe> trajetos) {
        this.trajetos = trajetos;
    }
    
}
