<!DOCTYPE html>
<!
To change this license header, choose License Headers in Project Properties.
To change this template file, choose Tools | Templates
and open the template in the editor.
-->
<html>
    <head>
        <link rel="icon" href="https://mobile.sasw.com.br/SatMobWeb/assets/img/favicon.png" />
        <title>SatMOB</title>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
        <link type="text/css" href="https://mobile.sasw.com.br/SatMobWeb/assets/css/batida-ponto.css" rel="stylesheet" />
        <link type="text/css" href="https://mobile.sasw.com.br/SatMobWeb/assets/css/bootstrap.css" rel="stylesheet" />
        <link type="text/css" href="https://mobile.sasw.com.br/SatMobWeb/assets/css/style.css" rel="stylesheet" />
        <link type="text/css" href="https://mobile.sasw.com.br/SatMobWeb/assets/css/stylePage.css" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/openlayers/openlayers.github.io@master/en/v6.4.3/css/ol.css" type="text/css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.css" />

        <!--<script type="text/javascript" src="https://mobile.sasw.com.br/SatMobWeb/assets/scripts/batida-ponto-1.0.0.js"></script>-->
        <script type="text/javascript" src="assets/js/batida-ponto-1.0.0.js"></script>
        <script type="text/javascript" src="https://mobile.sasw.com.br/SatMobWeb/assets/js/jquery-3.5.1.min.js"></script>
        <script type="text/javascript" src="https://mobile.sasw.com.br/SatMobWeb/assets/scripts/south-1.0.0.js"></script>
        <script src="https://cdn.jsdelivr.net/gh/openlayers/openlayers.github.io@master/en/v6.4.3/build/ol.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-confirm/3.2.3/jquery-confirm.min.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
    </head>
    <body style="background: #1391ff; width: 100vw; height: 100vh; overflow: hidden">
        <div style="background: #1391ff; width: 100vw; height: 100vh; overflow: hidden">
            <img id="logo_satweb" src="assets/img/logo_satweb.png" style="position: absolute; left: 0; right: 0; margin: auto; margin-top: 40px; width: 300px;"/>

            <svg version="1.1" id="loading" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                 viewBox="0 0 100 100" enable-background="new 0 0 100 100" xml:space="preserve">
            <rect fill="none" stroke="#fff" stroke-width="2" x="25" y="25" width="50" height="50">
            <animateTransform
                attributeName="transform"
                dur="0.5s"
                from="0 50 50"
                to="180 50 50"
                type="rotate"
                id="strokeBox"
                attributeType="XML"
                begin="rectBox.end"/>
            </rect>
            <rect x="25" y="25" fill="#fff" width="50" height="50">
            <animate
                attributeName="height"
                dur="1.3s"
                attributeType="XML"
                from="50" 
                to="0"
                id="rectBox" 
                fill="freeze"
                begin="0s;strokeBox.end"/>
            </rect>
            </svg>

            <div id="alertaErro" class="col-md-12 col-sm-12 col-xs-12" style="width: 400px; height: 200px; position: absolute; left: 0; right: 0; top: 0; bottom: 0; margin: auto;
                 max-width: 100%; max-height: 100%; overflow: hidden; padding: 10px; border: 2px solid #DDD; background: #FFF; border-radius: 6px;
                 border-top: 4px solid red !important; line-height: 200px; text-align: center; display: none">
                <span id="mensageErro"> Ocorreu um erro ao tentar carregar a batida de ponto.</span>
            </div>
        </div>

        <div id="alertaLogin" class="col-md-12 col-sm-12 col-xs-12" style="width: 400px; height: 200px;
             position: absolute; left: 0; right: 0; top: 0; bottom: 0; margin: auto; padding: 16px;
             max-width: 95vw; max-height: 100%; overflow: hidden; padding: 10px; border: 2px solid #DDD;
             background: #FFF; border-radius: 6px; border-top: 4px solid green !important;text-align: center;">
            <div class="alert">
                <span class="closebtn">&times;</span> 
                A senha não pode estar em branco
            </div>  

            <table  style="line-height: 200px; height: 200px; width: 400px; max-width: 95vw;">
                <tr style="line-height: normal;">
                    <td><label for="psw"><b>Senha: </b></label></td>
                    <td style="width: 135px">
                        <input type="password" style="height: 30px; width: 130px" pattern="[0-9]*" inputmode="numeric" placeholder="Digite a senha" name="psw" id="psw" required />
                    </td>
                    <td><div id="btnLogin" style="height: 30px; line-height: initial;">Entrar</div></td>
                </tr>
            </table>
        </div>
        <div id="content" > </div>
        <script>

            var url_string = window.location.href;
            var url = new URL(url_string);
            var matr = url.searchParams.get("token").split('_')[1].replace('matr=', '');
            var token = url.searchParams.get("token").split('_')[0];

            $(document).ready(function () {
                $("#loading").show();
                $("#alertaLogin").hide();
                carregarRelatorio(matr, token, 'access-sup');

                $("#btnLogin").click(function () {
                    if (!$("input[name='psw']").val()) {
                        $(".alert").show();
                        setTimeout(function () {
                            $(".alert").hide();
                        }, 3000);
                    } else {
                        try {
                            $("#loading").show();
                            $("#alertaLogin").hide();
                            carregarRelatorio(matr, token, $("input[name='psw']").val());
                        } catch (err) {
                            $("#loading").hide();
                            $("#alertaErro").show();
                        }
                    }
                });

                $(".closebtn").click(function () {
                    $(".alert").hide();
                });

                $("#psw").keydown(function (e) {
                    if (e.keyCode === 13) {
                        $('#btnLogin').click();
                        return false;
                    }
                });
            });
        </script>
    </body>
</html>