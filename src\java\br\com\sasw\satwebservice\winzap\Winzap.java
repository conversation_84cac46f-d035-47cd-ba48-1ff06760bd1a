/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.winzap;

import Arquivo.ArquivoLog;
import Arquivo.ArquivoLogs;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Filiais;
import SasDaos.FiliaisDao;
import br.com.sasw.pacotesuteis.sasdaos.EventoMsgWhatsAppDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import static br.com.sasw.satwebservice.Utilidades.obterParametros;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import javax.net.ssl.HttpsURLConnection;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;

/**
 *
 * <AUTHOR>
 */
@Path("/ws/winzap")
public class Winzap {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;
    private final EventoMsgWhatsAppDao eventoMsgWhatsAppDao;

    public Winzap() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\winzap\\"
                + getDataAtual("SQL") + "\\erro.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        eventoMsgWhatsAppDao = new EventoMsgWhatsAppDao();
    }

    private static final String TOKEN = "z5ftof3";
    private static final String ENDPOINT = "https://v1.utalk.chat/send/";
    private static final String URL_ZAP = "https://v1.utalk.chat/send/z5ftof3?";

    @POST
    @Path("/send/{empresa}/")
    public Response send(@Context UriInfo context, @PathParam(value = "empresa") String empresa,
            String params) throws UnsupportedEncodingException {

        this.logerro.Grava("SEND: " + params, this.caminho);

        String t = URLDecoder.decode(params, Charset.forName("UTF-8").toString());
        
        Map parametros = obterParametros(t);

        String mensagem = (String) parametros.getOrDefault("mensagem", null);
        String codfil = (String) parametros.getOrDefault("codfil", null);
        String operador = (String) parametros.getOrDefault("operador", null);
        String local = (String) parametros.getOrDefault("local", null);
        String latitude = (String) parametros.getOrDefault("latitude", null);
        String longitude = (String) parametros.getOrDefault("longitude", null);

        StringBuilder resposta = new StringBuilder();
        String telefone = null;
        if (mensagem == null) {
            mensagem = "";
        }

        try {
            this.persistencia = this.pool.getConexao("SATELLITE");

            Persistencia p = this.pool.getConexao(empresa);
            if (p == null) {
                telefone = "554898642888";
                telefone = "5561955489864288892199265";
                mensagem = "SEND: empresa " + empresa + " sem conexão.";
            } else {
                FiliaisDao filiaisDao = new FiliaisDao();
                Filiais filial = null;
                try {
                    filial = filiaisDao.buscarFilial(codfil, p);
                } catch (Exception e) {
                    this.logerro.Grava("Envio erro: " + e.getMessage(), this.caminho);
                }

                if (filial == null) {
                    telefone = "554898642888";
                    telefone = "5561992199265";
                    mensagem = "SEND: empresa  " + empresa + " sem info filial " + codfil + ".";
                } else {
                    telefone = filial.getFone2();

                    if (null == telefone || telefone.equals("")) {
                        telefone = "554898642888";
                        telefone = "5561992199265";
                        mensagem = "SEND: filial " + codfil + " empresa " + empresa + " sem telefone de contato (filiais.fone2)";
                    } else {
                        // Removendo caracteres especiais do número
                        telefone = telefone.replaceAll("[ \\(\\)\\+\\-\\.,]", "");
                        if (!telefone.startsWith("55")) {
                            telefone = "55" + telefone;
                        }
                    }
                }
            }

            if (operador != null) {
                mensagem = mensagem + "\r\n" + "Operador: " + operador;
            }
            if (local != null) {
                mensagem = mensagem + "\r\n" + "Local: " + local;
            }
            if (latitude != null && longitude != null) {
                mensagem = mensagem + "\r\n" + "http://maps.google.com/maps?&z=10&q=" + latitude + "+" + longitude + "&ll=" + latitude + "+" + longitude;
            }

            String id = this.eventoMsgWhatsAppDao.inserirMensagem("chat", TOKEN, mensagem, telefone, "@c.us",
                    empresa, codfil, RecortaAteEspaço(operador, 0, 10), this.persistencia);

            /*URL url = UriBuilder.fromUri(ENDPOINT + TOKEN)
                    .queryParam("cmd", "chat")
                    .queryParam("id", id)
                    .queryParam("to", telefone + "@c.us")
                    .queryParam("msg", mensagem)
                    .build().toURL();*/

            String urlZap = URL_ZAP + "cmd=chat&id=" + id + "&to=" + telefone.replace("+", "") + "&msg=" + mensagem.replace(" ", "%20");

            URL url = new URL(urlZap);
            
            HttpsURLConnection httpCon = (HttpsURLConnection) url.openConnection();
            httpCon.setRequestMethod("GET");

            BufferedReader reader = new BufferedReader(new InputStreamReader(httpCon.getInputStream()));
            
            int responseCode = httpCon.getResponseCode();

            BufferedReader in = new BufferedReader(new InputStreamReader(httpCon.getInputStream()));
            String linha;
            while ((linha = in.readLine()) != null) {
                resposta.append(linha);
            }
            in.close();
            this.logerro.Grava("Envio: " + url.toString(), this.caminho);
            this.logerro.Grava("Resposta envio: " + resposta, this.caminho);
        } catch (Exception e) {
            this.logerro.Grava("Erro envio: " + e.getMessage(), this.caminho);
        } finally {

            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(resposta)
                    .build();
        }
    }

    @POST
    @Path("/sos/{empresa}/")
    public Response sendSOS(@Context UriInfo context, @PathParam(value = "empresa") String empresa,
            String params) throws UnsupportedEncodingException {

        this.logerro.Grava("SOS recebido: " + params, this.caminho);

        Map parametros = obterParametros(URLDecoder.decode(params, Charset.forName("UTF-8").toString()));

        String codfil = (String) parametros.getOrDefault("codfil", null);
        String operador = (String) parametros.getOrDefault("operador", null);
        String local = (String) parametros.getOrDefault("local", null);
        String latitude = (String) parametros.getOrDefault("latitude", null);
        String longitude = (String) parametros.getOrDefault("longitude", null);

        StringBuilder resposta = new StringBuilder();
        String telefone = null;
        String mensagem = "Pedido de SOS";
        try {
            this.persistencia = this.pool.getConexao("SATELLITE");

            Persistencia p = this.pool.getConexao(empresa);
            if (p == null) {
                telefone = "554898642888";
                telefone = "5561992199265";
                mensagem = "SOS: empresa " + empresa + " sem conexão.";
            } else {
                FiliaisDao filiaisDao = new FiliaisDao();
                Filiais filial = null;
                try {
                    filial = filiaisDao.buscarFilial(codfil, p);
                } catch (Exception e) {
                    this.logerro.Grava("Envio erro: " + e.getMessage(), this.caminho);
                }

                if (filial == null) {
                    telefone = "554898642888";
                    telefone = "5561992199265";
                    mensagem = "SOS: empresa  " + empresa + " sem info filial " + codfil + ".";
                } else {
                    telefone = filial.getFone2();

                    if (null == telefone || telefone.equals("")) {
                        telefone = "554898642888";
                        telefone = "5561992199265";
                        mensagem = "SOS: filial " + codfil + " empresa " + empresa + " sem telefone de emergência (filiais.fone2)";
                    } else {
                        // Removendo caracteres especiais do número
                        telefone = telefone.replaceAll("[ \\(\\)\\+\\-\\.,]", "");
                        if (!telefone.startsWith("55")) {
                            telefone = "55" + telefone;
                        }
                    }
                }
            }

            if (operador != null) {
                mensagem = mensagem + "\r\n" + "Operador: " + operador;
            }
            if (local != null) {
                mensagem = mensagem + "\r\n" + "Local: " + local;
            }
            if (latitude != null && longitude != null) {
                mensagem = mensagem + "\r\n" + "http://maps.google.com/maps?&z=10&q=" + latitude + "+" + longitude + "&ll=" + latitude + "+" + longitude;
            }

            String id = this.eventoMsgWhatsAppDao.inserirMensagem("chat", TOKEN, mensagem, telefone, "@c.us",
                    empresa, codfil, RecortaAteEspaço(operador, 0, 10), this.persistencia);

            String urlZap = URL_ZAP + "cmd=chat&id=" + id + "&to=" + telefone.replace("+", "") + "&msg=" + mensagem.replace(" ", "%20");

            URL url = new URL(urlZap);
            
            /*URL url = UriBuilder.fromUri(ENDPOINT + TOKEN)
                    .queryParam("cmd", "chat")
                    .queryParam("id", id)
                    .queryParam("to", telefone + "@c.us")
                    .queryParam("msg", mensagem)
                    .build().toURL();*/

            HttpsURLConnection httpCon = (HttpsURLConnection) url.openConnection();
            httpCon.setRequestMethod("GET");
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(httpCon.getInputStream()));

            int responseCode = httpCon.getResponseCode();

            BufferedReader in = new BufferedReader(new InputStreamReader(httpCon.getInputStream()));
            String linha;
            while ((linha = in.readLine()) != null) {
                resposta.append(linha);
            }
            in.close();
            this.logerro.Grava("Envio: " + url.toString(), this.caminho);
            this.logerro.Grava("Resposta envio: " + resposta, this.caminho);
        } catch (Exception e) {
            this.logerro.Grava("Erro envio: " + e.getMessage(), this.caminho);
        } finally {

            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(resposta)
                    .build();
        }
    }

    @POST
    public void webhook(String param) throws UnsupportedEncodingException {

        this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\winzap\\"
                + getDataAtual("SQL") + "\\log.txt";
        this.logerro.Grava("webhook recebido: " + param, this.caminho);

        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String event = (String) parametros.getOrDefault("event", null);
        String token = (String) parametros.getOrDefault("token", null);
        String user = (String) parametros.getOrDefault("user", null);
        String contact_number = (String) parametros.getOrDefault("contact[number]", null);
        String contact_name = (String) parametros.getOrDefault("contact[name]", null);
        String contact_server = (String) parametros.getOrDefault("contact[server]", null);
        String chat_dtm = (String) parametros.getOrDefault("chat[dtm]", null);
        if (chat_dtm != null) {
            chat_dtm = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(Long.parseLong(chat_dtm) * 1000));
        }
        String chat_uid = (String) parametros.getOrDefault("chat[uid]", null);
        String chat_muid = (String) parametros.getOrDefault("chat[muid]", null);
        String chat_dir = (String) parametros.getOrDefault("chat[dir]", null);
        String chat_type = (String) parametros.getOrDefault("chat[type]", null);
        String chat_body = (String) parametros.getOrDefault("chat[body]", null);
        String ack = (String) parametros.getOrDefault("ack", null);
        String blob = (String) parametros.getOrDefault("blob", null);
        String chat_fn = (String) parametros.getOrDefault("chat[fn]", null);
        String fn = (String) parametros.getOrDefault("fn", null);
        String muid = (String) parametros.getOrDefault("muid", null);
        String id = (String) parametros.getOrDefault("id", null);

        this.persistencia = this.pool.getConexao("SATELLITE");

        if (event != null) {
            switch (event) {
                case "ack":
                    if (id != null && ack != null && muid != null) {
                        try {
                            this.logerro.Grava("ack atualizado: " + param, this.caminho);
                            this.eventoMsgWhatsAppDao.atualizarMensagem(muid, ack, id, this.persistencia);
                        } catch (Exception e) {
                            this.logerro.Grava("Erro: " + e.getMessage(), this.caminho);
                        }
                    }
                    break;
                case "chat":
                    if (event != null
                            && token != null
                            && user != null
                            && contact_number != null
                            && contact_name != null
                            && contact_server != null
                            && chat_dtm != null
                            && chat_uid != null
                            && chat_muid != null
                            && chat_dir != null
                            && chat_type != null
                            && chat_body != null
                            && ack != null) {
                        try {
                            this.logerro.Grava("chat atualizado: " + param, this.caminho);
                            this.eventoMsgWhatsAppDao.atualizarMensagem(chat_muid, contact_server, chat_dtm, chat_dir,
                                    chat_body, contact_number, chat_uid, ack, event,
                                    user, chat_type, token, contact_name, this.persistencia);
                        } catch (Exception e) {
                            this.logerro.Grava("Erro: " + e.getMessage(), this.caminho);
                        }
                    } else if (event != null
                            && token != null
                            && user != null
                            && contact_number != null
                            && contact_name != null
                            && contact_server != null
                            && chat_dtm != null
                            && chat_uid != null
                            && chat_dir != null
                            && chat_type != null
                            && chat_body != null
                            && ack != null) {
                        try {
                            this.logerro.Grava("chat recebido: " + param, this.caminho);
                            this.eventoMsgWhatsAppDao.receberMensagem(contact_server, chat_dtm, chat_dir,
                                    chat_body, contact_number, chat_uid, ack, event,
                                    user, chat_type, token, contact_name, this.persistencia);
                            
                            if(ack.equals("-1")){
                                this.logerro.Grava("chat respondido: " + param, this.caminho);
                                br.com.sasw.pacotesuteis.utilidades.winzap.Winzap
                                        .enviarMensagem("Não envie mensagens a este número. Esse canal é exclusivo para envio de mensagens automáticas.\nSASw Tecnologia",
                                                contact_number, "1", "WINZAP", null, null, null, "SASW", this.persistencia, new ArquivoLogs(this.caminho));
                            }
                        } catch (Exception e) {
                            this.logerro.Grava("Erro: " + e.getMessage(), this.caminho);
                        }
                    } else if (chat_muid != null
                            && chat_dtm != null
                            && chat_uid != null
                            && chat_dir != null
                            && ack != null) {
                        try {
                            this.logerro.Grava("chat atualizado: " + param, this.caminho);
                            this.eventoMsgWhatsAppDao.atualizarMensagem(chat_muid, chat_dtm, chat_uid, chat_dir, ack, this.persistencia);
                        } catch (Exception e) {
                            this.logerro.Grava("Erro: " + e.getMessage(), this.caminho);
                        }
                    }
                    break;

            }
        }
    }
}
