/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.rotas.beans;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class BaixaServicoBean {
    
    private String param;
    private String codpessoa;
    private String codCliAuth;
    private String operador;
    private String dataAtual;
    private String horaAtual;
    private String sequencia;
    private String codfil;
    private String parada;
    private String er;
    private String tiposerv;
    private String hora1;
    private String hrcheg;
    private String hrsaida;
    private String hrsaidavei;
    private String km;
    private String obs;
    private String latitude;
    private String longitude;
    private List<Guias> guias;
    private String FormaPgto;
    private String ObsFormaPgto;
    private String valorPagamento;
    
    private String portalGuias;
    private String somenteExclusao;

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getCodpessoa() {
        return codpessoa;
    }

    public void setCodpessoa(String codpessoa) {
        this.codpessoa = codpessoa;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public String getSequencia() {
        return sequencia;
    }

    public String getDataAtual() {
        return dataAtual;
    }

    public void setDataAtual(String dataAtual) {
        this.dataAtual = dataAtual;
    }

    public String getHoraAtual() {
        return horaAtual;
    }

    public void setHoraAtual(String horaAtual) {
        this.horaAtual = horaAtual;
    }

    public void setSequencia(String sequencia) {
        this.sequencia = sequencia;
    }

    public String getCodfil() {
        return codfil;
    }

    public void setCodfil(String codfil) {
        this.codfil = codfil;
    }

    public String getParada() {
        return parada;
    }

    public void setParada(String parada) {
        this.parada = parada;
    }

    public String getEr() {
        return er;
    }

    public void setEr(String er) {
        this.er = er;
    }

    public String getTiposerv() {
        return tiposerv;
    }

    public void setTiposerv(String tiposerv) {
        this.tiposerv = tiposerv;
    }

    public String getHora1() {
        return hora1;
    }

    public void setHora1(String hora1) {
        this.hora1 = hora1;
    }

    public String getHrcheg() {
        return hrcheg;
    }

    public void setHrcheg(String hrcheg) {
        this.hrcheg = hrcheg;
    }

    public String getHrsaida() {
        return hrsaida;
    }

    public void setHrsaida(String hrsaida) {
        this.hrsaida = hrsaida;
    }

    public String getHrsaidavei() {
        return hrsaidavei;
    }

    public void setHrsaidavei(String hrsaidavei) {
        this.hrsaidavei = hrsaidavei;
    }

    public String getKm() {
        return km;
    }

    public void setKm(String km) {
        this.km = km;
    }

    public String getObs() {
        return obs;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }

    public List<Guias> getGuias() {
        return guias;
    }

    public void setGuias(List<Guias> guias) {
        this.guias = guias;
    }

    public String getCodCliAuth() {
        return codCliAuth;
    }

    public void setCodCliAuth(String codCliAuth) {
        this.codCliAuth = codCliAuth;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getFormaPgto() {
        return FormaPgto;
    }

    public void setFormaPgto(String FormaPgto) {
        this.FormaPgto = FormaPgto;
    }

    public String getObsFormaPgto() {
        return ObsFormaPgto;
    }

    public void setObsFormaPgto(String ObsFormaPgto) {
        this.ObsFormaPgto = ObsFormaPgto;
    }

    public String getValorPagamento() {
        return valorPagamento;
    }

    public void setValorPagamento(String valorPagamento) {
        this.valorPagamento = valorPagamento;
    }

    public String getPortalGuias() {
        return portalGuias;
    }

    public void setPortalGuias(String portalGuias) {
        this.portalGuias = portalGuias;
    }

    public String getSomenteExclusao() {
        return somenteExclusao;
    }

    public void setSomenteExclusao(String somenteExclusao) {
        this.somenteExclusao = somenteExclusao;
    }    
}
