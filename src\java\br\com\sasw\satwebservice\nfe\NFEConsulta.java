/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.nfe;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import br.com.sasw.pacotesuteis.sasbeans.XMLNFE;
import br.com.sasw.pacotesuteis.sasdaos.XMLNFEDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.satwebservice.nfe.deserializers.NFeConsultaDeserializer;
import br.com.sasw.satwebservice.nfe.deserializers.NFeEnvioDeserializer;
import br.com.sasw.satwebservice.nfe.exception.NFeException;
import br.com.sasw.satwebservice.nfe.models.ConsultaModel;
import br.com.sasw.satwebservice.nfe.models.EnvioModel;
import br.com.sasw.satwebservice.nfe.models.ProtocoloModel;
import br.com.sasw.satwebservice.nfe.models.RejeicaoModel;
import br.com.sasw.satwebservice.nfe.models.RetornoModel;
import br.com.sasw.satwebservice.nfe.serializer.RetornoSerializer;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.internal.StringMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.Response;
import org.json.XML;

/**
 *
 * <AUTHOR>
 */
public class NFEConsulta {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;

    public NFEConsulta() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\NFe\\"
                + getDataAtual("SQL") + "\\erro.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    public Response consulta(String input) {

        Gson gsonRetorno = new GsonBuilder()
                .registerTypeAdapter(RetornoModel.class, new RetornoSerializer())
                .create();
        Gson gsonNFe = new GsonBuilder()
                .registerTypeAdapter(EnvioModel.class, new NFeConsultaDeserializer())
                .create();
        Gson gsonNFeEnvio = new GsonBuilder()
                .registerTypeAdapter(EnvioModel.class, new NFeEnvioDeserializer())
                .create();

        Map retorno = new HashMap<>();
        List processamentos = new ArrayList<>();
        try {
//             Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao("SATEXCEL");
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido");
            }

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\NFe\\"
                    + getDataAtual("SQL") + "\\consulta.txt";

            this.logerro.Grava("Consulta: "+input, this.caminho);

            JsonElement inputJson = new JsonParser().parse(input).getAsJsonObject().get("NFE");
            ConsultaModel nfe;
            ProtocoloModel protocolo;
            RejeicaoModel rejeicao;
            XMLNFEDao xmlNFEDao = new XMLNFEDao();
            XMLNFE xmlNFE;
            String status;
            StringMap xmlRetorno, nfeProc, retEnviNFe, protNFe, infProt, NFe, infNFeSupl;

            if (inputJson instanceof JsonArray) {
                JsonArray nfeJsonArray = inputJson.getAsJsonArray();
                JsonObject nfeJsonObectj;
                for (int i = 0; i < nfeJsonArray.size(); i++) {

                    protocolo = null;
                        
                    nfeJsonObectj = nfeJsonArray.get(i).getAsJsonObject();
                    try {
                        nfe = gsonNFe.fromJson(nfeJsonObectj, ConsultaModel.class);
                        xmlNFE = xmlNFEDao.buscarXMLNFE(nfe.getChave(), persistencia);

                        if (xmlNFE == null) {
                            // Chave não encontrada
                            this.logerro.Grava("Chave não encontrada: " + nfe.getChave(), this.caminho);
                            throw new NFeException(new NFeException.NFeError(nfeJsonObectj, new RejeicaoModel("NFE.chave", nfe.getChave())));

                        } else if (xmlNFE.getXML_Retorno() == null || xmlNFE.getXML_Retorno().equals("")) {
                            // Ainda em processamento
                            processamentos.add(new RetornoModel(gsonNFeEnvio.fromJson(new JsonParser().parse(xmlNFE.getXML_Envio()), EnvioModel.class),
                                    3, xmlNFE.getTOKEN()));
                        } else {

                            try {
                                xmlRetorno = new Gson().fromJson(XML.toJSONObject(xmlNFE.getXML_Retorno(), true).toString(), StringMap.class);
                                nfeProc = (StringMap) xmlRetorno.get("nfeProc");
                                if (nfeProc == null) {
                                    retEnviNFe = (StringMap) xmlRetorno.get("retEnviNFe");
                                    protNFe = (StringMap) retEnviNFe.get("protNFe");
                                } else {
                                    protNFe = (StringMap) nfeProc.get("protNFe");
                                }
                                infProt = (StringMap) protNFe.get("infProt");

                                status = infProt.getOrDefault("cStat", "").toString();

                                if (status.equals("100")) {
                                    // 100 é o stauts de sucesso
                                    protocolo = new ProtocoloModel();
                                    if (!infProt.getOrDefault("nProt", "").toString().equals("")) {
                                        protocolo.setNumeronfe(infProt.get("nProt").toString());
                                    }
                                    if (!infProt.getOrDefault("nProt", "").toString().equals("")) {
                                        protocolo.setSerie(xmlNFE.getSerie());
                                    }
                                    if (!infProt.getOrDefault("chNFe", "").toString().equals("")) {
                                        protocolo.setToken(infProt.get("chNFe").toString());
                                        protocolo.setLinknfe("https://mobile.sasw.com.br/SatMobWeb/danfe/impressao.html?chave="+infProt.get("chNFe").toString());
                                    }
                                    if (!infProt.getOrDefault("dhRecbto", "").toString().equals("")) {
                                        protocolo.setData(infProt.get("dhRecbto").toString());
                                    }
                                    if (nfeProc != null
                                            && nfeProc.get("NFe") != null) {
                                        NFe = (StringMap) nfeProc.get("NFe");
                                        infNFeSupl = (StringMap) NFe.get("infNFeSupl");
                                        protocolo.setQRCode(infNFeSupl.get("qrCode").toString());
                                    }

                                    processamentos.add(new RetornoModel(gsonNFeEnvio.fromJson(new JsonParser().parse(xmlNFE.getXML_Envio()), EnvioModel.class),
                                            5, xmlNFE.getTOKEN(), protocolo));
                                } else {
                                    protocolo = new ProtocoloModel();
                                    if (!infProt.getOrDefault("chNFe", "").toString().equals("")) {
                                        protocolo.setToken(infProt.get("chNFe").toString());
                                    }
                                    if (!infProt.getOrDefault("dhRecbto", "").toString().equals("")) {
                                        protocolo.setData(infProt.get("dhRecbto").toString());
                                    }
                                    rejeicao = null;
                                    if (!infProt.getOrDefault("xMotivo", "").toString().equals("")) {
                                        rejeicao = new RejeicaoModel(infProt.get("xMotivo").toString());
                                    }
                                    processamentos.add(new RetornoModel(gsonNFeEnvio.fromJson(new JsonParser().parse(xmlNFE.getXML_Envio()), EnvioModel.class),
                                            9, xmlNFE.getTOKEN(), protocolo, rejeicao));
                                }
                            } catch (Exception exx) {
                                this.logerro.Grava(exx.getMessage(), this.caminho);
                                processamentos
                                        .add(new RetornoModel(new NFeException(new NFeException.NFeError(nfeJsonObectj, new RejeicaoModel(xmlNFE.getXML_Retorno())))));
                            }

//                            System.out.println(infProt);
                        }
//                    nfeJsonObectj.addProperty("chave", chaveNFE);
//                    throw new NFeException(new NFeException.NFeError(nfeJsonObectj, 3));
                    } catch (NFeException nfeException) {
                        this.logerro.Grava(nfeException.getMessage(), this.caminho);
                        processamentos.add(new RetornoModel(nfeException, nfeJsonObectj));
                    } catch (Exception ex) {
                        this.logerro.Grava(ex.getMessage(), this.caminho);
                        processamentos
                                .add(new RetornoModel(new NFeException(new NFeException.NFeError(nfeJsonObectj, new RejeicaoModel("Erro interno")))));
                    }
                }
            }

            retorno.put("NFE", processamentos);

        } catch (Exception e) {
            // Salvando em log o que foi mandado
            retorno.clear();
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);
        } finally {
            String resposta = gsonRetorno.toJson(retorno);
            this.logerro.Grava("Resposta: "+resposta, this.caminho);

            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(resposta)
                    .build();
        }
    }
}
