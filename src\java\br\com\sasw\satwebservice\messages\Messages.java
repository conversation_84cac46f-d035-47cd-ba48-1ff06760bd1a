/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.messages;

import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.formatarString;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.ResourceBundle;

/**
 *
 * <AUTHOR>
 */
public class Messages {

    public Messages(){
        this.idioma = "PT";
    }
    
    public Messages(String idioma) {
        this.idioma = idioma;
    }
    
    private String idioma;
    
    public String getMessage(String key) {
        String message = "";
        try{
            message = ResourceBundle.getBundle("br.com.sasw.satwebservice.messages.messages", new Locale(idioma)).getString(key);
        } catch (Exception e){
            message = key;
        }
        return message;
    }
    
    public String getSimboloMoeda(String moeda){
        String simbolo;
        switch(moeda){
            case "USD":
                simbolo = "$";
                break;
            case "MXN":
                simbolo = "$";
                break;
            case "EUR":
                simbolo = "€";
                break;
            case "GBP":
                simbolo = "£";
                break;
            case "CLP":
                simbolo = "$";
                break;
            case "COP":
                simbolo = "$";
                break;
            case "BRL":
            default:
                simbolo = "R$";
        }
        return simbolo;
    }
    
    /**
     * Retorna o número de telefone formatado
     * @param numero
     * @return 
     */
    public String getTelefone(String numero) {
        String message;
        try{
            switch(idioma){
                case "en":
                    message = formatarString(numero, "(###) ###-####");
                    break;
                default:
                    message = formatarString(numero, "(##) ########?");
            }
        } catch (Exception e){
            message = numero;
        }
        return message;
    }
    
    public String getCEP(String cep) {
        String message;
        try{
            switch(idioma){
                case "en":
                    message = cep.startsWith("000") ? cep.replace("000", "") : cep;
                    break;
                default:
                    message = formatarString(cep, "#####-###");
            }
        } catch (Exception e){
            message = cep;
        }
        return message;
    }
    
    /**
     * Retorna a data no formato do idioma
     * @param data
     * @param formatoEntrada - formato de entrada: yyyy-MM-dd, yyyyMMdd, etc...
     * @return 
     */
    public String getData(String data, String formatoEntrada) {
        String message;
        try{
            LocalDate  datetime = LocalDate.parse(data, DateTimeFormatter.ofPattern(formatoEntrada));
            switch(idioma){
                case "en":
                    message = datetime.format(DateTimeFormatter.ofPattern("MM/dd/yyyy"));
                    break;
                default:
                    message = datetime.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
            }
        } catch (Exception e){
            message = data;
        }
        return message;
    }
    
    /**
     * Retorna a hora no formato do idioma
     * @param hora
     * @param formatoEntrada - formato de entrada: HH:mm hh:mma.
     * @return 
     */
    public String getHora(String hora, String formatoEntrada) {
        String message;
        try{
            LocalTime  datetime = LocalTime.parse(hora, DateTimeFormatter.ofPattern(formatoEntrada));
            switch(idioma){
                case "en":
                    message = datetime.format(DateTimeFormatter.ofPattern("hh:mma"));
                    break;
                default:
                    message = datetime.format(DateTimeFormatter.ofPattern("HH:mm"));
            }
        } catch (Exception e){
            message = hora;
        }
        return message;
    }
        

    public String getIdioma() {
        return idioma;
    }

    public void setIdioma(String idioma) {
        this.idioma = idioma;
    }
}
