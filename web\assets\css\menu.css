/*
*/
/* 
    Created on : Feb 17, 2017, 10:13:17 AM
    Author     : <PERSON>
*/

@media only screen and (max-width: 2000px) and (min-width: 701px) {
                    [id*="tabelaCxForte"] thead tr th:nth-child(1),
                    [id*="tabelaCxForte"] thead tr th:nth-child(2),
                    [id*="tabelaCxForte"] tbody tr td:nth-child(1),
                    [id*="tabelaCxForte"] tbody tr td:nth-child(2){
                        min-width:90px !important;
                        width:90px !important;
                        max-width:90px !important;
                    }
                }

[id*="btnLogout"]{
    display:block;
}

[id*="btnSair"] img{
    display:block;
}

div[ref="operacoes"],
div[ref="comercial"],
div[ref="recursos"],
div[ref="relatorios"]{
    animation: slideInLeft 0.8s ease !important;
}

div[ref="faturamento"],
div[ref="caixa_forte"],
div[ref="cadastros"],
div[ref="faturamento"],
div[ref="refeicoes"],
div[ref="configuracoes"],
div[ref="cofres"],
div[ref="tesouraria"]{
    animation: slideInRight 0.8s ease !important;
}

@media only screen and (max-width: 700px) and (min-width: 10px) {                                      
    .sate-element{
        margin-left:-20px !important;
        text-align:center !important;
    }

    .sate-element img{
        max-width:40px !important;
    }

    .sate-element label{
        font-size:9pt !important;
        font-weight: bold !important;
        line-height:10px !important;
        text-align:center !important;
    }

    div[ref="configuracoes"]{
        position:absolute !important;
        top:270px;
        left:-120px !important;
    }

    div[ref="configuracoes"] img{
        margin-left:16px !important;
    }
    
    div[ref="caixa_forte"]{
        position:absolute !important;
        top:270px;
        left:100px !important;
    }

    div[ref="caixa_forte"] img{
        margin-left:0px !important;
    }

    div[ref="tesouraria"]{
        position:absolute !important;
        top:355px;
        left:38px;
        right:0;
        margin:auto !important;
    }

    div[ref="tesouraria"] img{
        margin-left:5px !important;
    }
    
    div[ref="comercial"]{
        margin-left:0px !important;
    }

    div[ref="operacoes"]{
        margin-left:10px !important;
    }

    div[ref="cadastros"]{
        position:absolute !important;
        right:20px !important;
        margin-top: 28px;
    }
    
    div[ref="cadastros"] img{
        margin-left:10px !important;
    }

    div[ref="cofres"]{
        margin-top: 14px;
        margin-left:30px !important;
    }

    div[ref="cofres"] img{
        margin-left:8px !important;
    }

    div[ref="faturamento"]{
        position:absolute !important;
        right:120px !important;
        top:0px;
    }
    
    div[ref="refeicoes"]{
        position:absolute !important;
        right:50px !important;
        top:43px;
    }

    div[ref="faturamento"] img{
        margin-left:5px !important;
    }
    
    div[ref="refeicoes"] img{
        margin-left:5px !important;
    }
    
    div[ref="relatorios"]{
        position:absolute !important;
        top:270px;
        right:70px !important;
    }

    div[ref="relatorios"] img{
        margin-left:0px !important;
    }

    div[ref="botoes-rodape"]{
        position:absolute !important;
        white-space:nowrap!important;
        position:absolute;
        width:100%;
        bottom:0px !important;
        background-color:red;
    }

    div[id*="cadastro"] img,
    div[id*="exportar"] img{
        max-width:40px !important;
        margin-top:40px !important;
    }

    div[id*="cadastro"] table,
    div[id*="exportar"] table{
        max-width:350px !important;
        margin:0px !important;
        overflow:hidden !important
    }

    [id*="formOperacoes"] div,
    [id*="formCadastros"] div,
    [id*="formRelatorios"] div,
    [id*="formComercial"] div,
    [id*="formRH"] div,
    [id*="formConfiguracoes"] div,
    [id*="formOutrasBases"] div,
    [íd*="formFaturamento"] div,
    [id*="folhadeponto"] div,
    [id*="formRelatoriosPosto"] div,
    [id*="uploadForm"] div,
    [id*="formImportar"] div,
    [id*="exportarFiliais"] div,
    [id*="exportarPstServ"] div,
    [id*="exportarPessoa"] div,
    [id*="formExportacao"] div,
    [id*="formExibicaoRelatorio"] div,
    [id*="exportarFuncion"] div,
    [id*="formCaixaForte"] div{
        overflow:hidden !important;
    }

    #divLogo{
        background-size:55% !important;
        margin-top:80px !important;
    }

    [id*="btnLogout"]{
        position:absolute !important;
        bottom:30px !important;
        right:12px !important;
    }

    [id*="btnSair"] img{
        position:absolute !important;
        bottom:34px !important;
        left:25px !important;
        width:30px !important;
        height:30px !important;
    }
    
    div[ref="caixa_forte"]{
        animation: slideInLeft 0.8s ease !important;
    }
    
    div[ref="relatorios"]{
        animation: slideInRight 0.8s ease !important;
    }
    
    div[ref="tesouraria"]{
        animation: slideInUp 0.8s ease !important;
    }
}

[id*="btnLogout"],
[id*="btnSair"] img{
    animation: zoomIn 1s ease !important;
}

.ui-inputfield, .ui-inputtext{
    color: #333333 !important;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75) !important;
    font-weight: normal !important;
    cursor: text !important;
    background: none #ffffff !important;
    border: 1px solid #cccccc !important;
    -webkit-border-radius: 3px !important;
    -moz-border-radius: 3px !important;
    border-radius: 3px !important;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075) !important;
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075) !important;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075) !important;
    -webkit-transition: border linear 0.2s, box-shadow linear 0.2s !important;
    -moz-transition: border linear 0.2s, box-shadow linear 0.2s !important;
    -ms-transition: border linear 0.2s, box-shadow linear 0.2s !important;
    -o-transition: border linear 0.2s, box-shadow linear 0.2s !important;
    transition: border linear 0.2s, box-shadow linear 0.2s !important;
}

.textoazul{
    color: #145B9B;
    font-family: Tahoma, Geneva, sans-serif !important;
    font-size: 14px !important;
}

.custom-button {
    height: 40px !important;
    width: 114px !important;
    text-shadow: none !important;
    color: #6a4f02 !important;
    background: #ebdc61 !important;
    background: -moz-linear-gradient(top, #ebdc61 0%, #ab8611 100%) !important
        background: -webkit-linear-gradient(top, #ebdc61 0%,#ab8611 100%) !important;
    background: linear-gradient(to bottom, #ebdc61 0%,#ab8611 100%) !important;
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ebdc61', endColorstr='#ab8611',GradientType=0 );
}

.linkazul:hover {
    color: #022a48;
    font-weight: bold;
    text-decoration: none;
    text-align: left;
}

.linkazul, .ui-widget-content a{
    padding: 0;
    margin: 0;
    text-decoration: none;
    -webkit-transition: background-color .4s linear, color .4s linear;
    -moz-transition: background-color .4s linear, color .4s linear;
    -o-transition: background-color .4s linear, color .4s linear;
    -ms-transition: background-color .4s linear, color .4s linear;
    transition: background-color .4s linear, color .4s linear;
    color: #022a48;
    cursor: pointer;
    font-family: Tahoma, Geneva, sans-serif !important;
    font-size: 14px !important;
}

.nestedAccordion .ui-accordion-header{
    font-size: 15px;
    margin-left: 30px;
}

.accordion .ui-state-default{
    border: none;
    background: transparent;
    color: #022a48;
    background: rgba(128, 128, 240, 0.5);
    font-family: Tahoma, Geneva, sans-serif;
    font-weight: bold;
}

.accordion .ui-accordion-content{
    padding: 0px;
}

.dlg40{
    top: 40px !important;
}

.dlg40 .ui-widget-header {
    background: #145B9B;
    color: #022a48;
    font-family: Tahoma, Geneva, sans-serif;
    font-weight: normal;
    text-shadow: 0px 0px 0px rgba(255,255,255,0.7);
}

.dlg{
    top: 150px !important;
}

.dlg .ui-widget-header {
    background: #145B9B;
    color: #022a48;
    font-family: Tahoma, Geneva, sans-serif;
    font-weight: normal;
    text-shadow: 0px 0px 0px rgba(255,255,255,0.7);
}

.pnl .ui-widget-content, .pnl .ui-panelgrid-cell {
    color: #022a48;
    border: none;
    font-size: 14px;
}

.pnl {
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: normal;
}

.linha1e{
    padding-left: 20px !important;
}

.linha3e{
    padding-left: 20px !important;
}

.linha1d{
    padding-right: 20px !important;
}

.linha3d{
    padding-right: 20px !important;
}

.cadastrar{
    width: 90vh;
    min-width: 100%;
}

.ui-autocomplete-input{
    width: 100% !important;
}

@media all and (min-width: 768px) {
    .linha1e{
        padding-left: 50px !important;
    }
    .linha1d{
        padding-right: 50px !important;
    }
    .linha3e{
        padding-left: 80px !important;
    }
    .linha3d{
        padding-right: 80px !important;
    }
    .panelMenu{
        width: 700px;
    }
    .cadastrar{
        width: 700px;
    }
}
