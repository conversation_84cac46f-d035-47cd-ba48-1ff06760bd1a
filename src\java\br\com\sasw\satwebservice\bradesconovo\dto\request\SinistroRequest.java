/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.dto.request;

/**
 *
 * <AUTHOR>
 */
public class SinistroRequest {

    private String cnpjEmpresa;
    private String codigoConexaoEmpresa;
    private String numeroPedido;
    private String dataOperacao;
    private String valorOperacao;
    private String numeroCofreInteligenteOrigem;

    public SinistroRequest() {}


    public String getCnpjEmpresa() {
        return cnpjEmpresa;
    }

    public void setCnpjEmpresa(String cnpjEmpresa) {
        this.cnpjEmpresa = cnpjEmpresa;
    }

    public String getCodigoConexaoEmpresa() {
        return codigoConexaoEmpresa;
    }

    public void setCodigoConexaoEmpresa(String codigoConexaoEmpresa) {
        this.codigoConexaoEmpresa = codigoConexaoEmpresa;
    }

    public String getNumeroPedido() {
        return numeroPedido;
    }

    public void setNumeroPedido(String numeroPedido) {
        this.numeroPedido = numeroPedido;
    }

    public String getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(String dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getValorOperacao() {
        return valorOperacao;
    }

    public void setValorOperacao(String valorOperacao) {
        this.valorOperacao = valorOperacao;
    }

    public String getNumeroCofreInteligenteOrigem() {
        return numeroCofreInteligenteOrigem;
    }

    public void setNumeroCofreInteligenteOrigem(String numeroCofreInteligenteOrigem) {
        this.numeroCofreInteligenteOrigem = numeroCofreInteligenteOrigem;
    }
}

