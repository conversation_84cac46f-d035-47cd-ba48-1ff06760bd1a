<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by <EMAIL> (PROCERGS) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/cte" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="tiposGeralCTe_v3.00.xsd"/>
	<xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema_v1.01.xsd"/>
	<xs:complexType name="TConsSitCTe">
		<xs:annotation>
			<xs:documentation>Tipo Pedido de Consulta da Situação Atual do Conhecimento de Transporte eletrônico</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xServ" type="TServ" fixed="CONSULTAR">
				<xs:annotation>
					<xs:documentation>Serviço Solicitado</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="chCTe" type="TChNFe">
				<xs:annotation>
					<xs:documentation>Chaves de acesso da CT-e, compostas por: UF do emitente, AAMM da emissão da CT-e, CNPJ do emitente, modelo, série e número da CT-e e código numérico + DV.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" use="required">
			<xs:simpleType>
				<xs:restriction base="TVerConsSitCTe"/>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="TRetConsSitCTe">
		<xs:annotation>
			<xs:documentation>Tipo Retorno de Pedido de Consulta da Situação Atual do Conhecimento de Transporte eletrônico</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="verAplic" type="TVerAplic">
				<xs:annotation>
					<xs:documentation>Versão do Aplicativo que processou o CT-e</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cStat" type="TStat">
				<xs:annotation>
					<xs:documentation>Código do status da mensagem enviada.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMotivo" type="TMotivo">
				<xs:annotation>
					<xs:documentation>Descrição literal do status do serviço solicitado.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cUF" type="TCodUfIBGE">
				<xs:annotation>
					<xs:documentation>código da UF de atendimento</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="protCTe" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:any processContents="skip">
							<xs:annotation>
								<xs:documentation>Retornar protCTe da versão correspondente do CT-e autorizado</xs:documentation>
							</xs:annotation>
						</xs:any>
					</xs:sequence>
					<xs:attribute name="versao" use="required">
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:whiteSpace value="preserve"/>
								<xs:enumeration value="1.03"/>
								<xs:enumeration value="1.04"/>
								<xs:enumeration value="2.00"/>
								<xs:enumeration value="3.00"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="procEventoCTe" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:sequence>
						<xs:any>
							<xs:annotation>
								<xs:documentation>Retornar procEventoCTe da versão correspondente do evento CT-e autorizado</xs:documentation>
							</xs:annotation>
						</xs:any>
					</xs:sequence>
					<xs:attribute name="versao" use="required">
						<xs:simpleType>
							<xs:restriction base="xs:string">
								<xs:whiteSpace value="preserve"/>
								<xs:enumeration value="1.04"/>
								<xs:enumeration value="2.00"/>
								<xs:enumeration value="3.00"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerConsSitCTe" use="required"/>
	</xs:complexType>
	<xs:simpleType name="TVerConsSitCTe">
		<xs:annotation>
			<xs:documentation> Tipo Versão do Consulta situação de CT-e - 2.00</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="3\.00"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
