/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.importacao.funcionarios;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Funcion;
import SasDaos.FuncionDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import br.com.sasw.satwebservice.importacao.exceptions.FuncionarioException;
import br.com.sasw.satwebservice.importacao.exceptions.FuncionarioException.FuncionarioErrorCode;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.internal.StringMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.Response;

/**
 *
 * <AUTHOR>
 */
public class ImportacaoFuncionarios {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;
    private final FuncionDao funcionsdao = new FuncionDao();

    /**
     * Creates a new instance of ImportacaoFuncions
     */
    public ImportacaoFuncionarios() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    public Response json(String empresa, String input) {

        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(FuncionarioException.class, new FuncionarioExceptionSerializer());
        Gson gson = gsonBuilder.create();
        Map retorno = new HashMap<>(), processamento;

        StringMap funcionObjectStr;
        Object funcionsObject = 0;

        List processamentos = new ArrayList<>();
        try {
            // Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao(empresa);
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\" + empresa + "\\Funcionarios\\"
                    + getDataAtual("SQL") + "\\log.txt";

            this.logerro.Grava(input, this.caminho);

            Funcion funcion;
            StringMap funcionJson = new Gson().fromJson(input, StringMap.class);
            funcionsObject = funcionJson.get("Funcion");
            if (funcionsObject instanceof ArrayList) {
                List<StringMap> funcionList = (ArrayList) funcionsObject;
                retorno.put("funcionArquivo", funcionList.size());
                this.logerro.Grava("INICIA FOR", this.caminho);
                Integer i= 0;
                for (StringMap funcionObject : funcionList) {
                    processamento = new HashMap<>();
                    i++;
                    this.logerro.Grava("GRAVA FUNCIONARIO: "+funcionObject.toString()+" Posicao:"+Integer.toString(i), this.caminho);
                    try {
                        funcion = obterFuncion(funcionObject);
                        processamento.put("matr", funcion.getMatr().toBigInteger().toString());

                        validarFuncion(funcion);
                        importarFuncions(funcion, this.persistencia);
                    } catch (FuncionarioException funcExc) {
//                        System.out.println(funcExc.getMessage());
//                        System.out.println(funcExc.getCode().getCode());
//                        System.out.println(funcExc.getCode().getStatus());
                        processamento.put("result", funcExc);
                    }
                    processamentos.add(processamento);

                }
            } else if (funcionsObject instanceof StringMap) {
                funcionObjectStr = (StringMap) ((StringMap) funcionsObject).get("Funcion");
                retorno.put("funcionArquivo", 1);
                processamento = new HashMap<>();
                try {
                    funcion = obterFuncion(funcionObjectStr);
                    processamento.put("matr", funcion.getMatr().toBigInteger().toString());

                    validarFuncion(funcion);
                    importarFuncions(funcion, this.persistencia);
                } catch (FuncionarioException funcExc) {
//                    System.out.println(funcExc.getMessage());
//                    System.out.println(funcExc.getCode().getCode());
//                    System.out.println(funcExc.getCode().getStatus());
                    processamento.put("result", funcExc);
                }
                processamentos.add(processamento);
            }

            retorno.put("status", "ok");
            retorno.put("resp", processamentos);

        } catch (Exception e) {
            // Salvando em log o que foi mandado
            this.logerro.Grava(retorno.toString(), this.caminho);
            retorno.clear();
            this.logerro.Grava(e.getMessage(), this.caminho);
            retorno.clear();
            retorno.put("status", "error");
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);
        } finally {
            this.logerro.Grava(gson.toJson(retorno), this.caminho);
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gson.toJson(retorno))
                    .build();
        }
    }

    public void importarFuncions(Funcion funcion, Persistencia persistencia) throws FuncionarioException {

        boolean sucessoBusca = false, resultadoBusca = false, sucessoAtualizacao = false, sucessoInsercao = false;
        try {
            resultadoBusca = this.funcionsdao.matriculaExiste(funcion.getMatr().toBigInteger().toString(), persistencia);
            sucessoBusca = true;
        } catch (Exception e) {
            this.logerro.Grava(e.getMessage(), this.caminho);
        }

        // Ocorreu algum erro na consulta, que foi devidamente registrado em log
        if (!sucessoBusca) {
            throw new FuncionarioException(new FuncionarioErrorCode(0));
        } else {

            // Encontrou algum funcion pela matricula
            if (resultadoBusca) {

//                // Se já existir funcion já cadastrado, não processar inclusão
//                if (aux.getSituacao().equals("A")) {
//                    throw new FuncionarioException(new FuncionarioErrorCode(6));
//                } else {
//
                // solicitação de inclusão de funcion inativo
                try {
                    this.funcionsdao.atualizarFuncion(funcion, persistencia);
                    sucessoAtualizacao = true;
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }

                if (sucessoAtualizacao) {
                    throw new FuncionarioException(new FuncionarioErrorCode(2));
                } else {
                    throw new FuncionarioException(new FuncionarioErrorCode(0));
                }
//                }
            } else {
                try {
                    this.funcionsdao.inserirFuncion(funcion, persistencia);
                    sucessoInsercao = true;
                } catch (Exception e) {
                    this.logerro.Grava(e.getMessage(), this.caminho);
                }

                if (sucessoInsercao) {
                    throw new FuncionarioException(new FuncionarioErrorCode(1));
                } else {
                    throw new FuncionarioException(new FuncionarioErrorCode(0));
                }
            }
        }
    }

    private Funcion validarFuncion(Funcion funcion) throws FuncionarioException {

        if (funcion.getMatr() != null && funcion.getMatr().toString().replace(".0", "").length() > 16) {
            throw new FuncionarioException("Matr", new FuncionarioErrorCode(9));
        }

        if (funcion.getNome() != null && !funcion.getNome().equals("") && funcion.getNome().length() > 120) {
            throw new FuncionarioException("Nome", new FuncionarioErrorCode(9));
        }

        if (funcion.getNome_Guer() != null && !funcion.getNome_Guer().equals("") && funcion.getNome_Guer().length() > 40) {
            throw new FuncionarioException("Nome_Guer", new FuncionarioErrorCode(9));
        }else if (funcion.getNome_Guer() != null && !funcion.getNome_Guer().equals("")){
            funcion.setNome_Guer("");
        }

        if (funcion.getDt_Nasc() != null && !funcion.getDt_Nasc().equals("") && funcion.getDt_Nasc().length() > 8) {
            throw new FuncionarioException("Dt_Nasc", new FuncionarioErrorCode(9));
        }

        if (funcion.getEstCivil() != null && !funcion.getEstCivil().equals("")
                && (funcion.getEstCivil().length() > 1
                || (!funcion.getEstCivil().equals("S")
                && !funcion.getEstCivil().equals("C")
                && !funcion.getEstCivil().equals("Q")
                && !funcion.getEstCivil().equals("D")
                && !funcion.getEstCivil().equals("V")
                && !funcion.getEstCivil().equals("M")))) {
            throw new FuncionarioException("EstCivil", new FuncionarioErrorCode(9));
        }

        if (funcion.getSexo() != null && !funcion.getSexo().equals("") && funcion.getSexo().length() > 1) {
            throw new FuncionarioException("Sexo", new FuncionarioErrorCode(9));
        }

        if (funcion.getNacionalid() != null && !funcion.getNacionalid().equals("") && funcion.getNacionalid().length() > 32) {
            throw new FuncionarioException("Nacionalid", new FuncionarioErrorCode(9));
        }

        if (funcion.getInstrucao() != null && !funcion.getInstrucao().equals("")
                && (funcion.getInstrucao().length() > 2
                || (!funcion.getInstrucao().equals("10")
                && !funcion.getInstrucao().equals("20")
                && !funcion.getInstrucao().equals("25")
                && !funcion.getInstrucao().equals("30")
                && !funcion.getInstrucao().equals("35")
                && !funcion.getInstrucao().equals("40")
                && !funcion.getInstrucao().equals("45")
                && !funcion.getInstrucao().equals("50")
                && !funcion.getInstrucao().equals("55")
                && !funcion.getInstrucao().equals("65")
                && !funcion.getInstrucao().equals("85") // Verificar Brinks - Carlos 09/04/2024
                && !funcion.getInstrucao().equals("90") // Verificar Brinks - Carlos 09/04/2024                
                && !funcion.getInstrucao().equals("75")))) {
            throw new FuncionarioException("Instrucao", new FuncionarioErrorCode(9));
        }

        if (funcion.getEndereco() != null && !funcion.getEndereco().equals("") && funcion.getEndereco().length() > 40) {
            throw new FuncionarioException("Endereco", new FuncionarioErrorCode(9));
        }

        if (funcion.getNumero() != null && !funcion.getNumero().equals("") && funcion.getNumero().length() > 8) {
            throw new FuncionarioException("Numero", new FuncionarioErrorCode(9));
        }

        if (funcion.getComplemento() != null && !funcion.getComplemento().equals("") && funcion.getComplemento().length() > 40) {
            throw new FuncionarioException("Complemento", new FuncionarioErrorCode(9));
        }else if (funcion.getComplemento() != null && !funcion.getComplemento().equals("")) {
            funcion.setComplemento("");
        }

        if (funcion.getBairro() != null && !funcion.getBairro().equals("") && funcion.getBairro().length() > 30) {
            throw new FuncionarioException("Bairro", new FuncionarioErrorCode(9));
        }else if (funcion.getBairro() != null && !funcion.getBairro().equals("")){
            funcion.setBairro("");
        }

        if (funcion.getUF() != null && !funcion.getUF().equals("") && funcion.getUF().length() > 2) {
            throw new FuncionarioException("UF", new FuncionarioErrorCode(9));
        }

        if (funcion.getCidade() != null && !funcion.getCidade().equals("") && funcion.getCidade().length() > 32) {
            throw new FuncionarioException("Cidade", new FuncionarioErrorCode(9));
        }

        if (funcion.getCEP() != null && !funcion.getCEP().equals("") && funcion.getCEP().length() > 9) {
            throw new FuncionarioException("CEP", new FuncionarioErrorCode(9));
        }

//                if (funcion.getNomePais() != null && !funcion.getNomePais().equals("")                && funcion.getNomePais().length() > 16) {            throw new FuncionarioException("NomePais", new FuncionarioErrorCode(9));        }
        if (funcion.getReg_MT() != null && !funcion.getReg_MT().equals("") && funcion.getReg_MT().length() > 15) {
            throw new FuncionarioException("Reg_MT", new FuncionarioErrorCode(9));
        }

        if (funcion.getCPF() != null && !funcion.getCPF().equals("") && funcion.getCPF().length() > 11) {
            throw new FuncionarioException("CPF", new FuncionarioErrorCode(9));
        }

        if (funcion.getFone1() != null && !funcion.getFone1().equals("") && funcion.getFone1().length() > 15) {
            throw new FuncionarioException("Fone1", new FuncionarioErrorCode(9));
        }else{
           funcion.setFone1("");
        }

        if (funcion.getFone2() != null && !funcion.getFone2().equals("") && funcion.getFone2().length() > 15) {
            throw new FuncionarioException("Fone2", new FuncionarioErrorCode(9));
        }

        if (funcion.getRG() != null && !funcion.getRG().equals("") && funcion.getRG().length() > 15) {
            throw new FuncionarioException("RG", new FuncionarioErrorCode(9));
        }

//                if (funcion.getUF_RG() != null && !funcion.getUF_RG().equals("")                && funcion.getUF_RG().length() > 2) {            throw new FuncionarioException("UF_RG", new FuncionarioErrorCode(9));        }
        if (funcion.getOrgEmis() != null && !funcion.getOrgEmis().equals("") && funcion.getOrgEmis().length() > 15) {
            throw new FuncionarioException("OrgEmis", new FuncionarioErrorCode(9));
        }

        if (funcion.getRgDtEmis() != null && !funcion.getRgDtEmis().equals("") && funcion.getRgDtEmis().length() > 8) {
            throw new FuncionarioException("RgDtEmis", new FuncionarioErrorCode(9));
        }

        if (funcion.getTitEleit() != null && !funcion.getTitEleit().equals("") && funcion.getTitEleit().length() > 14) {
            throw new FuncionarioException("TitEleit", new FuncionarioErrorCode(9));
        }

        if (funcion.getTitEZona() != null && !funcion.getTitEZona().equals("") && funcion.getTitEZona().length() > 6) {
            throw new FuncionarioException("TitEZona", new FuncionarioErrorCode(9));
        }

        if (funcion.getTitSecao() != null && !funcion.getTitSecao().equals("") && funcion.getTitSecao().length() > 6) {
            throw new FuncionarioException("TitSecao", new FuncionarioErrorCode(9));
        }

        if (funcion.getCTPS_Nro() != null && !funcion.getCTPS_Nro().equals("") && funcion.getCTPS_Nro().length() > 10) {
            throw new FuncionarioException("CTPS_Nro", new FuncionarioErrorCode(9));
        }

        if (funcion.getCTPS_Serie() != null && !funcion.getCTPS_Serie().equals("") && funcion.getCTPS_Serie().length() > 5) {
            throw new FuncionarioException("CTPS_Serie", new FuncionarioErrorCode(9));
        }

        if (funcion.getCTPS_UF() != null && !funcion.getCTPS_UF().equals("") && funcion.getCTPS_UF().length() > 2) {
            throw new FuncionarioException("CTPS_UF", new FuncionarioErrorCode(9));
        }

        if (funcion.getCTPS_Emis() != null && !funcion.getCTPS_Emis().equals("") && funcion.getCTPS_Emis().length() > 8) {
            throw new FuncionarioException("CTPS_Emis", new FuncionarioErrorCode(9));
        }

        if (funcion.getCNH() != null && !funcion.getCNH().equals("") && funcion.getCNH().length() > 15) {
            throw new FuncionarioException("CNH", new FuncionarioErrorCode(9));
        }

        if (funcion.getCategoria() != null && !funcion.getCategoria().equals("") && funcion.getCategoria().length() > 5) {
            throw new FuncionarioException("Categoria", new FuncionarioErrorCode(9));
        }

        if (funcion.getDt_VenCNH() != null && !funcion.getDt_VenCNH().equals("") && funcion.getDt_VenCNH().length() > 8) {
            throw new FuncionarioException("Dt_VenCNH", new FuncionarioErrorCode(9));
        }

        if (funcion.getReservista() != null && !funcion.getReservista().equals("") && funcion.getReservista().length() > 40) {
            throw new FuncionarioException("Reservista", new FuncionarioErrorCode(9));
        }

        if (funcion.getReservCat() != null && !funcion.getReservCat().equals("") && funcion.getReservCat().length() > 10) {
            throw new FuncionarioException("ReservCat", new FuncionarioErrorCode(9));
        }

        if (funcion.getNaturalid() != null && !funcion.getNaturalid().equals("") && funcion.getNaturalid().length() > 32) {
            throw new FuncionarioException("Naturalid", new FuncionarioErrorCode(9));
        }

//                if (funcion.getEstadoNatal() != null && !funcion.getEstadoNatal().equals("")                && funcion.getEstadoNatal().length() > ) {            throw new FuncionarioException("EstadoNatal", new FuncionarioErrorCode(9));        }
//                if (funcion.getDt_ChegadaBrasil() != null && !funcion.getDt_ChegadaBrasil().equals("")                && funcion.getDt_ChegadaBrasil().length() > ) {            throw new FuncionarioException("Dt_ChegadaBrasil", new FuncionarioErrorCode(9));        }
//                if (funcion.getCartaModelo19() != null && !funcion.getCartaModelo19().equals("")                && funcion.getCartaModelo19().length() > ) {            throw new FuncionarioException("CartaModelo19", new FuncionarioErrorCode(9));        }
//                if (funcion.getConjugeBrasil() != null && !funcion.getConjugeBrasil().equals("")                && funcion.getConjugeBrasil().length() > ) {            throw new FuncionarioException("ConjugeBrasil", new FuncionarioErrorCode(9));        }
//                if (funcion.getNaturalizado() != null && !funcion.getNaturalizado().equals("")                && funcion.getNaturalizado().length() > ) {            throw new FuncionarioException("Naturalizado", new FuncionarioErrorCode(9));        }
//                if (funcion.getFilhosBrasil() != null && !funcion.getFilhosBrasil().equals("")                && funcion.getFilhosBrasil().length() > ) {            throw new FuncionarioException("FilhosBrasil", new FuncionarioErrorCode(9));        }
//                if (funcion.getNFilhosBrasil() != null && !funcion.getNFilhosBrasil().equals("")                && funcion.getNFilhosBrasil().length() > ) {            throw new FuncionarioException("NFilhosBrasil", new FuncionarioErrorCode(9));        }
//                if (funcion.getNRegistroGeral() != null && !funcion.getNRegistroGeral().equals("")                && funcion.getNRegistroGeral().length() > ) {            throw new FuncionarioException("NRegistroGeral", new FuncionarioErrorCode(9));        }
//                if (funcion.getNDecreto() != null && !funcion.getNDecreto().equals("")                && funcion.getNDecreto().length() > ) {            throw new FuncionarioException("NDecreto", new FuncionarioErrorCode(9));        }
//                if (funcion.getTipoVisto() != null && !funcion.getTipoVisto().equals("")                && funcion.getTipoVisto().length() > ) {            throw new FuncionarioException("TipoVisto", new FuncionarioErrorCode(9));        }
        if (funcion.getEmail() != null && !funcion.getEmail().equals("") && funcion.getEmail().length() > 60) {
            throw new FuncionarioException("Email", new FuncionarioErrorCode(9));
        }

        if (funcion.getCodPonto() != null && !funcion.getCodPonto().equals("") && funcion.getCodPonto().length() > 10) {
            throw new FuncionarioException("CodPonto", new FuncionarioErrorCode(9));
        }

//                if (funcion.getCodRecebimento() != null && !funcion.getCodRecebimento().equals("")                && funcion.getCodRecebimento().length() > ) {            throw new FuncionarioException("CodRecebimento", new FuncionarioErrorCode(9));        }
        if (funcion.getSituacao() != null && !funcion.getSituacao().equals("") && funcion.getSituacao().length() > 1) {
            throw new FuncionarioException("Situacao", new FuncionarioErrorCode(9));
        }else{
            if(funcion.getSituacao().equals("01") || funcion.getSituacao().equals("18")){
               funcion.setSituacao("A");
            }else if(funcion.getSituacao().equals("02")){
                funcion.setSituacao("R");                
            }else if(funcion.getSituacao().equals("03")){
                funcion.setSituacao("D");                
            }else{
                funcion.setSituacao("F");                
            }
        }

//                if (funcion.getCodTipoFuncionario() != null && !funcion.getCodTipoFuncionario().equals("")                && funcion.getCodTipoFuncionario().length() > ) {            throw new FuncionarioException("CodTipoFuncionario", new FuncionarioErrorCode(9));        }
        if (funcion.getSecao() != null && !funcion.getSecao().equals("") && funcion.getSecao().length() > 35) {
            throw new FuncionarioException("Secao", new FuncionarioErrorCode(9));
        }

        if (funcion.getCargo() != null && !funcion.getCargo().equals("") && funcion.getCargo().length() > 10) {
            throw new FuncionarioException("Cargo", new FuncionarioErrorCode(9));
        }

        if (funcion.getSindicato() != null && !funcion.getSindicato().equals("") && funcion.getSindicato().length() > 10) {
            throw new FuncionarioException("Sindicato", new FuncionarioErrorCode(9));
        }

//        if (funcion.getJornada() != null && !funcion.getJornada().equals("") && funcion.getJornada().length() > 6) {
//            throw new FuncionarioException("Jornada", new FuncionarioErrorCode(9));
//        }
//        if (funcion.getHorario() != null && !funcion.getHorario().equals("") && funcion.getHorario().length() > 10) {
//            throw new FuncionarioException("Horario", new FuncionarioErrorCode(9));
//        }
        if (funcion.getDepIR() != null && !funcion.getDepIR().equals("") && funcion.getDepIR().length() > 2) {
            throw new FuncionarioException("DepIR", new FuncionarioErrorCode(9));
        }

        if (funcion.getDepSF() != null && !funcion.getDepSF().equals("") && funcion.getDepSF().length() > 2) {
            throw new FuncionarioException("DepSF", new FuncionarioErrorCode(9));
        }

//                if (funcion.getDt_Base() != null && !funcion.getDt_Base().equals("")                && funcion.getDt_Base().length() > ) {            throw new FuncionarioException("Dt_Base", new FuncionarioErrorCode(9));        }
//        if (funcion.getSalario() != null && !funcion.getSalario().equals("") //&& funcion.getSalario().length() > 15
//                ) {
//            throw new FuncionarioException("Salario", new FuncionarioErrorCode(9));
//        }
//                if (funcion.getSituacaoFGTS() != null && !funcion.getSituacaoFGTS().equals("")                && funcion.getSituacaoFGTS().length() > ) {            throw new FuncionarioException("SituacaoFGTS", new FuncionarioErrorCode(9));        }
        if (funcion.getFGTSOpcao() != null && !funcion.getFGTSOpcao().equals("") && funcion.getFGTSOpcao().length() > 8) {
            throw new FuncionarioException("FGTSOpcao", new FuncionarioErrorCode(9));
        }

//                if (funcion.getContaFGTS() != null && !funcion.getContaFGTS().equals("")                && funcion.getContaFGTS().length() > ) {            throw new FuncionarioException("ContaFGTS", new FuncionarioErrorCode(9));        }
//                if (funcion.getSaldoFGTS() != null && !funcion.getSaldoFGTS().equals("")                && funcion.getSaldoFGTS().length() > ) {            throw new FuncionarioException("SaldoFGTS", new FuncionarioErrorCode(9));        }
//                if (funcion.getDt_SaldoFGTS() != null && !funcion.getDt_SaldoFGTS().equals("")                && funcion.getDt_SaldoFGTS().length() > ) {            throw new FuncionarioException("Dt_SaldoFGTS", new FuncionarioErrorCode(9));        }
        if (funcion.getPIS() != null && !funcion.getPIS().equals("") && funcion.getPIS().length() > 11) {
            throw new FuncionarioException("PIS", new FuncionarioErrorCode(9));
        }

//                if (funcion.getDt_CadastroPIS() != null && !funcion.getDt_CadastroPIS().equals("")                && funcion.getDt_CadastroPIS().length() > ) {            throw new FuncionarioException("Dt_CadastroPIS", new FuncionarioErrorCode(9));        }
//                if (funcion.getCodBancoPIS() != null && !funcion.getCodBancoPIS().equals("")                && funcion.getCodBancoPIS().length() > ) {            throw new FuncionarioException("CodBancoPIS", new FuncionarioErrorCode(9));        }
//                if (funcion.getContribuicaoSindical() != null && !funcion.getContribuicaoSindical().equals("")                && funcion.getContribuicaoSindical().length() > ) {            throw new FuncionarioException("ContribuicaoSindical", new FuncionarioErrorCode(9));        }
//                if (funcion.getAposentado() != null && !funcion.getAposentado().equals("")                && funcion.getAposentado().length() > ) {            throw new FuncionarioException("Aposentado", new FuncionarioErrorCode(9));        }
//                if (funcion.getMais65Anos() != null && !funcion.getMais65Anos().equals("")                && funcion.getMais65Anos().length() > ) {            throw new FuncionarioException("Mais65Anos", new FuncionarioErrorCode(9));        }
//                if (funcion.getAjudaCusto() != null && !funcion.getAjudaCusto().equals("")                && funcion.getAjudaCusto().length() > ) {            throw new FuncionarioException("AjudaCusto", new FuncionarioErrorCode(9));        }
//                if (funcion.getPercentualAdiantamento() != null && !funcion.getPercentualAdiantamento().equals("")                && funcion.getPercentualAdiantamento().length() > ) {            throw new FuncionarioException("PercentualAdiantamento", new FuncionarioErrorCode(9));        }
//                if (funcion.getArredondamento() != null && !funcion.getArredondamento().equals("")                && funcion.getArredondamento().length() > ) {            throw new FuncionarioException("Arredondamento", new FuncionarioErrorCode(9));        }
        if (funcion.getDt_Admis() != null && !funcion.getDt_Admis().equals("") && funcion.getDt_Admis().length() > 8) {
            throw new FuncionarioException("Dt_Admis", new FuncionarioErrorCode(9));
        }

        if (funcion.getTipoAdm() != null && !funcion.getTipoAdm().equals("") && funcion.getTipoAdm().length() > 1) {
            throw new FuncionarioException("TipoAdm", new FuncionarioErrorCode(9));
        }

//                if (funcion.getDt_Transferencia() != null && !funcion.getDt_Transferencia().equals("")                && funcion.getDt_Transferencia().length() > ) {            throw new FuncionarioException("Dt_Transferencia", new FuncionarioErrorCode(9));        }
//                if (funcion.getMotivoAdmissao() != null && !funcion.getMotivoAdmissao().equals("")                && funcion.getMotivoAdmissao().length() > ) {            throw new FuncionarioException("MotivoAdmissao", new FuncionarioErrorCode(9));        }
//                if (funcion.getContratoPrazoDeterminado() != null && !funcion.getContratoPrazoDeterminado().equals("")                && funcion.getContratoPrazoDeterminado().length() > ) {            throw new FuncionarioException("ContratoPrazoDeterminado", new FuncionarioErrorCode(9));        }
//                if (funcion.getFimPrazoContrato() != null && !funcion.getFimPrazoContrato().equals("")                && funcion.getFimPrazoContrato().length() > ) {            throw new FuncionarioException("FimPrazoContrato", new FuncionarioErrorCode(9));        }
//                if (funcion.getDt_Demissao() != null && !funcion.getDt_Demissao().equals("")                && funcion.getDt_Demissao().length() > ) {            throw new FuncionarioException("Dt_Demissao", new FuncionarioErrorCode(9));        }
//                if (funcion.getTipoDemissao() != null && !funcion.getTipoDemissao().equals("")                && funcion.getTipoDemissao().length() > ) {            throw new FuncionarioException("TipoDemissao", new FuncionarioErrorCode(9));        }
//                if (funcion.getMotivoDemissao() != null && !funcion.getMotivoDemissao().equals("")                && funcion.getMotivoDemissao().length() > ) {            throw new FuncionarioException("MotivoDemissao", new FuncionarioErrorCode(9));        }
//                if (funcion.getDt_Desligamento() != null && !funcion.getDt_Desligamento().equals("")                && funcion.getDt_Desligamento().length() > ) {            throw new FuncionarioException("Dt_Desligamento", new FuncionarioErrorCode(9));        }
//                if (funcion.getDt_UltimaMovimentacao() != null && !funcion.getDt_UltimaMovimentacao().equals("")                && funcion.getDt_UltimaMovimentacao().length() > ) {            throw new FuncionarioException("Dt_UltimaMovimentacao", new FuncionarioErrorCode(9));        }
//                if (funcion.getDt_PagamentoRescisao() != null && !funcion.getDt_PagamentoRescisao().equals("")                && funcion.getDt_PagamentoRescisao().length() > ) {            throw new FuncionarioException("Dt_PagamentoRescisao", new FuncionarioErrorCode(9));        }
//                if (funcion.getCodSaqueFGTS() != null && !funcion.getCodSaqueFGTS().equals("")                && funcion.getCodSaqueFGTS().length() > ) {            throw new FuncionarioException("CodSaqueFGTS", new FuncionarioErrorCode(9));        }
//                if (funcion.getAvisoPrevio() != null && !funcion.getAvisoPrevio().equals("")                && funcion.getAvisoPrevio().length() > ) {            throw new FuncionarioException("AvisoPrevio", new FuncionarioErrorCode(9));        }
//                if (funcion.getDt_AvisoPrevio() != null && !funcion.getDt_AvisoPrevio().equals("")                && funcion.getDt_AvisoPrevio().length() > ) {            throw new FuncionarioException("Dt_AvisoPrevio", new FuncionarioErrorCode(9));        }
//                if (funcion.getDiasAviso() != null && !funcion.getDiasAviso().equals("")                && funcion.getDiasAviso().length() > ) {            throw new FuncionarioException("DiasAviso", new FuncionarioErrorCode(9));        }
//                if (funcion.getDt_VencFerias() != null && !funcion.getDt_VencFerias().equals("")                && funcion.getDt_VencFerias().length() > ) {            throw new FuncionarioException("Dt_VencFerias", new FuncionarioErrorCode(9));        }
//                if (funcion.getInicioProgramacaoFerias1() != null && !funcion.getInicioProgramacaoFerias1().equals("")                && funcion.getInicioProgramacaoFerias1().length() > ) {            throw new FuncionarioException("InicioProgramacaoFerias1", new FuncionarioErrorCode(9));        }
//                if (funcion.getFimProgramacaoFerias1() != null && !funcion.getFimProgramacaoFerias1().equals("")                && funcion.getFimProgramacaoFerias1().length() > ) {            throw new FuncionarioException("FimProgramacaoFerias1", new FuncionarioErrorCode(9));        }
//                if (funcion.getAbono() != null && !funcion.getAbono().equals("")                && funcion.getAbono().length() > ) {            throw new FuncionarioException("Abono", new FuncionarioErrorCode(9));        }
//                if (funcion.getPrimeiraParcela13() != null && !funcion.getPrimeiraParcela13().equals("")                && funcion.getPrimeiraParcela13().length() > ) {            throw new FuncionarioException("PrimeiraParcela13", new FuncionarioErrorCode(9));        }
//                if (funcion.getDiasAdiantamento() != null && !funcion.getDiasAdiantamento().equals("")                && funcion.getDiasAdiantamento().length() > ) {            throw new FuncionarioException("DiasAdiantamento", new FuncionarioErrorCode(9));        }
//                if (funcion.getEventoAdiantamentoFerias() != null && !funcion.getEventoAdiantamentoFerias().equals("")                && funcion.getEventoAdiantamentoFerias().length() > ) {            throw new FuncionarioException("EventoAdiantamentoFerias", new FuncionarioErrorCode(9));        }
//                if (funcion.getFeriasColetivasGlobais() != null && !funcion.getFeriasColetivasGlobais().equals("")                && funcion.getFeriasColetivasGlobais().length() > ) {            throw new FuncionarioException("FeriasColetivasGlobais", new FuncionarioErrorCode(9));        }
//                if (funcion.getDiasFerias() != null && !funcion.getDiasFerias().equals("")                && funcion.getDiasFerias().length() > ) {            throw new FuncionarioException("DiasFerias", new FuncionarioErrorCode(9));        }
//                if (funcion.getDiasAbono() != null && !funcion.getDiasAbono().equals("")                && funcion.getDiasAbono().length() > ) {            throw new FuncionarioException("DiasAbono", new FuncionarioErrorCode(9));        }
//                if (funcion.getSaldoFerias() != null && !funcion.getSaldoFerias().equals("")                && funcion.getSaldoFerias().length() > ) {            throw new FuncionarioException("SaldoFerias", new FuncionarioErrorCode(9));        }
//                if (funcion.getSaldoFeriasAnterior() != null && !funcion.getSaldoFeriasAnterior().equals("")                && funcion.getSaldoFeriasAnterior().length() > ) {            throw new FuncionarioException("SaldoFeriasAnterior", new FuncionarioErrorCode(9));        }
//                if (funcion.getSaldoFeriasAuxiliar() != null && !funcion.getSaldoFeriasAuxiliar().equals("")                && funcion.getSaldoFeriasAuxiliar().length() > ) {            throw new FuncionarioException("SaldoFeriasAuxiliar", new FuncionarioErrorCode(9));        }
//                if (funcion.getObservacaoFerias() != null && !funcion.getObservacaoFerias().equals("")                && funcion.getObservacaoFerias().length() > ) {            throw new FuncionarioException("ObservacaoFerias", new FuncionarioErrorCode(9));        }
//                if (funcion.getDt_PagamentoFerias() != null && !funcion.getDt_PagamentoFerias().equals("")                && funcion.getDt_PagamentoFerias().length() > ) {            throw new FuncionarioException("Dt_PagamentoFerias", new FuncionarioErrorCode(9));        }
//                if (funcion.getDt_AvisoFerias() != null && !funcion.getDt_AvisoFerias().equals("")                && funcion.getDt_AvisoFerias().length() > ) {            throw new FuncionarioException("Dt_AvisoFerias", new FuncionarioErrorCode(9));        }
//                if (funcion.getDiasLicencaRemunerada1() != null && !funcion.getDiasLicencaRemunerada1().equals("")                && funcion.getDiasLicencaRemunerada1().length() > ) {            throw new FuncionarioException("DiasLicencaRemunerada1", new FuncionarioErrorCode(9));        }
//                if (funcion.getDiasLicencaRemunerada() != null && !funcion.getDiasLicencaRemunerada().equals("")                && funcion.getDiasLicencaRemunerada().length() > ) {            throw new FuncionarioException("DiasLicencaRemunerada", new FuncionarioErrorCode(9));        }
//                if (funcion.getDt_InicioLicenca() != null && !funcion.getDt_InicioLicenca().equals("")                && funcion.getDt_InicioLicenca().length() > ) {            throw new FuncionarioException("Dt_InicioLicenca", new FuncionarioErrorCode(9));        }
//                if (funcion.getMediaSalarioMaternidade() != null && !funcion.getMediaSalarioMaternidade().equals("")                && funcion.getMediaSalarioMaternidade().length() > ) {            throw new FuncionarioException("MediaSalarioMaternidade", new FuncionarioErrorCode(9));        }
//                if (funcion.getSituacaoRAIS() != null && !funcion.getSituacaoRAIS().equals("")                && funcion.getSituacaoRAIS().length() > ) {            throw new FuncionarioException("SituacaoRAIS", new FuncionarioErrorCode(9));        }
        if (funcion.getCt_Banco() != null && !funcion.getCt_Banco().equals("") && funcion.getCt_Banco().length() > 3) {
            throw new FuncionarioException("Ct_Banco", new FuncionarioErrorCode(9));
        }

        if (funcion.getCt_Agencia() != null && !funcion.getCt_Agencia().equals("") && funcion.getCt_Agencia().length() > 6) {
            throw new FuncionarioException("Ct_Agencia", new FuncionarioErrorCode(9));
        }

        if (funcion.getCt_Conta() != null && !funcion.getCt_Conta().equals("") && funcion.getCt_Conta().length() > 15) {
            throw new FuncionarioException("Ct_Conta", new FuncionarioErrorCode(9));
        }

//        if (funcion.getCodFil() != null && !funcion.getCodFil().equals("") 
////                && funcion.getCodFil().length() > 7
//                ) {
//            throw new FuncionarioException("CodFil", new FuncionarioErrorCode(9));
//        }
        if (funcion.getRaca() != null && !funcion.getRaca().equals("") && funcion.getRaca().length() > 1) {
            throw new FuncionarioException("Raca", new FuncionarioErrorCode(9));
        }

        if (funcion.getDefFis() != null && !funcion.getDefFis().equals("") && funcion.getDefFis().length() > 1) {
            throw new FuncionarioException("DefFis", new FuncionarioErrorCode(9));
        }

        return funcion;
    }

    private Funcion obterFuncion(StringMap stringMap) throws Exception {
        Funcion funcion = new Funcion();
        funcion.setMatr(stringMap.getOrDefault("Matr", "").toString()); // funcion.Matr
        funcion.setNome(stringMap.getOrDefault("Nome", "").toString()); // funcion.Nome
        String vNomeGuer  = stringMap.getOrDefault("Nome_guer", "").toString();
        funcion.setNome_Guer(FuncoesString.RecortaString(vNomeGuer + FuncoesString.SpaceString("", 15), 0, 15)); // funcion.Nome_guer
        funcion.setDt_Nasc(stringMap.getOrDefault("Dt_Nasc", "").toString()); // funcion.Dt_Nasc
        funcion.setEstCivil(stringMap.getOrDefault("EstCivil", "").toString()); // funcion.EstCivil
        funcion.setSexo(stringMap.getOrDefault("Sexo", "").toString()); // funcion.Sexo
        funcion.setNacionalid(stringMap.getOrDefault("Nacionalidade", "").toString()); // funcion.Nacionalidade
        funcion.setInstrucao(stringMap.getOrDefault("Instrucao", "").toString()); // funcion.Instrucao
        String vEndereco = stringMap.getOrDefault("Endereco", "").toString();
        funcion.setEndereco(FuncoesString.RecortaString(vEndereco + FuncoesString.SpaceString("", 40), 0, 40));
        funcion.setNumero(stringMap.getOrDefault("Numero", "").toString()); // funcion.Numero
        funcion.setComplemento(stringMap.getOrDefault("Complemento", "").toString()); // funcion.Complemento
        funcion.setBairro(stringMap.getOrDefault("Bairro", "").toString()); // funcion.Bairro
        funcion.setUF(stringMap.getOrDefault("UF", "").toString()); // funcion.UF
        funcion.setCidade(stringMap.getOrDefault("Cidade", "").toString()); // funcion.Cidade
        funcion.setCEP(stringMap.getOrDefault("CEP", "").toString()); // funcion.CEP
//        funcion.setNomePais(stringMap.getOrDefault("NomePais","").toString()); // funcion.NomePais
        funcion.setReg_MT(stringMap.getOrDefault("Reg_MT", "").toString()); // funcion.Reg_MT
        funcion.setCPF(stringMap.getOrDefault("CPF", "").toString()); // funcion.CPF
        funcion.setFone1(stringMap.getOrDefault("Fone1", "").toString()); // funcion.Fone1
        funcion.setFone2(stringMap.getOrDefault("Fone2", "").toString()); // funcion.Fone2
        funcion.setRG(stringMap.getOrDefault("RG", "").toString()); // funcion.RG
//        funcion.setUF_RG(stringMap.getOrDefault("UF_RG","").toString()); // funcion.UF_RG
        funcion.setOrgEmis(stringMap.getOrDefault("OrgEmis", "").toString()); // funcion.OrgEmis
        funcion.setRgDtEmis(stringMap.getOrDefault("RgDtEmis", "").toString()); // funcion.RgDtEmis
        funcion.setTitEleit(stringMap.getOrDefault("TitEleit", "").toString()); // funcion.TitEleit
        funcion.setTitEZona(stringMap.getOrDefault("TitEZona", "").toString()); // funcion.TitEZona
        funcion.setTitSecao(stringMap.getOrDefault("TitSecao", "").toString()); // funcion.TitSecao
        funcion.setCTPS_Nro(stringMap.getOrDefault("CTPS_Nro", "").toString()); // funcion.CTPS_Nro
        funcion.setCTPS_Serie(stringMap.getOrDefault("CTPS_Serie", "").toString()); // funcion.CTPS_Serie
        funcion.setCTPS_UF(stringMap.getOrDefault("CTPS_UF", "").toString()); // funcion.CTPS_UF
        funcion.setCTPS_Emis(stringMap.getOrDefault("CTPS_Emis", "").toString()); // funcion.CTPS_Emis
        funcion.setCNH(stringMap.getOrDefault("CNH", "").toString()); // funcion.CNH
        funcion.setCategoria(stringMap.getOrDefault("Categoria", "").toString()); // funcion.Categoria
        funcion.setDt_VenCNH(stringMap.getOrDefault("Dt_VenCNH", "").toString()); // funcion.Dt_VenCNH
        funcion.setReservista(stringMap.getOrDefault("Reservista", "").toString()); // funcion.Reservista
        funcion.setReservCat(stringMap.getOrDefault("ReservCat", "").toString()); // funcion.ReservCat
        funcion.setNaturalid(stringMap.getOrDefault("Naturalid", "").toString()); // funcion.Naturalid
//        funcion.setEstadoNatal(stringMap.getOrDefault("EstadoNatal","").toString()); // funcion.EstadoNatal
//        funcion.setDt_ChegadaBrasil(stringMap.getOrDefault("Dt_ChegadaBrasil","").toString()); // funcion.Dt_ChegadaBrasil
//        funcion.setCartaModelo19(stringMap.getOrDefault("CartaModelo19","").toString()); // funcion.CartaModelo19
//        funcion.setConjugeBrasil(stringMap.getOrDefault("ConjugeBrasil","").toString()); // funcion.ConjugeBrasil
//        funcion.setNaturalizado(stringMap.getOrDefault("Naturalizado","").toString()); // funcion.Naturalizado
//        funcion.setFilhosBrasil(stringMap.getOrDefault("FilhosBrasil","").toString()); // funcion.FilhosBrasil
//        funcion.setNFilhosBrasil(stringMap.getOrDefault("NFilhosBrasil","").toString()); // funcion.NFilhosBrasil
//        funcion.setNRegistroGeral(stringMap.getOrDefault("NRegistroGeral","").toString()); // funcion.NRegistroGeral
//        funcion.setNDecreto(stringMap.getOrDefault("NDecreto","").toString()); // funcion.NDecreto
//        funcion.setTipoVisto(stringMap.getOrDefault("TipoVisto","").toString()); // funcion.TipoVisto
        funcion.setEmail(stringMap.getOrDefault("Email", "").toString()); // funcion.Email
        funcion.setCodPonto(stringMap.getOrDefault("CodPonto", "").toString()); // funcion.CodPonto
//        funcion.setCodRecebimento(stringMap.getOrDefault("CodRecebimento","").toString()); // funcion.CodRecebimento
        String vSituacao = stringMap.getOrDefault("CodSituacao", "").toString();
        funcion.setSituacao(FuncoesString.RecortaString(vSituacao + FuncoesString.SpaceString("", 1), 0, 1)); // funcion.CodSituacao
//        funcion.setCodTipoFuncionario(stringMap.getOrDefault("CodTipoFuncionario","").toString()); // funcion.CodTipoFuncionario
        funcion.setSecao(stringMap.getOrDefault("Secao", "").toString()); // funcion.Secao
        funcion.setCodCargo(stringMap.getOrDefault("Cargo", "").toString()); // funcion.Cargo
        funcion.setSindicato(stringMap.getOrDefault("Sindicato", "").toString()); // funcion.Sindicato
        funcion.setJornada(stringMap.getOrDefault("Jornada", "").toString()); // funcion.Jornada
//        funcion.setHorario(stringMap.getOrDefault("Horario","").toString()); // funcion.Horario
        funcion.setDepIR(stringMap.getOrDefault("DepIR", "").toString()); // funcion.DepIR
        funcion.setDepSF(stringMap.getOrDefault("DepSF", "").toString()); // funcion.DepSF
//        funcion.setDt_Base(stringMap.getOrDefault("Dt_Base","").toString()); // funcion.Dt_Base
        funcion.setSalario(stringMap.getOrDefault("Salario", "").toString()); // funcion.Salario
//        funcion.setSituacaoFGTS(stringMap.getOrDefault("SituacaoFGTS","").toString()); // funcion.SituacaoFGTS
        funcion.setFGTSOpcao(stringMap.getOrDefault("FGTSOpcao", "").toString()); // funcion.FGTSOpcao
//        funcion.setContaFGTS(stringMap.getOrDefault("ContaFGTS","").toString()); // funcion.ContaFGTS
//        funcion.setSaldoFGTS(stringMap.getOrDefault("SaldoFGTS","").toString()); // funcion.SaldoFGTS
//        funcion.setDt_SaldoFGTS(stringMap.getOrDefault("Dt_SaldoFGTS","").toString()); // funcion.Dt_SaldoFGTS
        funcion.setPIS(stringMap.getOrDefault("PIS", "").toString()); // funcion.PIS
//        funcion.setDt_CadastroPIS(stringMap.getOrDefault("Dt_CadastroPIS","").toString()); // funcion.Dt_CadastroPIS
//        funcion.setCodBancoPIS(stringMap.getOrDefault("CodBancoPIS","").toString()); // funcion.CodBancoPIS
//        funcion.setContribuicaoSindical(stringMap.getOrDefault("ContribuicaoSindical","").toString()); // funcion.ContribuicaoSindical
//        funcion.setAposentado(stringMap.getOrDefault("Aposentado","").toString()); // funcion.Aposentado
//        funcion.setMais65Anos(stringMap.getOrDefault("Mais65Anos","").toString()); // funcion.Mais65Anos
//        funcion.setAjudaCusto(stringMap.getOrDefault("AjudaCusto","").toString()); // funcion.AjudaCusto
//        funcion.setPercentualAdiantamento(stringMap.getOrDefault("PercentualAdiantamento","").toString()); // funcion.PercentualAdiantamento
//        funcion.setArredondamento(stringMap.getOrDefault("Arredondamento","").toString()); // funcion.Arredondamento
        funcion.setDt_Admis(stringMap.getOrDefault("Dt_Admis", "").toString()); // funcion.Dt_Admis
        funcion.setTipoAdm(stringMap.getOrDefault("TipoAdm", "").toString()); // funcion.TipoAdm
//        funcion.setDt_Transferencia(stringMap.getOrDefault("Dt_Transferencia","").toString()); // funcion.Dt_Transferencia
//        funcion.setMotivoAdmissao(stringMap.getOrDefault("MotivoAdmissao","").toString()); // funcion.MotivoAdmissao
//        funcion.setContratoPrazoDeterminado(stringMap.getOrDefault("ContratoPrazoDeterminado","").toString()); // funcion.ContratoPrazoDeterminado
//        funcion.setFimPrazoContrato(stringMap.getOrDefault("FimPrazoContrato","").toString()); // funcion.FimPrazoContrato
//        funcion.setDt_Demissao(stringMap.getOrDefault("Dt_Demissao","").toString()); // funcion.Dt_Demissao
//        funcion.setTipoDemissao(stringMap.getOrDefault("TipoDemissao","").toString()); // funcion.TipoDemissao
//        funcion.setMotivoDemissao(stringMap.getOrDefault("MotivoDemissao","").toString()); // funcion.MotivoDemissao
//        funcion.setDt_Desligamento(stringMap.getOrDefault("Dt_Desligamento","").toString()); // funcion.Dt_Desligamento
//        funcion.setDt_UltimaMovimentacao(stringMap.getOrDefault("Dt_UltimaMovimentacao","").toString()); // funcion.Dt_UltimaMovimentacao
//        funcion.setDt_PagamentoRescisao(stringMap.getOrDefault("Dt_PagamentoRescisao","").toString()); // funcion.Dt_PagamentoRescisao
//        funcion.setCodSaqueFGTS(stringMap.getOrDefault("CodSaqueFGTS","").toString()); // funcion.CodSaqueFGTS
//        funcion.setAvisoPrevio(stringMap.getOrDefault("AvisoPrevio","").toString()); // funcion.AvisoPrevio
//        funcion.setDt_AvisoPrevio(stringMap.getOrDefault("Dt_AvisoPrevio","").toString()); // funcion.Dt_AvisoPrevio
//        funcion.setDiasAviso(stringMap.getOrDefault("DiasAviso","").toString()); // funcion.DiasAviso
//        funcion.setDt_VencFerias(stringMap.getOrDefault("Dt_VencFerias","").toString()); // funcion.Dt_VencFerias
//        funcion.setInicioProgramacaoFerias1(stringMap.getOrDefault("InicioProgramacaoFerias1","").toString()); // funcion.InicioProgramacaoFerias1
//        funcion.setFimProgramacaoFerias1(stringMap.getOrDefault("FimProgramacaoFerias1","").toString()); // funcion.FimProgramacaoFerias1
//        funcion.setAbono(stringMap.getOrDefault("Abono","").toString()); // funcion.Abono
//        funcion.setPrimeiraParcela13(stringMap.getOrDefault("PrimeiraParcela13","").toString()); // funcion.PrimeiraParcela13
//        funcion.setDiasAdiantamento(stringMap.getOrDefault("DiasAdiantamento","").toString()); // funcion.DiasAdiantamento
//        funcion.setEventoAdiantamentoFerias(stringMap.getOrDefault("EventoAdiantamentoFerias","").toString()); // funcion.EventoAdiantamentoFerias
//        funcion.setFeriasColetivasGlobais(stringMap.getOrDefault("FeriasColetivasGlobais","").toString()); // funcion.FeriasColetivasGlobais
//        funcion.setDiasFerias(stringMap.getOrDefault("DiasFerias","").toString()); // funcion.DiasFerias
//        funcion.setDiasAbono(stringMap.getOrDefault("DiasAbono","").toString()); // funcion.DiasAbono
//        funcion.setSaldoFerias(stringMap.getOrDefault("SaldoFerias","").toString()); // funcion.SaldoFerias
//        funcion.setSaldoFeriasAnterior(stringMap.getOrDefault("SaldoFeriasAnterior","").toString()); // funcion.SaldoFeriasAnterior
//        funcion.setSaldoFeriasAuxiliar(stringMap.getOrDefault("SaldoFeriasAuxiliar","").toString()); // funcion.SaldoFeriasAuxiliar
//        funcion.setObservacaoFerias(stringMap.getOrDefault("ObservacaoFerias","").toString()); // funcion.ObservacaoFerias
//        funcion.setDt_PagamentoFerias(stringMap.getOrDefault("Dt_PagamentoFerias","").toString()); // funcion.Dt_PagamentoFerias
//        funcion.setDt_AvisoFerias(stringMap.getOrDefault("Dt_AvisoFerias","").toString()); // funcion.Dt_AvisoFerias
//        funcion.setDiasLicencaRemunerada1(stringMap.getOrDefault("DiasLicencaRemunerada1","").toString()); // funcion.DiasLicencaRemunerada1
//        funcion.setDiasLicencaRemunerada(stringMap.getOrDefault("DiasLicencaRemunerada","").toString()); // funcion.DiasLicencaRemunerada
//        funcion.setDt_InicioLicenca(stringMap.getOrDefault("Dt_InicioLicenca","").toString()); // funcion.Dt_InicioLicenca
//        funcion.setMediaSalarioMaternidade(stringMap.getOrDefault("MediaSalarioMaternidade","").toString()); // funcion.MediaSalarioMaternidade
//        funcion.setSituacaoRAIS(stringMap.getOrDefault("SituacaoRAIS","").toString()); // funcion.SituacaoRAIS
        funcion.setCt_Banco(stringMap.getOrDefault("Ct_Banco", "").toString()); // funcion.Ct_Banco
        funcion.setCt_Agencia(stringMap.getOrDefault("Ct_Agencia", "").toString()); // funcion.Ct_Agencia
        funcion.setCt_Conta(stringMap.getOrDefault("Ct_Conta", "").toString()); // funcion.Ct_Conta
        funcion.setCodFil(stringMap.getOrDefault("CodFil", "").toString()); // funcion.CodFil
        funcion.setRaca(stringMap.getOrDefault("Raca", "").toString()); // funcion.Raca
        funcion.setDefFis(stringMap.getOrDefault("DefFis", "").toString()); // funcion.DefFis

        funcion.setDt_Alter(getDataAtual("SQL"));
        funcion.setHr_Alter(getDataAtual("HORA"));
        funcion.setOperador("WS-IMP");

        funcion.setDt_Situac(getDataAtual("SQL"));
        return funcion;
    }

}
