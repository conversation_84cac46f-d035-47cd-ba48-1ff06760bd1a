package br.com.sasw.satwebservice.bcb;

import com.google.gson.Gson;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.http.conn.ssl.TrustAllStrategy;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class CliemteServicoBCB {

    private static final int HTTP_STATUS_OK = 200;
    private static final String HTTP_HEADER_ACCEPT = "Accept";
    private static final String HTTP_HEADER_APPLICATION_JSON = "application/json";
    private static final String ARRAY_JSON_VAZIO = "[]"; 
    private static final String FORMATO_DATA_BCB = "MM-dd-yyyy";

    private static final String BASE_URL_COTACAO_DOLAR =
            "https://olinda.bcb.gov.br/olinda/servico/PTAX/versao/v1/odata/CotacaoDolarDia(dataCotacao=@dataCotacao)?@dataCotacao='%s'&$format=json";

    private static final String BASE_URL_MOEDAS = 
            "https://olinda.bcb.gov.br/olinda/servico/PTAX/versao/v1/odata/Moedas?$top=100&$format=json";

    private static final String BASE_URL_COTACAO_DOLAR_PERIODO =
            "https://olinda.bcb.gov.br/olinda/servico/PTAX/versao/v1/odata/CotacaoDolarPeriodo(dataInicial=@dataInicial,dataFinalCotacao=@dataFinalCotacao)?@dataInicial='%s'&@dataFinalCotacao='%s'&$format=json";

    private static final String BASE_URL_COTACAO_MOEDA_DIA =
            "https://olinda.bcb.gov.br/olinda/servico/PTAX/versao/v1/odata/CotacaoMoedaDia(moeda=@moeda,dataCotacao=@dataCotacao)?@moeda='%s'&@dataCotacao='%s'&$top=100&$format=json";

    private final Gson gson;
    private final CloseableHttpClient httpClient;

    public CliemteServicoBCB() throws Exception {
        this.gson = new Gson();
        this.httpClient = HttpClients.custom()
                .setSSLContext(new SSLContextBuilder()
                        .loadTrustMaterial(null, TrustAllStrategy.INSTANCE).build())
                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                .build();
    }

    /**
     * Busca a cotação do dólar para uma data específica no Banco Central.
     *
     * @param dataCotacao A data para a qual se deseja a cotação.
     * @return Uma string contendo um objeto json com a cotação.
     * @throws Exception Se ocorrer um erro durante a comunicação com a API ou
     * parsing do JSON.
     */
    public String lerCotacaoDolarDia(LocalDate dataCotacao) throws Exception {
        String dataFormatada = dataCotacao.format(DateTimeFormatter.ofPattern(
                FORMATO_DATA_BCB));
        String urlString = String.format(BASE_URL_COTACAO_DOLAR, dataFormatada);

        HttpGet request = new HttpGet(urlString);
        request.setHeader(HTTP_HEADER_ACCEPT, HTTP_HEADER_APPLICATION_JSON);

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == HTTP_STATUS_OK) {
                HttpEntity entity = response.getEntity();
                String jsonResponse = EntityUtils.toString(entity);

                CotacaoDolarApiResponse apiResponse = gson.fromJson(
                        jsonResponse, CotacaoDolarApiResponse.class);

                if (apiResponse != null && apiResponse.getValue() != null &&
                        !apiResponse.getValue().isEmpty()) {
                    return gson.toJson(apiResponse.getValue().get(0));
                } else {
                    CotacaoDolarDia resultadoEmBranco = new CotacaoDolarDia();
                    resultadoEmBranco.setDataHoraCotacao(dataCotacao.format(
                            DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    return gson.toJson(resultadoEmBranco);
                }
            } else {
                throw new Exception(
                        "Erro ao acessar a API Cotação: " + statusCode + 
                                ". Resposta: " + EntityUtils.toString(
                                response.getEntity()));
            }
        }
    }

    /**
     * Consulta a lista de moedas no Banco Central.
     *
     * @return Uma string JSON representando apenas o array de objetos Moeda.
     * @throws IOException Se ocorrer um erro durante a comunicação HTTP.
     * @throws Exception Se ocorrer um erro ao ler ou parsear o JSON.
     */
    public String lerMoeda() throws IOException, Exception {
        HttpGet request = new HttpGet(BASE_URL_MOEDAS);
        request.setHeader(HTTP_HEADER_ACCEPT, HTTP_HEADER_APPLICATION_JSON);

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == HTTP_STATUS_OK) {
                HttpEntity entity = response.getEntity();
                String fullJsonResponse = EntityUtils.toString(entity);

                MoedasApiResponse moedasApiResponse = gson.fromJson(
                        fullJsonResponse, MoedasApiResponse.class);

                if (moedasApiResponse != null && moedasApiResponse.getValue() 
                        != null) {
                    return gson.toJson(moedasApiResponse.getValue());
                } else {
                    return ARRAY_JSON_VAZIO; 
                }
            } else {
                throw new IOException("Erro ao acessar a API de Moedas: " + 
                        statusCode + ". Resposta: " + EntityUtils.toString(
                                response.getEntity()));
            }
        }
    }

    /**
     * Ler a cotação do dólar em um período.
     *
     * @param dataInicio A data de início do período.
     * @param dataFim A data de fim do período.
     * @return Uma string JSON representando apenas o array de objetos de 
     * cotação. Retorna uma string de array JSON vazio "[]" se nenhuma cotação 
     * for encontrada.
     * @throws IOException Se ocorrer um erro durante a comunicação HTTP.
     * @throws Exception Se ocorrer um erro ao ler ou parsear o JSON.
     */
    public String lerCotacaoDolarPeriodo(LocalDate dataInicio, LocalDate dataFim) throws IOException, Exception {
        String dataInicioFormatada = dataInicio.format(DateTimeFormatter.
                ofPattern(FORMATO_DATA_BCB));
        String dataFimFormatada = dataFim.format(DateTimeFormatter.
                ofPattern(FORMATO_DATA_BCB));
        
        String urlString = String.format(BASE_URL_COTACAO_DOLAR_PERIODO, 
                dataInicioFormatada, dataFimFormatada);

        HttpGet request = new HttpGet(urlString);
        request.setHeader(HTTP_HEADER_ACCEPT, HTTP_HEADER_APPLICATION_JSON);

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == HTTP_STATUS_OK) {
                HttpEntity entity = response.getEntity();
                String fullJsonResponse = EntityUtils.toString(entity);

                CotacaoDolarPeriodoApiResponse apiResponse = gson.fromJson(
                        fullJsonResponse, CotacaoDolarPeriodoApiResponse.class);

                if (apiResponse != null && apiResponse.getValue() != null) {
                    return gson.toJson(apiResponse.getValue());
                } else {
                    return ARRAY_JSON_VAZIO; 
                }
            } else {
                throw new IOException(
                        "Erro ao acessar a API de Cotação por Período: " + 
                                statusCode + ". Resposta: " + 
                                EntityUtils.toString(response.getEntity()));
            }
        }
    }

    /**
     * Ler a cotação de uma moeda específica para uma data.
     *
     * @param moeda O símbolo da moeda (ex: "EUR", "JPY").
     * @param dataCotacao A data da cotação.
     * @return Uma string JSON representando apenas o array de objetos de 
     * cotação da moeda. Retorna uma string de array JSON vazio "[]" se nenhuma
     * cotação for encontrada.
     * @throws IOException Se ocorrer um erro durante a comunicação HTTP.
     * @throws Exception Se ocorrer um erro ao ler ou parsear o JSON.
     */
    public String lerCotacaoMoedaDia(String moeda, LocalDate dataCotacao) throws IOException, Exception {
        String dataFormatada = dataCotacao.format(DateTimeFormatter.ofPattern(
                FORMATO_DATA_BCB));
        
        String urlString = String.format(BASE_URL_COTACAO_MOEDA_DIA, moeda, 
                dataFormatada);

        HttpGet request = new HttpGet(urlString);
        request.setHeader(HTTP_HEADER_ACCEPT, HTTP_HEADER_APPLICATION_JSON);

        try (CloseableHttpResponse response = httpClient.execute(request)) {
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == HTTP_STATUS_OK) {
                HttpEntity entity = response.getEntity();
                String fullJsonResponse = EntityUtils.toString(entity);

                CotacaoMoedaDiaApiResponse apiResponse = gson.fromJson(
                        fullJsonResponse, CotacaoMoedaDiaApiResponse.class);

                if (apiResponse != null && apiResponse.getValue() != null) {
                    return gson.toJson(apiResponse.getValue());
                } else {
                    return ARRAY_JSON_VAZIO; 
                }
            } else {
                throw new IOException(
                        "Erro ao acessar a API de Cotação de Moeda por Dia: " + 
                                statusCode + ". Resposta: " + 
                                EntityUtils.toString(response.getEntity()));
            }
        }
    }


    /**
     * Fechar o cliente HTTP quando ele não for mais necessário.
     */
    public void fecharConexao() { // Renomeado de closeHttpClient para fecharConexao
        try {
            if (httpClient != null) {
                httpClient.close();
            }
        } catch (IOException e) {
            System.err.println("Erro ao fechar o HTTP client: " + e.getMessage());
            e.printStackTrace();
        }
    }








}