/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.importacao.rotas.models;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TrajetosGTVe {

    private int parada;
    private String hora;
    private String tipo;
    private String classificacao;
    private String codcliorigem;
    private String codclidestino;
    private String horadestino;
    private int paradadestino;
    private String pedidocliente;
    private BigDecimal valor;
    private String moeda;
    private String observacao;
    private ClientesGTVe emissor;
    private ClientesGTVe origem;
    private ClientesGTVe destino;
    private VeiculosGTVe veiculo;
    private List<GuiasGTVe> guias;

    public int getParada() {
        return parada;
    }

    public void setParada(int parada) {
        this.parada = parada;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getClassificacao() {
        return classificacao;
    }

    public void setClassificacao(String classificacao) {
        this.classificacao = classificacao;
    }

    public String getCodcliorigem() {
        return codcliorigem;
    }

    public void setCodcliorigem(String codcliorigem) {
        this.codcliorigem = codcliorigem;
    }

    public String getCodclidestino() {
        return codclidestino;
    }

    public void setCodclidestino(String codclidestino) {
        this.codclidestino = codclidestino;
    }

    public String getHoradestino() {
        return horadestino;
    }

    public void setHoradestino(String horadestino) {
        this.horadestino = horadestino;
    }

    public int getParadadestino() {
        return paradadestino;
    }

    public void setParadadestino(int paradadestino) {
        this.paradadestino = paradadestino;
    }

    public BigDecimal getValor() {
        return valor;
    }

    public void setValor(BigDecimal valor) {
        this.valor = valor;
    }

    public String getMoeda() {
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }

    public ClientesGTVe getEmissor() {
        return emissor;
    }

    public void setEmissor(ClientesGTVe emissor) {
        this.emissor = emissor;
    }

    public ClientesGTVe getOrigem() {
        return origem;
    }

    public void setOrigem(ClientesGTVe origem) {
        this.origem = origem;
    }

    public ClientesGTVe getDestino() {
        return destino;
    }

    public void setDestino(ClientesGTVe destino) {
        this.destino = destino;
    }

    public VeiculosGTVe getVeiculo() {
        return veiculo;
    }

    public void setVeiculo(VeiculosGTVe veiculo) {
        this.veiculo = veiculo;
    }

    public List<GuiasGTVe> getGuias() {
        if(guias == null){
            guias = new ArrayList<>();
        }
        return guias;
    }

    public void setGuias(List<GuiasGTVe> guias) {
        this.guias = guias;
    }

    public String getHora() {
        return hora;
    }

    public void setHora(String hora) {
        this.hora = hora;
    }

    public String getPedidocliente() {
        return pedidocliente;
    }

    public void setPedidocliente(String pedidocliente) {
        this.pedidocliente = pedidocliente;
    }
}
