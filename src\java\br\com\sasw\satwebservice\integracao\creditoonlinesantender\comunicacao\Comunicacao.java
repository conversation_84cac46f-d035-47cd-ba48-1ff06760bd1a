/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.integracao.creditoonlinesantender.comunicacao;

import Arquivo.ArquivoLog;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.satwebservice.integracao.creditoonlinesantender.bean.Autorizacao;
import br.com.sasw.satwebservice.integracao.creditoonlinesantender.bean.Chave;
import br.com.sasw.satwebservice.integracao.creditoonlinesantender.seguranca.ChavePrivada;
import br.com.sasw.satwebservice.integracao.creditoonlinesantender.seguranca.Cripto;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.MalformedURLException;
import java.net.URL;
import javax.net.ssl.HttpsURLConnection;
import org.apache.commons.codec.binary.Base64;

/**
 *
 * <AUTHOR>
 */
public class Comunicacao {
    
    private final String X_APPLICATION_KEY_PRESERVE = "8aa649b032010137b970005056a171c8";
    private final String X_APPLICATION_KEY_TRANSVIP = "a745dfa032010137b976005056a171c8";
    
    private String X_APPLICATION_KEY;
    
    private final String URL_AUTENTICAR = "https://esbapihi.santander.com.br/system-security/v2/signature-authentication";
    private final String URL_CHAVES = "https://esbapihi.santander.com.br/cryptographic-security/v1/key/public";
    private final String URL_OBTER_CREDITO = "https://esbapihi.santander.com.br/automated-teller-machine/v1/partner/get-credit";
    private final String URL_REALIZAR_CREDITO = "https://esbapihi.santander.com.br/automated-teller-machine/v1/partner/make-credit";
    
    public Comunicacao(String empresa, String corpo) throws Exception{
        switch(empresa){
            case "SATTRANSVIP":
                this.X_APPLICATION_KEY = this.X_APPLICATION_KEY_TRANSVIP;
                this.chavePrivada = new ChavePrivada("transvip.pem");
                break;
            case "SATPRESERVE":
                this.X_APPLICATION_KEY = this.X_APPLICATION_KEY_PRESERVE;
                this.chavePrivada = new ChavePrivada("preserve.pem");
                break;
            default:
                throw new Exception("Empresa inválida");
        }
        
        this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\" + empresa + "\\"
                    + "\\creditoonlinesantender\\" + DataAtual.getDataAtual("SQL") + "\\log.txt";
        this.logerro = new ArquivoLog();

        this.corpo = corpo;

        this.autorizacao = this.chavePrivada.geraChavePrivada();
        this.cripto = Cripto.inicializar();
        this.chave = new Chave(this.cripto.getClientPublicKey());
    }
    
    private ChavePrivada chavePrivada;
    private final Autorizacao autorizacao;
    private final Cripto cripto;
    private final Chave chave;
    
    private final String caminho;
    private final ArquivoLog logerro;
    
    private String x_access_token;
    private String x_uid;
    private String bearer;
    private final String corpo;
    
    private URL url;
    private HttpsURLConnection httpsURLConnection;
    private OutputStream outputStream;
    private OutputStreamWriter outputStreamWriter;
    private int responseCode;
    private String response, error;
    
    public void autenticar() throws Exception{
        try{
            this.url = new URL(this.URL_AUTENTICAR);
            this.httpsURLConnection = (HttpsURLConnection) this.url.openConnection();

            //add reuqest header
            this.httpsURLConnection.setRequestMethod("POST");
            this.httpsURLConnection.setDoInput(true);
            this.httpsURLConnection.setDoOutput(true);
            this.httpsURLConnection.addRequestProperty("X-Application-Key", this.X_APPLICATION_KEY);

            this.outputStream = this.httpsURLConnection.getOutputStream();
            this.outputStreamWriter = new OutputStreamWriter(this.outputStream, "UTF-8");    
            this.outputStreamWriter.write(this.autorizacao.toString());
            this.outputStreamWriter.flush();
            this.outputStreamWriter.close();
            this.outputStream.close();

            this.httpsURLConnection.connect();
            this.responseCode = this.httpsURLConnection.getResponseCode();
            if(this.responseCode != 200){
                this.error = getErro(this.httpsURLConnection);
                this.httpsURLConnection.disconnect();
                
                this.logerro.Grava(this.responseCode+" "+this.error, this.caminho);
                throw new Exception(this.responseCode+" "+this.error);
            } else {
                this.x_access_token = this.httpsURLConnection.getHeaderField("x-access-token");
                this.x_uid = this.httpsURLConnection.getHeaderField("x-uid");
                
                this.bearer = new String(Base64.encodeBase64(("Bearer " + this.x_uid+":"+this.x_access_token).getBytes()), "UTF-8");
                
                this.response = getResposta(this.httpsURLConnection);
                this.httpsURLConnection.disconnect();
                this.logerro.Grava("obterCredito\r\n" + this.response, this.caminho);
            }
        } catch (MalformedURLException e){
            this.logerro.Grava(e.getMessage(), this.caminho);
            throw new Exception("MalformedURLException");
        } catch (IOException e){
            this.logerro.Grava(e.getMessage(), this.caminho);
            throw new Exception("IOException");
        }
    }
    
    public void trocarChaves() throws Exception {
        try{
            this.url = new URL(this.URL_CHAVES);
            this.httpsURLConnection = (HttpsURLConnection) this.url.openConnection();
            
            //add reuqest header
            this.httpsURLConnection.setRequestMethod("POST");
            this.httpsURLConnection.setDoInput(true);
            this.httpsURLConnection.setDoOutput(true);
            this.httpsURLConnection.addRequestProperty("X-Application-Key", this.X_APPLICATION_KEY);
            this.httpsURLConnection.setRequestProperty("Authorization", this.bearer);

            this.outputStream = this.httpsURLConnection.getOutputStream();
            this.outputStreamWriter = new OutputStreamWriter(this.outputStream, "UTF-8");    
            this.outputStreamWriter.write(this.chave.toString());
            this.outputStreamWriter.flush();
            this.outputStreamWriter.close();
            this.outputStream.close();
            
            this.httpsURLConnection.connect();
            this.responseCode = this.httpsURLConnection.getResponseCode();
            if(this.responseCode != 200){
                this.error = getErro(this.httpsURLConnection);
                
                this.httpsURLConnection.disconnect();
                
                this.logerro.Grava(this.responseCode+" "+this.error, this.caminho);
                throw new Exception(this.responseCode+" "+this.error);
            } else {
                this.response = getResposta(this.httpsURLConnection);
                
                this.httpsURLConnection.disconnect();
                
                this.logerro.Grava("trocarChaves\r\n" + this.response, this.caminho);
                this.cripto.setServerPublicKey(new JsonParser().parse(this.response).getAsJsonObject().get("serverPublicKey").getAsString());
            }
        } catch (JsonSyntaxException e){
            this.logerro.Grava(e.getMessage(), this.caminho);
            throw new Exception("JsonSyntaxException");
        }catch (MalformedURLException e){
            this.logerro.Grava(e.getMessage(), this.caminho);
            throw new Exception("MalformedURLException");
        } catch (IOException e){
            this.logerro.Grava(e.getMessage(), this.caminho);
            throw new Exception("IOException");
        }           
    }
    
    public String obterCredito() throws Exception{
        try{
            this.url = new URL(this.URL_OBTER_CREDITO);
            this.httpsURLConnection = (HttpsURLConnection) this.url.openConnection();
            
            //add reuqest header
            this.httpsURLConnection.setRequestMethod("POST");
            this.httpsURLConnection.setDoInput(true);
            this.httpsURLConnection.setDoOutput(true);
            this.httpsURLConnection.addRequestProperty("X-Application-Key", this.X_APPLICATION_KEY);
            this.httpsURLConnection.setRequestProperty("Authorization", this.bearer);

            this.outputStream = this.httpsURLConnection.getOutputStream();
            this.outputStreamWriter = new OutputStreamWriter(this.outputStream, "UTF-8");    
            this.outputStreamWriter.write(this.corpo);
            this.outputStreamWriter.flush();
            this.outputStreamWriter.close();
            this.outputStream.close();

            this.httpsURLConnection.connect();
            this.responseCode = this.httpsURLConnection.getResponseCode();
            if(this.responseCode != 200){
                this.error = getErro(this.httpsURLConnection);
                
                this.httpsURLConnection.disconnect();
                
                this.logerro.Grava(this.responseCode+" "+this.error, this.caminho);
                throw new Exception(this.responseCode+" "+this.error);
            } else {
                this.response = getResposta(this.httpsURLConnection);
                this.httpsURLConnection.disconnect();
                this.logerro.Grava("obterCredito\r\n" + this.response, this.caminho);
                return this.response;
            }
        } catch (MalformedURLException e){
            this.logerro.Grava(e.getMessage(), this.caminho);
            throw new Exception("MalformedURLException");
        } catch (IOException e){
            this.logerro.Grava(e.getMessage(), this.caminho);
            throw new Exception("IOException");
        }  
    }
    
    public String realizarCredito() throws Exception{
        try{
            this.url = new URL(this.URL_REALIZAR_CREDITO);
            this.httpsURLConnection = (HttpsURLConnection) this.url.openConnection();
            
            //add reuqest header
            this.httpsURLConnection.setRequestMethod("POST");
            this.httpsURLConnection.setDoInput(true);
            this.httpsURLConnection.setDoOutput(true);
            this.httpsURLConnection.addRequestProperty("X-Application-Key", this.X_APPLICATION_KEY);
            this.httpsURLConnection.setRequestProperty("Authorization", this.bearer);

            this.outputStream = this.httpsURLConnection.getOutputStream();
            this.outputStreamWriter = new OutputStreamWriter(this.outputStream, "UTF-8");    
            this.outputStreamWriter.write(this.corpo);
            this.outputStreamWriter.flush();
            this.outputStreamWriter.close();
            this.outputStream.close();

            this.httpsURLConnection.connect();
            this.responseCode = this.httpsURLConnection.getResponseCode();
            if(this.responseCode != 200){
                this.error = getErro(this.httpsURLConnection);
                
                this.httpsURLConnection.disconnect();
                
                this.logerro.Grava(this.responseCode+" "+this.error, this.caminho);
                throw new Exception(this.responseCode+" "+this.error);
            } else {
                this.response = getResposta(this.httpsURLConnection);
                this.httpsURLConnection.disconnect();
                this.logerro.Grava("obterCredito\r\n" + this.response, this.caminho);
                return this.response;
            }
        } catch (MalformedURLException e){
            this.logerro.Grava(e.getMessage(), this.caminho);
            throw new Exception("MalformedURLException");
        } catch (IOException e){
            this.logerro.Grava(e.getMessage(), this.caminho);
            throw new Exception("IOException");
        }  
    }
    
    private BufferedReader reader;
    private StringBuilder stringBuilder;
    private String line;
    
    private String getErro(HttpsURLConnection conn) throws IOException{
        this.reader = new BufferedReader(new InputStreamReader((conn.getErrorStream())));
        this.stringBuilder = new StringBuilder();
        while ((this.line = this.reader.readLine()) != null) {
            this.stringBuilder.append(this.line);
        }
        this.reader.close();
        
        String err = this.stringBuilder.toString();
        if(err.contains("mensagemAltair")){
            err = new JsonParser().parse(this.stringBuilder.toString())
                    .getAsJsonObject().get("mensagemAltair")
                    .getAsJsonObject().get("erroAltair")
                    .getAsJsonObject().get("mensagemErro").getAsString();
        }
         
        return err;
    }
    
    private String getResposta(HttpsURLConnection conn) throws IOException{
        this.reader = new BufferedReader(new InputStreamReader((InputStream) conn.getContent()));
        this.stringBuilder = new StringBuilder();
        while ((this.line = this.reader.readLine()) != null) {
            this.stringBuilder.append(this.line);
        }
        this.reader.close();
        
        String resp = this.stringBuilder.toString();
        if(resp.contains("mensagemAltair")){
            resp = new JsonParser().parse(this.stringBuilder.toString())
                    .getAsJsonObject().get("mensagemAltair")
                    .getAsJsonObject().get("avisoAltair")
                    .getAsJsonObject().get("mensagemAviso").getAsString();
        }
        
        return resp;
    }

    public String getCaminho() {
        return caminho;
    }
}
