

.horario-ok{
    color: green !important;
}
.atrasado{
    color: red !important;
}

.excluido{
    color: cyan !important;
}

.sem-localizacao{
    background: yellow !important;
}

.ui-widget-content{
    border: none;
}

.ui-tabs{
    background: transparent;
}

.ui-tabs .ui-tabs-panel {
    background: white;
    border: 1px solid #d2d6de !important;
}

.cadastrar, .cadastrarMaior{
    width: 90vw;
    min-width: 100%;
}

@media all and (min-width: 768px) {
    .cadastrar{
        width: 700px;
    }

    .cadastrarMaior{
        width: 95vw;
        height: 100vh !important;
    }
}

.dlgCadastrar{
    height: auto; max-height:98% !important; min-width:95% !important; max-width:95% !important;
    border:thin solid #666 !important; box-shadow:0px 0px 5px #303030 !important; overflow: hidden !important; 
    overflow-y: auto !important; padding-bottom:20px !important;border-top:4px solid #3C8DBC !important;
    border-radius:8px !important; font-family:'Open Sans', sans-serif !important; padding:0px 0px 20px 0px !important; 
    background-color:#EEE !important;
}

.dlgCadastrar .ui-dialog-content{
    padding-bottom: 10px !important;
    padding-top: 0px !important;
}

.tabela{
    display: flex;
    flex-direction: column;
    font-size: 12px;
    background: white;
    padding:0px !important;
    margin:0px !important;
    min-height:100% !important;
    max-width:100% !important;
    height:100% !important;
    max-height:100% !important;
    width:100% !important;
}

@media only screen and (max-width: 700px) and (min-width: 10px) {

   /* 
   
   NÃO DESCOMENTAR ISSO....
   ESTÁ "QUEBRANDO" AS INPUTS DE DATA EM CELULARES!!!
   
   #divDadosFilial,
    #divDadosFilial div,
    .FilialNome,
    .FilialEndereco,
    .FilialBairroCidade{
        min-width:100% !important;
        width:100% !important;
        max-width:100% !important;
        text-align: center !important;
    }*/

    .ui-paginator-top {
        white-space: normal !important;
    }

    .tabela .ui-datatable-scrollable-body{
        flex-grow: 1;

    }
}

@media only screen and (max-width: 2000px) and (min-width: 701px) {
    .DataGrid{
        width:100% !important;
        border:none !important
    }
}             

#divCorporativo{
    bottom:30px !important;
}

#corporativo {
    max-width: 18vw;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

#corporativo label[ref="lblCheck"]{
    font-size:11px !important;
    min-width:75px !important;
    font-weight:500 !important;
}

.ui-paginator{
    color: #333333 !important;
    font-weight: normal !important;
    font-size: 14px;
}

.ui-paginator-bottom{
    display: none;
}

footer .ui-chkbox-box {
    max-width: 12px !important;
    max-height: 12px !important;
}

.ui-dialog .ui-panel-content {
    height: auto !important;
}

.ui-selectonemenu{
    min-width: auto !important;
}

.ui-selectonemenu.ui-state-default {
    background: #fff !important;
}

.ui-selectonemenu.ui-state-disabled {
    color: #555 !important;
    background: #f7f7f7 !important;
    opacity: 0.7 !important;
}

.ui-chkbox .ui-chkbox-box {
    background: #fff;
}

#body {
    height: calc(100% - 40px);
    position: relative;
    display: flex;
    flex-direction: column;
}

#main {
    flex-grow: 1;
}

.ui-datatable-scrollable-body,
.FundoPagina > .ui-panel-content {
    height: 100% !important;
}

.FundoPagina {
    border: thin solid #CCC !important;
    border-top:4px solid #3C8DBC !important;
}

.texto-centro {
    text-align: center;
}

.ui-datatable-scrollable-theadclone {
    visibility: collapse;
}

.azul{
    color: #0043ff !important;
}

.verde{
    color: green !important;
}

.ciano{
    color: cyan !important;
}

.preto{
    color: black !important;
}

.max-700{
    max-width: 700px
}

.max-900{
    max-width: 900px
}

.coluna-top{
    top: 0px;
    position: absolute;
}

.negrito, .negrito-normal{
    font-weight: bold;
}

.fonte-menor{
    font-size: 0.8em !important;
}

.ui-datatable thead th{
    white-space:pre-line !important;
}

.contratovencido {
    background-color: yellow !important;
    background-image: none !important;
}

.inativo {
    color: red !important;
}

.aguardando{
    position: absolute;
    top: 0;
    left: 0;
    text-align: center;
    height: 100vh;
    width: 100%;
    background: transparent;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
}

.ui-blockui-content {
    position: absolute;
    text-align: center;
    padding: 0px !important;
    width: 80%;
}

.progresso{
    margin: 0 !important;
}

.progresso .ui-progressbar-value { 
    background-image: url('../img/pb.gif') !important;
    height: 50px;
}

.progressoRodape{
    width: 100%;
    height:6px;
}

.progressoRodape .ui-progressbar .ui-progressbar-value.ui-widget-header,
.progressoRodape .ui-progressbar.ui-widget-content{
    height:6px;
    font-size: 12px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    border: 0 none;
    margin: 0 !important;
    background-color: #022a48 !important;
    background-image: -moz-linear-gradient(top, #dbdbdb, #dbdbdb);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#dbdbdb), to(#dbdbdb));
    background-image: -webkit-linear-gradient(top, #dbdbdb, #dbdbdb);
    background-image: -o-linear-gradient(top, #dbdbdb, #dbdbdb);
    background-image: linear-gradient(top, #dbdbdb, #dbdbdb);
    background-image: -ms-linear-gradient(top, #dbdbdb, #dbdbdb);
    background-repeat: repeat-x;
    filter: progid:dximagetransform.microsoft.gradient(startColorstr='#149bdf', endColorstr='#0480be', GradientType=0);
    -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    -moz-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-transition: width 0.6s ease;
    -moz-transition: width 0.6s ease;
    -ms-transition: width 0.6s ease;
    -o-transition: width 0.6s ease;
    transition: width 0.6s ease;
}

.ui-selectonemenu-filter-container{
    width: 100%;
}

.ui-state-highlight .ui-icon {
    background-image: url('/SatMobWeb/javax.faces.resource/images/ui-icons_333333_256x240.png.xhtml?ln=primefaces-bootstrap');
}

.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
}

.ui-selectonemenu-panel .ui-selectonemenu-filter{
    width: 100%;
}

.ui-selectonemenu-panel.hideDisabled .ui-selectonemenu-item.ui-state-disabled, .hideDisabled .ui-state-disabled {
    display: none;
}
/*
.ui-state-error, .ui-widget-content .ui-state-error, .ui-widget-header .ui-state-error {
    border: 1px solid #eed3d7 !important;
    background: rgba(2,42,72,0.3) repeat-x !important;
    color: #16244a !important;
}
*/
* { 
    font-family: "Source Sans Pro","Trebuchet MS","Helvetica Neue",Helvetica,Arial,sans-serif;
    outline: 0 !important;
}

.ui-fileupload-content{
    background: transparent;
    padding: 0px;
}

.ui-fileupload-files{
    width: 100%;
    background: white;
}

.ui-fileupload{
    width: 100%;
    height: 140px;
    border: 1px solid grey;
    background: white;
    border-radius: 3px;
}

.ui-fileupload-preview{
    display: none;
}

.ui-fileupload-progress{
    width: 100%;
}

.ui-fileupload-content .ui-progressbar{
    width: 100%;
}

.ui-blockui {
    background: #2384cd !important;
    opacity: 0.60;
}

.ui-dialog-footer{
    text-align: center !important;
}

.celula-right {
    text-align: right !important;
}

header {
    display: table;
    vertical-align: middle;
    z-index: 999;
    left: 0;
    top: 0;
    width: 100%;
    min-height: 63px;
    height: auto;
    overflow: auto;

}

.btnLogar{
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
}

.cabecalho{
    color: black;
    font-size: 20px;
}

.subcabecalho{
    color: grey;
    font-style: italic;
}

.cabecalhoFilial{
    border-radius: 0px;
}

.ui-growl {
    z-index: 999999 !important;
}

.ui-state-disabled, .ui-widget-content .ui-state-disabled{
    opacity: 0.7;
}

h3 {
    margin: 0;
    padding: 0;
    font-weight: bold;
}

.ui-datatable thead th {
    padding: 4px 3px;
}

.tabela{
    white-space:pre-line !important;
}

.tabela .ui-widget-content, .tabelaGuia .ui-widget-content{
    background: white;
}

.tabela td, .tabelaGuia td{
    padding: 4px 3px !important;
    margin-bottom: 0px;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabela .ui-datatable-selectable, .tabelaGuia .ui-datatable-selectable{
    border: 1px solid #dddddd !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabela .ui-datatable-scrollable-header-box, .tabelaGuia .ui-datatable-scrollable-header-box{
    background: white;
}
.tabela .ui-datatable-data .ui-widget-content, .tabelaGuia .ui-datatable-data .ui-widget-content{
    border: 1px solid #dddddd !important;
}
.tabela .ui-state-highlight , .tabelaGuia .ui-state-highlight { 
    background-color: #0082C3 !important; 
    background-image: none !important;
    color: white !important;
} 
.tabela .ui-state-hover , .tabelaGuia .ui-state-hover { 
    background-color: #E6E6E6 !important; 
    background-image: none !important;
    color: black !important;
}
.tabela .ui-datatable-scrollable-body , .tabelaGuia .ui-datatable-scrollable-body {
    background: white !important;
}

.tabela .ui-datatable-scrollable-body{
    height: calc(100vh - 66px - 30px - 28px - 66px - 20px);
    background:transparent;
}

.tabela .ui-datatable-scrollable-body.toggled{
    height: calc(100vh - 66px - 30px - 28px - 20px);
}

.ui-widget-header{
    background-color: transparent !important;
    border-style: none;
    color:#022a48;
}

.ui-dialog.ui-widget-conten.ui-dialog-titlebar{
    border-style: none;  
}

.ui-dialog-titlebar{
    background-color: transparent !important;
    border-style: none;
    border: none;
}

.ui-panel{
    padding: 0px !important;
}
.ui-panel .ui-panel-content{
    padding: 0px !important;
}  


.tabelaArquivos .ui-widget-content{
    background: white;
}
.tabelaArquivos td{
    padding: 4px 3px !important;
    margin-bottom: 0px;
    border: none !important;
    background: transparent !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabelaArquivos .ui-datatable-selectable{
    border: none !important;
    background: transparent !important;
    font-family: Tahoma, Verdana, Segoe, sans-serif
}
.tabelaArquivos .ui-datatable-scrollable-header-box{
    background: white;
}
.tabelaArquivos .ui-datatable-data .ui-widget-content{
    border: none !important;
}
.tabelaArquivos .ui-state-highlight { 
    background-color: #0082C3 !important; 
    background-image: none !important;
    color: white !important;
} 
.tabelaArquivos .ui-state-hover { 
    background-color: #E6E6E6 !important; 
    background-image: none !important;
    color: black !important;
}

.teste .ui-panelgrid .ui-panelgrid-cell {
    background-color: transparent;
}

.ui-panelgrid .ui-panelgrid-cell{
    border-style:none;
    padding-right: 5px;
    padding-left: 5px;
    padding-top: 0px
}

.ui-panelgrid-icones .ui-panelgrid-icones-cell{
    border-style:none;
    padding-right: 25px;
    padding-left: 0px;
    padding-top: 0px
}

.ui-panelgrid-cell {
    border-style: none;
    padding: 0px;
}

/*
.glyphicon-user {
    float: left;
    font-size: 30px;
    margin-right: 15px;
}
*/

/* Bootstrap overidding */
.container {
    max-width: 992px;
}

#h {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    min-height: 100%;
    height: auto;
    background: url('../img/bk.jpg') no-repeat bottom center fixed;
    background-color: #022a48;
    background-size: cover;
    color: white;
}
#graficosH{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    min-height: 100%;
    height: auto;
    background: url('../img/bk-blue.jpg') no-repeat bottom center fixed;
    background-size: cover;
    color: white;
}
.element-sx { float: left !important; }
.element-dx { float: right !important;}
.cadastro {
    font-size: 12px;
    position: relative;
    max-width: 714px;
    padding: 20px 15px 15px 15px;
    margin: 0 auto;
    height: auto;
    background-size: 62%;
}

.h-wrapper, .h-wrapper-com-footer {
    position: relative;
    max-width: 714px;
    padding: 420px 15px 15px 15px;
    margin: 0 auto;
    height: auto;
    background: url('../img/st_back.png') no-repeat top center;
    background-size: 62%;
}
.mt {
    position: relative;
    margin-bottom: 5px;
    font-size: 20px;
    line-height: 20px;
}
.sate-element {
    text-align: center;
    position: relative;
    width: auto;
    cursor: pointer;
}
.sate-element img {
    max-width: 90px;
}
.sate-element p {
    font-size: 12px;
}
.sate-icon-grid {
    position: relative;
    max-width: 950px;
    margin: 0 auto;
}

.sate-icon-wrapper {
    display: inline-block;
    vertical-align: middle;
    line-height: normal;
    position: absolute;
    top: 20px;
    width: 100%;
    overflow: auto;
    z-index: 998;
    /* background: #f0f; */
}
/*.ui-widget-content {
    
    background: url('../img/bk_1.jpg') ;
    background-size:100% 100%;
    -webkit-background-size: 100% 100%;
    -o-background-size: 100% 100%;
    -khtml-background-size: 100% 100%;
    -moz-background-size: 100% 100%;
    color: #fff !important;
    border: none;
    
    background: #142247;
    background: -moz-linear-gradient(top,  #142247 1%, #2d67a6 100%);
    background: -webkit-linear-gradient(top,  #142247 1%,#2d67a6 100%);
    background: linear-gradient(to bottom,  #142247 1%,#2d67a6 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#142247', endColorstr='#2d67a6',GradientType=0 );
    
}*/
/*
.ui-inputfield {
    background: none !important;
}
*/

.janelaConfirmar{
    padding-right: 360px;
    float: right;
}

.janelaCadastroBarraTitulo{
    background-color: #265a88;
    height: 40px;
    padding: 5px;
}
.janelaCadastroBarraStatus{
    background-color: #fff;
    height: 40px;
    padding: 5px;
    padding-left: 10px;
    font-size: 11px !important;
    font-weight: normal !important;
    color: #000 !important;
    text-align: center;
}

.janelaMenuPrincipal .ui-widget-content{
    background: url('../img/bk.jpg') ;
    background-size:100% 100%;
    -webkit-background-size: 100% 100%;
    -o-background-size: 100% 100%;
    -khtml-background-size: 100% 100%;
    -moz-background-size: 100% 100%;
    color: #fff !important;
    border: none !important;
}

.janelaCadastro th{
    font-size: 11px;
}
.janelaCadastro td{
    color: #000 !important;
    font-size: 13px;
}
.janelaCadastro .ui-widget-content{
    color: #fff !important;
    border: none;
}
.janelaCadastro{
    background:#fff !important;
}
.ui-selectonemenu-items{
    background:#fff !important;
    color: #000 !important;
    font-size: 13px;
}
.janelaCadastro table{
    width: 100%;
}
.janelaCadastro table .ui-outputlabel.ui-widget{
    color:#000 !important;
    text-align: right;
    width: 100%;
}
.janelaCadastro table td, th{
    padding: 1px;
}
.janelaCadastro table td, th{
    padding: 1px;
}
.janelaCadastro .ui-selectlistbox-listcontainer{
    width: 80px;
}
/*.janelaCadastro .ui-inputfield {
    width: 90px;
}*/
.janelaCadastro input {
    min-height: 20px;
}
.janelaCadastro .ui-dialog,
.janelaCadastro .ui-dialog-content{
    padding: 0px !important;
}
.ui-tabs .ui-tabs-nav li a{
    font-size: 13px;
}
#formCCheques,
#formRH, 
#formfgts{
    width: 390px;
}
#formCCheques table,
#formfgts table {
    text-align: center;
    width: 100%;
}
#formfgts table .ui-outputlabel.ui-widget{

    text-align: right;
    width: 100%;
}
#formfgts table td, th{
    padding: 1px;
}
#formfgts .ui-selectlistbox-listcontainer,
#formCCheques .ui-selectlistbox-listcontainer {
    width: 80px;
}
#formfgts .ui-inputfield,
#formCCheques .ui-inputfield {
    width: 90px;
}
#formfgts input {
    min-height: 20px;
}
#formFmsgs .ui-state-hover {
    background: transparent !important;
    color: white !important;
    cursor: default !important;
    border: none !important;
    box-shadow: none !important;
}
.xqsate-login,
.sate-login,
.sate-login-altera,
.sate-login-definir,
.sate-login-primo,
.sate-login-quest,
.sate-login-troca {
    position: relative;
    margin-bottom: 10px;    
}
.sate-login img {
    /* float: right; */
}

.sate-login-primo select,
.sate-login-primo .ui-selectonemenu,
.sate-login-primo .ui-selectonemenu-label
{ width: 300px; }

.sate-login-primo .ui-selectonemenu,
.sate-login-primo .ui-inputfield {
    /* min-height: 40px; */
    background: #fff;
    box-shadow: none;
    padding: 4px;
}

.ui-selectonemenu-trigger {
    padding: 0;
    width: 30px;
}

.ui-selectonemenu .ui-selectonemenu-trigger {
    width: 2em;
}

.ui-selectonemenu-trigger .ui-icon {
    margin: 10px auto 0 auto;
}

/* controla a largura dos campos de inputs por cada tela */

.sate-login input[type=text],
.sate-login input[type=password] {
    width: 270px;
    height: 40px;
}

.sate-login-altera input[type=password] {
    width: 210px;
}
.sate-login-troca input[type=text],
.sate-login-primo input[type=text] {
    width: 300px;
    height: 40px;
}
.sate-login-quest input[type=text],
.sate-login-definir input[type=text],
.sate-login-definir input[type=password] {
    width: 317px;
}
.rodape-links {
    z-index: 999;
    position: relative;
    width: 100%;
    height: auto;
}
.rodape-links ul {
    margin: 0;
    padding: 0;
    list-style: none;
    text-align: center;
}
.rodape-links ul li {
    display: inline-block;
    padding: 3px;
    margin: 0;
    cursor: pointer;
}
.rodape-links ul li img {
    margin: 0 5px;
}
.rodape-wrapper {
    background: #328fca;
    background: -moz-linear-gradient(top,  #328fca 0%, #16244a 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#328fca), color-stop(100%,#16244a));
    background: -webkit-linear-gradient(top,  #328fca 0%,#16244a 100%);
    background: -o-linear-gradient(top,  #328fca 0%,#16244a 100%);
    background: -ms-linear-gradient(top,  #328fca 0%,#16244a 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#328fca', endColorstr='#16244a',GradientType=0 );
    text-align: center;
    border-radius: 5px;
    padding-bottom: 14px;
    margin-bottom: 5px;
}
.rodape-wrapper ul {
    margin: 0 auto;
    padding: 0;
    list-style: none;
    display: table;
}
.rodape-wrapper ul li {
    display: table-cell;
    height: auto;
    width: auto;
    padding: 14px 5px 0 5px;
}
.rodape-wrapper ul li img {
    max-width: 130px;
}
.ui-clock {
    border: 0px;
    background: none;
    font-size: 11px;
    line-height: 12px;
    font-family: sans-serif;
    font-weight:normal;
    color: white;
    text-shadow: none;
}
.ui-growl {  
    top: 20%;  
    right: 40%;
}

.glyphicon-refresh:before {
    content:"\e031"
}
.glyphicon-list-alt:before {
    content:"\e032"
}

.ui-icon-arrowrefresh-1-w {
    background-position:-128px -64px
}
.ui-icon-arrowrefresh-1-n {
    background-position:-144px -64px
}
.ui-icon-arrowrefresh-1-e {
    background-position:-160px -64px
}
.ui-icon-arrowrefresh-1-s {
    background-position:-176px -64px
}
.ui-icon-refresh {
    background-position:-64px -80px
}

.ui-growl-item-container{
    background-color: #3f6382 !important;
}

.custom-button {
    height: 40px;
    width: 130px;
    text-shadow: none;
    color: #6a4f02;
    background: #ebdc61;
    background: -moz-linear-gradient(top, #ebdc61 0%, #ab8611 100%);
    background: -webkit-linear-gradient(top, #ebdc61 0%,#ab8611 100%);
    background: linear-gradient(to bottom, #ebdc61 0%,#ab8611 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ebdc61', endColorstr='#ab8611',GradientType=0 );
}

#sPw {
    font-weight: bold;
    color: #ffd800;
}

/*** END HERE ***/

/*** 27/10/2015 ***/

.rodapeinfos {        
    font-size: 12px;
    line-height: 12px;
}
.rodape-text {
    font-size: 11px !important;
    line-height: 11px !important;
    font-weight: bold;
}

/*** END HERE ***/

.sate-error {
    position: relative;
    margin: 0 auto;
    padding: 15px;
    background: rgba(236,221,96,.7);
    border-radius: 4px;
}
a { 
    padding: 0;
    margin: 0;
    text-decoration: none; 
    -webkit-transition: background-color .4s linear, color .4s linear;
    -moz-transition: background-color .4s linear, color .4s linear;
    -o-transition: background-color .4s linear, color .4s linear;
    -ms-transition: background-color .4s linear, color .4s linear;
    transition: background-color .4s linear, color .4s linear;
    color: #fff;
    cursor: pointer;
}
a:hover, a:focus {
    text-decoration: none;
    color: #ffd800;
}
.nopadding {
    padding: 0 !important;
    margin: 0;
}
input {
    min-height: 30px;
}

#body{
    min-height: calc(100vh - 63px);
    margin-bottom: 63px;
    clear: both;
}
#body.toggled{
    min-height: calc(100vh - 20px);
    margin-bottom:20px;
}

#bodyKanban{
    min-height: calc(100vh - 63px - 20px);
    /*margin-bottom: 83px;*/
    clear: both;
    display: inline;
}
#bodyKanban.toggled{
    min-height: calc(100vh - 50px);
    margin-bottom:20px;
}

.ui-paginator{
    background:white !important;
}

.status{
    text-align: center;
    height: 20px;
    width: 20px;
    float: left;
    left: 50%;
    position: absolute;
}
.status.toggled{
    bottom: 40px;
}

footer {
    position: fixed;
    display: table;
    vertical-align: middle;
    z-index: 999;
    left: 0;
    bottom: 0;
    width: 100%;
    min-height: 63px;
    height: auto;
    overflow: auto;
    color: #fff;
    /* text-align: center; */
    font-size: 11px;
    line-height: normal;
}
.footer-toggler{
    width: 20px;
    float: right;
}
.footer-toggler.toggled{
    display:block;
}
footer.toggled .footer-body{
    display:none;
}
.footer-body{
    -webkit-box-shadow: 0px -5px 5px 0px rgba(0,0,0,0.15);
    -moz-box-shadow: 0px -5px 5px 0px rgba(0,0,0,0.15);
    box-shadow: 0px -5px 5px 0px rgba(0,0,0,0.15);
    background: #2384cd;
    background: -moz-linear-gradient(top,  #2384cd 0%, #3f6382 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#2384cd), color-stop(100%,#3f6382));
    background: -webkit-linear-gradient(top,  #2384cd 0%,#3f6382 100%);
    background: -o-linear-gradient(top,  #2384cd 0%,#3f6382 100%);
    background: -ms-linear-gradient(top,  #2384cd 0%,#3f6382 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2384cd', endColorstr='#3f6382',GradientType=0 );
}
footer table {
    width: 100%; /**/
    min-height: 60px;
    /* display: inline-block; */
    margin: 0;
}
footer img {
    max-width: 100%;
    max-height: 35px;
}
footer table tr td {
    padding: 4px; /**/
    /* border: 1px solid white; */
}
.footer-time {
    /* margin-top: 6px; */
    text-align: center; /**/
}
.footer-user {
    /* margin-top: 10px; */
    /* text-align: center; */
}
.footer-user img {
    cursor: pointer;
}

/* max-width: 768px */

@media all and (max-width: 768px) {
    .h-wrapper, .h-wrapper-com-footer {
        position: relative;
        max-width: 714px;
        padding: 420px 15px 15px 15px;
        margin: 0 auto;
        height: auto;
        background: url('../img/st_back.png') no-repeat top center;
        background-color: #022a48;
        background-size: 62%;
    }
    .h-wrapper {
        background-size: 90%;
    }
    .h-wrapper-com-footer {
        padding: 60px 15px 15px 15px;
    }
    .rodape-wrapper ul li {
        display: block;
        float: left;
        height: auto;
        width: 100%;
        padding: 14px 15px 0 15px;
    }
    .sate-element {
        float: none;
        /* background: #00f; */
    }
    .sate-login input[type=text],
    .sate-login input[type=password],
    .sate-login-troca input[type=text],
    .sate-login-definir input[type=password],
    .sate-login-quest input[type=text],
    .sate-login-primo input[type=text],
    .sate-login-ltera input[type=password],
    .sate-login-primo select,
    .sate-login-primo .ui-selectonemenu,
    .sate-login-primo .ui-selectonemenu-label,
    .sate-login-primo .ui-autocomplete,
    .sate-login-primo .ui-autocomplete-input,
    .sate-login-primo .ui-autocomplete-panel
    {
        width: 100%;
        min-width: 100%;
    }
    .sate-login-primo .btnLogar{
        text-align: center;
        display: block;
    }
    .base {
        margin: 55px auto 0 auto;
    }
    .col-topo {
        margin-top: 50px;
    }
    .footer-tables table {
        display: table;
        width: 100%;
        text-align: center;
    }
    .footer-time {
        display: none;
    }
    footer ul li {
        display: line-block;
    }
    .footer-logos {
        text-align: center;
    }
    .footer-user {
        text-align: center;
    }
}