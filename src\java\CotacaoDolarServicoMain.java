
import br.com.sasw.satwebservice.bcb.CliemteServicoBCB;
import java.time.LocalDate;

public class CotacaoDolarServicoMain {

    public static void main(String[] args) {

        try {
            CliemteServicoBCB service = new CliemteServicoBCB();
            LocalDate dataDesejada = LocalDate.of(2025, 8, 15); // Data que você especificou
            String cotacao = service.lerCotacaoDolarDia(dataDesejada);
            System.out.println("Cotação do Dólar para " + dataDesejada + ":");
            System.out.println("Cotação: " + cotacao);
        } catch (Exception e) {
            System.err.println("Ocorreu um erro ao buscar a cotação do dólar: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
