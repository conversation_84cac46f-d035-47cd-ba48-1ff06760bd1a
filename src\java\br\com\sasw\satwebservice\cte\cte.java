
package br.com.sasw.satwebservice.cte;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.XMLNFSE;
import SasDaos.XMLNFSEDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.StringReader;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

/**
 *
 * <AUTHOR>
 */
@WebServlet(name = "cte", urlPatterns = {"/cte"})
public class cte extends HttpServlet {

    private ArquivoLog logerro;
    private SasPoolPersistencia pool;

    @Override
    public void init() {
        pool = new SasPoolPersistencia();
        pool.setTamanhoPool(50);
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    /**
     * Processes requests for both HTTP <code>GET</code> and <code>POST</code>
     * methods.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    protected void processRequest(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        response.setContentType("text/html;charset=UTF-8");
        logerro = new ArquivoLog();
//        Trace.gerarTrace(getServletContext(), this.getServletName(), request.getParameterMap(), "cte", "cte", logerro);
        PrintWriter resp = response.getWriter();
        String autuso = request.getParameter("autuso");
        String xml = request.getParameter("xml");
        Boolean uso = (null != autuso && !autuso.equals("")) && !autuso.equals("0");
        
        String nota = request.getParameter("nota");
        String praca = request.getParameter("praca");
        
        String paramRequest = request.getParameter("param");
        String param = (null != paramRequest && !paramRequest.equals("")) ? paramRequest : "";
        try {
            if(nota != null && praca != null && paramRequest != null){
                Persistencia persistencia = pool.getConexao(param);

                XMLNFSEDao xmlnfseDao = new XMLNFSEDao();
                XMLNFSE xmlnnfse = xmlnfseDao.buscarNota(nota, praca, persistencia);

                xml = xmlnnfse.getXML_Envio();
                autuso = xmlnnfse.getNFERetorno();
                uso = (null != autuso && !autuso.equals("")) && !autuso.equals("0");
                persistencia.FechaConexao();
            }
            
            
            String arquivo;
            switch (param) {
                case "SATIBL":
                    arquivo = this.getClass().getResource("DACTE-OS_MOD_IBL.html").getPath().replace("%20", " ");
                    break;
                case "SATTSEG":
                    arquivo = this.getClass().getResource("DACTE-OS_MOD_TECNOSEG.html").getPath().replace("%20", " ");
                    break;
                default:
                    arquivo = this.getClass().getResource("DACTE-OS_MOD.html").getPath().replace("%20", " ");
                    break;
            }
            File fileDir = new File(arquivo);
            BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(fileDir), "UTF8"));
            InputSource is = new InputSource(new StringReader(xml));
            DocumentBuilder dBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            Document doc = dBuilder.parse(is);
            
            String caminho =  "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\cte\\" +
            DataAtual.getDataAtual("SQL") + ".txt";
            logerro.Grava(xml, caminho);
            
            Pattern tagPattern = Pattern.compile("@([a-zA-Z0-9])*[\\.a-zA-Z0-9]*");
            Matcher tagmatch;
            StringBuilder sb = new StringBuilder();
            String line = br.readLine();
            int i = 0;
            
            while (line != null) {//xmunini, xmunfim
                tagmatch = tagPattern.matcher(line);
                while (tagmatch.find()) {
                    String tag = tagmatch.group();
                    if(tag.equals("@IMAGEM")){
                        line = line.replace(tag, GetLogo(ProcuraTag(doc,"emit","CNPJ",i,uso)));
                    } else if(tag.equals("@autUSOXX")){
                        line = line.replace(tag, uso ? autuso : "SEM VALOR FISCAL ");
                    } else {
                        String[] xmlTag = tag.split("\\.");
                        if(xmlTag.length >= 2) {
                            if(xmlTag[1].equals("xMunIni")||xmlTag[1].equals("xMunFim")){
                                String cnpj = ProcuraTag(doc,"emit","CNPJ",i,uso);
                                if(cnpj.equals("02445414000150")
                                        || cnpj.equals("02445414000583")
                                        || cnpj.equals("02445414000400")
                                        || cnpj.equals("02445414000230")
                                        || cnpj.equals("02445414000664")
                                        || cnpj.equals("02445414000745")
                                        || cnpj.equals("04829165000121")
                                        || cnpj.equals("26729300000108")
                                        || cnpj.equals("26729300000108")
                                        || cnpj.equals("26729300000361")
                                        || cnpj.equals("26729300000523")
                                        || cnpj.equals("26729300000795")
                                        || cnpj.contains("06263849000649")
                                        || cnpj.contains("062638490")
                                        || cnpj.contains("26729300")
                                        || cnpj.contains("01873815")
                                        || cnpj.contains("08139850")
                                        || cnpj.contains("08165946")
                                        || cnpj.contains("08787673")
                                        || cnpj.contains("11179264")
                                        || cnpj.contains("15235637")
                                        || cnpj.contains("40864571")
                                        || cnpj.contains("07608821")                                        
                                        || cnpj.contains("06263849000134")
                                        || cnpj.contains("06263849000304")
                                        || cnpj.contains("06263849000649")
                                        || cnpj.contains("32139981000149")
                                        || cnpj.contains("07782730000130")                                        
                                        || cnpj.contains("06263849000568")                                        
                                        || cnpj.contains("00914803000313")){
                                    line = line.replace(tagmatch.group(), ProcuraTag(doc,xmlTag[xmlTag.length-2],xmlTag[xmlTag.length-1],i,uso));
                                } else {
                                    line = line.replace(tagmatch.group(), "");
                                }
                            } else {
                                line = line.replace(tagmatch.group(), ProcuraTag(doc,xmlTag[xmlTag.length-2],xmlTag[xmlTag.length-1],i,uso));
                                if(xmlTag[xmlTag.length-2].equals("Comp") && xmlTag[xmlTag.length-1].equals("vComp")) i++;
                            }
                        } else {
                            line = line.replace(tagmatch.group(), ProcuraAtributo(doc,tag));
                        }
                    }
                }
                sb.append(line);
                sb.append(System.lineSeparator());
                line = br.readLine();
            }
            resp.print(sb.toString());
        } catch (Exception e){
            String caminho =  "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\cte\\" +
            DataAtual.getDataAtual("SQL") + ".txt";
            logerro.Grava("<?xml version=\"1.0\"?>\r\n"
                    + "<resp>0</resp>\r\n"
                    + "<error>\r\n\t"
                    + "<cause>\r\n\t\t"
                    + e.getMessage() + "\r\n\t"
                    + "</cause>\r\n\t"
                    + "</error>", caminho);
            resp.print("<?xml version=\"1.0\"?>\r\n"
                    + "<resp>0</resp>\r\n"
                    + "<error>\r\n\t"
                    + "<cause>\r\n\t\t"
                    + e.getMessage() + "\r\n\t"
                    + "</cause>\r\n\t"
                    + "</error>");
        } finally{
            resp.close();
        } 
    }
    
    private String GetLogo(String cnpj){
        String logo;
        switch(cnpj){
            case "11179264000170":
            case "08787673000145":
            case "08787673000226":
            case "11179264000251":
            case "08165946000110":
            case "11179264000502":
            case "08139850000198":
            case "40864571000199":
            case "11179264000685":
            case "11179264000928":
            case "11179264000766":
            case "11179264001061":
            case "11179264000847":
            case "11179264001223":
            case "11179264001304":
            case "01873815000148":
            case "11179264001495":
            case "11179264001576":
                logo = "assets/logosCTE/LOGO_PRESERVE.png";
                break;
            case "02445414000150":
            case "02445414000583":
            case "02445414000400":
            case "02445414000230":
            case "02445414000664":
            case "02445414000745":
            case "04829165000121":  
                logo = "assets/logosCTE/LogoTVip.jpg";
                break;
            case "23245012000262":
            case "23245012000181":
            case "23245012000343":
            case "23245012000424":
            case "23245012000505":
            case "23245012000777":
            case "23245012000858":
            case "23245012000939":
            case "23245012001072":
            case "23245012001234":
            case "23245012001315":
            case "23245012001404":
            case "23245012001587":
            case "23245012001668":
                logo = "assets/logosCTE/logo_rodoban.jpg";
                break;
            case "02103266000195":
            case "06291321000179":
            case "09494448000183":
                logo = "assets/logosCTE/Logomarca Transexcel.jpg";
                break;
            case "02361081000180":
            case "02361081000261":
                logo = "assets/logosCTE/logotseg.jpg";
                break;
            case "07957111000210":
            case "07957111000130":
            case "07957111000482":
            case "07957111000300":
            case "04781359000102":
            case "04617596000124":
            case "07957111000644":
            case "07957111000725":
            case "07957111000806":
            case "20840697000116":
                logo = "assets/logosCTE/LogoCPV.jpg";
                break;
            case "26324424000103":
            case "31546484000364":
            case "31546484000526":
            case "26324424000286":
            case "26324424000367":
                logo = "assets/logosCTE/logoTransfederalCTE.png";                
                break;
            case "06145774000197":
                logo = "assets/logosCTE/logo_invioseg.png";                
                break;
            case "02060306000169":
                logo = "assets/logosCTE/logo_fenix.jpg";                
                break;
            case "26729300000108":
            case "28474085000169":
            case "26729300000361":
            case "26729300000795":
                logo = "assets/logosCTE/logo_/.jpg";                
                break;
            case "06263849000134":
            case "06263849000304":
            case "06263849000568":
            case "06263849000649":
            case "06263849000720":
                logo = "assets/logosCTE/Logo_Brasifort.png";                
                break;            
            case "08819936000150":
                logo = "assets/logosCTE/logo_Fidelys.png";                
                break; 
            case "10226121000100":
                logo = "assets/logosCTE/logo_Inviseg.png";                
                break;             
            case "07608821000154":
            case "07608821000740":
            case "07608821000316":
            case "07608821000588":
                logo = "assets/logosCTE/logo_Cefor.jpg";                
                break; 
            case "07782730000130":
                logo = "assets/logosCTE/logo_global.jpg";                
                break;                                             
            case "32139981000149":
                logo = "assets/logosCTE/logo_deltacorp.jpg";                
                break;                                                             
            case "10446347000116":
            case "10446347000205":
            case "10446347000469":
            case "10446347000540":                                
                logo = "assets/logosCTE/logo_LogoForca.jpg";                
                break;                 
            case "00914803000313":                                
                logo = "assets/logosCTE/logo_federal.jpg";                
                break;                 
            default:
                logo = "";
        }
        return logo;
    }
    
    
    /**
     * Retorna o antecedentes de um nó.
     * @param no
     * @return 
     */
    private String PossuiNoPai(Node no){
        if(null != no.getParentNode()){
            return PossuiNoPai(no.getParentNode()) +"/"+no.getNodeName();
        }
        return "";
    }
    
    /**
     * Busca um atributo de um nó - usado para bucar o id para o código de barras.
     * @param doc
     * @param tag
     * @return
     * @throws XPathExpressionException 
     */
    private String ProcuraAtributo(Document doc, String tag) throws XPathExpressionException{
        String retorno = "";
        if(tag.contains("@")) tag = tag.replace("@", "");
        NodeList nodeList = doc.getElementsByTagName(tag);
        for (int i = 0; i < nodeList.getLength(); i++){
            Node no = nodeList.item(i);
            XPathFactory xPathfactory = XPathFactory.newInstance();
            XPath xpath = xPathfactory.newXPath();
            XPathExpression expr = xpath.compile("/"+no.getParentNode().getNodeName()+"/"+(tag)+"[@Id]");
            NodeList nos = (NodeList) expr.evaluate(doc, XPathConstants.NODESET);
            retorno = nos.item(0).getAttributes().getNamedItem("Id").getNodeValue();
            if(retorno.contains("CTe")) retorno = retorno.replace("CTe", "");
            return retorno;
        }
        return retorno;
    }
    
    /**
     * Busca uma tag no xml. 
     * @param doc
     * @param pai
     * @param filho
     * @param indice - Usado para <Comp><xNome> e <Comp><vComp>
     * @return
     * @throws XPathExpressionException 
     */
    private String ProcuraTag(Document doc, String pai, String filho, int indice, boolean uso) throws XPathExpressionException{
        String retorno = "";
        if(pai.contains("@")) pai = pai.replace("@", "");
        NodeList nodeList = doc.getElementsByTagName(filho);
        for (int i = 0; i < nodeList.getLength(); i++){
            Node no = nodeList.item(i);
            if(null != no.getParentNode()){
                String busca = PossuiNoPai(no.getParentNode())+"/"+no.getNodeName();
                String param = pai+"/"+filho;
                if(busca.contains(param)) {
                    XPathFactory xPathfactory = XPathFactory.newInstance();
                    XPath xpath = xPathfactory.newXPath();
                    XPathExpression expr = xpath.compile(busca+"/text()");
                    NodeList nos = (NodeList) expr.evaluate(doc, XPathConstants.NODESET);
                    if(nos.getLength() == 1) retorno =  nos.item(0).getNodeValue();
                    else if(nos.getLength() > 1 && nos.getLength()> indice) retorno = nos.item(indice).getNodeValue();
                    else retorno = "";
                    /**
                     * Formata a saída da data e da hora.
                     */
                    if(filho.contains("dhEmi")){
                        String[] parts = retorno.split("T");
                        DateTimeFormatter d = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        LocalDate data = LocalDate.parse(parts[0], d);
                        
                        DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                        retorno = data.format(formatter2)+" "+parts[1].split("-")[0];
                    }
                    if(filho.contains("xObs")){
                        retorno = uso ? "<span class=\"fonte10\">"+retorno+"</span>" :
                                "<span class=\"fonte16\">SEM VALOR FISCAL</span><br><span class=\"fonte10\">"+retorno+"</span> ";
                    }
                    return retorno;
                }
            }
        }
        if(filho.contains("xObs")){
            retorno = uso ? "<span class=\"fonte10\">"+retorno+"</span>" :
                    "<span class=\"fonte16\">SEM VALOR FISCAL</span><br><span class=\"fonte10\">"+retorno+"</span> ";
        }
        return retorno;
    }
    
    // <editor-fold defaultstate="collapsed" desc="HttpServlet methods. Click on the + sign on the left to edit the code.">
    /**
     * Handles the HTTP <code>GET</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Handles the HTTP <code>POST</code> method.
     *
     * @param request servlet request
     * @param response servlet response
     * @throws ServletException if a servlet-specific error occurs
     * @throws IOException if an I/O error occurs
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        processRequest(request, response);
    }

    /**
     * Returns a short description of the servlet.
     *
     * @return a String containing servlet description
     */
    @Override
    public String getServletInfo() {
        return "Short description";
    }// </editor-fold>

}
