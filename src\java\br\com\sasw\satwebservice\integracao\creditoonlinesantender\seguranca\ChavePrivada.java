package br.com.sasw.satwebservice.integracao.creditoonlinesantender.seguranca;

import br.com.sasw.satwebservice.integracao.creditoonlinesantender.bean.Autorizacao;
import br.com.sasw.satwebservice.integracao.creditoonlinesantender.excecoes.ApplicationException;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.SignatureException;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import org.apache.commons.codec.binary.Base64;

public class ChavePrivada {

    private final String arquivoPem;
    private final String systemAcronym = "YV";
    private RSAPrivateKey rsaPrivateKey;
    private String nonce;
    private long timestamp;
    private String signature;

    public ChavePrivada(String arquivoPem) {
        try{
            this.arquivoPem = ChavePrivada.class.getResource(arquivoPem).getPath().replace("%20", " ");;
        } catch (Exception e){
            throw new ApplicationException("Arquivo não encontrado.");
        }
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getSystemAcronym() {
        return systemAcronym;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public RSAPrivateKey getRsaPrivateKey() {
        return rsaPrivateKey;
    }

    public void setRsaPrivateKey(RSAPrivateKey rsaPrivateKey) {
        this.rsaPrivateKey = rsaPrivateKey;
    }

    public void inicializaRSAPrivateKey() {

        byte[] pemContent;

        if (arquivoPem != null) {
            try {
                pemContent = readPemFile(arquivoPem);
                KeyFactory rsaFact = KeyFactory.getInstance("RSA");
                rsaPrivateKey = (RSAPrivateKey) rsaFact.generatePrivate(new PKCS8EncodedKeySpec(pemContent));
            } catch (IOException e) {
                throw new ApplicationException(e);
            } catch (NoSuchAlgorithmException e) {
                throw new ApplicationException(e);
            } catch (InvalidKeySpecException e) {
                throw new ApplicationException(e);
            }
        } else {
            throw new ApplicationException("Arquivo pem está vazio");
        }
    }

    public byte[] readPemFile(String pathToPem) throws IOException {
        String pemContent = "";
        try (BufferedReader br = new BufferedReader(new FileReader(pathToPem))) {
            String line;
            while ((line = br.readLine()) != null) {
                pemContent += line.trim();
            }
            br.close();
            pemContent = pemContent.replace("-----BEGIN PRIVATE KEY-----", "");
            pemContent = pemContent.replace("-----END PRIVATE KEY-----", "");
        }
        return Base64.decodeBase64(pemContent);
    }

    public static String sign(PrivateKey privateKey, String message) throws NoSuchAlgorithmException, InvalidKeyException, SignatureException, UnsupportedEncodingException {
        Signature sign = Signature.getInstance("SHA256withRSA");
        sign.initSign(privateKey);
        sign.update(message.getBytes("UTF-8"));
        return new String(Base64.encodeBase64(sign.sign()), "UTF-8");
    }

    public Autorizacao geraChavePrivada() {

        inicializaRSAPrivateKey();

        timestamp = System.currentTimeMillis();
        nonce = String.valueOf(Math.random());
        try {
            signature = sign(rsaPrivateKey, systemAcronym + "#" + timestamp + "#" + nonce);
            return new Autorizacao(nonce, timestamp, signature);
        } catch (Exception e) {
            throw new ApplicationException(e);
        }
    }
}
