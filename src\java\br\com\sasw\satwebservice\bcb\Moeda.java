package br.com.sasw.satwebservice.bcb;

import com.google.gson.annotations.SerializedName;

/**
 *
 * <AUTHOR>
 */
public class Moeda {
    
    @SerializedName(value = "simbolo")
    private String simbolo;
    @SerializedName(value = "nomeFormatado")
    private String nomeFormatado;
    @SerializedName(value = "tipoMoeda")
    private String tipoMoeda;
    @SerializedName(value = "numDecimal")
    private int numDecimal;

    public String getSimbolo() {
        return simbolo;
    }

    public String getNomeFormatado() {
        return nomeFormatado;
    }

    public String getTipoMoeda() {
        return tipoMoeda;
    }

    public int getNumDecimal() {
        return numDecimal;
    }
    
}
