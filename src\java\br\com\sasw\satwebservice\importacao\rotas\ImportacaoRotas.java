/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.importacao.rotas;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.CxForte;
import SasBeans.OS_Vig;
import SasBeans.Pedido;
import SasBeans.Rotas;
import SasBeans.Rt_Perc;
import SasDaos.CxForteDao;
import SasDaos.OS_VigDao;
import SasDaos.PedidoDao;
import SasDaos.RotasDao;
import SasDaos.Rt_PercDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import br.com.sasw.satwebservice.importacao.exceptions.RotaException;
import br.com.sasw.satwebservice.importacao.exceptions.RotaException.RotaErrorCode;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.internal.StringMap;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.Response;

/**
 *
 * <AUTHOR>
 */
public class ImportacaoRotas {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;
    private final CxForteDao cxForteDao = new CxForteDao();
    private final OS_VigDao os_VigDao = new OS_VigDao();
    private final PedidoDao pedidoDao = new PedidoDao();
    private final RotasDao rotasDao = new RotasDao();
    private final Rt_PercDao rt_PercDao = new Rt_PercDao();

    /**
     * Creates a new instance of ImportacaoClientes
     */
    public ImportacaoRotas() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
    }

    public Response json(String empresa, String input) {

        GsonBuilder gsonBuilder = new GsonBuilder();
        gsonBuilder.registerTypeAdapter(RotaException.class, new RotaExceptionSerializer());
        Gson gson = gsonBuilder.create();
        Map retorno = new HashMap<>(), processamento;
        List processamentos = new ArrayList<>();
        try {
            // Validando parâmetro de conexão
            this.persistencia = this.pool.getConexao(empresa);
            if (this.persistencia == null) {
                throw new Exception("ParamInvalido (" + empresa + ")");
            }

            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Importacao\\" + empresa + "\\Rotas\\"
                    + getDataAtual("SQL") + "\\log.txt";

            this.logerro.Grava(input, this.caminho);

            String sequencia, numero;
            CxForte cxForte;
            OS_Vig os_Vig;
            Pedido pedido;
            Rotas rota;
            Rt_Perc trajeto;
            StringMap rotasJson = new Gson().fromJson(input, StringMap.class);
            Object rotasObject = rotasJson.get("rotas");

            if (rotasObject instanceof ArrayList) {
                List<StringMap> rotasList = (ArrayList) rotasObject;
                retorno.put("rotasArquivo", rotasList.size());
                for (StringMap rotaObject : rotasList) {
                    processamento = new HashMap<>();
                    try {
                        rota = obterRota(rotaObject);
                        processamento.put("rota", rota.getRota());
                        validarRota(rota);

                        sequencia = this.rotasDao.buscarSeqRota(rota.getCodFil().toPlainString(), rota.getRota(), rota.getData(), this.persistencia);
                        if (sequencia == null) {
                            // inserir rota
                            rota.setFlag_Excl("");
                            rota.setTpVeic("F");
                            rota.setViagem("N");
                            rota.setBACEN("N");
                            rota.setAeroporto("N");
                            rota.setATM("N");

                            try {
                                sequencia = this.rotasDao.inserirRotaSequencia(rota, this.persistencia);
                            } catch (Exception insertRota) {
                                throw new RotaException(insertRota.getMessage(), new RotaErrorCode(0));
                            }
                        }

                        trajeto = obterTrajeto(rotaObject, sequencia);
                        processamento.put("parada", trajeto.getParada());
                        trajeto = validarTrajeto(trajeto);

                        if (trajeto.getSolicitante().equals("1")) {
                            cxForte = this.cxForteDao.getCxForte(trajeto.getCodFil(), this.persistencia);

                            if (trajeto.getER().equals("E")) {
                                pedido = obterPedido(trajeto);
                                pedido.setCodCli1(cxForte.getCodCli());
                            } else if (trajeto.getER().equals("R")) {
                                pedido = obterPedido(trajeto);
                            } else {
                                pedido = null;
                            }

                            // Verificar / Inserir Numero Pedido
                            if (pedido != null) {
                                os_Vig = this.os_VigDao.obterOSPedido(pedido.getCodCli1(), pedido.getCodCli2(), pedido.getCodFil().toPlainString(), cxForte.getCodCli(), this.persistencia);

                                if (os_Vig != null) {
                                    pedido.setOS(os_Vig.getOS());
                                    trajeto.setOS(os_Vig.getOS());
                                } else {
                                    pedido.setOS("0");
                                    trajeto.setOS("0");
                                }

                                numero = this.pedidoDao.inserirPedidoNumero(pedido, persistencia);
                                trajeto.setPedido(numero);
                            } else {
                                trajeto.setPedido("0");
                            }

                            // Inserir Trajeto
                            trajeto.setDt_Incl(LocalDate.now());
                            trajeto.setHr_Incl(getDataAtual("HORA"));
                            trajeto.setOperIncl(RecortaAteEspaço("SatWebService", 0, 10));
                            try {
                                this.rt_PercDao.inserirTrajeto(trajeto, this.persistencia);
                            } catch (Exception insertTrajeto) {
                                throw new RotaException(insertTrajeto.getMessage(), new RotaErrorCode(0));
                            }
                        } else {
                            // Excluir trajeto
                            trajeto.setFlag_Excl("*");
                            trajeto.setDt_Excl(LocalDate.now());
                            trajeto.setHr_Excl(getDataAtual("HORA"));
                            trajeto.setOperExcl(RecortaAteEspaço("SatWebService", 0, 10));
                            try {
                                this.rt_PercDao.Atualiza_Flag_Excl(trajeto, this.persistencia);
                            } catch (Exception updateTrajeto) {
                                throw new RotaException(updateTrajeto.getMessage(), new RotaErrorCode(0));
                            }
                        }
                    } catch (RotaException rotaExc) {
                        System.out.println(rotaExc.getMessage());
                        System.out.println(rotaExc.getCode().getCode());
                        System.out.println(rotaExc.getCode().getStatus());
                        processamento.put("result", rotaExc);
                    }
                    processamentos.add(processamento);
                }
            } else if (rotasObject instanceof StringMap) {
                StringMap rotaObject = (StringMap) ((StringMap) rotasObject).get("clientes");
                retorno.put("rotasArquivo", 1);
                processamento = new HashMap<>();
                try {
                    rota = obterRota(rotaObject);
                    processamento.put("rota", rota.getRota());
                    validarRota(rota);

                    sequencia = this.rotasDao.buscarSeqRota(rota.getCodFil().toPlainString(), rota.getRota(), rota.getData(), this.persistencia);
                    if (sequencia == null) {
                        // inserir rota
                        rota.setFlag_Excl("");
                        rota.setTpVeic("F");
                        rota.setViagem("N");
                        rota.setBACEN("N");
                        rota.setAeroporto("N");
                        rota.setATM("N");

                        try {
                            sequencia = this.rotasDao.inserirRotaSequencia(rota, this.persistencia);
                        } catch (Exception insertRota) {
                            throw new RotaException(insertRota.getMessage(), new RotaErrorCode(0));
                        }
                    }

                    trajeto = obterTrajeto(rotaObject, sequencia);
                    processamento.put("parada", trajeto.getParada());
                    trajeto = validarTrajeto(trajeto);

                    if (trajeto.getSolicitante().equals("1")) {
                        cxForte = this.cxForteDao.getCxForte(trajeto.getCodFil(), this.persistencia);

                        if (trajeto.getER().equals("E")) {
                            pedido = obterPedido(trajeto);
                            pedido.setCodCli1(cxForte.getCodCli());
                        } else if (trajeto.getER().equals("R")) {
                            pedido = obterPedido(trajeto);
                        } else {
                            pedido = null;
                        }

                        if (pedido != null) {
                            os_Vig = this.os_VigDao.obterOSPedido(pedido.getCodCli1(), pedido.getCodCli2(), pedido.getCodFil().toPlainString(), cxForte.getCodCli(), this.persistencia);

                            if (os_Vig != null) {
                                pedido.setOS(os_Vig.getOS());
                                trajeto.setOS(os_Vig.getOS());
                            } else {
                                pedido.setOS("0");
                                trajeto.setOS("0");
                            }

                            numero = this.pedidoDao.inserirPedidoNumero(pedido, persistencia);
                            trajeto.setPedido(numero);
                        } else {
                            trajeto.setPedido("0");
                        }

                        // insere trajeto
                        trajeto.setDt_Incl(LocalDate.now());
                        trajeto.setHr_Incl(getDataAtual("HORA"));
                        trajeto.setOperIncl(RecortaAteEspaço("SatWebService", 0, 10));
                        try {
                            this.rt_PercDao.inserirTrajeto(trajeto, this.persistencia);;
                        } catch (Exception insertTrajeto) {
                            throw new RotaException(insertTrajeto.getMessage(), new RotaErrorCode(0));
                        }
                    } else {
                        // exclui trajeto
                        trajeto.setFlag_Excl("*");
                        trajeto.setDt_Excl(LocalDate.now());
                        trajeto.setHr_Excl(getDataAtual("HORA"));
                        trajeto.setOperExcl(RecortaAteEspaço("SatWebService", 0, 10));
                        try {
                            this.rt_PercDao.Atualiza_Flag_Excl(trajeto, this.persistencia);
                        } catch (Exception updateTrajeto) {
                            throw new RotaException(updateTrajeto.getMessage(), new RotaErrorCode(0));
                        }
                    }
                } catch (RotaException rotaExc) {
                    System.out.println(rotaExc.getMessage());
                    System.out.println(rotaExc.getCode().getCode());
                    System.out.println(rotaExc.getCode().getStatus());
                    processamento.put("result", rotaExc);
                }
                processamentos.add(processamento);
            }

            retorno.put("status", "ok");
            retorno.put("resp", processamentos);

        } catch (Exception e) {
            // Salvando em log o que foi mandado
            retorno.clear();
            retorno.put("status", "error");
            retorno.put("error", e.getMessage());
            this.logerro.Grava(e.getMessage(), this.caminho);
        } finally {
            this.logerro.Grava(gson.toJson(retorno), this.caminho);
//            return gson.toJson(retorno);

            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(gson.toJson(retorno))
                    .build();
        }
    }

    private Rotas validarRota(Rotas rota) throws RotaException {

        /**
         * rotas.codfil Integer Código da filial da empresa
         */
        try {
            if (rota.getCodFil() == null
                    || rota.getRota().equals("")) {
                throw new Exception();
            }
            Integer.parseInt(rota.getCodFil().toBigInteger().toString());
        } catch (Exception e) {
            throw new RotaException("codfil", new RotaErrorCode(9));
        }

        /**
         * rotas.numero String(03) Número da rota. Utilizar numeração
         * hexadecimal de 000 a FFF.
         */
        try {
            if (rota.getRota() == null
                    || rota.getRota().equals("")
                    || rota.getRota().length() > 3) {
                throw new Exception(); // Validando rota não está vazio e o tamanho é menor igual a 3.
            }
            Integer.parseInt(rota.getRota(), 16); // Validando rota é um número hexadecimal
        } catch (Exception e) {
            throw new RotaException("rota", new RotaErrorCode(9));
        }

        /**
         * rotas.data String(8) Data do serviço
         */
        try {
            if (rota.getData() == null
                    || rota.getData().equals("")
                    || rota.getData().length() > 8) {
                throw new Exception();
            }
            LocalDate.parse(rota.getData(), DateTimeFormatter.ofPattern("yyyyMMdd")); // Validando data é realmente uma data
        } catch (Exception e) {
            throw new RotaException("data", new RotaErrorCode(9));
        }

        return rota;
    }

    private Rotas obterRota(StringMap stringMap) {
        Rotas rota = new Rotas();

        rota.setCodFil(stringMap.getOrDefault("codfil", "").toString());
        rota.setRota(stringMap.getOrDefault("numero", "").toString());
        rota.setData(stringMap.getOrDefault("data", "").toString());
        rota.setDtFim(stringMap.getOrDefault("data", "").toString());

        rota.setOperador(FuncoesString.RecortaAteEspaço("SatWebService", 0, 10));
        rota.setDt_Alter(LocalDate.now());
        rota.setHr_Alter(getDataAtual("HORA"));

        return rota;
    }

    private Rt_Perc obterTrajeto(StringMap stringMap, String seqRota) throws RotaException {

        Rt_Perc trajeto = new Rt_Perc();
        trajeto.setSequencia(seqRota);
        trajeto.setSolicitante(stringMap.get("acao").toString().replace(".0", ""));
        trajeto.setCodFil(stringMap.get("codfil").toString());
        trajeto.setParada(Integer.parseInt(stringMap.get("parada").toString()));
        trajeto.setHora1(stringMap.get("hora").toString());
        trajeto.setHora1ParadaAtual(stringMap.get("janela").toString()); // janela
        trajeto.setER(stringMap.get("tipo").toString());
        trajeto.setTipoSrv(stringMap.get("classificacao").toString());
        trajeto.setCodCli1(stringMap.get("codcliparada").toString());
        trajeto.setCodCli2(stringMap.getOrDefault("codclidestino", "").toString());
        trajeto.setHora1D(stringMap.getOrDefault("horadestino", "").toString());
        try {
            trajeto.setDPar(Integer.parseInt(stringMap.getOrDefault("paradadestino", "0").toString()));
        } catch (Exception e) {
            throw new RotaException("paradadestino", new RotaErrorCode(9));
        }
        trajeto.setPedidoCliente(stringMap.getOrDefault("pedidocliente", "").toString());
        trajeto.setValor(stringMap.getOrDefault("valor", "").toString());
        trajeto.setMoeda(stringMap.getOrDefault("moeda", "").toString());
        trajeto.setObserv(stringMap.getOrDefault("observacao", "").toString());
        trajeto.setData(stringMap.getOrDefault("data", "").toString());
        trajeto.setFlag_Excl("");
        trajeto.setOperador(FuncoesString.RecortaAteEspaço("SatWebService", 0, 10));
        trajeto.setDt_Alter(LocalDate.now());
        trajeto.setHr_Alter(getDataAtual("HORA"));

        return trajeto;
    }

    private Rt_Perc validarTrajeto(Rt_Perc trajeto) throws RotaException {

        /**
         * rotas.acao Integer 1-inclusão;3-exclusão; Tipo de movimento na tabela
         * de rotas
         */
        try {
            if (trajeto.getSolicitante() == null
                    || trajeto.getSolicitante().equals("")) {
                throw new Exception();
            }
            if (Integer.parseInt(trajeto.getSolicitante()) != 1
                    && Integer.parseInt(trajeto.getSolicitante()) != 3) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new RotaException("acao", new RotaErrorCode(9));
        }

        /**
         * rotas.parada Integer Número da parada /ordem
         */
        if (trajeto.getParada() == 0) {
            throw new RotaException("parada", new RotaErrorCode(9));
        }

        /**
         * rotas.hora String(5) Hora programada para o serviço
         */
        try {
            if (trajeto.getHora1() == null
                    || trajeto.getHora1().equals("")
                    || trajeto.getHora1().length() > 5) {
                throw new Exception();
            }
            LocalTime.parse(trajeto.getHora1(), DateTimeFormatter.ofPattern("HH:mm")); // Validando data é realmente uma hora
        } catch (Exception e) {
            throw new RotaException("hora", new RotaErrorCode(9));
        }

        /**
         * rotas.tipo E – Entrega R – Recolhimento T-Transbordo Tipo de parada
         */
        if (trajeto.getER() == null
                || trajeto.getER().equals("")
                || (!trajeto.getER().equals("E")
                && !trajeto.getER().equals("R")
                && !trajeto.getER().equals("T"))) {
            throw new RotaException("tipo", new RotaErrorCode(9));
        }

        /**
         * rotas.classificacao R-Rotineiro V-Eventual E-Emergencial
         * A-Assistencia técnica Classificação do Serviço
         */
        if (trajeto.getTipoSrv() == null
                || trajeto.getTipoSrv().equals("")
                || (!trajeto.getTipoSrv().equals("R")
                && !trajeto.getTipoSrv().equals("V")
                && !trajeto.getTipoSrv().equals("E")
                && !trajeto.getTipoSrv().equals("A"))) {
            throw new RotaException("classificacao", new RotaErrorCode(9));
        }

        /**
         * rotas.codcliparada String(7) Código do cliente na tabela de clientes
         * para a parada atual
         */
        if (trajeto.getCodCli1() == null
                || trajeto.getCodCli1().equals("")
                || trajeto.getCodCli1().length() > 7) {
            throw new RotaException("codcliparada", new RotaErrorCode(9));
        }

        /**
         * rotas.codcliparada String(7) Código do cliente na tabela de clientes
         * para a parada atual
         */
        if (trajeto.getCodCli1() == null
                || trajeto.getCodCli1().equals("")
                || trajeto.getCodCli1().length() > 7) {
            throw new RotaException("codcliparada", new RotaErrorCode(9));
        }

        if (trajeto.getER().equals("R")) {

            /**
             * rotas.codclidestino String(7) Código do cliente na tabela de
             * clientes para a parada de entrega.(informar somente quando
             * recolhimento).
             */
            if (trajeto.getCodCli2() == null
                    || trajeto.getCodCli2().equals("")
                    || trajeto.getCodCli2().length() > 7) {
                throw new RotaException("codclidestino", new RotaErrorCode(9));
            }

            /**
             * rotas.horadestino String(5) Hora programada para
             * entrega.(informar somente quando recolhimento).
             */
            try {
                if (trajeto.getHora1D() == null
                        || trajeto.getHora1D().equals("")
                        || trajeto.getHora1D().length() > 5) {
                    throw new Exception();
                }
                LocalTime.parse(trajeto.getHora1D(), DateTimeFormatter.ofPattern("HH:mm")); // Validando data é realmente uma hora
            } catch (Exception e) {
                throw new RotaException("horadestino", new RotaErrorCode(9));
            }

            /**
             * rotas.parada Integer Número da parada para entrega.( informar
             * somente quando recolhimento).
             */
            if (trajeto.getParada() == 0) {
                throw new RotaException("paradadestino", new RotaErrorCode(9));
            }
        }

        /**
         * rotas.pedidocliente String(20) Número/código do pedido do cliente
         */
        if (trajeto.getPedidoCliente() != null
                && !trajeto.getPedidoCliente().equals("")
                && trajeto.getPedidoCliente().length() > 20) {
            throw new RotaException("pedidocliente", new RotaErrorCode(9));
        }

        /**
         * rotas.valor Float Valor total de entrega/recolhimento
         */
        if (trajeto.getValor() == null
                || trajeto.getValor().equals("")) {
            trajeto.setValor("0");
        }

        try {
            Float.parseFloat(trajeto.getValor());
        } catch (Exception e) {
            throw new RotaException("valor", new RotaErrorCode(9));
        }

        /**
         * rotas.moeda String(3) Moeda: BRL: Real MXN:Pesos mexicanos USD-Dólar
         * americano EUR-Euro GBP-Libra esterlina IEN-Iene OUT-Outros
         * (identificar em Observações)
         *
         */
        if (trajeto.getMoeda() != null
                && !trajeto.getMoeda().equals("")
                && trajeto.getMoeda().length() > 3
                && !trajeto.getMoeda().equals("BRL")
                && !trajeto.getMoeda().equals("MXN")
                && !trajeto.getMoeda().equals("USD")
                && !trajeto.getMoeda().equals("EUR")
                && !trajeto.getMoeda().equals("GBP")
                && !trajeto.getMoeda().equals("IEN")
                && !trajeto.getMoeda().equals("OUT")) {
            throw new RotaException("moeda", new RotaErrorCode(9));
        }

        /**
         * rotas.obervacao String(120) Observação
         */
        if (trajeto.getObserv() != null
                && !trajeto.getObserv().equals("")
                && trajeto.getObserv().length() > 120) {
            throw new RotaException("obervacao", new RotaErrorCode(9));
        }

        return trajeto;
    }

    private Pedido obterPedido(Rt_Perc trajeto) throws RotaException {
        Pedido pedido = new Pedido();
        pedido.setSeqRota(trajeto.getSequencia().toBigInteger().toString());

        String hora1 = "08:00", hora2 = "18:00";
        try {
            hora1 = trajeto.getHora1ParadaAtual().split("-")[0];
            hora2 = trajeto.getHora1ParadaAtual().split("-")[1];

            LocalTime.parse(hora1, DateTimeFormatter.ofPattern("HH:mm"));
            LocalTime.parse(hora2, DateTimeFormatter.ofPattern("HH:mm"));
        } catch (Exception e) {
            throw new RotaException("janela", new RotaErrorCode(9));
        }

        if (trajeto.getER().equals("E")) {
            pedido.setCodCli1("");
            pedido.setCodCli2(trajeto.getCodCli1());
            pedido.setHora1O("08:00");
            pedido.setHora2O("18:00");
            pedido.setHora1D(hora1);
            pedido.setHora2D(hora2);
        } else {
            pedido.setCodCli1(trajeto.getCodCli1());
            pedido.setCodCli2(trajeto.getCodCli2());
            pedido.setHora1O(hora1);
            pedido.setHora2O(hora2);
            pedido.setHora1D("08:00");
            pedido.setHora2D("18:00");
        }

        pedido.setCodFil(trajeto.getCodFil());
        pedido.setData(trajeto.getData());
        pedido.setParada(trajeto.getParada());
        pedido.setClassifSrv(trajeto.getTipoSrv());
        pedido.setTipo("T");
        pedido.setPedidoCliente(trajeto.getPedidoCliente());
        pedido.setValor(trajeto.getValor());
        pedido.setTipoMoeda(trajeto.getMoeda());
        pedido.setObs(trajeto.getObserv());

        pedido.setFlag_Excl("");
        pedido.setOperIncl(FuncoesString.RecortaAteEspaço("SatWebService", 0, 10));
        pedido.setDt_Incl(getDataAtual("SQL"));
        pedido.setHr_Incl(getDataAtual("HORA"));

        pedido.setOperador(FuncoesString.RecortaAteEspaço("SatWebService", 0, 10));
        pedido.setDt_Alter(getDataAtual("SQL"));
        pedido.setHr_Alter(getDataAtual("HORA"));

        return pedido;
    }
}
