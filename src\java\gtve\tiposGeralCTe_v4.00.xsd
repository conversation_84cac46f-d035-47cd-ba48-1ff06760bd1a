<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by <EMAIL> (PROCERGS) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/cte" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:simpleType name="TDateTimeUTC">
		<xs:annotation>
			<xs:documentation>Data e Hora, formato UTC (AAAA-MM-DDThh:mm:ssTZD, onde TZD = +hh:mm ou -hh:mm)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="(((20(([02468][048])|([13579][26]))-02-29))|(20[0-9][0-9])-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))T(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d([\-,\+](0[0-9]|10|11):00|([\+](12):00))"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TAmb">
		<xs:annotation>
			<xs:documentation>Tipo Ambiente</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="2"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Tano">
		<xs:annotation>
			<xs:documentation> Tipo ano</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCodUfIBGE">
		<xs:annotation>
			<xs:documentation>Tipo Código da UF da tabela do IBGE</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="11"/>
			<xs:enumeration value="12"/>
			<xs:enumeration value="13"/>
			<xs:enumeration value="14"/>
			<xs:enumeration value="15"/>
			<xs:enumeration value="16"/>
			<xs:enumeration value="17"/>
			<xs:enumeration value="21"/>
			<xs:enumeration value="22"/>
			<xs:enumeration value="23"/>
			<xs:enumeration value="24"/>
			<xs:enumeration value="25"/>
			<xs:enumeration value="26"/>
			<xs:enumeration value="27"/>
			<xs:enumeration value="28"/>
			<xs:enumeration value="29"/>
			<xs:enumeration value="31"/>
			<xs:enumeration value="32"/>
			<xs:enumeration value="33"/>
			<xs:enumeration value="35"/>
			<xs:enumeration value="41"/>
			<xs:enumeration value="42"/>
			<xs:enumeration value="43"/>
			<xs:enumeration value="50"/>
			<xs:enumeration value="51"/>
			<xs:enumeration value="52"/>
			<xs:enumeration value="53"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCodMunIBGE">
		<xs:annotation>
			<xs:documentation>Tipo Código do Município da tabela do IBGE</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{7}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCOrgaoIBGE">
		<xs:annotation>
			<xs:documentation>Tipo Código de orgão (UF da tabela do IBGE + 90 SUFRAMA + 91 RFB +  94 SVC-RS + 95 SVC-SP + 96  Sinc. Chaves do RS para SVSP </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="11"/>
			<xs:enumeration value="12"/>
			<xs:enumeration value="13"/>
			<xs:enumeration value="14"/>
			<xs:enumeration value="15"/>
			<xs:enumeration value="16"/>
			<xs:enumeration value="17"/>
			<xs:enumeration value="21"/>
			<xs:enumeration value="22"/>
			<xs:enumeration value="23"/>
			<xs:enumeration value="24"/>
			<xs:enumeration value="25"/>
			<xs:enumeration value="26"/>
			<xs:enumeration value="27"/>
			<xs:enumeration value="28"/>
			<xs:enumeration value="29"/>
			<xs:enumeration value="31"/>
			<xs:enumeration value="32"/>
			<xs:enumeration value="33"/>
			<xs:enumeration value="35"/>
			<xs:enumeration value="41"/>
			<xs:enumeration value="42"/>
			<xs:enumeration value="43"/>
			<xs:enumeration value="50"/>
			<xs:enumeration value="51"/>
			<xs:enumeration value="52"/>
			<xs:enumeration value="53"/>
			<xs:enumeration value="90"/>
			<xs:enumeration value="91"/>
			<xs:enumeration value="93"/>
			<xs:enumeration value="94"/>
			<xs:enumeration value="95"/>
			<xs:enumeration value="96"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TChDFe">
		<xs:annotation>
			<xs:documentation>Tipo Chave de Documento Fiscal Eletrônico</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="44"/>
			<xs:pattern value="[0-9]{44}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCnpj">
		<xs:annotation>
			<xs:documentation>Tipo Número do CNPJ</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{14}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TFone">
		<xs:annotation>
			<xs:documentation>Tipo Número do Telefone</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{6,14}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCnpjVar">
		<xs:annotation>
			<xs:documentation>Tipo Número do CNPJ tamanho varíavel (3-14)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{3,14}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCnpjOpc">
		<xs:annotation>
			<xs:documentation>Tipo Número do CNPJ Opcional</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{0}|[0-9]{14}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCpf">
		<xs:annotation>
			<xs:documentation>Tipo Número do CPF</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{11}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCpfVar">
		<xs:annotation>
			<xs:documentation>Tipo Número do CPF de tamanho variável (3-11)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{3,11}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TData">
		<xs:annotation>
			<xs:documentation> Tipo data AAAA-MM-DD</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="(((20(([02468][048])|([13579][26]))-02-29))|(20[0-9][0-9])-((((0[1-9])|(1[0-2]))-((0[1-9])|(1\d)|(2[0-8])))|((((0[13578])|(1[02]))-31)|(((0[1,3-9])|(1[0-2]))-(29|30)))))"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0302">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 5 dígitos, sendo 3 de corpo e 2 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{2}|[1-9]{1}[0-9]{0,2}(\.[0-9]{2})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0303">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 6 dígitos, sendo 3 de corpo e 3 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{3}|[1-9]{1}[0-9]{0,2}(\.[0-9]{3})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0302_0303">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 6  ou 5 dígitos, sendo 3 de corpo e 3 ou 2 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{1,3}(\.[0-9]{2,3})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0302Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 5 dígitos, sendo 3 de corpo e 2 decimais, utilizado em tags opcionais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[0-9]{1}[1-9]{1}|0\.[1-9]{1}[0-9]{1}|[1-9]{1}[0-9]{0,2}(\.[0-9]{2})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0803">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 11 dígitos, sendo 8 de corpo e 3 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{3}|[1-9]{1}[0-9]{0,7}(\.[0-9]{3})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0803Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 11 dígitos, sendo 8 de corpo e 3 decimais utilizado em tags opcionais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[1-9]{1}[0-9]{2}|0\.[0-9]{2}[1-9]{1}|0\.[0-9]{1}[1-9]{1}[0-9]{1}|[1-9]{1}[0-9]{0,7}(\.[0-9]{3})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0804">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 12 dígitos, sendo 8 de corpo e 4decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{4}|[1-9]{1}[0-9]{0,7}(\.[0-9]{4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0804Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 12 dígitos, sendo 8 de corpo e 4 decimais, utilizado em tags opcionais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[1-9]{1}[0-9]{3}|0\.[0-9]{3}[1-9]{1}|0\.[0-9]{2}[1-9]{1}[0-9]{1}|0\.[0-9]{1}[1-9]{1}[0-9]{2}|[1-9]{1}[0-9]{0,7}(\.[0-9]{4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_0906Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 15 dígitos, sendo 9 de corpo e 6 decimais, utilizado em tags opcionais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[1-9]{1}[0-9]{5}|0\.[0-9]{1}[1-9]{1}[0-9]{4}|0\.[0-9]{2}[1-9]{1}[0-9]{3}|0\.[0-9]{3}[1-9]{1}[0-9]{2}|0\.[0-9]{4}[1-9]{1}[0-9]{1}|0\.[0-9]{5}[1-9]{1}|[1-9]{1}[0-9]{0,8}(\.[0-9]{6})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1104">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 15 dígitos, sendo 11 de corpo e 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{4}|[1-9]{1}[0-9]{0,10}(\.[0-9]{4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1104Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 15 dígitos, sendo 11 de corpo e 4 decimais, utilizado em tags opcionais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[1-9]{1}[0-9]{3}|0\.[0-9]{3}[1-9]{1}|0\.[0-9]{2}[1-9]{1}[0-9]{1}|0\.[0-9]{1}[1-9]{1}[0-9]{2}|[1-9]{1}[0-9]{0,10}(\.[0-9]{4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1203">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 15 dígitos, sendo 12 de corpo e 3 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{3}|[1-9]{1}[0-9]{0,11}(\.[0-9]{3})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1203Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 15 dígitos, sendo 12 de corpo e 3 decimais, utilizado em tags opcionais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[1-9]{1}[0-9]{2}|0\.[0-9]{2}[1-9]{1}|0\.[0-9]{1}[1-9]{1}[0-9]{1}|[1-9]{1}[0-9]{0,11}(\.[0-9]{3})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1204">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 16 dígitos, sendo 12 de corpo e 4 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{4}|[1-9]{1}[0-9]{0,11}(\.[0-9]{4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1204Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 16 dígitos, sendo 12 de corpo e 4 decimais, utilizado em tags opcionais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[1-9]{1}[0-9]{3}|0\.[0-9]{3}[1-9]{1}|0\.[0-9]{2}[1-9]{1}[0-9]{1}|0\.[0-9]{1}[1-9]{1}[0-9]{2}|[1-9]{1}[0-9]{0,11}(\.[0-9]{4})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1302">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 15 dígitos, sendo 13 de corpo e 2 decimais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|0\.[0-9]{2}|[1-9]{1}[0-9]{0,12}(\.[0-9]{2})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDec_1302Opc">
		<xs:annotation>
			<xs:documentation>Tipo Decimal com 15 dígitos, sendo 13 de corpo e 2 decimais, utilizado em tags opcionais</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0\.[0-9]{1}[1-9]{1}|0\.[1-9]{1}[0-9]{1}|[1-9]{1}[0-9]{0,12}(\.[0-9]{2})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TIe">
		<xs:annotation>
			<xs:documentation>Tipo Inscrição Estadual do Emitente</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="14"/>
			<xs:pattern value="[0-9]{2,14}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TIeDest">
		<xs:annotation>
			<xs:documentation>Tipo Inscrição Estadual do Destinatário</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:maxLength value="14"/>
			<xs:pattern value="[0-9]{0,14}|ISENTO"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TJust">
		<xs:annotation>
			<xs:documentation>Tipo Justificativa</xs:documentation>
		</xs:annotation>
		<xs:restriction base="TString">
			<xs:minLength value="15"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TMed">
		<xs:annotation>
			<xs:documentation> Tipo temp médio em segundos</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{1,4}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TModCTOS">
		<xs:annotation>
			<xs:documentation>Tipo Modelo Documento Fiscal</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="67"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TModGTVe">
		<xs:annotation>
			<xs:documentation>Tipo Modelo Documento Fiscal</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="64"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TModCT_Carga_OS">
		<xs:annotation>
			<xs:documentation>Tipo Modelo Documento Fiscal</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="57"/>
			<xs:enumeration value="67"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TModCT">
		<xs:annotation>
			<xs:documentation>Tipo Modelo Documento Fiscal</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="57"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TModNF">
		<xs:annotation>
			<xs:documentation>Tipo Modelo Documento Fiscal - NF Remetente</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="01"/>
			<xs:enumeration value="04"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TtipoUnidTransp">
		<xs:annotation>
			<xs:documentation>Tipo da Unidade de Transporte</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="2"/>
			<xs:enumeration value="3"/>
			<xs:enumeration value="4"/>
			<xs:enumeration value="5"/>
			<xs:enumeration value="6"/>
			<xs:enumeration value="7"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TtipoUnidCarga">
		<xs:annotation>
			<xs:documentation>Tipo da Unidade de Carga</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="2"/>
			<xs:enumeration value="3"/>
			<xs:enumeration value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TMotivo">
		<xs:annotation>
			<xs:documentation>Tipo Motivo</xs:documentation>
		</xs:annotation>
		<xs:restriction base="TString">
			<xs:minLength value="1"/>
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TNF">
		<xs:annotation>
			<xs:documentation>Tipo Número do Documento Fiscal</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[1-9]{1}[0-9]{0,8}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TProt">
		<xs:annotation>
			<xs:documentation>Tipo Número do Protocolo de Status</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TRec">
		<xs:annotation>
			<xs:documentation>Tipo Número do Recibo do envio de lote de NF-e</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TSerie">
		<xs:annotation>
			<xs:documentation>Tipo Série do Documento Fiscal </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="0|[1-9]{1}[0-9]{0,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TServ">
		<xs:annotation>
			<xs:documentation>Tipo Serviço solicitado</xs:documentation>
		</xs:annotation>
		<xs:restriction base="TString"/>
	</xs:simpleType>
	<xs:simpleType name="TStat">
		<xs:annotation>
			<xs:documentation>Tipo Código da Mensagem enviada</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TString">
		<xs:annotation>
			<xs:documentation> Tipo string genérico</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[!-ÿ]{1}[ -ÿ]{0,}[!-ÿ]{1}|[!-ÿ]{1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TUf">
		<xs:annotation>
			<xs:documentation>Tipo Sigla da UF</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="AC"/>
			<xs:enumeration value="AL"/>
			<xs:enumeration value="AM"/>
			<xs:enumeration value="AP"/>
			<xs:enumeration value="BA"/>
			<xs:enumeration value="CE"/>
			<xs:enumeration value="DF"/>
			<xs:enumeration value="ES"/>
			<xs:enumeration value="GO"/>
			<xs:enumeration value="MA"/>
			<xs:enumeration value="MG"/>
			<xs:enumeration value="MS"/>
			<xs:enumeration value="MT"/>
			<xs:enumeration value="PA"/>
			<xs:enumeration value="PB"/>
			<xs:enumeration value="PE"/>
			<xs:enumeration value="PI"/>
			<xs:enumeration value="PR"/>
			<xs:enumeration value="RJ"/>
			<xs:enumeration value="RN"/>
			<xs:enumeration value="RO"/>
			<xs:enumeration value="RR"/>
			<xs:enumeration value="RS"/>
			<xs:enumeration value="SC"/>
			<xs:enumeration value="SE"/>
			<xs:enumeration value="SP"/>
			<xs:enumeration value="TO"/>
			<xs:enumeration value="EX"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TUF_sem_EX">
		<xs:annotation>
			<xs:documentation>Tipo Sigla da UF, sem Exterior</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="AC"/>
			<xs:enumeration value="AL"/>
			<xs:enumeration value="AM"/>
			<xs:enumeration value="AP"/>
			<xs:enumeration value="BA"/>
			<xs:enumeration value="CE"/>
			<xs:enumeration value="DF"/>
			<xs:enumeration value="ES"/>
			<xs:enumeration value="GO"/>
			<xs:enumeration value="MA"/>
			<xs:enumeration value="MG"/>
			<xs:enumeration value="MS"/>
			<xs:enumeration value="MT"/>
			<xs:enumeration value="PA"/>
			<xs:enumeration value="PB"/>
			<xs:enumeration value="PE"/>
			<xs:enumeration value="PI"/>
			<xs:enumeration value="PR"/>
			<xs:enumeration value="RJ"/>
			<xs:enumeration value="RN"/>
			<xs:enumeration value="RO"/>
			<xs:enumeration value="RR"/>
			<xs:enumeration value="RS"/>
			<xs:enumeration value="SC"/>
			<xs:enumeration value="SE"/>
			<xs:enumeration value="SP"/>
			<xs:enumeration value="TO"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TVerAplic">
		<xs:annotation>
			<xs:documentation>Tipo Versão do Aplicativo</xs:documentation>
		</xs:annotation>
		<xs:restriction base="TString">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TLatitude">
		<xs:annotation>
			<xs:documentation>Coordenada geográfica Latitude</xs:documentation>
		</xs:annotation>
		<xs:restriction base="TString">
			<xs:pattern value="[0-9]\.[0-9]{6}|[1-8][0-9]\.[0-9]{6}|90\.[0-9]{6}|-[0-9]\.[0-9]{6}|-[1-8][0-9]\.[0-9]{6}|-90\.[0-9]{6}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TLongitude">
		<xs:annotation>
			<xs:documentation>Coordenada geográfica Longitude</xs:documentation>
		</xs:annotation>
		<xs:restriction base="TString">
			<xs:pattern value="[0-9]\.[0-9]{6}|[1-9][0-9]\.[0-9]{6}|1[0-7][0-9]\.[0-9]{6}|180\.[0-9]{6}|-[0-9]\.[0-9]{6}|-[1-9][0-9]\.[0-9]{6}|-1[0-7][0-9]\.[0-9]{6}|-180\.[0-9]{6}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TIPv4">
		<xs:annotation>
			<xs:documentation>Tipo IP versão 4</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TPlaca">
		<xs:annotation>
			<xs:documentation>Tipo Placa </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[A-Z]{2,3}[0-9]{4}|[A-Z]{3,4}[0-9]{3}|[A-Z0-9]{7}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="TRSAKeyValueType">
		<xs:annotation>
			<xs:documentation>Tipo que representa uma chave publica padrão RSA</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Modulus" type="xs:base64Binary"/>
			<xs:element name="Exponent" type="xs:base64Binary"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
