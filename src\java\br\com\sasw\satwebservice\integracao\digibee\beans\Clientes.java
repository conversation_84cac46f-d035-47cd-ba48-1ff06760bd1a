/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.integracao.digibee.beans;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @param <T> Tipo do Cliente (Envio/Resposta)
 */
public class Clientes <T> {
    
    private List clientes;

    public List<T> getClientes() {
        return clientes;
    }

    public void  setClientes(List<T> clientes) {
        this.clientes = clientes;
    }
}
