/*
 */
package br.com.sasw.satwebservice.bradescoapi;

import Arquivo.ArquivoLog;
import Dados.Consulta;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasDaos.FiliaisDao;
import br.com.sasw.pacotesuteis.sasbeans.TOKENS;
import br.com.sasw.pacotesuteis.sasdaos.TOKENSDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.satwebservice.Utilidades.obterParametros;
import br.com.sasw.satwebservice.executasvc.ValidarToken;
import br.com.sasw.satwebservice.messages.Messages;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.sql.ResultSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Map;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
@Path("/bradescoapi/")
public class IntegracaoBradesco {

    private final ArquivoLog logerro;
    private ArquivoLog logexecucao;
    private ArquivoLog arquivohtml;
    private final SasPoolPersistencia pool;
    private String caminho;
    private String caminhoweb;
    private final Messages messages;
    private final FiliaisDao filiaisDao;
    private final TOKENSDao tokenDao;
    private ResultSet vResultadoConsulta;
    private String vRetorno;
    private String vNumPed;

    public IntegracaoBradesco() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\BradescoAPI"
                + "\\erro.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        messages = new Messages();

        filiaisDao = new FiliaisDao();
        tokenDao = new TOKENSDao();
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/obterTokenBrad")
    public String obterTokenBrad(String param) throws UnsupportedEncodingException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vClientID = (String) parametros.getOrDefault("clientid", null);
        String vClientSecret = (String) parametros.getOrDefault("clientsecret", null);
        String vSenhaCertificado = (String) parametros.getOrDefault("senha", null);
//        String vCNPJ_filial = (String) parametros.getOrDefault("cnpj_filial", null);
//        String vCodTransp = (String) parametros.getOrDefault("codtranspitau", null);
//        String vCodfil = (String) parametros.getOrDefault("codfilitau", null);
        String vBDx = (String) parametros.getOrDefault("parametro", null);
        String vData = (String) parametros.getOrDefault("data", null);
        vData = vData.substring(6, 8) + "/" + vData.substring(4, 6) + "/" + vData.substring(0, 4);//"20/04/2023"; 20230401

        // PRESERVE File vCertificado = new File("C:\\Clientes\\Preserve\\BradescoAPIde\\Arquivos\\certificado.crt");
        //File vCertificado = new File("C:\\Clientes\\CORPVS\\BradescoAPI\\Cert\\cert.p12");
        File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "", vSQLF = "", vTokenGerado = "";
        //Corpvs 
        //vSenhaCertificado = "Corp!@#@$#$%@1975BB";
        //vSenhaCertificado = "";
        try {

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENSDao tokensDao = new TOKENSDao();
            TOKENS vGerToken = new TOKENS();
            vGerToken.setBancoDados(vBDx);
            vGerToken.setModulo("SATMOB");
            vGerToken.setChave("WS");
            vGerToken.setData(getDataAtual("SQL"));
            vGerToken.setHora(getDataAtual("HORA"));
            vTokenGerado = tokensDao.gerarToken(vGerToken, dbsatellite);
            TOKENS qToken = this.tokenDao.obterToken(vTokenGerado, dbsatellite);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\BradescoAPI\\"
                        + getDataAtual("SQL") + "\\" + qToken.getBancoDados() + "\\obterTokenBrad.txt";

            }

            try {
                GetAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                //vjnRetorno.put("token", vRetorno);                
                String vToken = vRetorno;
                consultaSuprimentosProc(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                //consultaRecolhimentoProc(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
//                conectaitau(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                //consultaTransportadora(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vRetorno);
                vjnRetorno.put("Retorno:", vRetorno);
            } catch (Exception e) {
                throw new Exception("PstServDao.listarPostosPessoa - " + e.getMessage() + "\r\n"
                        + vSQL);
            }
        } finally {
            vRetorno = vjnRetorno.toString();
            return vRetorno;
            /*
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
             */
        }
    }

            
    public String GetAccessToken(String clientId, String clientSecret, File certificado, String senhaCertificado) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();

        try {

            //URL url = new URL("https://sts.rdhi.com.br/api/oauth/token");
            URL url = new URL("https://sts.itau.com.br/api/oauth/token");  //Producao

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);

            // Add certificate
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            String body = "grant_type=client_credentials"
                    + "&client_id=" + clientId
                    + "&client_secret=" + clientSecret;

            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);

            if (!jsonObject.get("access_token").getAsString().equals(null)) {
                connection.disconnect();
                vRetorno = jsonObject.get("access_token").getAsString();
            } else {
                connection.disconnect();
                vRetorno = response.toString();
            }
            //vRetorno =  + bufferedReader.toString();

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String GetAccessTokenh(String clientId, String clientSecret, File certificado, String senhaCertificado) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();

        try {

            URL url = new URL("https://sts.rdhi.com.br/api/oauth/token");
            //URL url = new URL("https://sts.itau.com.br/api/oauth/token");  //Producao

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);

            // Add certificate
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            String body = "grant_type=client_credentials"
                    + "&client_id=66f710ec-37e7-4645-9b3e-44f9958897f9"
                    + "&client_secret=34c05187-dfc0-4c14-9ecb-e3c5bc89c5fc";

            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);

            if (!jsonObject.get("access_token").getAsString().equals(null)) {
                connection.disconnect();
                vRetorno = jsonObject.get("access_token").getAsString();
            } else {
                connection.disconnect();
                vRetorno = response.toString();
            }
            //vRetorno =  + bufferedReader.toString();

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String obterAccessToken(String clientId, String clientSecret, File certificado, String senhaCertificado) throws MalformedURLException, IOException {

        HttpURLConnection conn = null;
        StringBuilder response = new StringBuilder();

        //URL targetUrl = new URL("https://sts.rdhi.com.br/api/oauth/token");
        URL targetUrl = new URL("https://sts.itau.com.br/api/oauth/token");

        HttpURLConnection httpConnection = (HttpURLConnection) targetUrl.openConnection();
        httpConnection.setDoOutput(true);
        httpConnection.setRequestMethod("POST");
        httpConnection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

        String input = "client_id=" + clientId + "&client_secret=" + clientSecret + "&grant_type=client_credentials";

        OutputStream outputStream = httpConnection.getOutputStream();
        outputStream.write(input.getBytes());
        outputStream.flush();
        int vret = httpConnection.getResponseCode();
        if (httpConnection.getResponseCode() != 200) {
            throw new RuntimeException("Failed : HTTP error code : "
                    + httpConnection.getResponseCode());
        }

        BufferedReader br = new BufferedReader(new InputStreamReader((conn.getInputStream())));

        String output;
        System.out.println("Output from Server .... \n");
        while ((output = br.readLine()) != null) {
            vRetorno = output;
        }
        vRetorno = response.toString();
        conn.disconnect();
        return response.toString();

    }

    public String consultaTransportadora(String clientId, String clientSecret, File certificado, String senhaCertificado, String vToken) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/consultachavetransportadoras");
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/consultachavetransportadoras");

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);
            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Authorization", "Bearer " + vToken);
            //connection.addRequestProperty("x-itau-correlationID", "a1e64241-7fdb-4d05-a7f6-c44febcdd8d9");
            //connection.addRequestProperty("x-itau-flowID", "1");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // Add certificate
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            /*
            String body = "cnpj_matriz=8787673000145"
                        + "&cnpj_filial=8787673000145"
                        + "&codigo_transportadora=162"
                        + "&codigo_filial=02";
             */
            String body = "{\"cnpj_matriz\":\"11179264000170\","
                    + "\"cnpj_filial\":\"11179264000170\","
                    + "\"codigo_transportadora\":\"160\","
                    + "\"codigo_filial\":\"02\"}";
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            int vret = connection.getResponseCode();
            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);
            if (!jsonObject.get("chave_unica").getAsString().equals(null)) {
                vRetorno = jsonObject.get("chave_unica").getAsString();
            } else {
                vRetorno = response.toString();
            }
            //vRetorno =  + bufferedReader.toString();

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
//                    " - "+Integer.toString(vret);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();

    }

    public String conectaitau(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {
        StringBuilder response = new StringBuilder();
        try {
            String urlString = ("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_processantes?data_atendimento=" + vData);
            URL url = new URL(urlString);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx"), "acesso2023".toCharArray());

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, "acesso2023".toCharArray());

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(keyManagerFactory.getKeyManagers(), null, null);

            connection.setSSLSocketFactory(sslContext.getSocketFactory());

            connection.setRequestMethod("GET");
            connection.setDoInput(true);

            response.append(connection.getContent().toString());

            InputStream inputStream = connection.getInputStream();
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            int vret = connection.getResponseCode();

            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
            vRetorno = bufferedReader.lines().toString() + "--" + response.toString();
        } catch (Exception e) {
            vRetorno = e.toString();
//                    " - "+Integer.toString(vret);
            e.printStackTrace();
        } finally {
            vRetorno = vRetorno;
        }

// read the response
//        vRetorno ="";
        return null;

    }

    public String consultaSuprimentosProc(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_processantes?data_atendimento=" + vData);
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_processantes?data_atendimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI2NmY3MTBlYy0zN2U3LTQ2NDUtOWIzZS00NGY5OTU4ODk3ZjkiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.XcJSJlFHWHo6r3DeDh5m_nKVRC-alYxoYnt0yH0IcgA");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            Consulta SQLPdrLog;
            String vSQLLog;
            if (vResposta == 404) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'suprimentos_processantes', \n"
                        + "'data_atendimento=" + vData + "', \n"
                        + "'', \n"
                        + "'SEM RESPOSTA DE SUPRIMENTO', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

                InputStream vErro = connection.getErrorStream();
                String vErro2;
                BufferedReader br = null;

                /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
                 */
                while ((vErro2 = bufferedReader.readLine()) != null) {
                    response.append(vErro2);
                }

                //String body = "data_atendimento=" + vData;
                //OutputStream outputStream = connection.getOutputStream();
                //outputStream.write(body.toString().getBytes());
                //outputStream.close();            
//            String line = null;
//            while ((line = bufferedReader.readLine()) != null) {
//                response.append(line);
//            }            
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava(response.toString(), caminho);
                String vRespostaServer = response.toString();
                Persistencia dbpadrao, dbsatellite;
                //dbsatellite = this.pool.getConexao("SATELLITE");
                dbpadrao = this.pool.getConexao("SATPRESERVE");
                //dbsatellite.FechaConexao();  

                // Gravar JSON
                String vTotalRegistros = "";
                String vOrgao_centralizador = "";
                String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
                String vCodBanco = "";
                String vTipoEmpresa = "";
                String vCodAgencia = "";
                String vNome_agencia_origem = "";
                String vNumeroSolicitacao = "";
                String vNumeroMalote = "";
                String vNumeroControle = "";
                String vTipoDenominacao = "";
                String vDescricaoDEnominacao = "";
                String vHorarioAtendimento = "";
                String vLocal_Atendimento = "";
                String vStatusSuprimento = "";
                String vDescricaoStatus = "";
                String vIdentificacaoApropricao = "";
                String vDescricaoApropriacao = "";
                String vSQL = "";
                int vPedido = 0;
                int vQtdePed = 0;
                String vIndice_controle_solicitacao = "";

                JsonArray jPedidoA = jsonObject.getAsJsonArray("data");

                if (!jsonObject.get("total_registros").equals(null)) {
                    vOrgao_centralizador = jsonObject.get("orgao_centralizador").toString();
                    //vDataPed = jsonObject.get("data_solicitacao").toString();
                    for (int i = 0; i < jPedidoA.size(); i++) {
                        JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();

                        vCodBanco = jPedido.getAsJsonPrimitive("codigo_banco").getAsString();
                        vTipoEmpresa = jPedido.getAsJsonPrimitive("tipo_empresa").getAsString();
                        vCodAgencia = jPedido.getAsJsonPrimitive("codigo_agencia").getAsString();
                        vNome_agencia_origem = jPedido.getAsJsonPrimitive("nome_agencia_origem").getAsString();
                        vNumeroSolicitacao = jPedido.getAsJsonPrimitive("numero_solicitacao").getAsString();
                        vNumeroMalote = jPedido.getAsJsonPrimitive("numero_malote").getAsString();
                        vNumeroControle = jPedido.getAsJsonPrimitive("numero_controle").getAsString();
                        vTipoDenominacao = jPedido.getAsJsonPrimitive("tipo_denominacao").getAsString();
                        vDescricaoDEnominacao = jPedido.getAsJsonPrimitive("descricao_denominacao").getAsString();
                        vHorarioAtendimento = jPedido.getAsJsonPrimitive("horario_atendimento").getAsString();
                        vLocal_Atendimento = jPedido.getAsJsonPrimitive("local_atendimento").getAsString();
                        if (jsonObject.get("status_suprimento") != null) {
                            vStatusSuprimento = jPedido.getAsJsonPrimitive("status_suprimento").getAsString();
                        } else {
                            vStatusSuprimento = "N";
                        }
                        vDescricaoStatus = jPedido.getAsJsonPrimitive("descricao_status_suprimento").getAsString();
                        vIdentificacaoApropricao = jPedido.getAsJsonPrimitive("identificacao_apropriacao").getAsString();
                        vDescricaoApropriacao = jPedido.getAsJsonPrimitive("descricao_identificacao_apropriacao").getAsString();
                        vIndice_controle_solicitacao = jPedido.getAsJsonPrimitive("indice_controle_solicitacao").getAsString();
                        Consulta qTmpX, qTmpX2, qTmpXPed;

                        //Grava Log JSON
                        vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                                + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                + "'suprimentos_processantes', \n"
                                + "'data_atendimento=" + vData + "', \n"
                                + "'" + vIndice_controle_solicitacao + "', \n"
                                + "'" + vRespostaServer + "', \n"
                                + "'SATSERVER', \n"
                                + "'" + getDataAtual("SQL") + "', \n"
                                + "'" + getDataAtual("HORA") + "')";
                        SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                        SQLPdrLog.insert();

                        vSQL = "Select Max(Numero)+1 Numero from Pedido \n"
                                + " where CodFil = 1 ";

                        try {
                            qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                            qTmpX.select();
                        } catch (Exception e) {
                            logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                            throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                                    + vSQL);
                        }
                        int vConta = 0;
                        Consulta SQLPdr;

                        if (qTmpX.Proximo()) {
                            vConta++;
                            vPedido = qTmpX.getInt("NUMERO");
                            String vSQLPed = "(Select Count(*) from Pedido where Data = '" + vDataPed + "' and PedidoCliente = '" + vNumeroControle + "')";

                            try {
                                vSQL = "Select Clientes.Codigo, Clientes.Nred, OS_Vig.OS, OS_Vig.CliDst, CliCxf.Nred NRedCxf, CliCxf.Codigo CodCliCxf, " + vSQLPed + " QtdePed from Clientes \n"
                                        + "Left Join OS_Vig on OS_Vig.Cliente = Clientes.Codigo\n"
                                        + "                and OS_Vig.CodFil = Clientes.CodFil\n"
                                        + "                and OS_Vig.Situacao = 'A'      \n"
                                        + "Left Join Clientes CliCxf on CliCXf.Codigo = '9990001' "
                                        + "                         and CliCxf.CodFil = Clientes.CodFil "
                                        + "where Clientes.InterfExt like '%" + vTipoEmpresa + " " + vCodAgencia + " " + vNome_agencia_origem + "%'";
                                qTmpX2 = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                qTmpX2.select();
                                if (!vNumeroSolicitacao.equals("")) {
                                    String vOS = "";
                                    if (qTmpX2.Proximo()) {
                                        vCodCli = qTmpX2.getString("Codigo");
                                        vNomeCli = qTmpX2.getString("Nred");
                                        vOS = qTmpX2.getString("OS");
                                        vQtdePed = qTmpX2.getInt("QtdePed");

                                    }
                                    if (vQtdePed == 0) {
                                        vSQL = "insert into Pedido(Numero, Codfil, Data, Tipo, Codcli1, Nred1, Hora1o, Hora2O, "
                                                + "Codcli2, Nred2, Hora1D, Hora2D, Solicitante, Valor, Obs, OperIncl, ClassifSrv, Dt_Incl, "
                                                + " hr_Incl, OS, PedidoCliente, Situacao,   Operador, Dt_alter, Hr_Alter, Flag_Excl)"
                                                + "Values(\n"
                                                + vPedido + ", \n"
                                                + "1" + ", \n"
                                                + "'" + vDataPed + "', \n"
                                                + "'T', \n"
                                                + "'" + "9990001" + "', \n"
                                                + "'" + "PRESERV CX FORTE" + "', \n"
                                                + "'08:00'" + ", \n"
                                                + "'18:00'" + ", \n"
                                                + "'" + vCodCli + "', \n"
                                                + "'" + vNomeCli + "', \n"
                                                + "'" + vHorarioAtendimento + "', \n"
                                                + "'" + vHorarioAtendimento + "', \n"
                                                + "'WS ITAU'" + ", \n"
                                                + "0" + ", \n"
                                                + "'IMPORTACAO'" + ", \n"
                                                + "'SATSERVER'" + ", \n"
                                                + "'V'" + ", \n"
                                                + "'" + getDataAtual("SQL") + "', \n"
                                                + "'" + getDataAtual("HORA") + "',\n"
                                                + "'" + vOS + "', \n"
                                                + "'" + vNumeroControle + "'" + ", \n"
                                                + "'PD'" + ", \n"
                                                + "'SATSERVER'" + ", \n"
                                                + "'" + getDataAtual("SQL") + "', \n"
                                                + "'" + getDataAtual("HORA") + "',\n"
                                                + "''" + ") \n";
                                        SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                        SQLPdr.insert();
                                    }
                                    //}
                                }
                            } catch (Exception e) {
                                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                throw new Exception("Pedido - " + e.getMessage() + "\r\n"
                                        + vSQL);
                            }

                        }
                        //Gravar Pedido

                        vSQL = "";
                        //Buscar Detalhes
                        if (vQtdePed == 0) {
                            consultaSuprimentosProcDet(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vPedido, vStatusSuprimento);
                        }
                    }
                    vRetorno = jsonObject.get("indice_controle_solicitacao")
                            + "-RespostaCompleta:" + response.toString();
                } else {
                    vRetorno = response.toString();
                }
                bufferedReader.close();
            }
            //vRetorno = response.toString() + bufferedReader.toString();            
        } catch (Exception e) {
            vRetorno = e.toString() + " - Resposta EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaSuprimentosProcDet(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vNumPed, int NumPedSat, String vStatus) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_detalhes?tipo_solicitante=1&indice_controle_solicitacao=" + vNumPed);
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_detalhes?tipo_solicitante=1&indice_controle_solicitacao=" + vNumPed);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI2NmY3MTBlYy0zN2U3LTQ2NDUtOWIzZS00NGY5OTU4ODk3ZjkiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.XcJSJlFHWHo6r3DeDh5m_nKVRC-alYxoYnt0yH0IcgA");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            InputStream vErro = connection.getErrorStream();
            String vErro2;
            BufferedReader br = null;
            /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
             */
            Persistencia dbpadrao, dbsatellite;
            //dbsatellite = this.pool.getConexao("SATELLITE");
            dbpadrao = this.pool.getConexao("SATPRESERVE");

            String line = null;

            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }

            // Gravar JSON
            Consulta SQLPdrLog;
            String vSQLLog;
            String vRespostaServer = response.toString();
            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'suprimentos_detalhes', \n"
                    + "'indice_controle_solicitacao=" + vNumPed + "', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + vRespostaServer + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
            SQLPdrLog.insert();
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);

            String vDenominacao = "";
            String vHorario;
            String vDescricaoSuprimento;
            String vValorTotal;
            String vTipo;
            String vQtde;

            vDenominacao = jsonObject.get("tipo_denominacao").getAsString();
            vHorario = jsonObject.get("horario_atendimento").getAsString();
            vDescricaoSuprimento = jsonObject.get("descricao_suprimento").getAsString();
            vValorTotal = jsonObject.get("valor_total_malote_suprimento").getAsString();
            Consulta SQLPdr;
            String vSQL = "";
            if (vStatus == null) {
                    vStatus = "S";
                } else {
                    vStatus = "S";
                }                
            if (jsonObject.get("descricao_suprimento").getAsString().equals("CONFIRMADO")) {                
                JsonArray jPedidoA = jsonObject.getAsJsonArray("data");

                for (int i = 0; i < jPedidoA.size(); i++) {
                    vDenominacao = jsonObject.get("tipo_denominacao").getAsString();
                    JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();

                    vTipo = jPedido.getAsJsonPrimitive("valor_denominacao").getAsString();
                    vQtde = jPedido.getAsJsonPrimitive("quantidade_em_milheiros").getAsString();

                    //Gravar Pedido
                    try {
                        //if (!vQtde.equals("0")) {
                        if (vDenominacao.toString().equals("C")) {
                            vSQL = "insert into PedidoDN(Numero, Codfil, Codigo, Docto, Qtde) Values(\n"
                                    + NumPedSat + ", \n"
                                    + "1" + ", \n"
                                    + vTipo + ", \n"
                                    + "'IMP', \n"
                                    + vQtde + "*1000);Update Pedido Set Valor = " + vValorTotal + " where Numero = " + NumPedSat + " and codfil = 1;delete from PedidoDN where Numero = " + NumPedSat + " and codfil = 1 and Qtde = 0;\n";
                        } else {
                            if (vTipo.contains("1.0")) {
                                vTipo = "100";
                            } else {
                                vTipo = vTipo.replace("0.", "");
                            }
                            vSQL = "insert into PedidoMD(Numero, Codfil, Codigo, Docto, Qtde) Values(\n"
                                    + NumPedSat + ", \n"
                                    + "1" + ", \n"
                                    + vTipo + ", \n"
                                    + "'IMP', \n"
                                    + vQtde + "*1000);Update Pedido Set Valor = " + vValorTotal + " where Numero = " + NumPedSat + " and codfil = 1;delete from PedidoMD where Numero = " + NumPedSat + " and codfil = 1 and Qtde = 0;\n";
                        }
                        SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                        SQLPdr.insert();
                        //}
                    } catch (Exception e) {
                        logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                        throw new Exception("PedidoDN - " + e.getMessage() + "\r\n"
                                + vSQL);
                    }

                    //Buscar Detalhes
                }                
                vRetorno = jsonObject.get("indice_controle_solicitacao")
                        + "-RespostaCompleta:" + response.toString();
            } else {
                //aqui inicia
                apropriaSuprimentosProc(clientId, clientSecret, certificado, senhaCertificado, Token, vNumPed, NumPedSat, vStatus);                
                vSQL = " Update Pedido Set Valor = 0, Obs = 'CANCELADO' where Numero = " + NumPedSat + " and codfil = 1;\n";
                SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                SQLPdr.update();
                vRetorno = response.toString();
            
            }
            apropriaSuprimentosProc(clientId, clientSecret, certificado, senhaCertificado, Token, vNumPed, NumPedSat, vStatus);
            //vRetorno = response.toString() + bufferedReader.toString();

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String apropriaSuprimentosProc(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vNumPed, int NumPedSat, String vStatus) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            this.logerro.Grava("INICIA APROPRIACAO", caminho);
            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_apropriacoes/" + vNumPed+"?");                               
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_apropriacoes/" + vNumPed + "?");

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            this.logerro.Grava("APROPRIACAO URL:"+url.toString(), caminho);
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI2NmY3MTBlYy0zN2U3LTQ2NDUtOWIzZS00NGY5OTU4ODk3ZjkiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.XcJSJlFHWHo6r3DeDh5m_nKVRC-alYxoYnt0yH0IcgA");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("PUT");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();
            if (vStatus == null) {
                vStatus = "S";
            }

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            this.logerro.Grava("RESPOSTA APROPRIACAO: "+Integer.toString(connection.getResponseCode()), caminho);
            /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
             */
            String body = "{\"status_solicitacao\":\"" + vStatus + "\"}";
            this.logerro.Grava("GRAVA BODY: "+body, caminho);
            
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();
            
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            int vret = connection.getResponseCode();

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            InputStream vErro = connection.getErrorStream();
            String vErro2;
            BufferedReader br = null;

            this.logerro.Grava("GRAVA RESPOSTA BODY: "+Integer.toString(vResposta), caminho);
            
            this.logerro.Grava("GRAVA RESPOSTA ERRO: "+vErro, caminho);
            String line = null;
            
            this.logerro.Grava("GRAVA RESPOSTA ERRO: "+vErro, caminho);
            
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
           
            String vRespostaServer;
            vRespostaServer = response.toString();
            this.logerro.Grava("RESPOSTA_SERVER_APROPRIA: "+vRespostaServer, caminho);
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);
// Gravar JSON
            Consulta SQLPdrLog;
            String vSQLLog;

            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'suprimentos_apropriacoes', \n"
                    + "'" + vNumPed + "?', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + vRespostaServer + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
            SQLPdrLog.insert();

            String vDenominacao = "";
            String vHorario;
            String vDescricaoSuprimento;
            String vValorTotal;
            String vTipo;
            String vQtde;

            vRetorno = response.toString();

            this.logerro.Grava("SERVER_APROPRIACAO: "+vRetorno, caminho);
            
            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
            e.printStackTrace();
            this.logerro.Grava("ERRO_SERVER_APROPRIA: "+vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaSuprimentosTransp(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_transportantes?data_atendimento=" + vData);
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_transportantes?data_atendimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("x-itau-apikey", clientId);
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI2NmY3MTBlYy0zN2U3LTQ2NDUtOWIzZS00NGY5OTU4ODk3ZjkiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.XcJSJlFHWHo6r3DeDh5m_nKVRC-alYxoYnt0yH0IcgA");
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);

            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            // Add certificate
            File p12 = vCertificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            //String body = "data_atendimento=" + vData;
            OutputStream outputStream = connection.getOutputStream();
            //outputStream.write(body.toString().getBytes());
            outputStream.close();

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
            vRetorno = response.toString() + bufferedReader.toString();

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaRecolhimentoProc(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_eventuais?data_atendimento=" + vData);
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_eventuais?data_atendimento=" + vData);
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_processantes?data_atendimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", "66f710ec-37e7-4645-9b3e-44f9958897f9");
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI2NmY3MTBlYy0zN2U3LTQ2NDUtOWIzZS00NGY5OTU4ODk3ZjkiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.XcJSJlFHWHo6r3DeDh5m_nKVRC-alYxoYnt0yH0IcgA");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificado.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadoh.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI2NmY3MTBlYy0zN2U3LTQ2NDUtOWIzZS00NGY5OTU4ODk3ZjkiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.XcJSJlFHWHo6r3DeDh5m_nKVRC-alYxoYnt0yH0IcgA");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "https://hom-secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadoh.pfx");
            senhaCertificado = "1234";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            
            this.logerro.Grava("RESPOSTA_CONSULTA_REALIZA:"+Integer.toString(vResposta), caminho);

            if (vResposta == 403){
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                Token = vRetorno;
            }
            
            Consulta SQLPdrLog;
            String vSQLLog;

            if (vResposta == 404) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_eventuais?data_atendimento" + " ', \n"
                        + "'recolhimentos_eventuais?data_atendimento=" + vData + "', \n"
                        + "'" + "', \n"
                        + "'SEM RESPOSTA RECOLHIMENTO', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            }else{
                this.logerro.Grava("RESPOSTA_CONSULTA_REALIZA_NAO_INSERIU:"+Integer.toString(vResposta), caminho);
            }

            
            InputStream vErro = connection.getErrorStream();
            this.logerro.Grava("Erro_CONSULTA_REALIZA:"+vErro, caminho);
            String vErro2;
            BufferedReader br = null;
            /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                            br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
             */
            while ((vErro2 = bufferedReader.readLine()) != null) {
                response.append(vErro2);
            }
            this.logerro.Grava("Erro2_CONSULTA_REALIZA:"+vErro2, caminho);
            //String body = "data_atendimento=" + vData;
            //OutputStream outputStream = connection.getOutputStream();
            //outputStream.write(body.toString().getBytes());
            //outputStream.close();            
//            String line = null;
//            while ((line = bufferedReader.readLine()) != null) {
//                response.append(line);
//            }
            String vRespostaServer = response.toString();
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);

            Persistencia dbpadrao, dbsatellite;
            //dbsatellite = this.pool.getConexao("SATELLITE");
            dbpadrao = this.pool.getConexao("SATPRESERVE");
            //dbsatellite.FechaConexao();            

            String vTotalRegistros = "";
            String vOrgao_centralizador = "";
            String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
            String vCodBanco = "";
            String vTipoEmpresa = "";
            String vCodAgencia = "";
            String vNome_agencia_origem = "";
            String vNumeroSolicitacao = "";
            String vNumeroMalote = "";
            String vNumeroControle = "";
            String vTipoDenominacao = "";
            String vDescricaoDEnominacao = "";
            String vHorarioAtendimento = "";
            String vLocal_Atendimento = "";
            String vStatusSuprimento = "";
            String vDescricaoStatus = "";
            String vIdentificacaoApropricao = "";
            String vDescricaoApropriacao = "";
            String vIdentificadorCancelamento = "";
            String vSQL = "";
            int vPedido = 0;
            int vQtdePed = 0;
            String vIndice_controle_solicitacao = "";

            JsonArray jPedidoA = jsonObject.getAsJsonArray("data");

            if (!jsonObject.isJsonNull()) {
                //vDataPed = jsonObject.get("data_solicitacao").toString();                
                for (int i = 0; i < jPedidoA.size(); i++) {

                    JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();

                    JsonArray jPedidoB = jPedido.getAsJsonArray("registros");
                    if (!jPedidoB.isJsonNull()) {
                        this.logerro.Grava("Json: " + jPedido.toString(), caminho);
                        for (int z = 0; z < jPedidoB.size(); z++) {
                            JsonObject jPedidoC = jPedidoB.get(z).getAsJsonObject();
                            vCodBanco = jPedidoC.getAsJsonPrimitive("codigo_banco").getAsString();
                            vTipoEmpresa = jPedidoC.getAsJsonPrimitive("tipo_empresa").getAsString();
                            vCodAgencia = jPedidoC.getAsJsonPrimitive("codigo_agencia").getAsString();
                            vNome_agencia_origem = jPedidoC.getAsJsonPrimitive("nome_agencia").getAsString();
                            vOrgao_centralizador = jPedido.get("orgao_centralizador").getAsString();
                            vNumeroSolicitacao = jPedidoC.getAsJsonPrimitive("solicitacao").getAsString();
                            vHorarioAtendimento = jPedidoC.getAsJsonPrimitive("horario").getAsString();
                            vLocal_Atendimento = jPedidoC.getAsJsonPrimitive("local").getAsString();
                            vIdentificacaoApropricao = jPedidoC.getAsJsonPrimitive("identificador_apropriacao").getAsString();
                            vDescricaoApropriacao = jPedidoC.getAsJsonPrimitive("descricao_apropriacao").getAsString();
                            vIdentificadorCancelamento = jPedidoC.getAsJsonPrimitive("identificador_cancelamento").getAsString();
                            vIndice_controle_solicitacao = jPedidoC.getAsJsonPrimitive("indice_controle_solicitacao").getAsString();                            
                            Consulta qTmpX, qTmpX2, qTmpXPed;

                            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                    + "'recolhimentos_eventuais?data_atendimento" + " ', \n"
                                    + "'recolhimentos_eventuais?data_atendimento=" + vData + "', \n"
                                    + "'" + vIndice_controle_solicitacao + "', \n"
                                    + "'" + vRespostaServer + "', \n"
                                    + "'SATSERVER', \n"
                                    + "'" + getDataAtual("SQL") + "', \n"
                                    + "'" + getDataAtual("HORA") + "')";
                            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));                            
                            SQLPdrLog.insert();
                            // Log JSON                                                                            
                            //Final log JSON
                            // Apropria Carlos 31/08/2023
                            
                            if (!vIndice_controle_solicitacao.equals("")){
                               recolhimentos_eventuais_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vIdentificadorCancelamento);
                            }
                            vSQL = "Select Max(Numero)+1 Numero from Pedido \n"
                                    + " where CodFil = 1 ";

                            try {
                                qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                qTmpX.select();
                            } catch (Exception e) {
                                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                                        + vSQL);
                            }
                            int vConta = 0;
                            Consulta SQLPdr;

                            if (qTmpX.Proximo()) {
                                vConta++;
                                vPedido = qTmpX.getInt("NUMERO");
                                String vSQLPed = "(Select Count(*) from Pedido where Data = '" + vDataPed + "' and Solicitante = '" + vIndice_controle_solicitacao + "')";

                                try {
                                    vSQL = "Select Clientes.Codigo, Clientes.Nred, OS_Vig.OS, OS_Vig.CliDst, CliCxf.Nred NRedCxf, CliCxf.Codigo CodCliCxf, " + vSQLPed + " QtdePed from Clientes \n"
                                            + "Left Join OS_Vig on OS_Vig.Cliente = Clientes.Codigo\n"
                                            + "                and OS_Vig.CodFil = Clientes.CodFil\n"
                                            + "                and OS_Vig.Situacao = 'A'      \n"
                                            + "Left Join Clientes CliCxf on CliCXf.Codigo = '9990001' "
                                            + "                         and CliCxf.CodFil = Clientes.CodFil "
                                            + "where Clientes.InterfExt like '%" + vTipoEmpresa + " " + vCodAgencia + " " + vNome_agencia_origem + "%'";
                                    qTmpX2 = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                    qTmpX2.select();
                                    if (!vNumeroSolicitacao.equals("")) {
                                        String vOS = "";
                                        if (qTmpX2.Proximo()) {
                                            vCodCli = qTmpX2.getString("Codigo");
                                            vNomeCli = qTmpX2.getString("Nred");
                                            vOS = qTmpX2.getString("OS");
                                            vQtdePed = qTmpX2.getInt("QtdePed");

                                        }
                                        if (vQtdePed == 0) {
                                            vSQL = "insert into Pedido(Numero, Codfil, Data, Tipo, Codcli1, Nred1, Hora1o, Hora2O, "
                                                    + "Codcli2, Nred2, Hora1D, Hora2D, Solicitante, Valor, Obs, OperIncl, ClassifSrv, Dt_Incl, "
                                                    + " hr_Incl, OS, PedidoCliente, Situacao,   Operador, Dt_alter, Hr_Alter, Flag_Excl)"
                                                    + "Values(\n"
                                                    + vPedido + ", \n"
                                                    + "1" + ", \n"
                                                    + "'" + vDataPed + "', \n"
                                                    + "'T', \n"
                                                    + "'" + vCodCli + "', \n"
                                                    + "'" + vNomeCli + "', \n"
                                                    + "'" + vHorarioAtendimento + "', \n"
                                                    + "'" + vHorarioAtendimento + "', \n"
                                                    + "'" + "9990001" + "', \n"
                                                    + "'" + "PRESERV CX FORTE" + "', \n"
                                                    + "'08:00'" + ", \n"
                                                    + "'18:00'" + ", \n"
                                                    //+ "'WS ITAU'" + ", \n"
                                                    + "'" + vIndice_controle_solicitacao + "'" + ", \n"
                                                    + "0" + ", \n"
                                                    + "'IMPORTACAO - WS ITAU'" + ", \n"
                                                    + "'SATSERVER'" + ", \n"
                                                    + "'V'" + ", \n"
                                                    + "'" + getDataAtual("SQL") + "', \n"
                                                    + "'" + getDataAtual("HORA") + "',\n"
                                                    + "'" + vOS + "', \n"
                                                    + "'" + vNumeroControle + "'" + ", \n"
                                                    + "'PD'" + ", \n"
                                                    + "'SATSERVER'" + ", \n"
                                                    + "'" + getDataAtual("SQL") + "', \n"
                                                    + "'" + getDataAtual("HORA") + "',\n"
                                                    + "''" + ") \n";
                                            SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                            SQLPdr.insert();
                                            vIndice_controle_solicitacao = ""; 
                                        }
                                        //}
                                    }
                                } catch (Exception e) {
                                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                    throw new Exception("Pedido - " + e.getMessage() + "\r\n"
                                            + vSQL);
                                }

                            }
                        }
                    }
                    //Gravar Pedido

                    vSQL = "";
                    //Buscar Apropriar
                    if (vQtdePed >= 0) {
                        //recolhimentos_eventuais_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao);
                        //consultaSuprimentosProcDet(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vPedido, vStatusSuprimento);
                    }
                }
                vRetorno = jsonObject.get("indice_controle_solicitacao")
                        + "-RespostaCompleta:" + response.toString();
            } else {
                vRetorno = response.toString();
            }

            //vRetorno = response.toString() + bufferedReader.toString();
            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString() + " - Resposta EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaRecolhimentoRealizados(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadoh.pfx");
        logexecucao.Grava("TOKEN INICIO - Recolhimentos Realizados: ", caminho);
        Token = GetAccessTokenh(clientId, clientSecret, certificado, senhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
        logexecucao.Grava("TOKEN - Recolhimentos Realizados: " + Token, caminho);
        try {

            URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_realizados?data_atendimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", "66f710ec-37e7-4645-9b3e-44f9958897f9");
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI2NmY3MTBlYy0zN2U3LTQ2NDUtOWIzZS00NGY5OTU4ODk3ZjkiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.XcJSJlFHWHo6r3DeDh5m_nKVRC-alYxoYnt0yH0IcgA");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificado.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadoh.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI2NmY3MTBlYy0zN2U3LTQ2NDUtOWIzZS00NGY5OTU4ODk3ZjkiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.XcJSJlFHWHo6r3DeDh5m_nKVRC-alYxoYnt0yH0IcgA");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "https://hom-secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            logexecucao.Grava("URL - Recolhimentos Realizados: " + url.toString(), caminho);
            
            senhaCertificado = "1234";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            logexecucao.Grava("Recolhimentos Realizados Resposta: " + Integer.toString(vResposta, vResposta), caminho);

            Consulta SQLPdrLog;
            String vSQLLog;
            if (vResposta == 403){
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                Token = vRetorno;
            }

            if (vResposta == 404 || vResposta == 400) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_realizados?data_atendimento" + " ', \n"
                        + "'recolhimentos_realizados?data_atendimento=" + vData + "', \n"
                        + "'" + "', \n"
                        + "'SEM RESPOSTA RECOLHIMENTO', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            }else{
                logexecucao.Grava("!= 400 - Recolhimentos Realizados Resposta: " + Integer.toString(vResposta, vResposta), caminho);
            }

            InputStream vErro = connection.getErrorStream();
            String vErro2;
            BufferedReader br = null;
            
        
            /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                            br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
             */
            while ((vErro2 = bufferedReader.readLine()) != null) {
                response.append(vErro2);
            }

                logexecucao.Grava("Recolhimentos Realizados Resposta Erro: " + vErro +"/n"
                              +" Normal:"+vErro2, caminho);
            //String body = "data_atendimento=" + vData;
            //OutputStream outputStream = connection.getOutputStream();
            //outputStream.write(body.toString().getBytes());
            //outputStream.close();            
//            String line = null;
//            while ((line = bufferedReader.readLine()) != null) {
//                response.append(line);
//            }
            String vRespostaServer = response.toString();
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);

            Persistencia dbpadrao, dbsatellite;
            //dbsatellite = this.pool.getConexao("SATELLITE");
            dbpadrao = this.pool.getConexao("SATPRESERVE");
            //dbsatellite.FechaConexao();            

            String vTotalRegistros = "";
            String vOrgao_centralizador = "";
            String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
            String vCodBanco = "";
            String vTipoEmpresa = "";
            String vCodAgencia = "";
            String vNome_agencia_origem = "";
            String vNumeroSolicitacao = "";
            String vNumeroMalote = "";
            String vNumeroControle = "";
            String vTipoDenominacao = "";
            String vDescricaoDEnominacao = "";
            String vHorarioAtendimento = "";
            String vLocal_Atendimento = "";
            String vStatusSuprimento = "";
            String vDescricaoStatus = "";
            String vIdentificacaoApropricao = "";
            String vDescricaoApropriacao = "";
            String vValorRec = "";
            String vDescRec = "";
            String vSQL = "";
            int vPedido = 0;
            int vQtdePed = 0;
            String vIndice_controle_solicitacao = "";

            JsonArray jPedidoA = jsonObject.getAsJsonArray("data");

            if (!jsonObject.isJsonNull()) {
                //vDataPed = jsonObject.get("data_solicitacao").toString();                
                for (int i = 0; i < jPedidoA.size(); i++) {

                    JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();

                    JsonArray jPedidoB = jPedido.getAsJsonArray("registros");
                    if (!jPedidoB.isJsonNull()) {
                        this.logerro.Grava("Json: " + jPedido.toString(), caminho);
                        for (int z = 0; z < jPedidoB.size(); z++) {
                            JsonObject jPedidoC = jPedidoB.get(z).getAsJsonObject();
                            vCodBanco = jPedidoC.getAsJsonPrimitive("codigo_banco").getAsString();
                            vTipoEmpresa = jPedidoC.getAsJsonPrimitive("tipo_empresa").getAsString();
                            vCodAgencia = jPedidoC.getAsJsonPrimitive("codigo_agencia").getAsString();
                            vNome_agencia_origem = jPedidoC.getAsJsonPrimitive("nome_agencia").getAsString();
                            vOrgao_centralizador = "";//jPedido.get("orgao_centralizador").getAsString();
                            vHorarioAtendimento = jPedidoC.getAsJsonPrimitive("horario").getAsString();
                            vIndice_controle_solicitacao = jPedidoC.getAsJsonPrimitive("indice_controle_solicitacao").getAsString();
                            vValorRec = jPedidoC.getAsJsonPrimitive("valor_recolhimento").getAsString();
                            vDescRec = jPedidoC.getAsJsonPrimitive("descricao_situacao_recolhimento").getAsString();
                            Consulta qTmpX, qTmpX2, qTmpXPed;
                            logexecucao.Grava("LOG: " + vSQL+"-"+vDescRec+"-"+vIndice_controle_solicitacao+"-"+vNome_agencia_origem, caminho);
                            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                    + "'recolhimentos_realizados?data_atendimento" + " ', \n"
                                    + "'recolhimentos_realizados?data_atendimento=" + vData + "', \n"
                                    + "'" + vIndice_controle_solicitacao + "', \n"
                                    + "'" + vRespostaServer + "', \n"
                                    + "'SATSERVER', \n"
                                    + "'" + getDataAtual("SQL") + "', \n"
                                    + "'" + getDataAtual("HORA") + "')";
                            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                            SQLPdrLog.insert();
                            recolhimentos_realizados_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, "S");
                            // Log JSON                    
                            //Final log JSON
                            // Apropria 31/08/2023 Carlos
                           
                            vSQL = "Select Max(Numero)+1 Numero from Pedido \n"
                                    + " where CodFil = 1 ";

                            try {
                                qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                qTmpX.select();
                            } catch (Exception e) {
                                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                                        + vSQL);
                            }
                            int vConta = 0;
                            Consulta SQLPdr;

                            if (qTmpX.Proximo()) {
                                vConta++;
                                vPedido = qTmpX.getInt("NUMERO");
                                String vSQLPed = "(Select Count(*) from Pedido where Data = '" + vDataPed + "' and Solicitante = '" + vIndice_controle_solicitacao + "')";
                                String vSQLNumPed = "(Select top 1 Numero from Pedido where Data = '" + vDataPed + "' and Solicitante = '" + vIndice_controle_solicitacao + "')";
                                
                                try {
                                    vSQL = "Select Clientes.Codigo, Clientes.Nred, OS_Vig.OS, OS_Vig.CliDst, CliCxf.Nred NRedCxf, CliCxf.Codigo CodCliCxf, " + vSQLPed + " QtdePed,  " + vSQLNumPed + " NumPed "
                                            + "from Clientes \n"
                                            + "Left Join OS_Vig on OS_Vig.Cliente = Clientes.Codigo\n"
                                            + "                and OS_Vig.CodFil = Clientes.CodFil\n"
                                            + "                and OS_Vig.Situacao = 'A'      \n"
                                            + "Left Join Clientes CliCxf on CliCXf.Codigo = '9990001' "
                                            + "                         and CliCxf.CodFil = Clientes.CodFil "
                                            + "where Clientes.InterfExt like '%" + vTipoEmpresa + " " + vCodAgencia + " " + vNome_agencia_origem + "%'";
                                    logexecucao.Grava("Consulta SQL: " + vSQL+"-"+vSQLNumPed+"-"+vSQLPed+"-"+vIndice_controle_solicitacao, caminho);
                                    qTmpX2 = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                    qTmpX2.select();
                                    if (!vNumeroSolicitacao.equals("")) {
                                        String vOS = "";
                                        if (qTmpX2.Proximo()) {
                                            vCodCli = qTmpX2.getString("Codigo");
                                            vNomeCli = qTmpX2.getString("Nred");
                                            vOS = qTmpX2.getString("OS");
                                            vQtdePed = qTmpX2.getInt("QtdePed");
                                            vPedido = qTmpX2.getInt("NumPed");
                                            logexecucao.Grava("Erro SQL: " + vCodCli+"-"+vNomeCli+"-"+vOS+"-"+vQtdePed+"-"+vIndice_controle_solicitacao, caminho);
                                        }
                                        if (vQtdePed >= 0) {
                                            vSQL = "insert into Pedido(Numero, Codfil, Data, Tipo, Codcli1, Nred1, Hora1o, Hora2O, "
                                                    + "Codcli2, Nred2, Hora1D, Hora2D, Solicitante, Valor, Obs, OperIncl, ClassifSrv, Dt_Incl, "
                                                    + " hr_Incl, OS, PedidoCliente, Situacao,   Operador, Dt_alter, Hr_Alter, Flag_Excl)"
                                                    + "Values(\n"
                                                    + vPedido + ", \n"
                                                    + "1" + ", \n"
                                                    + "'" + vDataPed + "', \n"
                                                    + "'T', \n"
                                                    + "'" + vCodCli + "', \n"
                                                    + "'" + vNomeCli + "', \n"
                                                    + "'" + vHorarioAtendimento + "', \n"
                                                    + "'" + vHorarioAtendimento + "', \n"
                                                    + "'" + "9990001" + "', \n"
                                                    + "'" + "PRESERV CX FORTE" + "', \n"
                                                    + "'08:00'" + ", \n"
                                                    + "'18:00'" + ", \n"
                                                    //+ "'WS ITAU'" + ", \n"
                                                    + "'" + vIndice_controle_solicitacao + "'" + ", \n"
                                                    + vValorRec + ", \n"
                                                    + "'IMPORTACAO - WS ITAU - REALIZADO'" + ", \n"
                                                    + "'SATSERVER'" + ", \n"
                                                    + "'V'" + ", \n"
                                                    + "'" + getDataAtual("SQL") + "', \n"
                                                    + "'" + getDataAtual("HORA") + "',\n"
                                                    + "'" + vOS + "', \n"
                                                    + "'" + vNumeroControle + "'" + ", \n"
                                                    + "'PD'" + ", \n"
                                                    + "'SATSERVER'" + ", \n"
                                                    + "'" + getDataAtual("SQL") + "', \n"
                                                    + "'" + getDataAtual("HORA") + "',\n"
                                                    + "''" + ") \n";
                                            logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                            SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                            SQLPdr.insert();
                                            vIndice_controle_solicitacao = "";
                                        } else {
                                            vSQL = " Update Pedido Set Obs = Substring(Obs+" + vDescRec+",1,80) , "
                                                    + " Valor = " + vValorRec
                                                    + " where Numero = " + vPedido
                                                    + "   and CodFil = 1 ";
                                            SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                            logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                            SQLPdr.update();
                                        }

                                        //}
                                    }else{
                                        logexecucao.Grava("Erro: NAO PROCESSOU" , caminho);
                                    }
                                       
                                } catch (Exception e) {
                                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                    throw new Exception("Pedido - " + e.getMessage() + "\r\n"
                                            + vSQL);
                                }

                            }
                        }
                    }
                    //Gravar Pedido

                    vSQL = "";
                    //Buscar Apropriar
                    if (vQtdePed >= 0) {
                        recolhimentos_realizados_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, "S");
                        //consultaSuprimentosProcDet(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vPedido, vStatusSuprimento);
                    }
                }
                vRetorno = jsonObject.get("indice_controle_solicitacao")
                        + "-RespostaCompleta:" + response.toString();
                logexecucao.Grava("Retorno: " + vRetorno, caminho);
            } else {
                vRetorno = response.toString();
                logexecucao.Grava("Retorno: " + vRetorno, caminho);
            }

            //vRetorno = response.toString() + bufferedReader.toString();
            bufferedReader.close();

        } catch (Exception e) {
            this.logerro.Grava("-"+vRetorno, caminho);
            vRetorno = e.toString() + " - Resposta EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String recolhimentos_eventuais_confirmacao(String clientId, String clientSecret, File certificado, String senhaCertificado, String vToken, String vNumPed, String vIdentificadorCencelamento) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_eventuais_confirmacao");

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);
            connection.addRequestProperty("Content-Type", "application/json");
            //connection.addRequestProperty("x-itau-correlationID", "a1e64241-7fdb-4d05-a7f6-c44febcdd8d9");
            //connection.addRequestProperty("x-itau-flowID", "1");
            connection.addRequestProperty("x-itau-apikey", "66f710ec-37e7-4645-9b3e-44f9958897f9");
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI2NmY3MTBlYy0zN2U3LTQ2NDUtOWIzZS00NGY5OTU4ODk3ZjkiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.XcJSJlFHWHo6r3DeDh5m_nKVRC-alYxoYnt0yH0IcgA");
            connection.addRequestProperty("Authorization", "Bearer " + vToken);
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // Add certificate
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadoh.pfx");
            senhaCertificado = "1234";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            /*
            String body = "cnpj_matriz=8787673000145"
                        + "&cnpj_filial=8787673000145"
                        + "&codigo_transportadora=162"
                        + "&codigo_filial=02";
             */
            String body = " {\r\n \"indice_controle_solicitacao\": \"" + vNumPed + "\",\r\n \"status_solicitacao\": \"" + vIdentificadorCencelamento + "\"\r\n                }";

            // Guarda no Log
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'recolhimentos_eventuais_confirmacao', \n"
                    + "'', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + body + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
            SQLPdrLog.insert();
            this.logerro.Grava("recolhimentos_eventuais_confirmacao: " + body, caminho);
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();
            int vret = connection.getResponseCode();
            if (vret == 403){
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                vToken = vRetorno;
            }
            if (vret != 400 && vret != 404) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String line = null;
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                }
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                bufferedReader.close();
            }
            vret = connection.getResponseCode();
            this.logerro.Grava("recolhimentos_eventuais_confirmacao: " + "Ret: " + Integer.toString(vret), caminho);

            vRetorno = response.toString();
            
            if (vRetorno.contains("sucesso")) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_eventuais_confirmacao', \n"
                        + "'', \n"
                        + "'" + vNumPed + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            }
            //this.logerro.Grava(vRetorno, caminho);

        } catch (Exception e) {
            vRetorno = e.toString();
//                    " - "+Integer.toString(vret);
            this.logerro.Grava(vRetorno, caminho);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();

    }

    public String recolhimentos_processamentos_malotes_inclusao(String clientId, String clientSecret, File certificado, String senhaCertificado, String vToken, String vNumPed, String vIdentificadorCencelamento) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_processamentos_malotes_inclusao");

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);
            connection.addRequestProperty("Content-Type", "application/json");
            //connection.addRequestProperty("x-itau-correlationID", "a1e64241-7fdb-4d05-a7f6-c44febcdd8d9");
            //connection.addRequestProperty("x-itau-flowID", "1");
            connection.addRequestProperty("x-itau-apikey", "66f710ec-37e7-4645-9b3e-44f9958897f9");
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI2NmY3MTBlYy0zN2U3LTQ2NDUtOWIzZS00NGY5OTU4ODk3ZjkiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.XcJSJlFHWHo6r3DeDh5m_nKVRC-alYxoYnt0yH0IcgA");
            connection.addRequestProperty("Authorization", "Bearer " + vToken);
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // Add certificate
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadoh.pfx");
            senhaCertificado = "1234";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            /*
            String body = "cnpj_matriz=8787673000145"
                        + "&cnpj_filial=8787673000145"
                        + "&codigo_transportadora=162"
                        + "&codigo_filial=02";
             */
            String body = " {\r\n \"indice_controle_solicitacao\": \"" + vNumPed + "\",\r\n \"status_solicitacao\": \"" + vIdentificadorCencelamento + "\"\r\n                }";

            // Guarda no Log
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'recolhimentos_eventuais_confirmacao', \n"
                    + "'', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + body + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
            SQLPdrLog.insert();
            this.logerro.Grava("recolhimentos_eventuais_confirmacao: " + body, caminho);
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();
            int vret = connection.getResponseCode();
            if (vret == 403){
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                vToken = vRetorno;
            }
            if (vret != 400 && vret != 404) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String line = null;
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                }
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                bufferedReader.close();
            }
            vret = connection.getResponseCode();
            this.logerro.Grava("recolhimentos_eventuais_confirmacao: " + "Ret: " + Integer.toString(vret), caminho);

            vRetorno = response.toString();
            
            if (vRetorno.contains("sucesso")) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_eventuais_confirmacao', \n"
                        + "'', \n"
                        + "'" + vNumPed + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            }
            //this.logerro.Grava(vRetorno, caminho);

        } catch (Exception e) {
            vRetorno = e.toString();
//                    " - "+Integer.toString(vret);
            this.logerro.Grava(vRetorno, caminho);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();

    }
    
    private static boolean isComparaHora(String vHoraAtual, String vHoraCorte) throws ParseException {
        boolean vEventual = false;
        if (!new SimpleDateFormat("HH:mm").parse(vHoraCorte).before(new SimpleDateFormat("HH:mm").parse(vHoraAtual))) {
            vEventual = true;
        }
        return vEventual;
    }

    public String recolhimentos_realizados_confirmacao(String clientId, String clientSecret, File certificado, String senhaCertificado, String vToken, String vNumPed, String vIdentificadorCancelamento) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_realizados_confirmacao");

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);
            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Authorization", "Bearer " + vToken);
            //connection.addRequestProperty("x-itau-correlationID", "a1e64241-7fdb-4d05-a7f6-c44febcdd8d9");
            //connection.addRequestProperty("x-itau-flowID", "1");
            connection.addRequestProperty("x-itau-apikey", "66f710ec-37e7-4645-9b3e-44f9958897f9");
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI2NmY3MTBlYy0zN2U3LTQ2NDUtOWIzZS00NGY5OTU4ODk3ZjkiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.XcJSJlFHWHo6r3DeDh5m_nKVRC-alYxoYnt0yH0IcgA");
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // Add certificate
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadoh.pfx");
            senhaCertificado = "1234";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            /*
            String body = "cnpj_matriz=8787673000145"
                        + "&cnpj_filial=8787673000145"
                        + "&codigo_transportadora=162"
                        + "&codigo_filial=02";
             */
            String body = " {\r\n \"identificador_confirmacao\": \"S\",\r\n \"indice_controle_solicitacao\": \"" + vNumPed + "\"\r\n                }";
            // Guarda no Log
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'recolhimentos_realizados_confirmacao', \n"
                    + "'', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + body + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
            SQLPdrLog.insert();

            this.logerro.Grava("recolhimentos_realizados_confirmacao: " + body, caminho);
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();
            int vret = connection.getResponseCode();
            this.logerro.Grava("recolhimentos_realizados_confirmacao: " + "Ret: " + Integer.toString(vret), caminho);
            String line = null;
            if (vret == 403){
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                vToken = vRetorno;
            }
            if (vret != 400 && vret != 404) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                    JsonParser jsonParser = new JsonParser();
                    JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                }
                bufferedReader.close();
            }
            vret = connection.getResponseCode();
            this.logerro.Grava("recolhimentos_realizados_confirmacao2: " + "Ret: " + Integer.toString(vret), caminho);

            vRetorno = response.toString();
            if (vRetorno.contains("sucesso")) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_realizados_confirmacao', \n"
                        + "'', \n"
                        + "'" + vNumPed + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual        ("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            }

            this.logerro.Grava("recolhimentos_realizados_confirmacao: " + vRetorno, caminho);

        } catch (Exception e) {
            vRetorno = e.toString();
//                    " - "+Integer.toString(vret);
            this.logerro.Grava(vRetorno, caminho);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();

    }

}
