/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.integracao.creditoonlinesantender.bean;

/**
 *
 * <AUTHOR>
 */
public class Credito {

    private Operation operation;
    
    public class Operation{
        private String PGType;

        public String getPGType() {
            return PGType;
        }

        public void setPGType(String PGType) {
            this.PGType = PGType;
        }
    }

    public Operation getOperation() {
        return operation;
    }

    public void setOperation(Operation operation) {
        this.operation = operation;
    }
//    
//    public class Customer{
//        private String documentNumber;
//        private String entity;
//        private String branch;
//        private String number;
//
//        public String getDocumentNumber() {
//            return documentNumber;
//        }
//
//        public void setDocumentNumber(String documentNumber) {
//            this.documentNumber = documentNumber;
//        }
//
//        public String getEntity() {
//            return entity;
//        }
//
//        public void setEntity(String entity) {
//            this.entity = entity;
//        }
//
//        public String getBranch() {
//            return branch;
//        }
//
//        public void setBranch(String branch) {
//            this.branch = branch;
//        }
//
//        public String getNumber() {
//            return number;
//        }
//
//        public void setNumber(String number) {
//            this.number = number;
//        }
//    }
//    
//    public class Carrier{
//        private String documentNumber;
//
//        public String getDocumentNumber() {
//            return documentNumber;
//        }
//
//        public void setDocumentNumber(String documentNumber) {
//            this.documentNumber = documentNumber;
//        }
//    }
//    
//    private String locationCode;
//    
//    private class GVT{
//        private String code;
//        private String declaredValue;
//        private String fairValue;
//
//        public String getCode() {
//            return code;
//        }
//
//        public void setCode(String code) {
//            this.code = code;
//        }
//
//        public String getDeclaredValue() {
//            return declaredValue;
//        }
//
//        public void setDeclaredValue(String declaredValue) {
//            this.declaredValue = declaredValue;
//        }
//
//        public String getFairValue() {
//            return fairValue;
//        }
//
//        public void setFairValue(String fairValue) {
//            this.fairValue = fairValue;
//        }
//    }
//    
//    private String summaryDescriptionDifference;
//    private String costCenter;
}
