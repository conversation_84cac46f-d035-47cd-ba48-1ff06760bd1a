/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.nfe.deserializers;

import br.com.sasw.satwebservice.nfe.exception.NFeException;
import br.com.sasw.satwebservice.nfe.models.ConsultaModel;
import br.com.sasw.satwebservice.nfe.models.RejeicaoModel;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import java.lang.reflect.Type;

/**
 *
 * <AUTHOR>
 */
public class NFeConsultaDeserializer implements JsonDeserializer<ConsultaModel> {

    @Override
    public ConsultaModel deserialize(JsonElement json, Type typeOfT,
            JsonDeserializationContext context) throws NFeException {
        final JsonObject jsonObject = json.getAsJsonObject();

        ConsultaModel consulta = new ConsultaModel();

        try {
            consulta.setChave(jsonObject.get("chave").getAsString());
            if (consulta.getChave().length() > 80) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.chave")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.chave", jsonObject.get("chave").toString())));
        }

        return consulta;
    }
}
