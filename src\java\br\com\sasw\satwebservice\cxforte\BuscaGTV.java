/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.cxforte;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Rt_Perc;
import SasDaos.CxFGuiasDao;
import SasDaos.Rt_GuiasDao;
import SasDaos.Rt_PercDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.UriInfo;

/**
 *
 * <AUTHOR>
 */
@Path("/ws-cxforte/buscaGTV")
public class BuscaGTV {

    private final ArquivoLog logerro;
    private final SasPoolPersistencia pool;
    private Persistencia persistencia;
    private String caminho;
    private final Rt_PercDao rt_PercDao;

    @Context
    private UriInfo context;

    /**
     * Creates a new instance of BuscaGTV
     */
    public BuscaGTV() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\CxForte\\"
                + "\\ERRO\\" + getDataAtual("SQL") + "\\log.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        rt_PercDao = new Rt_PercDao();
    }

    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response post(String input) {
        Gson gson = new GsonBuilder().create();
        Map retorno = new HashMap<>();
        try {
            // Convertendo o input para java
            JsonObject jsonObject = new JsonParser().parse(input).getAsJsonObject();

            // Buscando as informações do json
            String param;
            try{
                param = jsonObject.get("param").getAsString().replace(".0", "");
            } catch (Exception ee){
                // Salvando em log o que foi mandado
                this.logerro.Grava(input, this.caminho);
                throw new Exception("param");
            }
            
            // Atualizando o caminho do log com o novo parâmetro
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\CxForte\\" + param + "\\"
                    + getDataAtual("SQL") + "\\log.txt";
            
            // Salvando em log o que foi mandado
            this.logerro.Grava(input, this.caminho);
            
            String guia;
            try{
                guia = jsonObject.get("guia").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("guia");
            }
            
            String serie;
            try{
                serie = jsonObject.get("serie").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("serie");
            }
            
            String rota;
            try{
                rota = jsonObject.get("rota").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("rota");
            }
            
            String seqRota;
            try{
                seqRota = jsonObject.get("seqrota").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("seqrota");
            }
            
            String codFil;
            try{
                codFil = jsonObject.get("codfil").getAsString().replace(".0", "");
            } catch (Exception ee){
                throw new Exception("codfil");
            }
            
//            String vCodCliCxFX = jsonObject.get("cxforte").getAsString().replace(".0", "");
//            String vData = jsonObject.get("data").getAsString().replace(".0", "");

            persistencia = pool.getConexao(param);
            if(persistencia == null) throw new Exception("persistencia");

            Rt_Perc listaGuias;
            if (Integer.valueOf(rota) == 90) {
                //Tratamento para Guias com baixa Mobile Carlos 27/02/2019
                listaGuias = rt_PercDao.listarGuiasRt_Guias(guia, serie, codFil, persistencia);

                if (listaGuias == null) {
                    listaGuias = rt_PercDao.listarGuiasGTV(guia, serie, codFil, seqRota, persistencia);
                }

                /**
                 * @TODO validação
                 */
                if (listaGuias == null || listaGuias.getNRed().equals(pool)) {
                    listaGuias = rt_PercDao.listarGuiasCxfGuias(guia, serie, codFil, seqRota, persistencia);
                }
            } else {
                listaGuias = rt_PercDao.listarGuiasTesSaidas(guia, serie, codFil, persistencia);

                if (listaGuias == null) {
                    listaGuias = rt_PercDao.listarGuiasCxfGuias(guia, serie, codFil, seqRota, persistencia);
                }
            }

            if (listaGuias != null) {
                retorno.put("cliOri", listaGuias.getCliOri());
                retorno.put("NRedOri", listaGuias.getNRed());
                retorno.put("OS", listaGuias.getOS());

                Rt_Perc pedido = rt_PercDao.listarGuiasPedido(guia, serie, seqRota, codFil, persistencia);
                if (pedido != null && pedido.getParada() > 0) {
                    retorno.put("parada", pedido.getParada());
                } else if (pedido != null) {
                    retorno.put("parada", pedido.getPedido().toBigInteger().intValue());
                } else {
                    retorno.put("parada", 0);
                }

                /**
                 * @TODO validação if vHrO <> '' then rLbHora1.Caption := vHrO
                 * else
                 */
                retorno.put("hora", listaGuias.getHora1());

                if (serie.equals("CXF")) {
                    retorno.put("cliDst", listaGuias.getCliOri());
                    retorno.put("NRedDst", listaGuias.getNRed());
                } else {
                    retorno.put("cliDst", listaGuias.getCliDst());
                    retorno.put("NRedDst", listaGuias.getNRedDst());
                }

                if (new BigDecimal(listaGuias.getValor()).intValue() <= 0) {
                    Rt_GuiasDao rt_GuiasDao = new Rt_GuiasDao();
                    String valorTotal = rt_GuiasDao.obterValorTotal(seqRota, guia, serie, persistencia);

                    if (valorTotal != null) {
                        retorno.put("valor", valorTotal);
                    } else if (new BigDecimal(valorTotal).intValue() <= 0) {
                        CxFGuiasDao cxfguiasDao = new CxFGuiasDao();
                        valorTotal = cxfguiasDao.obterValorTotal(guia, serie, persistencia);
                        if (valorTotal != null) {
                            retorno.put("valor", valorTotal);
                        } else {
                            retorno.put("valor", "");
                        }
                    } else {
                        retorno.put("valor", "");
                    }
                } else {
                    retorno.put("valor", listaGuias.getValor());
                }

            } else {
                retorno.put("cliOri", "");
                retorno.put("NRedOri", "");
                retorno.put("OS", "");
                retorno.put("cliDst", "");
                retorno.put("NRedDst", "");
                retorno.put("parada", 0);
                retorno.put("valor", "");
                retorno.put("hora", "");
            }
        } catch (Exception e) {
            retorno = new HashMap();
            retorno.put("erro", e.getMessage());
            this.logerro.Grava("Erro: "+e.getMessage(), this.caminho);
        } finally {
            try {
                persistencia.FechaConexao();
            } catch (Exception p) {
                this.logerro.Grava("Fecha Conexao: " + p.getMessage(), this.caminho);
            }
            
            this.logerro.Grava("Resposta: "+gson.toJson(retorno), this.caminho);
        }

        return Response
                .status(Response.Status.OK)
                .type("application/json")
                .entity(gson.toJson(retorno))
                .build();
    }
}
