/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.service;

import br.com.sasw.satwebservice.bradesconovo.dao.BradescoCertificateDao;
import br.com.sasw.satwebservice.bradesconovo.dao.BradescoIntegraDao;
import br.com.sasw.satwebservice.bradesconovo.dao.entity.BradescoCertificateEntity;
import br.com.sasw.satwebservice.bradesconovo.dto.request.ListaPontoAtendimentoRequest;
import br.com.sasw.satwebservice.bradesconovo.utils.BradescoUtils;
import com.google.gson.Gson;
import java.io.UnsupportedEncodingException;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import static br.com.sasw.satwebservice.bradesconovo.utils.BradescoConstants.*;
import java.net.URI;

/**
 *
 * <AUTHOR> Silva
 */
public class ListaPontoAtendimentoService {

    private final BradescoTokenService bradescoTokenService = new BradescoTokenService();
    private final BradescoCertificateDao bradescoCertificateDao = new BradescoCertificateDao();
    private final BradescoIntegraDao bradescoIntegraDao = new BradescoIntegraDao();
    private final Gson gson = new Gson();

    private final static String PONTO_ATENDIMENTO_PERSONALIZADO_PATH = "/conciliacao-integrada/lista-ponto-atendimento/personalizados";
    private final static String PONTO_ATENDIMENTO_MASSIFICADOS_PATH = "/conciliacao-integrada/lista-ponto-atendimento/massificados";
    private final static String PONTO_ATENDIMENTO_COFRE_PATH = "/conciliacao-integrada/lista-ponto-atendimento/cofres";
    private final static String PONTO_ATENDIMENTO_AGENCIAS_PATH = "/conciliacao-integrada/lista-ponto-atendimento/agencias";

    public String getPontoAtendimentoPersonalidados(String empresa, String cnpj, 
            String token, String page, String size, String sort, String query, 
            String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));

            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    PONTO_ATENDIMENTO_PERSONALIZADO_PATH, page, size, sort);
            HttpPost httpPost = new HttpPost(uri);

            String rawRequest = buildRequest(bradescoCertificateEntity.getChaveCoin(ambiente), cnpj);
            httpPost.setEntity(new StringEntity(rawRequest));
            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, PONTO_ATENDIMENTO_PERSONALIZADO_PATH, query, bradescoCertificateEntity.getBancoDeDados(), "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String getPontoAtendimentoMassificados(String empresa, String cnpj, 
            String token, String page, String size, String sort, String query, 
            String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    PONTO_ATENDIMENTO_MASSIFICADOS_PATH, page, size, sort);
            HttpPost httpPost = new HttpPost(uri);

            String rawRequest = buildRequest(bradescoCertificateEntity.
                    getChaveCoin(ambiente), cnpj);
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, PONTO_ATENDIMENTO_MASSIFICADOS_PATH, 
                        query, bradescoCertificateEntity.getBancoDeDados(), "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String getPontoAtendimentoCofres(String empresa, String cnpj, 
            String token, String page, String size, String sort, String query, 
            String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    PONTO_ATENDIMENTO_COFRE_PATH, page, size, sort);
            HttpPost httpPost = new HttpPost(uri);

            String rawRequest = buildRequest(bradescoCertificateEntity.
                    getChaveCoin(ambiente), cnpj);
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, PONTO_ATENDIMENTO_COFRE_PATH, 
                        query, bradescoCertificateEntity.getBancoDeDados(), "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String getPontoAtendimentoAgencias(String empresa, String cnpj, 
            String token, String page, String size, String sort, String query, 
            String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    PONTO_ATENDIMENTO_AGENCIAS_PATH, page, size, sort);
            HttpPost httpPost = new HttpPost(uri);

            String rawRequest = buildRequest(bradescoCertificateEntity.
                    getChaveCoin(ambiente), cnpj);
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, PONTO_ATENDIMENTO_AGENCIAS_PATH, 
                        query, bradescoCertificateEntity.getBancoDeDados(),"POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    private String buildRequest(String senhaConexao, String cnpj) {
        ListaPontoAtendimentoRequest listaPontoAtendimentoRequest = new ListaPontoAtendimentoRequest();
        listaPontoAtendimentoRequest.setCnpjEmpresa(cnpj);
        listaPontoAtendimentoRequest.setCodigoConexaoEmpresa(senhaConexao);
        return gson.toJson(listaPontoAtendimentoRequest);
    }
}
