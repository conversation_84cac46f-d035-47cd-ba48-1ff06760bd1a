/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.dto.request;

/**
 *
 * <AUTHOR>
 */
public class CofreCreditoRequest {
   
    private String cnpjEmpresa;
    private String senhaConexao;
    private String numeroPedido;
    private String cnpjCliente;
    private String numeroSequencialCliente;
    private String cnpjPontoCliente;
    private String agenciaCredito;
    private String contaCredito;
    private String digitoContaCredito;
    private String dataOperacao;
    private String valorOperacao;
    private String numeroCofreInteligenteOrigem;
    private String numeroOCT;
    private String nomeRemetenteOCT;

    // Getters e Setters
    public String getCnpjEmpresa() {
        return cnpjEmpresa;
    }

    public void setCnpjEmpresa(String cnpjEmpresa) {
        this.cnpjEmpresa = cnpjEmpresa;
    }

    public String getSenhaConexao() {
        return senhaConexao;
    }

    public void setSenhaConexao(String senhaConexao) {
        this.senhaConexao = senhaConexao;
    }

    public String getNumeroPedido() {
        return numeroPedido;
    }

    public void setNumeroPedido(String numeroPedido) {
        this.numeroPedido = numeroPedido;
    }

    public String getCnpjCliente() {
        return cnpjCliente;
    }

    public void setCnpjCliente(String cnpjCliente) {
        this.cnpjCliente = cnpjCliente;
    }

    public String getNumeroSequencialCliente() {
        return numeroSequencialCliente;
    }

    public void setNumeroSequencialCliente(String numeroSequencialCliente) {
        this.numeroSequencialCliente = numeroSequencialCliente;
    }

    public String getCnpjPontoCliente() {
        return cnpjPontoCliente;
    }

    public void setCnpjPontoCliente(String cnpjPontoCliente) {
        this.cnpjPontoCliente = cnpjPontoCliente;
    }

    public String getAgenciaCredito() {
        return agenciaCredito;
    }

    public void setAgenciaCredito(String agenciaCredito) {
        this.agenciaCredito = agenciaCredito;
    }

    public String getContaCredito() {
        return contaCredito;
    }

    public void setContaCredito(String contaCredito) {
        this.contaCredito = contaCredito;
    }

    public String getDigitoContaCredito() {
        return digitoContaCredito;
    }

    public void setDigitoContaCredito(String digitoContaCredito) {
        this.digitoContaCredito = digitoContaCredito;
    }

    public String getDataOperacao() {
        return dataOperacao;
    }

    public void setDataOperacao(String dataOperacao) {
        this.dataOperacao = dataOperacao;
    }

    public String getValorOperacao() {
        return valorOperacao;
    }

    public void setValorOperacao(String valorOperacao) {
        this.valorOperacao = valorOperacao;
    }

    public String getNumeroCofreInteligenteOrigem() {
        return numeroCofreInteligenteOrigem;
    }

    public void setNumeroCofreInteligenteOrigem(String numeroCofreInteligenteOrigem) {
        this.numeroCofreInteligenteOrigem = numeroCofreInteligenteOrigem;
    }

    public String getNumeroOCT() {
        return numeroOCT;
    }

    public void setNumeroOCT(String numeroOCT) {
        this.numeroOCT = numeroOCT;
    }

    public String getNomeRemetenteOCT() {
        return nomeRemetenteOCT;
    }

    public void setNomeRemetenteOCT(String nomeRemetenteOCT) {
        this.nomeRemetenteOCT = nomeRemetenteOCT;
    }
}
