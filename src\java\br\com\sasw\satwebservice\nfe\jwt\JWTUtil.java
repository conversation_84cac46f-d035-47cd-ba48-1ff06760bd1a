/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.nfe.jwt;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 *
 * <AUTHOR>
 */
public class JWTUtil {

    private static String key = "SECRET_TOKEN";

    public static final String TOKEN_HEADER = "Authentication";

    public static String create(String subject) {
        try {
            return Jwts.builder()
                    .setSubject(subject)
                    .signWith(SignatureAlgorithm.HS512, key)
                    .setExpiration(new SimpleDateFormat("dd-MM-yyyy HH:mm:ssZ").parse("08-06-2020 19:00:00-0300"))
                    .compact();
        } catch (ParseException ex) {
            ex.printStackTrace();
        }
        return null;
    }

    public static Jws<Claims> decode(String token){
        return Jwts.parser().setSigningKey(key).parseClaimsJws(token);
    }
}
