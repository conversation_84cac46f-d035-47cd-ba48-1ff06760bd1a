/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.integracao.digibee.beans.resposta;

import br.com.sasw.satwebservice.integracao.digibee.beans.Pedido;

/**
 *
 * <AUTHOR>
 */
public class PedidoResposta extends Pedido{
    
    private String numero;
    private String resposta;
  
    private String recolhimentoData;
    private String recolhimentoHora;
    private String recolhimentoGtv;
    private String recolhimentoValor;
    
    private String entregaData;
    private String entregaHora;
    private String entregaGtv;
    private String entregaValor;

    public String getNumero() {
        return numero;
    }

    public void setNumero(String numero) {
        this.numero = numero;
    }

    public String getResposta() {
        return resposta;
    }

    public void setResposta(String resposta) {
        this.resposta = resposta;
    }
    
    public String getRecolhimentoData() {
        return recolhimentoData;
    }

    public void setRecolhimentoData(String recolhimentoData) {
        this.recolhimentoData = recolhimentoData;
    }

    public String getRecolhimentoHora() {
        return recolhimentoHora;
    }

    public void setRecolhimentoHora(String recolhimentoHora) {
        this.recolhimentoHora = recolhimentoHora;
    }

    public String getRecolhimentoGtv() {
        return recolhimentoGtv;
    }

    public void setRecolhimentoGtv(String recolhimentoGtv) {
        this.recolhimentoGtv = recolhimentoGtv;
    }

    public String getRecolhimentoValor() {
        return recolhimentoValor;
    }

    public void setRecolhimentoValor(String recolhimentoValor) {
        this.recolhimentoValor = recolhimentoValor;
    }

    public String getEntregaData() {
        return entregaData;
    }

    public void setEntregaData(String entregaData) {
        this.entregaData = entregaData;
    }

    public String getEntregaHora() {
        return entregaHora;
    }

    public void setEntregaHora(String entregaHora) {
        this.entregaHora = entregaHora;
    }

    public String getEntregaGtv() {
        return entregaGtv;
    }

    public void setEntregaGtv(String entregaGtv) {
        this.entregaGtv = entregaGtv;
    }

    public String getEntregaValor() {
        return entregaValor;
    }

    public void setEntregaValor(String entregaValor) {
        this.entregaValor = entregaValor;
    }    
}
