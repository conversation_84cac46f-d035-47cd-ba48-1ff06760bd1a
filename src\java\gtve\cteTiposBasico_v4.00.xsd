<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by <EMAIL> (PROCERGS) -->
<xs:schema xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/cte" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema_v1.01.xsd"/>
	<xs:include schemaLocation="tiposGeralCTe_v4.00.xsd"/>
	<xs:simpleType name="TModTranspGTVe">
		<xs:annotation>
			<xs:documentation> Tipo Modal transporte GTVe</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="01"/>
			<xs:enumeration value="06"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TFinGTVe">
		<xs:annotation>
			<xs:documentation>Tipo Finalidade da GTV-e</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="TGTVe">
		<xs:annotation>
			<xs:documentation>Tipo Guia de Transporte de Valores Eletrônica (Modelo 64)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infCte">
				<xs:annotation>
					<xs:documentation>Informações do CT-e do tipo GTV-e</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ide">
							<xs:annotation>
								<xs:documentation>Identificação da GTV-e </xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="cUF" type="TCodUfIBGE">
										<xs:annotation>
											<xs:documentation>Código da UF do emitente da GTV-e.</xs:documentation>
											<xs:documentation>Utilizar a Tabela do IBGE.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="cCT">
										<xs:annotation>
											<xs:documentation>Código numérico que compõe a Chave de Acesso. </xs:documentation>
											<xs:documentation>Número aleatório gerado pelo emitente para cada CT-e, com o objetivo de evitar acessos indevidos ao documento.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{8}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="CFOP" type="TCfop">
										<xs:annotation>
											<xs:documentation>Código Fiscal de Operações e Prestações</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="natOp">
										<xs:annotation>
											<xs:documentation>Natureza da Operação</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="mod" type="TModGTVe">
										<xs:annotation>
											<xs:documentation>Modelo do documento fiscal</xs:documentation>
											<xs:documentation>Utilizar o código 64 para identificação do CT-e Guia de Transporte de Valores</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="serie">
										<xs:annotation>
											<xs:documentation>Série da GTV-e</xs:documentation>
											<xs:documentation>Preencher com "0" no caso de série única</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TSerie"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="nCT" type="TNF">
										<xs:annotation>
											<xs:documentation>Número da GTV-e</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="dhEmi">
										<xs:annotation>
											<xs:documentation>Data e hora de emissão da GTV-e</xs:documentation>
											<xs:documentation>Formato AAAA-MM-DDTHH:MM:DD TZD</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TDateTimeUTC"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpImp">
										<xs:annotation>
											<xs:documentation>Formato de impressão do DACTE</xs:documentation>
											<xs:documentation>Preencher com: 1 - Retrato; 2 - Paisagem.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpEmis">
										<xs:annotation>
											<xs:documentation>Forma de emissão da GTV-e</xs:documentation>
											<xs:documentation>Preencher com:
1 - Normal;
 2- Contingencia offline 
7 - Autorização pela SVC-RS;
 8 - Autorização pela SVC-SP</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="7"/>
												<xs:enumeration value="8"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="cDV">
										<xs:annotation>
											<xs:documentation>Digito Verificador da chave de acesso da GTV-e</xs:documentation>
											<xs:documentation>Informar o dígito  de controle da chave de acesso do CT-e, que deve ser calculado com a aplicação do algoritmo módulo 11 (base 2,9) da chave de acesso. </xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{1}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpAmb" type="TAmb">
										<xs:annotation>
											<xs:documentation>Tipo do Ambiente</xs:documentation>
											<xs:documentation>Preencher com:1 - Produção; 2 - Homologação</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="tpCTe">
										<xs:annotation>
											<xs:documentation>Tipo da GTV-e</xs:documentation>
											<xs:documentation>Preencher com:
 4 - GTV-e</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TFinGTVe"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="verProc">
										<xs:annotation>
											<xs:documentation>Versão do processo de emissão</xs:documentation>
											<xs:documentation>Iinformar a versão do aplicativo emissor de CT-e.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="20"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="cMunEnv" type="TCodMunIBGE">
										<xs:annotation>
											<xs:documentation>Código do Município de envio da GTV-e (de onde o documento foi transmitido)</xs:documentation>
											<xs:documentation>Utilizar a tabela do IBGE. Informar 9999999 para as operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="xMunEnv">
										<xs:annotation>
											<xs:documentation>Nome do Município de envio da GTV-e (de onde o documento foi transmitido)</xs:documentation>
											<xs:documentation>Informar PAIS/Municipio para as operações com o exterior.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="2"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="UFEnv" type="TUf">
										<xs:annotation>
											<xs:documentation>Sigla da UF de envio da GTV-e (de onde o documento foi transmitido)</xs:documentation>
											<xs:documentation>Informar 'EX' para operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="modal">
										<xs:annotation>
											<xs:documentation>Modal da GTV-e</xs:documentation>
											<xs:documentation>Preencher com:
01-Rodoviário 
06-Multimodal</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TModTranspGTVe">
												<xs:enumeration value="01"/>
												<xs:enumeration value="06"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpServ">
										<xs:annotation>
											<xs:documentation>Tipo do Serviço</xs:documentation>
											<xs:documentation>Preencher com: 

9 - GTV</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="9"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="indIEToma">
										<xs:annotation>
											<xs:documentation>Indicador da IE do tomador:
1 – Contribuinte ICMS;
2 – Contribuinte isento de inscrição;
9 – Não Contribuinte</xs:documentation>
											<xs:documentation>Aplica-se ao tomador que for indicado no toma3 ou toma4</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="9"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="dhSaidaOrig">
										<xs:annotation>
											<xs:documentation>Data e hora de saida da origem</xs:documentation>
											<xs:documentation>Formato AAAA-MM-DDTHH:MM:DD TZD</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TDateTimeUTC"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="dhChegadaDest">
										<xs:annotation>
											<xs:documentation>Data e hora de chegada no destino</xs:documentation>
											<xs:documentation>Formato AAAA-MM-DDTHH:MM:DD TZD</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TDateTimeUTC"/>
										</xs:simpleType>
									</xs:element>
									<xs:choice>
										<xs:element name="toma">
											<xs:annotation>
												<xs:documentation>Indicador do "papel" do tomador do serviço no GT-e</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="toma">
														<xs:annotation>
															<xs:documentation>Tomador do Serviço</xs:documentation>
															<xs:documentation>Preencher com:
															0-Remetente;
															1-Destinatário
															</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:whiteSpace value="preserve"/>
																<xs:enumeration value="0"/>
																<xs:enumeration value="1"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="tomaTerceiro">
											<xs:annotation>
												<xs:documentation>Indicador do "papel" do tomador do serviço no CTV-e</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="toma">
														<xs:annotation>
															<xs:documentation>Tomador do Serviço</xs:documentation>
															<xs:documentation>Preencher com: 
															4 - Outros
															Obs: Informar os dados cadastrais do tomador do serviço</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:whiteSpace value="preserve"/>
																<xs:enumeration value="4"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:choice>
														<xs:element name="CNPJ" type="TCnpjOpc">
															<xs:annotation>
																<xs:documentation>Número do CNPJ</xs:documentation>
																<xs:documentation>Em caso de empresa não estabelecida no Brasil, será informado o CNPJ com zeros.															
Informar os zeros não significativos.</xs:documentation>
															</xs:annotation>
														</xs:element>
														<xs:element name="CPF" type="TCpf">
															<xs:annotation>
																<xs:documentation>Número do CPF</xs:documentation>
																<xs:documentation>Informar os zeros não significativos.</xs:documentation>
															</xs:annotation>
														</xs:element>
													</xs:choice>
													<xs:element name="IE" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Inscrição Estadual</xs:documentation>
															<xs:documentation>Informar a IE do tomador ou ISENTO se tomador é contribuinte do ICMS isento de inscrição no cadastro de contribuintes do ICMS. Caso o tomador não seja contribuinte do ICMS não informar o conteúdo.</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TIeDest"/>
														</xs:simpleType>
													</xs:element>
													<xs:element name="xNome">
														<xs:annotation>
															<xs:documentation>Razão Social ou Nome</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:maxLength value="60"/>
																<xs:minLength value="2"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="xFant" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Nome Fantasia</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:maxLength value="60"/>
																<xs:minLength value="2"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="fone" type="TFone" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Telefone</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="enderToma" type="TEndereco">
														<xs:annotation>
															<xs:documentation>Dados do endereço</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="email" type="TEmail" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Endereço de email</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:choice>
									<xs:sequence minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informar apenas
para tpEmis diferente de 1</xs:documentation>
										</xs:annotation>
										<xs:element name="dhCont" type="TDateTimeUTC">
											<xs:annotation>
												<xs:documentation>Data e Hora da entrada em contingência</xs:documentation>
												<xs:documentation>Informar a data e hora no formato AAAA-MM-DDTHH:MM:SS</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="xJust">
											<xs:annotation>
												<xs:documentation>Justificativa da entrada em contingência</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TString">
													<xs:minLength value="15"/>
													<xs:maxLength value="256"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="compl" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Dados complementares da GTV-e para fins operacionais ou comerciais</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="xCaracAd" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Característica adicional do transporte</xs:documentation>
											<xs:documentation>Texto livre:
REENTREGA; DEVOLUÇÃO; REFATURAMENTO; etc</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="15"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xCaracSer" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Característica adicional do serviço</xs:documentation>
											<xs:documentation>Texto livre:
											ENTREGA EXPRESSA; LOGÍSTICA REVERSA; CONVENCIONAL; EMERGENCIAL; etc</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="30"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xEmi" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Funcionário emissor da GTV-e</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="20"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xObs" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Observações Gerais</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="2000"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="ObsCont" minOccurs="0" maxOccurs="10">
										<xs:annotation>
											<xs:documentation>Campo de uso livre do contribuinte</xs:documentation>
											<xs:documentation>Informar o nome do campo no atributo xCampo e o conteúdo do campo no XTexto</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="xTexto">
													<xs:annotation>
														<xs:documentation>Conteúdo do campo</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="160"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
											<xs:attribute name="xCampo" use="required">
												<xs:annotation>
													<xs:documentation>Identificação do campo</xs:documentation>
												</xs:annotation>
												<xs:simpleType>
													<xs:restriction base="TString">
														<xs:minLength value="1"/>
														<xs:maxLength value="20"/>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
									<xs:element name="ObsFisco" minOccurs="0" maxOccurs="10">
										<xs:annotation>
											<xs:documentation>Campo de uso livre do contribuinte</xs:documentation>
											<xs:documentation>Informar o nome do campo no atributo xCampo e o conteúdo do campo no XTexto</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="xTexto">
													<xs:annotation>
														<xs:documentation>Conteúdo do campo</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
											<xs:attribute name="xCampo" use="required">
												<xs:annotation>
													<xs:documentation>Identificação do campo</xs:documentation>
												</xs:annotation>
												<xs:simpleType>
													<xs:restriction base="TString">
														<xs:minLength value="1"/>
														<xs:maxLength value="20"/>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="emit">
							<xs:annotation>
								<xs:documentation>Identificação do Emitente da GTV-e </xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="CNPJ" type="TCnpj">
										<xs:annotation>
											<xs:documentation>CNPJ do emitente</xs:documentation>
											<xs:documentation>Informar zeros não significativos</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="IE">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual do Emitente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIe"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="IEST" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual do Substituto Tributário</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIe"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão social ou Nome do emitente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xFant" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Nome fantasia</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="enderEmit" type="TEndeEmi">
										<xs:annotation>
											<xs:documentation>Endereço do emitente</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="rem">
							<xs:annotation>
								<xs:documentation>Informações do Remetente </xs:documentation>
								<xs:documentation>Poderá não ser informado para os CT-e de redespacho intermediário e serviço vinculado a multimodal. Nos demais casos deverá sempre ser informado.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpjOpc">
											<xs:annotation>
												<xs:documentation>Número do CNPJ</xs:documentation>
												<xs:documentation>Em caso de empresa não estabelecida no Brasil, será informado o CNPJ com zeros.
												Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>Número do CPF</xs:documentation>
												<xs:documentation>Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
									<xs:element name="IE" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual</xs:documentation>
											<xs:documentation>Informar a IE do remetente ou ISENTO se remetente é contribuinte do ICMS isento de inscrição no cadastro de contribuintes do ICMS. Caso o remetente não seja contribuinte do ICMS não informar a tag.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIeDest"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão social ou nome do remetente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xFant" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Nome fantasia</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="fone" type="TFone" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Telefone</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="enderReme" type="TEndereco">
										<xs:annotation>
											<xs:documentation>Dados do endereço</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="email" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Endereço de email</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TEmail"/>
										</xs:simpleType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="dest">
							<xs:annotation>
								<xs:documentation>Informações do Destinatário </xs:documentation>
								<xs:documentation>Poderá não ser informado para os CT-e de redespacho intermediário e serviço vinculado a multimodal. Nos demais casos deverá sempre ser informado.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpjOpc">
											<xs:annotation>
												<xs:documentation>Número do CNPJ</xs:documentation>
												<xs:documentation>Em caso de empresa não estabelecida no Brasil, será informado o CNPJ com zeros.
												Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>Número do CPF</xs:documentation>
												<xs:documentation>Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
									<xs:element name="IE" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual</xs:documentation>
											<xs:documentation>Informar a IE do destinatário ou ISENTO se destinatário é contribuinte do ICMS isento de inscrição no cadastro de contribuintes do ICMS. Caso o destinatário não seja contribuinte do ICMS não informar o conteúdo.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIeDest"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão Social ou Nome do destinatário</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="fone" type="TFone" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Telefone</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="ISUF" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição na SUFRAMA</xs:documentation>
											<xs:documentation>(Obrigatório nas operações com as áreas com benefícios de incentivos fiscais sob controle da SUFRAMA)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{8,9}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="enderDest" type="TEndereco">
										<xs:annotation>
											<xs:documentation>Dados do endereço</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="email" type="TEmail" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Endereço de email</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="origem" type="TEndeEmi" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações do endereço da origem do serviço</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="destino" type="TEndeEmi" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações do endereço do destino do serviço</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="detGTV">
							<xs:annotation>
								<xs:documentation>Grupo de informações detalhadas da GTV-e </xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="infEspecie" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation>Informações das Espécies transportadas</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="tpEspecie">
													<xs:annotation>
														<xs:documentation>Tipo da Espécie</xs:documentation>
														<xs:documentation>1 - Cédula
2 - Cheque
3 - Moeda
4 - Outros</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:enumeration value="1"/>
															<xs:enumeration value="2"/>
															<xs:enumeration value="3"/>
															<xs:enumeration value="4"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="vEspecie" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor Transportada em Espécie indicada</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="tpNumerario">
													<xs:annotation>
														<xs:documentation>Nacionalidade do Numerário</xs:documentation>
														<xs:documentation>1 - Nacional
2 - Estrangeiro</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:enumeration value="1"/>
															<xs:enumeration value="2"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="xMoedaEstr" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Nome da Moeda</xs:documentation>
														<xs:documentation>Informar somente se tipo de numerário for 2 - Estrangeiro</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:maxLength value="60"/>
															<xs:minLength value="2"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:element name="qCarga" type="TDec_1104">
										<xs:annotation>
											<xs:documentation>Quantidade de volumes/malotes</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="infVeiculo" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation>Grupo de informações dos veículos utilizados no transporte de valores</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="placa" type="TPlaca">
													<xs:annotation>
														<xs:documentation>Placa do veículo </xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="UF" type="TUf" minOccurs="0">
													<xs:annotation>
														<xs:documentation>UF em que veículo está licenciado</xs:documentation>
														<xs:documentation>Sigla da UF de licenciamento do veículo.</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="RNTRC" minOccurs="0">
													<xs:annotation>
														<xs:documentation>RNTRC do transportador</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="[0-9]{8}|ISENTO"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="autXML" minOccurs="0" maxOccurs="10">
							<xs:annotation>
								<xs:documentation>Autorizados para download do XML do DF-e</xs:documentation>
								<xs:documentation>Informar CNPJ ou CPF. Preencher os zeros não significativos.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpj">
											<xs:annotation>
												<xs:documentation>CNPJ do autorizado</xs:documentation>
												<xs:documentation>Informar zeros não significativos</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>CPF do autorizado</xs:documentation>
												<xs:documentation>Informar zeros não significativos</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="infRespTec" type="TRespTec" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações do Responsável Técnico pela emissão do DF-e</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="versao" use="required">
						<xs:annotation>
							<xs:documentation>Versão do leiaute</xs:documentation>
							<xs:documentation>Ex: "4.00"</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="TVerCTe"/>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="Id" use="required">
						<xs:annotation>
							<xs:documentation>Identificador da tag a ser assinada</xs:documentation>
							<xs:documentation>Informar a chave de acesso do CT-e OS e precedida do literal "CTe"</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:ID">
								<xs:pattern value="CTe[0-9]{44}"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="infCTeSupl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Informações suplementares da GTV-e</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="qrCodCTe">
							<xs:annotation>
								<xs:documentation>Texto com o QR-Code impresso no DACTE</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:minLength value="50"/>
									<xs:maxLength value="1000"/>
									<xs:pattern value="((HTTPS?|https?)://.*\?chCTe=[0-9]{44}&amp;tpAmb=[1-2](&amp;sign=[!-ÿ]{1}[ -ÿ]{0,}[!-ÿ]{1}|[!-ÿ]{1})?)"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature"/>
		</xs:sequence>
		<xs:attribute name="versao" use="required">
			<xs:annotation>
				<xs:documentation>Versão do leiaute</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="TVerCTe"/>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="TProtCTe">
		<xs:annotation>
			<xs:documentation>Tipo Protocolo de status resultado do processamento da CT-e</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infProt">
				<xs:annotation>
					<xs:documentation>Dados do protocolo de status</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="tpAmb" type="TAmb">
							<xs:annotation>
								<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="verAplic" type="TVerAplic">
							<xs:annotation>
								<xs:documentation>Versão do Aplicativo que processou o CT-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="chCTe" type="TChDFe">
							<xs:annotation>
								<xs:documentation>Chaves de acesso da CT-e, </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="dhRecbto" type="TDateTimeUTC">
							<xs:annotation>
								<xs:documentation>Data e hora de processamento, no formato AAAA-MM-DDTHH:MM:SS TZD. </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nProt" type="TProt" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Número do Protocolo de Status do CT-e. </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="digVal" type="ds:DigestValueType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Digest Value da CT-e processado. Utilizado para conferir a integridade do CT-e original.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cStat">
							<xs:annotation>
								<xs:documentation>Código do status do CT-e.</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TStat"/>
							</xs:simpleType>
						</xs:element>
						<xs:element name="xMotivo" type="TMotivo">
							<xs:annotation>
								<xs:documentation>Descrição literal do status do CT-e.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Id" type="xs:ID" use="optional"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="infFisco" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Mensagem do Fisco</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="cMsg">
							<xs:annotation>
								<xs:documentation>Código do status da mensagem do fisco</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TStat"/>
							</xs:simpleType>
						</xs:element>
						<xs:element name="xMsg" type="TMotivo">
							<xs:annotation>
								<xs:documentation>Mensagem do Fisco</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="versao" use="required">
			<xs:simpleType>
				<xs:restriction base="TVerCTe"/>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="TProtCTeOS">
		<xs:annotation>
			<xs:documentation>Tipo Protocolo de status resultado do processamento do CT-e OS (Modelo 67)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infProt">
				<xs:annotation>
					<xs:documentation>Dados do protocolo de status</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="tpAmb" type="TAmb">
							<xs:annotation>
								<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="verAplic" type="TVerAplic">
							<xs:annotation>
								<xs:documentation>Versão do Aplicativo que processou o CT-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="chCTe" type="TChDFe">
							<xs:annotation>
								<xs:documentation>Chaves de acesso da CT-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="dhRecbto" type="TDateTimeUTC">
							<xs:annotation>
								<xs:documentation>Data e hora de processamento, no formato AAAA-MM-DDTHH:MM:SS TZD. </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nProt" type="TProt" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Número do Protocolo de Status do CT-e.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="digVal" type="ds:DigestValueType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Digest Value da CT-e processado. Utilizado para conferir a integridade do CT-e original.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cStat">
							<xs:annotation>
								<xs:documentation>Código do status do CT-e.</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TStat"/>
							</xs:simpleType>
						</xs:element>
						<xs:element name="xMotivo" type="TMotivo">
							<xs:annotation>
								<xs:documentation>Descrição literal do status do CT-e.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Id" type="xs:ID" use="optional"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="infFisco" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Mensagem do Fisco</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="cMsg">
							<xs:annotation>
								<xs:documentation>Código do status da mensagem do fisco</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TStat"/>
							</xs:simpleType>
						</xs:element>
						<xs:element name="xMsg" type="TMotivo">
							<xs:annotation>
								<xs:documentation>Mensagem do Fisco</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="versao" use="required">
			<xs:simpleType>
				<xs:restriction base="TVerCTe"/>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="TProtGTVe">
		<xs:annotation>
			<xs:documentation>Tipo Protocolo de status resultado do processamento da GTV-e (Modelo 64)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infProt">
				<xs:annotation>
					<xs:documentation>Dados do protocolo de status</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="tpAmb" type="TAmb">
							<xs:annotation>
								<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="verAplic" type="TVerAplic">
							<xs:annotation>
								<xs:documentation>Versão do Aplicativo que processou a GTV-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="chCTe" type="TChDFe">
							<xs:annotation>
								<xs:documentation>Chaves de acesso da CT-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="dhRecbto" type="TDateTimeUTC">
							<xs:annotation>
								<xs:documentation>Data e hora de processamento, no formato AAAA-MM-DDTHH:MM:SS TZD. </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nProt" type="TProt" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Número do Protocolo de Status da GTV-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="digVal" type="ds:DigestValueType" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Digest Value da GTV-e processado. Utilizado para conferir a integridade da GTV-e original.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="cStat">
							<xs:annotation>
								<xs:documentation>Código do status da GTV-e.</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TStat"/>
							</xs:simpleType>
						</xs:element>
						<xs:element name="xMotivo" type="TMotivo">
							<xs:annotation>
								<xs:documentation>Descrição literal do status da GTV-e.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="Id" type="xs:ID" use="optional"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="infFisco" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Mensagem do Fisco</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="cMsg">
							<xs:annotation>
								<xs:documentation>Código do status da mensagem do fisco</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TStat"/>
							</xs:simpleType>
						</xs:element>
						<xs:element name="xMsg" type="TMotivo">
							<xs:annotation>
								<xs:documentation>Mensagem do Fisco</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="versao" use="required">
			<xs:simpleType>
				<xs:restriction base="TVerCTe"/>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="TRetCTe">
		<xs:annotation>
			<xs:documentation>Tipo Retorno do Pedido de Autorização de CT-e (Modelo 57)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cUF" type="TCodUfIBGE">
				<xs:annotation>
					<xs:documentation>Identificação da UF</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="verAplic" type="TVerAplic">
				<xs:annotation>
					<xs:documentation>Versão do Aplicativo que processou a CT-e</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cStat" type="TStat">
				<xs:annotation>
					<xs:documentation>código do status do retorno da consulta.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMotivo" type="TMotivo">
				<xs:annotation>
					<xs:documentation>Descrição literal do status do do retorno da consulta.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="protCTe" type="TProtCTe" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Reposta ao processamento do CT-e</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerCTe" use="required"/>
	</xs:complexType>
	<xs:complexType name="TRetGTVe">
		<xs:annotation>
			<xs:documentation>Tipo Retorno do Pedido de Autorização de GTV-e (Modelo 64)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cUF" type="TCodUfIBGE">
				<xs:annotation>
					<xs:documentation>Identificação da UF</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="verAplic" type="TVerAplic">
				<xs:annotation>
					<xs:documentation>Versão do Aplicativo que processou a GTV-e</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cStat" type="TStat">
				<xs:annotation>
					<xs:documentation>código do status do retorno da consulta.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMotivo" type="TMotivo">
				<xs:annotation>
					<xs:documentation>Descrição literal do status do do retorno da consulta.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="protCTe" type="TProtGTVe" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Reposta ao processamento do CT-e</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerCTe" use="required"/>
	</xs:complexType>
	<xs:complexType name="TRetCTeOS">
		<xs:annotation>
			<xs:documentation>Tipo Retorno do Pedido de Autorização de CT-e OS (Modelo 67)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpAmb" type="TAmb">
				<xs:annotation>
					<xs:documentation>Identificação do Ambiente:
1 - Produção
2 - Homologação</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cUF" type="TCodUfIBGE">
				<xs:annotation>
					<xs:documentation>Identificação da UF</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="verAplic" type="TVerAplic">
				<xs:annotation>
					<xs:documentation>Versão do Aplicativo que processou a CT-e</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cStat" type="TStat">
				<xs:annotation>
					<xs:documentation>código do status do retorno da consulta.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMotivo" type="TMotivo">
				<xs:annotation>
					<xs:documentation>Descrição literal do status do do retorno da consulta.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="protCTe" type="TProtCTeOS" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Reposta ao processamento do CT-e</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="versao" type="TVerCTe" use="required"/>
	</xs:complexType>
	<xs:complexType name="TCTe">
		<xs:annotation>
			<xs:documentation>Tipo Conhecimento de Transporte Eletrônico (Modelo 57)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infCte">
				<xs:annotation>
					<xs:documentation>Informações do CT-e</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ide">
							<xs:annotation>
								<xs:documentation>Identificação do CT-e</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="cUF" type="TCodUfIBGE">
										<xs:annotation>
											<xs:documentation>Código da UF do emitente do CT-e.</xs:documentation>
											<xs:documentation>Utilizar a Tabela do IBGE.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="cCT">
										<xs:annotation>
											<xs:documentation>Código numérico que compõe a Chave de Acesso. </xs:documentation>
											<xs:documentation>Número aleatório gerado pelo emitente para cada CT-e, com o objetivo de evitar acessos indevidos ao documento.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{8}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="CFOP" type="TCfop">
										<xs:annotation>
											<xs:documentation>Código Fiscal de Operações e Prestações</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="natOp">
										<xs:annotation>
											<xs:documentation>Natureza da Operação</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="mod" type="TModCT">
										<xs:annotation>
											<xs:documentation>Modelo do documento fiscal</xs:documentation>
											<xs:documentation>Utilizar o código 57 para identificação do CT-e, emitido em substituição aos modelos de conhecimentos em papel.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="serie">
										<xs:annotation>
											<xs:documentation>Série do CT-e</xs:documentation>
											<xs:documentation>Preencher com "0" no caso de série única</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TSerie"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="nCT" type="TNF">
										<xs:annotation>
											<xs:documentation>Número do CT-e</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="dhEmi">
										<xs:annotation>
											<xs:documentation>Data e hora de emissão do CT-e</xs:documentation>
											<xs:documentation>Formato AAAA-MM-DDTHH:MM:DD TZD</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TDateTimeUTC"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpImp">
										<xs:annotation>
											<xs:documentation>Formato de impressão do DACTE</xs:documentation>
											<xs:documentation>Preencher com: 1 - Retrato; 2 - Paisagem.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpEmis">
										<xs:annotation>
											<xs:documentation>Forma de emissão do CT-e </xs:documentation>
											<xs:documentation>Preencher com:
1 - Normal;
 3-Regime Especial NFF;  4-EPEC pela SVC; 5 - Contingência FSDA;
	7 - Autorização pela SVC-RS;
  8 - Autorização pela SVC-SP</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="3"/>
												<xs:enumeration value="4"/>
												<xs:enumeration value="5"/>
												<xs:enumeration value="7"/>
												<xs:enumeration value="8"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="cDV">
										<xs:annotation>
											<xs:documentation>Digito Verificador da chave de acesso do CT-e</xs:documentation>
											<xs:documentation>Informar o dígito  de controle da chave de acesso do CT-e, que deve ser calculado com a aplicação do algoritmo módulo 11 (base 2,9) da chave de acesso. </xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{1}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpAmb" type="TAmb">
										<xs:annotation>
											<xs:documentation>Tipo do Ambiente</xs:documentation>
											<xs:documentation>Preencher com:1 - Produção; 2 - Homologação.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="tpCTe" type="TFinCTe">
										<xs:annotation>
											<xs:documentation>Tipo do CT-e</xs:documentation>
											<xs:documentation>Preencher com:
	0 - CT-e Normal;
 1 - CT-e de Complemento de Valores;
 3 - CT-e de Substituição</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="procEmi" type="TProcEmi">
										<xs:annotation>
											<xs:documentation>Identificador do processo de emissão do CT-e</xs:documentation>
											<xs:documentation>Preencher com: 
											0 - emissão de CT-e com aplicativo do contribuinte;
											3- emissão CT-e pelo contribuinte com aplicativo fornecido pelo SEBRAE.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="verProc">
										<xs:annotation>
											<xs:documentation>Versão do processo de emissão</xs:documentation>
											<xs:documentation>Iinformar a versão do aplicativo emissor de CT-e.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="20"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="indGlobalizado" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Indicador de CT-e Globalizado</xs:documentation>
											<xs:documentation>Informar valor 1 quando for Globalizado e não informar a tag quando não tratar de CT-e Globalizado</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:enumeration value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="cMunEnv" type="TCodMunIBGE">
										<xs:annotation>
											<xs:documentation>Código do Município de envio do CT-e (de onde o documento foi transmitido)</xs:documentation>
											<xs:documentation>Utilizar a tabela do IBGE. Informar 9999999 para as operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="xMunEnv">
										<xs:annotation>
											<xs:documentation>Nome do Município de envio do CT-e (de onde o documento foi transmitido)</xs:documentation>
											<xs:documentation>Informar PAIS/Municipio para as operações com o exterior.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="2"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="UFEnv" type="TUf">
										<xs:annotation>
											<xs:documentation>Sigla da UF de envio do CT-e (de onde o documento foi transmitido)</xs:documentation>
											<xs:documentation>Informar 'EX' para operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="modal" type="TModTransp">
										<xs:annotation>
											<xs:documentation>Modal</xs:documentation>
											<xs:documentation>Preencher com:01-Rodoviário;
02-Aéreo;03-Aquaviário;04-Ferroviário;05-Dutoviário;06-Multimodal;</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="tpServ">
										<xs:annotation>
											<xs:documentation>Tipo do Serviço</xs:documentation>
											<xs:documentation>Preencher com: 
0 - Normal;1 - Subcontratação;
2 - Redespacho;3 - Redespacho Intermediário; 4 - Serviço Vinculado a Multimodal</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="3"/>
												<xs:enumeration value="4"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="cMunIni" type="TCodMunIBGE">
										<xs:annotation>
											<xs:documentation>Código do Município de início da prestação</xs:documentation>
											<xs:documentation>Utilizar a tabela do IBGE. Informar 9999999 para operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="xMunIni">
										<xs:annotation>
											<xs:documentation>Nome do Município do início da prestação</xs:documentation>
											<xs:documentation>Informar 'EXTERIOR' para operações com o exterior.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="2"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="UFIni" type="TUf">
										<xs:annotation>
											<xs:documentation>UF do início da prestação</xs:documentation>
											<xs:documentation>Informar 'EX' para operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="cMunFim" type="TCodMunIBGE">
										<xs:annotation>
											<xs:documentation>Código do Município de término da prestação</xs:documentation>
											<xs:documentation>Utilizar a tabela do IBGE. Informar 9999999 para operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="xMunFim">
										<xs:annotation>
											<xs:documentation>Nome do Município do término da prestação</xs:documentation>
											<xs:documentation>Informar 'EXTERIOR' para operações com o exterior.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="2"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="UFFim" type="TUf">
										<xs:annotation>
											<xs:documentation>UF do término da prestação</xs:documentation>
											<xs:documentation>Informar 'EX' para operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="retira">
										<xs:annotation>
											<xs:documentation>Indicador se o Recebedor retira no Aeroporto, Filial, Porto ou Estação de Destino?</xs:documentation>
											<xs:documentation>Preencher com: 0 - sim; 1 - não</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="0"/>
												<xs:enumeration value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xDetRetira" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Detalhes do retira</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="160"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="indIEToma">
										<xs:annotation>
											<xs:documentation>Indicador do papel do tomador na prestação do serviço:
1 – Contribuinte ICMS;
2 – Contribuinte isento de inscrição;
9 – Não Contribuinte</xs:documentation>
											<xs:documentation>Aplica-se ao tomador que for indicado no toma3 ou toma4</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="9"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:choice>
										<xs:element name="toma3">
											<xs:annotation>
												<xs:documentation>Indicador do "papel" do tomador do serviço no CT-e</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="toma">
														<xs:annotation>
															<xs:documentation>Tomador do Serviço</xs:documentation>
															<xs:documentation>Preencher com:
															0-Remetente;
															1-Expedidor;
															2-Recebedor;
															3-Destinatário
															Serão utilizadas as informações contidas no respectivo grupo, conforme indicado pelo conteúdo deste campo</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:whiteSpace value="preserve"/>
																<xs:enumeration value="0"/>
																<xs:enumeration value="1"/>
																<xs:enumeration value="2"/>
																<xs:enumeration value="3"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="toma4">
											<xs:annotation>
												<xs:documentation>Indicador do "papel" do tomador do serviço no CT-e</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="toma">
														<xs:annotation>
															<xs:documentation>Tomador do Serviço</xs:documentation>
															<xs:documentation>Preencher com: 
															4 - Outros
															Obs: Informar os dados cadastrais do tomador do serviço</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:whiteSpace value="preserve"/>
																<xs:enumeration value="4"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:choice>
														<xs:element name="CNPJ" type="TCnpjOpc">
															<xs:annotation>
																<xs:documentation>Número do CNPJ</xs:documentation>
																<xs:documentation>Em caso de empresa não estabelecida no Brasil, será informado o CNPJ com zeros.															
Informar os zeros não significativos.</xs:documentation>
															</xs:annotation>
														</xs:element>
														<xs:element name="CPF" type="TCpf">
															<xs:annotation>
																<xs:documentation>Número do CPF</xs:documentation>
																<xs:documentation>Informar os zeros não significativos.</xs:documentation>
															</xs:annotation>
														</xs:element>
													</xs:choice>
													<xs:element name="IE" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Inscrição Estadual</xs:documentation>
															<xs:documentation>Informar a IE do tomador ou ISENTO se tomador é contribuinte do ICMS isento de inscrição no cadastro de contribuintes do ICMS. Caso o tomador não seja contribuinte do ICMS não informar o conteúdo.</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TIeDest"/>
														</xs:simpleType>
													</xs:element>
													<xs:sequence>
														<xs:element name="xNome">
															<xs:annotation>
																<xs:documentation>Razão Social ou Nome</xs:documentation>
															</xs:annotation>
															<xs:simpleType>
																<xs:restriction base="TString">
																	<xs:maxLength value="60"/>
																	<xs:minLength value="2"/>
																</xs:restriction>
															</xs:simpleType>
														</xs:element>
														<xs:element name="xFant" minOccurs="0">
															<xs:annotation>
																<xs:documentation>Nome Fantasia</xs:documentation>
															</xs:annotation>
															<xs:simpleType>
																<xs:restriction base="TString">
																	<xs:maxLength value="60"/>
																	<xs:minLength value="2"/>
																</xs:restriction>
															</xs:simpleType>
														</xs:element>
														<xs:element name="fone" type="TFone" minOccurs="0">
															<xs:annotation>
																<xs:documentation>Telefone</xs:documentation>
															</xs:annotation>
														</xs:element>
														<xs:element name="enderToma" type="TEndereco">
															<xs:annotation>
																<xs:documentation>Dados do endereço</xs:documentation>
															</xs:annotation>
														</xs:element>
														<xs:element name="email" type="TEmail" minOccurs="0">
															<xs:annotation>
																<xs:documentation>Endereço de email</xs:documentation>
															</xs:annotation>
														</xs:element>
													</xs:sequence>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:choice>
									<xs:sequence minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informar apenas
para tpEmis diferente de 1</xs:documentation>
										</xs:annotation>
										<xs:element name="dhCont" type="TDateTimeUTC">
											<xs:annotation>
												<xs:documentation>Data e Hora da entrada em contingência</xs:documentation>
												<xs:documentation>Informar a data e hora no formato AAAA-MM-DDTHH:MM:SS</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="xJust">
											<xs:annotation>
												<xs:documentation>Justificativa da entrada em contingência</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TString">
													<xs:minLength value="15"/>
													<xs:maxLength value="256"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="compl" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Dados complementares do CT-e para fins operacionais ou comerciais</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="xCaracAd" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Característica adicional do transporte</xs:documentation>
											<xs:documentation>Texto livre:
REENTREGA; DEVOLUÇÃO; REFATURAMENTO; etc</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="15"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xCaracSer" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Característica adicional do serviço</xs:documentation>
											<xs:documentation>Texto livre:
											ENTREGA EXPRESSA; LOGÍSTICA REVERSA; CONVENCIONAL; EMERGENCIAL; etc</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="30"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xEmi" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Funcionário emissor do CTe</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="20"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="fluxo" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Previsão do fluxo da carga</xs:documentation>
											<xs:documentation>Preenchimento obrigatório para o modal aéreo.</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="xOrig" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Sigla ou código interno da Filial/Porto/Estação/ Aeroporto de Origem</xs:documentation>
														<xs:documentation>Observações para o modal aéreo:
														- Preenchimento obrigatório para o modal aéreo.
														- O código de três letras IATA do aeroporto de partida deverá ser incluído como primeira anotação. Quando não for possível, utilizar a sigla OACI.</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="pass" minOccurs="0" maxOccurs="unbounded">
													<xs:complexType>
														<xs:sequence>
															<xs:element name="xPass" minOccurs="0">
																<xs:annotation>
																	<xs:documentation>Sigla ou código interno da Filial/Porto/Estação/Aeroporto de Passagem</xs:documentation>
																	<xs:documentation>Observação para o modal aéreo:
																	- O código de três letras IATA, referente ao aeroporto de transferência, deverá ser incluído, quando for o caso. Quando não for possível,  utilizar a sigla OACI. Qualquer solicitação de itinerário deverá ser incluída.</xs:documentation>
																</xs:annotation>
																<xs:simpleType>
																	<xs:restriction base="TString">
																		<xs:minLength value="1"/>
																		<xs:maxLength value="15"/>
																	</xs:restriction>
																</xs:simpleType>
															</xs:element>
														</xs:sequence>
													</xs:complexType>
												</xs:element>
												<xs:element name="xDest" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Sigla ou código interno da Filial/Porto/Estação/Aeroporto de Destino</xs:documentation>
														<xs:documentation>Observações para o modal aéreo:
														- Preenchimento obrigatório para o modal aéreo.
														- Deverá ser incluído o código de três letras IATA do aeroporto de destino. Quando não for possível, utilizar a sigla OACI.</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="xRota" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Código da Rota de Entrega</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="10"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:element name="Entrega" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informações ref. a previsão de entrega</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:choice>
													<xs:element name="semData">
														<xs:annotation>
															<xs:documentation>Entrega sem data definida</xs:documentation>
															<xs:documentation>Esta opção é proibida para o modal aéreo.</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="tpPer">
																	<xs:annotation>
																		<xs:documentation>Tipo de data/período programado para entrega</xs:documentation>
																		<xs:documentation>0- Sem data definida</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:enumeration value="0"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="comData">
														<xs:annotation>
															<xs:documentation>Entrega com data definida</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="tpPer">
																	<xs:annotation>
																		<xs:documentation>Tipo de data/período programado para entrega</xs:documentation>
																		<xs:documentation>Preencher com:
																		1-Na data;
																		2-Até a data;
																		3-A partir da data</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:enumeration value="1"/>
																			<xs:enumeration value="2"/>
																			<xs:enumeration value="3"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="dProg" type="TData">
																	<xs:annotation>
																		<xs:documentation>Data programada </xs:documentation>
																		<xs:documentation>Formato AAAA-MM-DD</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="noPeriodo">
														<xs:annotation>
															<xs:documentation>Entrega no período definido</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="tpPer">
																	<xs:annotation>
																		<xs:documentation>Tipo período</xs:documentation>
																		<xs:documentation>4-no período</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:enumeration value="4"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="dIni" type="TData">
																	<xs:annotation>
																		<xs:documentation>Data inicial </xs:documentation>
																		<xs:documentation>Formato AAAA-MM-DD</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="dFim" type="TData">
																	<xs:annotation>
																		<xs:documentation>Data final </xs:documentation>
																		<xs:documentation>Formato AAAA-MM-DD</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:choice>
												<xs:choice>
													<xs:element name="semHora">
														<xs:annotation>
															<xs:documentation>Entrega sem hora definida</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="tpHor">
																	<xs:annotation>
																		<xs:documentation>Tipo de hora</xs:documentation>
																		<xs:documentation>0- Sem hora definida</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:enumeration value="0"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="comHora">
														<xs:annotation>
															<xs:documentation>Entrega com hora definida</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="tpHor">
																	<xs:annotation>
																		<xs:documentation>Tipo de hora</xs:documentation>
																		<xs:documentation>Preencher com:
																		1 - No horário;
																		2 - Até o horário;
																		3 - A partir do horário.</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:enumeration value="1"/>
																			<xs:enumeration value="2"/>
																			<xs:enumeration value="3"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="hProg" type="TTime">
																	<xs:annotation>
																		<xs:documentation>Hora programada </xs:documentation>
																		<xs:documentation>Formato HH:MM:SS</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="noInter">
														<xs:annotation>
															<xs:documentation>Entrega no intervalo de horário definido</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="tpHor">
																	<xs:annotation>
																		<xs:documentation> Tipo de hora</xs:documentation>
																		<xs:documentation>4 - No intervalo de tempo</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:enumeration value="4"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="hIni" type="TTime">
																	<xs:annotation>
																		<xs:documentation>Hora inicial </xs:documentation>
																		<xs:documentation>Formato HH:MM:SS</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="hFim" type="TTime">
																	<xs:annotation>
																		<xs:documentation>Hora final </xs:documentation>
																		<xs:documentation>Formato HH:MM:SS</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:choice>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:element name="origCalc" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Município de origem para efeito de cálculo do frete</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="2"/>
												<xs:maxLength value="40"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="destCalc" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Município de destino para efeito de cálculo do frete</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="2"/>
												<xs:maxLength value="40"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xObs" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Observações Gerais</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="2000"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="ObsCont" minOccurs="0" maxOccurs="10">
										<xs:annotation>
											<xs:documentation>Campo de uso livre do contribuinte</xs:documentation>
											<xs:documentation>Informar o nome do campo no atributo xCampo e o conteúdo do campo no XTexto</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="xTexto">
													<xs:annotation>
														<xs:documentation>Conteúdo do campo</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="160"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
											<xs:attribute name="xCampo" use="required">
												<xs:annotation>
													<xs:documentation>Identificação do campo</xs:documentation>
												</xs:annotation>
												<xs:simpleType>
													<xs:restriction base="TString">
														<xs:minLength value="1"/>
														<xs:maxLength value="20"/>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
									<xs:element name="ObsFisco" minOccurs="0" maxOccurs="10">
										<xs:annotation>
											<xs:documentation>Campo de uso livre do contribuinte</xs:documentation>
											<xs:documentation>Informar o nome do campo no atributo xCampo e o conteúdo do campo no XTexto</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="xTexto">
													<xs:annotation>
														<xs:documentation>Conteúdo do campo</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
											<xs:attribute name="xCampo" use="required">
												<xs:annotation>
													<xs:documentation>Identificação do campo</xs:documentation>
												</xs:annotation>
												<xs:simpleType>
													<xs:restriction base="TString">
														<xs:minLength value="1"/>
														<xs:maxLength value="20"/>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="emit">
							<xs:annotation>
								<xs:documentation>Identificação do Emitente do CT-e</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpj">
											<xs:annotation>
												<xs:documentation>CNPJ do emitente</xs:documentation>
												<xs:documentation>Informar zeros não significativos</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>CPF do emitente</xs:documentation>
												<xs:documentation>Informar zeros não significativos.

Usar com série específica 920-969 para emitente pessoa física com inscrição estadual</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
									<xs:element name="IE" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual do Emitente</xs:documentation>
											<xs:documentation>A IE do emitente somente ficará sem informação para o caso do Regime Especial da NFF (tpEmis=3)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIe"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="IEST" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual do Substituto Tributário</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIe"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão social ou Nome do emitente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xFant" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Nome fantasia</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="enderEmit" type="TEndeEmi">
										<xs:annotation>
											<xs:documentation>Endereço do emitente</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="CRT" type="TCRT">
										<xs:annotation>
											<xs:documentation>Código do Regime Tributário</xs:documentation>
											<xs:documentation>Informar: 1=Simples Nacional; 
2=Simples Nacional, excesso sublimite de receita bruta;
3=Regime Normal. 
4=Simples Nacional - Microempreendedor Individual – MEI.
</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="rem" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações do Remetente das mercadorias transportadas pelo CT-e</xs:documentation>
								<xs:documentation>Poderá não ser informado para os CT-e de redespacho intermediário e serviço vinculado a multimodal. Nos demais casos deverá sempre ser informado.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpjOpc">
											<xs:annotation>
												<xs:documentation>Número do CNPJ</xs:documentation>
												<xs:documentation>Em caso de empresa não estabelecida no Brasil, será informado o CNPJ com zeros.
												Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>Número do CPF</xs:documentation>
												<xs:documentation>Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
									<xs:element name="IE" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual</xs:documentation>
											<xs:documentation>Informar a IE do remetente ou ISENTO se remetente é contribuinte do ICMS isento de inscrição no cadastro de contribuintes do ICMS. Caso o remetente não seja contribuinte do ICMS não informar a tag.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIeDest"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão social ou nome do remetente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xFant" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Nome fantasia</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="fone" type="TFone" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Telefone</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="enderReme" type="TEndereco">
										<xs:annotation>
											<xs:documentation>Dados do endereço</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="email" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Endereço de email</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TEmail"/>
										</xs:simpleType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="exped" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações do Expedidor da Carga</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpjOpc">
											<xs:annotation>
												<xs:documentation>Número do CNPJ</xs:documentation>
												<xs:documentation>Em caso de empresa não estabelecida no Brasil, será informado o CNPJ com zeros.
												Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>Número do CPF</xs:documentation>
												<xs:documentation>Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
									<xs:element name="IE" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual</xs:documentation>
											<xs:documentation>Informar a IE do expedidor ou ISENTO se expedidor é contribuinte do ICMS isento de inscrição no cadastro de contribuintes do ICMS. Caso o expedidor não seja contribuinte do ICMS não informar a tag.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIeDest"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão Social ou Nome</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="fone" type="TFone" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Telefone</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="enderExped" type="TEndereco">
										<xs:annotation>
											<xs:documentation>Dados do endereço</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="email" type="TEmail" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Endereço de email</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="receb" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações do Recebedor da Carga</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpjOpc">
											<xs:annotation>
												<xs:documentation>Número do CNPJ</xs:documentation>
												<xs:documentation>Em caso de empresa não estabelecida no Brasil, será informado o CNPJ com zeros.
												Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>Número do CPF</xs:documentation>
												<xs:documentation>Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
									<xs:element name="IE" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual</xs:documentation>
											<xs:documentation>Informar a IE do recebedor ou ISENTO se recebedor é contribuinte do ICMS isento de inscrição no cadastro de contribuintes do ICMS. Caso o recebedor não seja contribuinte do ICMS não informar o conteúdo.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIeDest"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão Social ou Nome </xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="fone" type="TFone" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Telefone</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="enderReceb" type="TEndereco">
										<xs:annotation>
											<xs:documentation>Dados do endereço</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="email" type="TEmail" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Endereço de email</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="dest" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações do Destinatário do CT-e</xs:documentation>
								<xs:documentation>Poderá não ser informado para os CT-e de redespacho intermediário e serviço vinculado a multimodal. Nos demais casos deverá sempre ser informado.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpjOpc">
											<xs:annotation>
												<xs:documentation>Número do CNPJ</xs:documentation>
												<xs:documentation>Em caso de empresa não estabelecida no Brasil, será informado o CNPJ com zeros.
												Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>Número do CPF</xs:documentation>
												<xs:documentation>Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
									<xs:element name="IE" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual</xs:documentation>
											<xs:documentation>Informar a IE do destinatário ou ISENTO se destinatário é contribuinte do ICMS isento de inscrição no cadastro de contribuintes do ICMS. Caso o destinatário não seja contribuinte do ICMS não informar o conteúdo.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIeDest"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão Social ou Nome do destinatário</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="fone" type="TFone" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Telefone</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="ISUF" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição na SUFRAMA</xs:documentation>
											<xs:documentation>(Obrigatório nas operações com as áreas com benefícios de incentivos fiscais sob controle da SUFRAMA)</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{8,9}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="enderDest" type="TEndereco">
										<xs:annotation>
											<xs:documentation>Dados do endereço</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="email" type="TEmail" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Endereço de email</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="vPrest">
							<xs:annotation>
								<xs:documentation>Valores da Prestação de Serviço</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="vTPrest" type="TDec_1302">
										<xs:annotation>
											<xs:documentation>Valor Total da Prestação do Serviço</xs:documentation>
											<xs:documentation>Pode conter zeros quando o CT-e for de complemento de ICMS</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="vRec" type="TDec_1302">
										<xs:annotation>
											<xs:documentation>Valor a Receber</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="Comp" minOccurs="0" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation>Componentes do Valor da Prestação</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="xNome">
													<xs:annotation>
														<xs:documentation>Nome do componente</xs:documentation>
														<xs:documentation>Exxemplos: FRETE PESO, FRETE VALOR, SEC/CAT, ADEME, AGENDAMENTO, etc</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:maxLength value="15"/>
															<xs:minLength value="1"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="vComp" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor do componente</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="imp">
							<xs:annotation>
								<xs:documentation>Informações relativas aos Impostos</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="ICMS" type="TImp">
										<xs:annotation>
											<xs:documentation>Informações relativas ao ICMS</xs:documentation>
											<xs:documentation/>
										</xs:annotation>
									</xs:element>
									<xs:element name="vTotTrib" type="TDec_1302" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Valor Total dos Tributos</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="infAdFisco" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informações adicionais de interesse do Fisco</xs:documentation>
											<xs:documentation>Norma referenciada, informações complementares, etc</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="2000"/>
												<xs:minLength value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="ICMSUFFim" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informações do ICMS de partilha com a UF de término do serviço de transporte na operação interestadual</xs:documentation>
											<xs:documentation>Grupo a ser informado nas prestações interestaduais para consumidor final, não contribuinte do ICMS</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="vBCUFFim" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor da BC do ICMS na UF de término da prestação do serviço de transporte</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="pFCPUFFim" type="TDec_0302">
													<xs:annotation>
														<xs:documentation>Percentual do ICMS relativo ao Fundo de Combate à pobreza (FCP) na UF de término da prestação do serviço de transporte</xs:documentation>
														<xs:documentation>Alíquota adotada nas operações internas na UF do destinatário</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="pICMSUFFim" type="TDec_0302">
													<xs:annotation>
														<xs:documentation>Alíquota interna da UF de término da prestação do serviço de transporte</xs:documentation>
														<xs:documentation>Alíquota adotada nas operações internas na UF do destinatário</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="pICMSInter" type="TDec_0302">
													<xs:annotation>
														<xs:documentation>Alíquota interestadual das UF envolvidas</xs:documentation>
														<xs:documentation>Alíquota interestadual das UF envolvidas
</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vFCPUFFim" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor do ICMS relativo ao Fundo de Combate á Pobreza (FCP) da UF de término da prestação</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vICMSUFFim" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor do ICMS de partilha para a UF de término da prestação do serviço de transporte</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vICMSUFIni" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor do ICMS de partilha para a UF de início da prestação do serviço de transporte</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:choice>
							<xs:element name="infCTeNorm">
								<xs:annotation>
									<xs:documentation>Grupo de informações do CT-e Normal e Substituto</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="infCarga">
											<xs:annotation>
												<xs:documentation>Informações da Carga do CT-e</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="vCarga" type="TDec_1302" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Valor total da carga</xs:documentation>
															<xs:documentation>Dever ser informado para todos os modais, com exceção para o Dutoviário.</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="proPred">
														<xs:annotation>
															<xs:documentation>Produto predominante</xs:documentation>
															<xs:documentation>Informar a descrição do produto predominante</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:minLength value="1"/>
																<xs:maxLength value="60"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="xOutCat" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Outras características da carga</xs:documentation>
															<xs:documentation>"FRIA", "GRANEL", "REFRIGERADA", "Medidas: 12X12X12"</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:minLength value="1"/>
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="infQ" maxOccurs="unbounded">
														<xs:annotation>
															<xs:documentation>Informações de quantidades da Carga do CT-e</xs:documentation>
															<xs:documentation>Para o Aéreo é obrigatório o preenchimento desse campo da seguinte forma.
1 - Peso Bruto, sempre em quilogramas (obrigatório);
2 - Peso Cubado; sempre em quilogramas;
3 - Quantidade de volumes, sempre em unidades (obrigatório);
4 - Cubagem, sempre em metros cúbicos (obrigatório apenas quando for impossível preencher as dimensões da(s) embalagem(ens) na tag xDime do leiaute do Aéreo).</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="cUnid">
																	<xs:annotation>
																		<xs:documentation>Código da Unidade de Medida </xs:documentation>
																		<xs:documentation>Preencher com:
																		00-M3;
																		01-KG;
																		02-TON;
																		03-UNIDADE;
																		04-LITROS;
																		05-MMBTU</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:enumeration value="00"/>
																			<xs:enumeration value="01"/>
																			<xs:enumeration value="02"/>
																			<xs:enumeration value="03"/>
																			<xs:enumeration value="04"/>
																			<xs:enumeration value="05"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="tpMed">
																	<xs:annotation>
																		<xs:documentation>Tipo da Medida</xs:documentation>
																		<xs:documentation>Exemplos:
PESO BRUTO, PESO DECLARADO, PESO CUBADO, PESO AFORADO, PESO AFERIDO, PESO BASE DE CÁLCULO, LITRAGEM, CAIXAS e etc</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="20"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="qCarga" type="TDec_1104">
																	<xs:annotation>
																		<xs:documentation>Quantidade</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="vCargaAverb" type="TDec_1302Opc" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Valor da Carga para efeito de averbação</xs:documentation>
															<xs:documentation>Normalmente igual ao valor declarado da mercadoria, diferente por exemplo, quando a mercadoria transportada é isenta de tributos nacionais para exportação, onde é preciso averbar um valor maior, pois no caso de indenização, o valor a ser pago será maior</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="infDoc" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Informações dos documentos transportados pelo CT-e
Opcional para Redespacho Intermediario e Serviço vinculado a multimodal.</xs:documentation>
												<xs:documentation>Poderá não ser informado para os CT-e de redespacho intermediário e serviço vinculado a multimodal. Nos demais casos deverá sempre ser informado.</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:choice>
														<xs:element name="infNF" maxOccurs="unbounded">
															<xs:annotation>
																<xs:documentation>Informações das NF</xs:documentation>
																<xs:documentation>Este grupo deve ser informado quando o documento originário for NF </xs:documentation>
															</xs:annotation>
															<xs:complexType>
																<xs:sequence>
																	<xs:element name="nRoma" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Número do Romaneio da NF</xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="TString">
																				<xs:minLength value="1"/>
																				<xs:maxLength value="20"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="nPed" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Número do Pedido da NF</xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="TString">
																				<xs:minLength value="1"/>
																				<xs:maxLength value="20"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="mod" type="TModNF">
																		<xs:annotation>
																			<xs:documentation>Modelo da Nota Fiscal</xs:documentation>
																			<xs:documentation>Preencher com: 
01 - NF Modelo 01/1A e Avulsa; 
04 - NF de Produtor</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="serie">
																		<xs:annotation>
																			<xs:documentation>Série</xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="TString">
																				<xs:minLength value="1"/>
																				<xs:maxLength value="3"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="nDoc">
																		<xs:annotation>
																			<xs:documentation>Número </xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="TString">
																				<xs:minLength value="1"/>
																				<xs:maxLength value="20"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="dEmi" type="TData">
																		<xs:annotation>
																			<xs:documentation>Data de Emissão</xs:documentation>
																			<xs:documentation>Formato AAAA-MM-DD</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vBC" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Valor da Base de Cálculo do ICMS</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vICMS" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Valor Total do ICMS</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vBCST" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Valor da Base de Cálculo do ICMS ST</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vST" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Valor Total do ICMS ST</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vProd" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Valor Total dos Produtos</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vNF" type="TDec_1302">
																		<xs:annotation>
																			<xs:documentation>Valor Total da NF</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="nCFOP" type="TCfop">
																		<xs:annotation>
																			<xs:documentation>CFOP Predominante</xs:documentation>
																			<xs:documentation>CFOP da NF ou, na existência de mais de um, predominância pelo critério de valor econômico.</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="nPeso" type="TDec_1203Opc" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Peso total em Kg</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="PIN" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>PIN SUFRAMA</xs:documentation>
																			<xs:documentation>PIN atribuído pela SUFRAMA para a operação.</xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="xs:string">
																				<xs:whiteSpace value="preserve"/>
																				<xs:minLength value="2"/>
																				<xs:maxLength value="9"/>
																				<xs:pattern value="[1-9]{1}[0-9]{1,8}"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="dPrev" type="TData" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Data prevista de entrega</xs:documentation>
																			<xs:documentation>Formato AAAA-MM-DD</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:choice>
																		<xs:element name="infUnidCarga" type="TUnidCarga" minOccurs="0" maxOccurs="unbounded">
																			<xs:annotation>
																				<xs:documentation>Informações das Unidades de Carga (Containeres/ULD/Outros)</xs:documentation>
																				<xs:documentation>Dispositivo de carga utilizada (Unit Load Device - ULD) significa todo tipo de contêiner de carga, vagão, contêiner de avião, palete de aeronave com rede ou palete de aeronave com rede sobre um iglu. </xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="infUnidTransp" type="TUnidadeTransp" minOccurs="0" maxOccurs="unbounded">
																			<xs:annotation>
																				<xs:documentation>Informações das Unidades de Transporte (Carreta/Reboque/Vagão)</xs:documentation>
																				<xs:documentation>Deve ser preenchido com as informações das unidades de transporte utilizadas.</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																	</xs:choice>
																</xs:sequence>
															</xs:complexType>
														</xs:element>
														<xs:element name="infNFe" maxOccurs="unbounded">
															<xs:annotation>
																<xs:documentation>Informações das NF-e</xs:documentation>
															</xs:annotation>
															<xs:complexType>
																<xs:sequence>
																	<xs:element name="chave" type="TChDFe">
																		<xs:annotation>
																			<xs:documentation>Chave de acesso da NF-e</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="PIN" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>PIN SUFRAMA</xs:documentation>
																			<xs:documentation>PIN atribuído pela SUFRAMA para a operação.</xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="xs:string">
																				<xs:whiteSpace value="preserve"/>
																				<xs:minLength value="2"/>
																				<xs:maxLength value="9"/>
																				<xs:pattern value="[1-9]{1}[0-9]{1,8}"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="dPrev" type="TData" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Data prevista de entrega</xs:documentation>
																			<xs:documentation>Formato AAAA-MM-DD</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:choice>
																		<xs:element name="infUnidCarga" type="TUnidCarga" minOccurs="0" maxOccurs="unbounded">
																			<xs:annotation>
																				<xs:documentation>Informações das Unidades de Carga (Containeres/ULD/Outros)</xs:documentation>
																				<xs:documentation>Dispositivo de carga utilizada (Unit Load Device - ULD) significa todo tipo de contêiner de carga, vagão, contêiner de avião, palete de aeronave com rede ou palete de aeronave com rede sobre um iglu. </xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="infUnidTransp" type="TUnidadeTransp" minOccurs="0" maxOccurs="unbounded">
																			<xs:annotation>
																				<xs:documentation>Informações das Unidades de Transporte (Carreta/Reboque/Vagão)</xs:documentation>
																				<xs:documentation>Deve ser preenchido com as informações das unidades de transporte utilizadas.</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																	</xs:choice>
																</xs:sequence>
															</xs:complexType>
														</xs:element>
														<xs:element name="infOutros" maxOccurs="unbounded">
															<xs:annotation>
																<xs:documentation>Informações dos demais documentos</xs:documentation>
															</xs:annotation>
															<xs:complexType>
																<xs:sequence>
																	<xs:element name="tpDoc">
																		<xs:annotation>
																			<xs:documentation>Tipo de documento originário</xs:documentation>
																			<xs:documentation>Preencher com:
															00 - Declaração;
															10 - Dutoviário;
							

59 - CF-e SAT;

65 - NFC-e;
								99 - Outros</xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="xs:string">
																				<xs:whiteSpace value="preserve"/>
																				<xs:enumeration value="00"/>
																				<xs:enumeration value="10"/>
																				<xs:enumeration value="59"/>
																				<xs:enumeration value="65"/>
																				<xs:enumeration value="99"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="descOutros" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Descrição do documento</xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="TString">
																				<xs:minLength value="1"/>
																				<xs:maxLength value="100"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="nDoc" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Número </xs:documentation>
																		</xs:annotation>
																		<xs:simpleType>
																			<xs:restriction base="TString">
																				<xs:minLength value="1"/>
																				<xs:maxLength value="20"/>
																			</xs:restriction>
																		</xs:simpleType>
																	</xs:element>
																	<xs:element name="dEmi" type="TData" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Data de Emissão</xs:documentation>
																			<xs:documentation>Formato AAAA-MM-DD</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="vDocFisc" type="TDec_1302Opc" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Valor do documento</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="dPrev" type="TData" minOccurs="0">
																		<xs:annotation>
																			<xs:documentation>Data prevista de entrega</xs:documentation>
																			<xs:documentation>Formato AAAA-MM-DD</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:choice>
																		<xs:element name="infUnidCarga" type="TUnidCarga" minOccurs="0" maxOccurs="unbounded">
																			<xs:annotation>
																				<xs:documentation>Informações das Unidades de Carga (Containeres/ULD/Outros)</xs:documentation>
																				<xs:documentation>Dispositivo de carga utilizada (Unit Load Device - ULD) significa todo tipo de contêiner de carga, vagão, contêiner de avião, palete de aeronave com rede ou palete de aeronave com rede sobre um iglu. </xs:documentation>
																			</xs:annotation>
																		</xs:element>
																		<xs:element name="infUnidTransp" type="TUnidadeTransp" minOccurs="0" maxOccurs="unbounded">
																			<xs:annotation>
																				<xs:documentation>Informações das Unidades de Transporte (Carreta/Reboque/Vagão)</xs:documentation>
																				<xs:documentation>Deve ser preenchido com as informações das unidades de transporte utilizadas.</xs:documentation>
																			</xs:annotation>
																		</xs:element>
																	</xs:choice>
																</xs:sequence>
															</xs:complexType>
														</xs:element>
													</xs:choice>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="docAnt" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Documentos de Transporte Anterior</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="emiDocAnt" maxOccurs="unbounded">
														<xs:annotation>
															<xs:documentation>Emissor do documento anterior</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:choice>
																	<xs:element name="CNPJ" type="TCnpjOpc">
																		<xs:annotation>
																			<xs:documentation>Número do CNPJ</xs:documentation>
																			<xs:documentation>Em caso de empresa não estabelecida no Brasil, será informado o CNPJ com zeros.
																			Informar os zeros não significativos.</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="CPF" type="TCpf">
																		<xs:annotation>
																			<xs:documentation>Número do CPF</xs:documentation>
																			<xs:documentation>Informar os zeros não significativos.</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																</xs:choice>
																<xs:sequence minOccurs="0">
																	<xs:element name="IE" type="TIe">
																		<xs:annotation>
																			<xs:documentation>Inscrição Estadual</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																	<xs:element name="UF" type="TUf">
																		<xs:annotation>
																			<xs:documentation>Sigla da UF</xs:documentation>
																			<xs:documentation>Informar EX para operações com o exterior.</xs:documentation>
																		</xs:annotation>
																	</xs:element>
																</xs:sequence>
																<xs:element name="xNome">
																	<xs:annotation>
																		<xs:documentation>Razão Social ou Nome do expedidor</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:maxLength value="60"/>
																			<xs:minLength value="1"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="idDocAnt" maxOccurs="2">
																	<xs:annotation>
																		<xs:documentation>Informações de identificação dos documentos de Transporte Anterior</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:choice>
																			<xs:element name="idDocAntPap" maxOccurs="unbounded">
																				<xs:annotation>
																					<xs:documentation>Documentos de transporte anterior em papel</xs:documentation>
																				</xs:annotation>
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="tpDoc">
																							<xs:annotation>
																								<xs:documentation>Tipo do Documento de Transporte Anterior</xs:documentation>
																								<xs:documentation>Preencher com:
07-ATRE;							
08-DTA (Despacho de Transito Aduaneiro);
09-Conhecimento Aéreo Internacional;
10 – Conhecimento - Carta de Porte Internacional;
11 – Conhecimento Avulso;
12-TIF (Transporte Internacional Ferroviário); 13-BL (Bill of Lading)</xs:documentation>
																							</xs:annotation>
																							<xs:simpleType>
																								<xs:restriction base="TDocAssoc"/>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="serie">
																							<xs:annotation>
																								<xs:documentation>Série do Documento Fiscal</xs:documentation>
																							</xs:annotation>
																							<xs:simpleType>
																								<xs:restriction base="TString">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="3"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="subser" minOccurs="0">
																							<xs:annotation>
																								<xs:documentation>Série do Documento Fiscal</xs:documentation>
																							</xs:annotation>
																							<xs:simpleType>
																								<xs:restriction base="TString">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="2"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="nDoc">
																							<xs:annotation>
																								<xs:documentation>Número do Documento Fiscal</xs:documentation>
																							</xs:annotation>
																							<xs:simpleType>
																								<xs:restriction base="TString">
																									<xs:minLength value="1"/>
																									<xs:maxLength value="30"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="dEmi" type="TData">
																							<xs:annotation>
																								<xs:documentation>Data de emissão (AAAA-MM-DD)</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="idDocAntEle" maxOccurs="unbounded">
																				<xs:annotation>
																					<xs:documentation>Documentos de transporte anterior eletrônicos</xs:documentation>
																				</xs:annotation>
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="chCTe" type="TChDFe">
																							<xs:annotation>
																								<xs:documentation>Chave de acesso do CT-e</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																		</xs:choice>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="infModal">
											<xs:annotation>
												<xs:documentation>Informações do modal</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:any processContents="skip">
														<xs:annotation>
															<xs:documentation>XML do modal
Insira neste local o XML específico do modal (rodoviário, aéreo, ferroviário, aquaviário ou dutoviário).  </xs:documentation>
															<xs:documentation>O elemento do tipo -any- permite estender o documento XML com elementos não especificados pelo schema.
															Insira neste local - any- o XML específico do modal (rodoviário, aéreo, ferroviário, aquaviário ou dutoviário). A especificação do schema XML para cada modal pode ser encontrada nos arquivos que acompanham este pacote de liberação:
													Rodoviário - ver arquivo CTeModalRodoviario_v9.99
			Aéreo - ver arquivo CTeModalAereo_v9.99
				Aquaviário - arquivo CTeModalAquaviario_v9.99
				Ferroviário - arquivo CTeModalFerroviario_v9.99
				Dutoviário - arquivo CTeModalDutoviario_v9.99

Onde v9.99 é a a designação genérica para a versão do arquivo. Por exemplo, o arquivo para o schema do modal Rodoviário na versão 1.04 será denominado "CTeModalRodoviario_v1.04".</xs:documentation>
														</xs:annotation>
													</xs:any>
												</xs:sequence>
												<xs:attribute name="versaoModal" use="required">
													<xs:annotation>
														<xs:documentation>Versão do leiaute específico para o Modal</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="4\.(0[0-9]|[1-9][0-9])"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:attribute>
											</xs:complexType>
										</xs:element>
										<xs:element name="veicNovos" minOccurs="0" maxOccurs="unbounded">
											<xs:annotation>
												<xs:documentation>informações dos veículos transportados</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="chassi">
														<xs:annotation>
															<xs:documentation>Chassi do veículo</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:whiteSpace value="preserve"/>
																<xs:length value="17"/>
																<xs:pattern value="[A-Z0-9]+"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="cCor">
														<xs:annotation>
															<xs:documentation>Cor do veículo</xs:documentation>
															<xs:documentation>Código de cada montadora</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:minLength value="1"/>
																<xs:maxLength value="4"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="xCor">
														<xs:annotation>
															<xs:documentation>Descrição da cor</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:minLength value="1"/>
																<xs:maxLength value="40"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="cMod">
														<xs:annotation>
															<xs:documentation>Código Marca Modelo</xs:documentation>
															<xs:documentation>Utilizar tabela RENAVAM</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:minLength value="1"/>
																<xs:maxLength value="6"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="vUnit" type="TDec_1302">
														<xs:annotation>
															<xs:documentation>Valor Unitário do Veículo</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="vFrete" type="TDec_1302">
														<xs:annotation>
															<xs:documentation>Frete Unitário</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="cobr" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Dados da cobrança do CT-e</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="fat" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Dados da fatura</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="nFat" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Número da fatura</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="60"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="vOrig" type="TDec_1302Opc" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Valor original da fatura</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="vDesc" type="TDec_1302Opc" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Valor do desconto da fatura</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="vLiq" type="TDec_1302Opc" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Valor líquido da fatura</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="dup" minOccurs="0" maxOccurs="unbounded">
														<xs:annotation>
															<xs:documentation>Dados das duplicatas</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="nDup" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Número da duplicata</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:maxLength value="60"/>
																			<xs:minLength value="1"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="dVenc" type="TData" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Data de vencimento da duplicata (AAAA-MM-DD)</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="vDup" type="TDec_1302Opc" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Valor da duplicata</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="infCteSub" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Informações do CT-e de substituição </xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="chCte">
														<xs:annotation>
															<xs:documentation>Chave de acesso do CT-e a ser substituído (original)</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:pattern value="[0-9]{44}"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="indAlteraToma" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Indicador de CT-e Alteração de Tomador</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:enumeration value="1"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="infGlobalizado" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Informações do CT-e Globalizado</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="xObs">
														<xs:annotation>
															<xs:documentation>Preencher com informações adicionais, legislação do regime especial, etc</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:minLength value="15"/>
																<xs:maxLength value="256"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="infServVinc" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Informações do Serviço Vinculado a Multimodal</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="infCTeMultimodal" maxOccurs="unbounded">
														<xs:annotation>
															<xs:documentation>informações do CT-e multimodal vinculado</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="chCTeMultimodal" type="TChDFe">
																	<xs:annotation>
																		<xs:documentation>Chave de acesso do CT-e Multimodal</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="infCteComp" maxOccurs="10">
								<xs:annotation>
									<xs:documentation>Detalhamento do CT-e complementado</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="chCTe" type="TChDFe">
											<xs:annotation>
												<xs:documentation>Chave do CT-e complementado</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:choice>
						<xs:element name="autXML" minOccurs="0" maxOccurs="10">
							<xs:annotation>
								<xs:documentation>Autorizados para download do XML do DF-e</xs:documentation>
								<xs:documentation>Informar CNPJ ou CPF. Preencher os zeros não significativos.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpj">
											<xs:annotation>
												<xs:documentation>CNPJ do autorizado</xs:documentation>
												<xs:documentation>Informar zeros não significativos</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>CPF do autorizado</xs:documentation>
												<xs:documentation>Informar zeros não significativos</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="infRespTec" type="TRespTec" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações do Responsável Técnico pela emissão do DF-e</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="infSolicNFF" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Grupo de informações do pedido de emissão da Nota Fiscal Fácil</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="xSolic">
										<xs:annotation>
											<xs:documentation>Solicitação do pedido de emissão da NFF.</xs:documentation>
											<xs:documentation>Será preenchido com a totalidade de campos informados no aplicativo emissor serializado.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="2"/>
												<xs:maxLength value="2000"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="infPAA" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Grupo de Informação do Provedor de Assinatura e Autorização</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="CNPJPAA" type="TCnpj">
										<xs:annotation>
											<xs:documentation>CNPJ do Provedor de Assinatura e Autorização</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="PAASignature">
										<xs:annotation>
											<xs:documentation>Assinatura RSA do Emitente para DFe gerados por PAA</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="SignatureValue" type="xs:base64Binary">
													<xs:annotation>
														<xs:documentation>Assinatura digital padrão RSA</xs:documentation>
														<xs:documentation>Converter o atributo Id do DFe para array de bytes e assinar com a chave privada do RSA com algoritmo SHA1 gerando um valor no formato base64.</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="RSAKeyValue" type="TRSAKeyValueType">
													<xs:annotation>
														<xs:documentation>Chave Publica no padrão XML RSA Key</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="versao" use="required">
						<xs:annotation>
							<xs:documentation>Versão do leiaute</xs:documentation>
							<xs:documentation>Ex: "4.00"</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="TVerCTe"/>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="Id" use="required">
						<xs:annotation>
							<xs:documentation>Identificador da tag a ser assinada</xs:documentation>
							<xs:documentation>Informar a chave de acesso do CT-e e precedida do literal "CTe"</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:ID">
								<xs:pattern value="CTe[0-9]{44}"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="infCTeSupl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Informações suplementares do CT-e</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="qrCodCTe">
							<xs:annotation>
								<xs:documentation>Texto com o QR-Code impresso no DACTE</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:minLength value="50"/>
									<xs:maxLength value="1000"/>
									<xs:pattern value="((HTTPS?|https?)://.*\?chCTe=[0-9]{44}&amp;tpAmb=[1-2](&amp;sign=[!-ÿ]{1}[ -ÿ]{0,}[!-ÿ]{1}|[!-ÿ]{1})?)"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TCTeOS">
		<xs:annotation>
			<xs:documentation>Tipo Conhecimento de Transporte Eletrônico Outros Serviços (Modelo 67)</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="infCte">
				<xs:annotation>
					<xs:documentation>Informações do CT-e Outros Serviços</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="ide">
							<xs:annotation>
								<xs:documentation>Identificação do CT-e Outros Serviços</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="cUF" type="TCodUfIBGE">
										<xs:annotation>
											<xs:documentation>Código da UF do emitente do CT-e.</xs:documentation>
											<xs:documentation>Utilizar a Tabela do IBGE.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="cCT">
										<xs:annotation>
											<xs:documentation>Código numérico que compõe a Chave de Acesso. </xs:documentation>
											<xs:documentation>Número aleatório gerado pelo emitente para cada CT-e, com o objetivo de evitar acessos indevidos ao documento.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{8}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="CFOP" type="TCfop">
										<xs:annotation>
											<xs:documentation>Código Fiscal de Operações e Prestações</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="natOp">
										<xs:annotation>
											<xs:documentation>Natureza da Operação</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="mod" type="TModCTOS">
										<xs:annotation>
											<xs:documentation>Modelo do documento fiscal</xs:documentation>
											<xs:documentation>Utilizar o código 67 para identificação do CT-e Outros Serviços, emitido em substituição a Nota Fiscal Modelo 7 para transporte de pessoas, valores e excesso de bagagem.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="serie">
										<xs:annotation>
											<xs:documentation>Série do CT-e OS</xs:documentation>
											<xs:documentation>Preencher com "0" no caso de série única</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TSerie"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="nCT" type="TNF">
										<xs:annotation>
											<xs:documentation>Número do CT-e OS</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="dhEmi">
										<xs:annotation>
											<xs:documentation>Data e hora de emissão do CT-e OS</xs:documentation>
											<xs:documentation>Formato AAAA-MM-DDTHH:MM:DD TZD</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TDateTimeUTC"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpImp">
										<xs:annotation>
											<xs:documentation>Formato de impressão do DACTE OS</xs:documentation>
											<xs:documentation>Preencher com: 1 - Retrato; 2 - Paisagem.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpEmis">
										<xs:annotation>
											<xs:documentation>Forma de emissão do CT-e</xs:documentation>
											<xs:documentation>Preencher com:
1 - Normal;
 5 - Contingência FSDA;
7 - Autorização pela SVC-RS;
 8 - Autorização pela SVC-SP</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="5"/>
												<xs:enumeration value="7"/>
												<xs:enumeration value="8"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="cDV">
										<xs:annotation>
											<xs:documentation>Digito Verificador da chave de acesso do CT-e</xs:documentation>
											<xs:documentation>Informar o dígito  de controle da chave de acesso do CT-e, que deve ser calculado com a aplicação do algoritmo módulo 11 (base 2,9) da chave de acesso. </xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:pattern value="[0-9]{1}"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="tpAmb" type="TAmb">
										<xs:annotation>
											<xs:documentation>Tipo do Ambiente</xs:documentation>
											<xs:documentation>Preencher com:1 - Produção; 2 - Homologação</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="tpCTe" type="TFinCTe">
										<xs:annotation>
											<xs:documentation>Tipo do CT-e OS</xs:documentation>
											<xs:documentation>Preencher com:
0 - CT-e Normal; 
1 - CT-e Complementar; 

3 - CT-e de Substituição.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="procEmi" type="TProcEmi">
										<xs:annotation>
											<xs:documentation>Identificador do processo de emissão do CT-e OS</xs:documentation>
											<xs:documentation>Preencher com: 
											0 - emissão de CT-e com aplicativo do contribuinte;
											3- emissão CT-e pelo contribuinte com aplicativo fornecido pelo Fisco.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="verProc">
										<xs:annotation>
											<xs:documentation>Versão do processo de emissão</xs:documentation>
											<xs:documentation>Iinformar a versão do aplicativo emissor de CT-e.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="20"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="cMunEnv" type="TCodMunIBGE">
										<xs:annotation>
											<xs:documentation>Código do Município de envio do CT-e (de onde o documento foi transmitido)</xs:documentation>
											<xs:documentation>Utilizar a tabela do IBGE. Informar 9999999 para as operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="xMunEnv">
										<xs:annotation>
											<xs:documentation>Nome do Município de envio do CT-e (de onde o documento foi transmitido)</xs:documentation>
											<xs:documentation>Informar PAIS/Municipio para as operações com o exterior.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="2"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="UFEnv" type="TUf">
										<xs:annotation>
											<xs:documentation>Sigla da UF de envio do CT-e (de onde o documento foi transmitido)</xs:documentation>
											<xs:documentation>Informar 'EX' para operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="modal" type="TModTranspOS">
										<xs:annotation>
											<xs:documentation>Modal do CT-e OS</xs:documentation>
											<xs:documentation>Preencher com:
01-Rodoviário;
02- Aéreo;
03 - Aquaviário;
04 - Ferroviário.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="tpServ">
										<xs:annotation>
											<xs:documentation>Tipo do Serviço</xs:documentation>
											<xs:documentation>Preencher com: 

6 - Transporte de Pessoas;
7 - Transporte de Valores;
8 - Excesso de Bagagem.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="6"/>
												<xs:enumeration value="7"/>
												<xs:enumeration value="8"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="indIEToma">
										<xs:annotation>
											<xs:documentation>Indicador da IE do tomador:
1 – Contribuinte ICMS;
2 – Contribuinte isento de inscrição;
9 – Não Contribuinte</xs:documentation>
											<xs:documentation>Aplica-se ao tomador que for indicado no toma3 ou toma4</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:whiteSpace value="preserve"/>
												<xs:enumeration value="1"/>
												<xs:enumeration value="2"/>
												<xs:enumeration value="9"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="cMunIni" type="TCodMunIBGE" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Código do Município de início da prestação</xs:documentation>
											<xs:documentation>Utilizar a tabela do IBGE. Informar 9999999 para operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="xMunIni" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Nome do Município do início da prestação</xs:documentation>
											<xs:documentation>Informar 'EXTERIOR' para operações com o exterior.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="2"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="UFIni" type="TUf" minOccurs="0">
										<xs:annotation>
											<xs:documentation>UF do início da prestação</xs:documentation>
											<xs:documentation>Informar 'EX' para operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="cMunFim" type="TCodMunIBGE" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Código do Município de término da prestação</xs:documentation>
											<xs:documentation>Utilizar a tabela do IBGE. Informar 9999999 para operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="xMunFim" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Nome do Município do término da prestação</xs:documentation>
											<xs:documentation>Informar 'EXTERIOR' para operações com o exterior.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="2"/>
												<xs:maxLength value="60"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="UFFim" type="TUf" minOccurs="0">
										<xs:annotation>
											<xs:documentation>UF do término da prestação</xs:documentation>
											<xs:documentation>Informar 'EX' para operações com o exterior.</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="infPercurso" minOccurs="0" maxOccurs="25">
										<xs:annotation>
											<xs:documentation source=" Municípios onde ocorreram os carregamentos">Informações do Percurso do CT-e Outros Serviços</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="UFPer" type="TUf">
													<xs:annotation>
														<xs:documentation>Sigla das Unidades da Federação do percurso do veículo.</xs:documentation>
														<xs:documentation>Não é necessário repetir as UF de Início e Fim</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:sequence minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informar apenas
para tpEmis diferente de 1</xs:documentation>
										</xs:annotation>
										<xs:element name="dhCont" type="TDateTimeUTC">
											<xs:annotation>
												<xs:documentation>Data e Hora da entrada em contingência</xs:documentation>
												<xs:documentation>Informar a data e hora no formato AAAA-MM-DDTHH:MM:SS</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="xJust">
											<xs:annotation>
												<xs:documentation>Justificativa da entrada em contingência</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TString">
													<xs:minLength value="15"/>
													<xs:maxLength value="256"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="compl" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Dados complementares do CT-e para fins operacionais ou comerciais</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="xCaracAd" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Característica adicional do transporte</xs:documentation>
											<xs:documentation>Texto livre:
REENTREGA; DEVOLUÇÃO; REFATURAMENTO; etc</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="15"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xCaracSer" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Característica adicional do serviço</xs:documentation>
											<xs:documentation>Texto livre:
											ENTREGA EXPRESSA; LOGÍSTICA REVERSA; CONVENCIONAL; EMERGENCIAL; etc</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="30"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xEmi" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Funcionário emissor do CTe</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="20"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xObs" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Observações Gerais</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:minLength value="1"/>
												<xs:maxLength value="2000"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="ObsCont" minOccurs="0" maxOccurs="10">
										<xs:annotation>
											<xs:documentation>Campo de uso livre do contribuinte</xs:documentation>
											<xs:documentation>Informar o nome do campo no atributo xCampo e o conteúdo do campo no XTexto</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="xTexto">
													<xs:annotation>
														<xs:documentation>Conteúdo do campo</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="160"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
											<xs:attribute name="xCampo" use="required">
												<xs:annotation>
													<xs:documentation>Identificação do campo</xs:documentation>
												</xs:annotation>
												<xs:simpleType>
													<xs:restriction base="TString">
														<xs:minLength value="1"/>
														<xs:maxLength value="20"/>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
									<xs:element name="ObsFisco" minOccurs="0" maxOccurs="10">
										<xs:annotation>
											<xs:documentation>Campo de uso livre do contribuinte</xs:documentation>
											<xs:documentation>Informar o nome do campo no atributo xCampo e o conteúdo do campo no XTexto</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="xTexto">
													<xs:annotation>
														<xs:documentation>Conteúdo do campo</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:minLength value="1"/>
															<xs:maxLength value="60"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
											</xs:sequence>
											<xs:attribute name="xCampo" use="required">
												<xs:annotation>
													<xs:documentation>Identificação do campo</xs:documentation>
												</xs:annotation>
												<xs:simpleType>
													<xs:restriction base="TString">
														<xs:minLength value="1"/>
														<xs:maxLength value="20"/>
													</xs:restriction>
												</xs:simpleType>
											</xs:attribute>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="emit">
							<xs:annotation>
								<xs:documentation>Identificação do Emitente do CT-e OS</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="CNPJ" type="TCnpj">
										<xs:annotation>
											<xs:documentation>CNPJ do emitente</xs:documentation>
											<xs:documentation>Informar zeros não significativos</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="IE">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual do Emitente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIe"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="IEST" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual do Substituto Tributário</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIe"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão social ou Nome do emitente</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xFant" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Nome fantasia</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="enderEmit" type="TEndeEmi">
										<xs:annotation>
											<xs:documentation>Endereço do emitente</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="CRT" type="TCRT">
										<xs:annotation>
											<xs:documentation>Código do Regime Tributário</xs:documentation>
											<xs:documentation>Informar: 1=Simples Nacional; 
2=Simples Nacional, excesso sublimite de receita bruta;
3=Regime Normal;
4=Simples Nacional - Microempreendedor Individual – MEI.
</xs:documentation>
										</xs:annotation>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="toma" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações do Tomador/Usuário do Serviço</xs:documentation>
								<xs:documentation>Opcional para Excesso de Bagagem</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpjOpc">
											<xs:annotation>
												<xs:documentation>Número do CNPJ</xs:documentation>
												<xs:documentation>Em caso de empresa não estabelecida no Brasil, será informado o CNPJ com zeros.
												Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>Número do CPF</xs:documentation>
												<xs:documentation>Informar os zeros não significativos.</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
									<xs:element name="IE" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Inscrição Estadual</xs:documentation>
											<xs:documentation>Informar a IE do tomador ou ISENTO se tomador é contribuinte do ICMS isento de inscrição no cadastro de contribuintes do ICMS. Caso o tomador não seja contribuinte do ICMS não informar o conteúdo.</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TIeDest"/>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xNome">
										<xs:annotation>
											<xs:documentation>Razão social ou nome do tomador</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="xFant" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Nome fantasia</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="60"/>
												<xs:minLength value="2"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="fone" type="TFone" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Telefone</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="enderToma" type="TEndereco">
										<xs:annotation>
											<xs:documentation>Dados do endereço</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="email" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Endereço de email</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TEmail"/>
										</xs:simpleType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="vPrest">
							<xs:annotation>
								<xs:documentation>Valores da Prestação de Serviço</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="vTPrest" type="TDec_1302">
										<xs:annotation>
											<xs:documentation>Valor Total da Prestação do Serviço</xs:documentation>
											<xs:documentation>Pode conter zeros quando o CT-e for de complemento de ICMS</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="vRec" type="TDec_1302">
										<xs:annotation>
											<xs:documentation>Valor a Receber</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="Comp" minOccurs="0" maxOccurs="unbounded">
										<xs:annotation>
											<xs:documentation>Componentes do Valor da Prestação</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="xNome">
													<xs:annotation>
														<xs:documentation>Nome do componente</xs:documentation>
														<xs:documentation>Exxemplos: FRETE PESO, FRETE VALOR, SEC/CAT, ADEME, AGENDAMENTO, etc</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="TString">
															<xs:maxLength value="15"/>
															<xs:minLength value="1"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:element>
												<xs:element name="vComp" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor do componente</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="imp">
							<xs:annotation>
								<xs:documentation>Informações relativas aos Impostos</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:element name="ICMS" type="TImpOS">
										<xs:annotation>
											<xs:documentation>Informações relativas ao ICMS</xs:documentation>
											<xs:documentation/>
										</xs:annotation>
									</xs:element>
									<xs:element name="vTotTrib" type="TDec_1302" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Valor Total dos Tributos</xs:documentation>
										</xs:annotation>
									</xs:element>
									<xs:element name="infAdFisco" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informações adicionais de interesse do Fisco</xs:documentation>
											<xs:documentation>Norma referenciada, informações complementares, etc</xs:documentation>
										</xs:annotation>
										<xs:simpleType>
											<xs:restriction base="TString">
												<xs:maxLength value="2000"/>
												<xs:minLength value="1"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="ICMSUFFim" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informações do ICMS de partilha com a UF de término do serviço de transporte na operação interestadual</xs:documentation>
											<xs:documentation>Grupo a ser informado nas prestações interestaduais para consumidor final, não contribuinte do ICMS</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="vBCUFFim" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor da BC do ICMS na UF de término da prestação do serviço de transporte</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="pFCPUFFim" type="TDec_0302">
													<xs:annotation>
														<xs:documentation>Percentual do ICMS relativo ao Fundo de Combate à pobreza (FCP) na UF de término da prestação do serviço de transporte</xs:documentation>
														<xs:documentation>Alíquota adotada nas operações internas na UF do destinatário</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="pICMSUFFim" type="TDec_0302">
													<xs:annotation>
														<xs:documentation>Alíquota interna da UF de término da prestação do serviço de transporte</xs:documentation>
														<xs:documentation>Alíquota adotada nas operações internas na UF do destinatário</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="pICMSInter" type="TDec_0302">
													<xs:annotation>
														<xs:documentation>Alíquota interestadual das UF envolvidas</xs:documentation>
														<xs:documentation>Alíquota interestadual das UF envolvidas
</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vFCPUFFim" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor do ICMS relativo ao Fundo de Combate á Pobreza (FCP) da UF de término da prestação</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vICMSUFFim" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor do ICMS de partilha para a UF de término da prestação do serviço de transporte</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vICMSUFIni" type="TDec_1302">
													<xs:annotation>
														<xs:documentation>Valor do ICMS de partilha para a UF de início da prestação do serviço de transporte</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
									<xs:element name="infTribFed" minOccurs="0">
										<xs:annotation>
											<xs:documentation>Informações dos tributos federais</xs:documentation>
											<xs:documentation>Grupo a ser informado nas prestações interestaduais para consumidor final, não contribuinte do ICMS</xs:documentation>
										</xs:annotation>
										<xs:complexType>
											<xs:sequence>
												<xs:element name="vPIS" type="TDec_1302" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor do PIS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vCOFINS" type="TDec_1302" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor COFINS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vIR" type="TDec_1302" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor de Imposto de Renda</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vINSS" type="TDec_1302" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor do INSS</xs:documentation>
													</xs:annotation>
												</xs:element>
												<xs:element name="vCSLL" type="TDec_1302" minOccurs="0">
													<xs:annotation>
														<xs:documentation>Valor do CSLL</xs:documentation>
													</xs:annotation>
												</xs:element>
											</xs:sequence>
										</xs:complexType>
									</xs:element>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:choice>
							<xs:element name="infCTeNorm">
								<xs:annotation>
									<xs:documentation>Grupo de informações do CT-e OS Normal</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="infServico">
											<xs:annotation>
												<xs:documentation>Informações da Prestação do Serviço</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="xDescServ">
														<xs:annotation>
															<xs:documentation>Descrição do Serviço prestado</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:minLength value="1"/>
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="infQ" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Informações de quantidades da Carga do CT-e</xs:documentation>
															<xs:documentation>Para Transporte de Pessoas indicar número de passageiros, para excesso de bagagem e transporte de valores indicar número de Volumes/Malotes</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="qCarga" type="TDec_1104">
																	<xs:annotation>
																		<xs:documentation>Quantidade</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="infDocRef" minOccurs="0" maxOccurs="unbounded">
											<xs:annotation>
												<xs:documentation>Informações dos documentos referenciados</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:choice>
													<xs:sequence>
														<xs:element name="nDoc">
															<xs:annotation>
																<xs:documentation>Número </xs:documentation>
															</xs:annotation>
															<xs:simpleType>
																<xs:restriction base="TString">
																	<xs:minLength value="1"/>
																	<xs:maxLength value="20"/>
																</xs:restriction>
															</xs:simpleType>
														</xs:element>
														<xs:element name="serie" minOccurs="0">
															<xs:annotation>
																<xs:documentation>Série</xs:documentation>
															</xs:annotation>
															<xs:simpleType>
																<xs:restriction base="TString">
																	<xs:minLength value="1"/>
																	<xs:maxLength value="3"/>
																</xs:restriction>
															</xs:simpleType>
														</xs:element>
														<xs:element name="subserie" minOccurs="0">
															<xs:annotation>
																<xs:documentation>Subsérie</xs:documentation>
															</xs:annotation>
															<xs:simpleType>
																<xs:restriction base="TString">
																	<xs:minLength value="1"/>
																	<xs:maxLength value="3"/>
																</xs:restriction>
															</xs:simpleType>
														</xs:element>
														<xs:element name="dEmi" type="TData">
															<xs:annotation>
																<xs:documentation>Data de Emissão</xs:documentation>
																<xs:documentation>Formato AAAA-MM-DD</xs:documentation>
															</xs:annotation>
														</xs:element>
														<xs:element name="vDoc" type="TDec_1302" minOccurs="0">
															<xs:annotation>
																<xs:documentation>Valor Transportado</xs:documentation>
															</xs:annotation>
														</xs:element>
													</xs:sequence>
													<xs:element name="chBPe">
														<xs:annotation>
															<xs:documentation>Chave de acesso do BP-e que possui eventos excesso de bagagem</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TChDFe"/>
														</xs:simpleType>
													</xs:element>
												</xs:choice>
											</xs:complexType>
										</xs:element>
										<xs:element name="seg" minOccurs="0" maxOccurs="unbounded">
											<xs:annotation>
												<xs:documentation>Informações de Seguro da Carga</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="respSeg">
														<xs:annotation>
															<xs:documentation>Responsável pelo seguro</xs:documentation>
															<xs:documentation>Preencher com:

4 - Emitente do CT-e;

5 - Tomador de Serviço.
</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:whiteSpace value="preserve"/>
																<xs:minLength value="1"/>
																<xs:maxLength value="1"/>
																<xs:enumeration value="4"/>
																<xs:enumeration value="5"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="xSeg" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Nome da Seguradora</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:minLength value="1"/>
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="nApol" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Número da Apólice</xs:documentation>
															<xs:documentation>Obrigatório pela lei 11.442/07 (RCTRC)</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="TString">
																<xs:minLength value="1"/>
																<xs:maxLength value="20"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="infModal" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Informações do modal
Obrigatório para Pessoas e Bagagem</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:any processContents="skip">
														<xs:annotation>
															<xs:documentation>XML do modal
Insira neste local o XML específico do modal</xs:documentation>
															<xs:documentation>O elemento do tipo -any- permite estender o documento XML com elementos não especificados pelo schema.
													Insira neste local - any- o XML específico do modal (rodoviário). A especificação do schema XML para cada modal pode ser encontrada nos arquivos que acompanham este pacote de liberação:
											Rodoviário - ver arquivo CTeModalRodoviarioOS_v9.99

Onde v9.99 é a a designação genérica para a versão do arquivo. Por exemplo, o arquivo para o schema do modal Rodoviário na versão 4.00 será denominado "CTeModalRodoviarioOS_v4.00".</xs:documentation>
														</xs:annotation>
													</xs:any>
												</xs:sequence>
												<xs:attribute name="versaoModal" use="required">
													<xs:annotation>
														<xs:documentation>Versão do leiaute específico para o Modal</xs:documentation>
													</xs:annotation>
													<xs:simpleType>
														<xs:restriction base="xs:string">
															<xs:whiteSpace value="preserve"/>
															<xs:pattern value="4\.(0[0-9]|[1-9][0-9])"/>
														</xs:restriction>
													</xs:simpleType>
												</xs:attribute>
											</xs:complexType>
										</xs:element>
										<xs:element name="infCteSub" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Informações do CT-e de substituição </xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="chCte">
														<xs:annotation>
															<xs:documentation>Chave de acesso do CT-e a ser substituído (original)</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:pattern value="[0-9]{44}"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="refCTeCanc" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Chave de acesso do CT-e Cancelado
Somente para Transporte de Valores</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="TChDFe"/>
											</xs:simpleType>
										</xs:element>
										<xs:element name="cobr" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Dados da cobrança do CT-e</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="fat" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Dados da fatura</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="nFat" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Número da fatura</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:minLength value="1"/>
																			<xs:maxLength value="60"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="vOrig" type="TDec_1302Opc" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Valor original da fatura</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="vDesc" type="TDec_1302Opc" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Valor do desconto da fatura</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="vLiq" type="TDec_1302Opc" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Valor líquido da fatura</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="dup" minOccurs="0" maxOccurs="unbounded">
														<xs:annotation>
															<xs:documentation>Dados das duplicatas</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="nDup" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Número da duplicata</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:maxLength value="60"/>
																			<xs:minLength value="1"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="dVenc" type="TData" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Data de vencimento da duplicata (AAAA-MM-DD)</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="vDup" type="TDec_1302Opc" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Valor da duplicata</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="infGTVe" minOccurs="0" maxOccurs="unbounded">
											<xs:annotation>
												<xs:documentation>Informações das GTV-e relacionadas ao CT-e OS</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="chCTe">
														<xs:annotation>
															<xs:documentation>Chave de acesso da GTV-e</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:pattern value="[0-9]{44}"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="Comp" maxOccurs="unbounded">
														<xs:annotation>
															<xs:documentation>Componentes do Valor da GTVe</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="tpComp">
																	<xs:annotation>
																		<xs:documentation>Tipo do Componente</xs:documentation>
																		<xs:documentation>1-Custodia
2-Embarque
3-Tempo de espera
4-Malote
5-Ad Valorem
6-Outros</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:whiteSpace value="preserve"/>
																			<xs:enumeration value="1"/>
																			<xs:enumeration value="2"/>
																			<xs:enumeration value="3"/>
																			<xs:enumeration value="4"/>
																			<xs:enumeration value="5"/>
																			<xs:enumeration value="6"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="vComp" type="TDec_1302">
																	<xs:annotation>
																		<xs:documentation>Valor do componente</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="xComp" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Nome do componente (informar apenas para outros)</xs:documentation>
																		<xs:documentation>Exemplos: FRETE PESO, FRETE VALOR, SEC/CAT, ADEME, AGENDAMENTO, etc</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="TString">
																			<xs:maxLength value="15"/>
																			<xs:minLength value="0"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="infCteComp" maxOccurs="10">
								<xs:annotation>
									<xs:documentation>Detalhamento do CT-e complementado</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="chCTe" type="TChDFe">
											<xs:annotation>
												<xs:documentation>Chave do CT-e complementado</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:choice>
						<xs:element name="autXML" minOccurs="0" maxOccurs="10">
							<xs:annotation>
								<xs:documentation>Autorizados para download do XML do DF-e</xs:documentation>
								<xs:documentation>Informar CNPJ ou CPF. Preencher os zeros não significativos.</xs:documentation>
							</xs:annotation>
							<xs:complexType>
								<xs:sequence>
									<xs:choice>
										<xs:element name="CNPJ" type="TCnpj">
											<xs:annotation>
												<xs:documentation>CNPJ do autorizado</xs:documentation>
												<xs:documentation>Informar zeros não significativos</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="CPF" type="TCpf">
											<xs:annotation>
												<xs:documentation>CPF do autorizado</xs:documentation>
												<xs:documentation>Informar zeros não significativos</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:choice>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
						<xs:element name="infRespTec" type="TRespTec" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Informações do Responsável Técnico pela emissão do DF-e</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
					<xs:attribute name="versao" use="required">
						<xs:annotation>
							<xs:documentation>Versão do leiaute</xs:documentation>
							<xs:documentation>Ex: "4.00"</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="TVerCTe"/>
						</xs:simpleType>
					</xs:attribute>
					<xs:attribute name="Id" use="required">
						<xs:annotation>
							<xs:documentation>Identificador da tag a ser assinada</xs:documentation>
							<xs:documentation>Informar a chave de acesso do CT-e OS e precedida do literal "CTe"</xs:documentation>
						</xs:annotation>
						<xs:simpleType>
							<xs:restriction base="xs:ID">
								<xs:pattern value="CTe[0-9]{44}"/>
							</xs:restriction>
						</xs:simpleType>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="infCTeSupl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Informações suplementares do CT-e</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="qrCodCTe">
							<xs:annotation>
								<xs:documentation>Texto com o QR-Code impresso no DACTE</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:minLength value="50"/>
									<xs:maxLength value="1000"/>
									<xs:pattern value="((HTTPS?|https?)://.*\?chCTe=[0-9]{44}&amp;tpAmb=[1-2](&amp;sign=[!-ÿ]{1}[ -ÿ]{0,}[!-ÿ]{1}|[!-ÿ]{1})?)"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element ref="ds:Signature"/>
		</xs:sequence>
		<xs:attribute name="versao" use="required">
			<xs:annotation>
				<xs:documentation>Versão do leiaute</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="TVerCTe"/>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	<xs:complexType name="TEndeEmi">
		<xs:annotation>
			<xs:documentation>Tipo Dados do Endereço</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="xLgr">
				<xs:annotation>
					<xs:documentation>Logradouro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="nro">
				<xs:annotation>
					<xs:documentation>Número</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xCpl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Complemento</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xBairro">
				<xs:annotation>
					<xs:documentation>Bairro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cMun" type="TCodMunIBGE">
				<xs:annotation>
					<xs:documentation>Código do município (utilizar a tabela do IBGE)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMun">
				<xs:annotation>
					<xs:documentation>Nome do município</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="CEP" minOccurs="0">
				<xs:annotation>
					<xs:documentation>CEP</xs:documentation>
					<xs:documentation>Informar zeros não significativos</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{8}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UF" type="TUF_sem_EX">
				<xs:annotation>
					<xs:documentation>Sigla da UF</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="fone" type="TFone" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Telefone</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TEndereco">
		<xs:annotation>
			<xs:documentation>Tipo Dados do Endereço</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="xLgr">
				<xs:annotation>
					<xs:documentation>Logradouro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="255"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="nro">
				<xs:annotation>
					<xs:documentation>Número</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xCpl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Complemento</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xBairro">
				<xs:annotation>
					<xs:documentation>Bairro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cMun" type="TCodMunIBGE">
				<xs:annotation>
					<xs:documentation>Código do município (utilizar a tabela do IBGE)</xs:documentation>
					<xs:documentation>Informar 9999999 para operações com o exterior.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMun">
				<xs:annotation>
					<xs:documentation>Nome do município</xs:documentation>
					<xs:documentation>Informar EXTERIOR para operações com o exterior.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="CEP" minOccurs="0">
				<xs:annotation>
					<xs:documentation>CEP</xs:documentation>
					<xs:documentation>Informar os zeros não significativos</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{8}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UF" type="TUf">
				<xs:annotation>
					<xs:documentation>Sigla da UF</xs:documentation>
					<xs:documentation>Informar EX para operações com o exterior.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cPais" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Código do país</xs:documentation>
					<xs:documentation>Utilizar a tabela do BACEN</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{1,4}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xPais" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Nome do país</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TEndernac">
		<xs:annotation>
			<xs:documentation>Tipo Dados do Endereço</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="xLgr">
				<xs:annotation>
					<xs:documentation>Logradouro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="255"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="nro">
				<xs:annotation>
					<xs:documentation>Número</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xCpl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Complemento</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xBairro">
				<xs:annotation>
					<xs:documentation>Bairro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cMun" type="TCodMunIBGE">
				<xs:annotation>
					<xs:documentation>Código do município (utilizar a tabela do IBGE), informar 9999999 para operações com o exterior.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMun">
				<xs:annotation>
					<xs:documentation>Nome do município, , informar EXTERIOR para operações com o exterior.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="CEP" minOccurs="0">
				<xs:annotation>
					<xs:documentation>CEP</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{8}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UF" type="TUf">
				<xs:annotation>
					<xs:documentation>Sigla da UF</xs:documentation>
					<xs:documentation>Informar EX para operações com o exterior.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TEndOrg">
		<xs:annotation>
			<xs:documentation>Tipo Dados do Endereço</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="xLgr">
				<xs:annotation>
					<xs:documentation>Logradouro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="nro">
				<xs:annotation>
					<xs:documentation>Número</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xCpl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Complemento</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xBairro">
				<xs:annotation>
					<xs:documentation>Bairro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cMun" type="TCodMunIBGE">
				<xs:annotation>
					<xs:documentation>Código do município (utilizar a tabela do IBGE), informar 9999999 para operações com o exterior.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMun">
				<xs:annotation>
					<xs:documentation>Nome do município</xs:documentation>
					<xs:documentation>Informar EXTERIOR para operações com o exterior.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="CEP" minOccurs="0">
				<xs:annotation>
					<xs:documentation>CEP</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{8}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UF" type="TUf">
				<xs:annotation>
					<xs:documentation>Sigla da UF</xs:documentation>
					<xs:documentation>Informar EX para operações com o exterior.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cPais" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Código do país</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{1,4}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xPais" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Nome do país</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="fone" type="TFone" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Telefone</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TLocal">
		<xs:annotation>
			<xs:documentation>Tipo Dados do Local de Origem ou Destino</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="cMun" type="TCodMunIBGE">
				<xs:annotation>
					<xs:documentation>Código do município (utilizar a tabela do IBGE)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMun">
				<xs:annotation>
					<xs:documentation>Nome do município</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UF" type="TUf">
				<xs:annotation>
					<xs:documentation>Sigla da UF</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TEndReEnt">
		<xs:annotation>
			<xs:documentation> Tipo Dados do Local de Retirada ou Entrega</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:choice>
				<xs:element name="CNPJ" type="TCnpj">
					<xs:annotation>
						<xs:documentation>Número do CNPJ</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="CPF" type="TCpf">
					<xs:annotation>
						<xs:documentation>Número do CPF</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:choice>
			<xs:element name="xNome">
				<xs:annotation>
					<xs:documentation>Razão Social ou Nome</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xLgr">
				<xs:annotation>
					<xs:documentation>Logradouro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="255"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="nro">
				<xs:annotation>
					<xs:documentation>Número</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xCpl" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Complemento</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="xBairro">
				<xs:annotation>
					<xs:documentation>Bairro</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="cMun" type="TCodMunIBGE">
				<xs:annotation>
					<xs:documentation>Código do município (utilizar a tabela do IBGE) </xs:documentation>
					<xs:documentation>Informar 9999999 para operações com o exterior.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xMun">
				<xs:annotation>
					<xs:documentation>Nome do município</xs:documentation>
					<xs:documentation>Informar EXTERIOR para operações com o exterior.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="UF" type="TUf">
				<xs:annotation>
					<xs:documentation>Sigla da UF</xs:documentation>
					<xs:documentation>Informar EX para operações com o exterior.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TImp">
		<xs:annotation>
			<xs:documentation>Tipo Dados do Imposto CT-e</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:element name="ICMS00">
				<xs:annotation>
					<xs:documentation>Prestação sujeito à tributação normal do ICMS</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>classificação Tributária do Serviço</xs:documentation>
								<xs:documentation>00 - tributação normal ICMS</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="00"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="vBC" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor da BC do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="pICMS" type="TDec_0302">
							<xs:annotation>
								<xs:documentation>Alíquota do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vICMS" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ICMS20">
				<xs:annotation>
					<xs:documentation>Prestação sujeito à tributação com redução de BC do ICMS</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>Classificação Tributária do serviço</xs:documentation>
								<xs:documentation>20 - tributação com BC reduzida do ICMS</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="20"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="pRedBC" type="TDec_0302Opc">
							<xs:annotation>
								<xs:documentation>Percentual de redução da BC</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vBC" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor da BC do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="pICMS" type="TDec_0302">
							<xs:annotation>
								<xs:documentation>Alíquota do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vICMS" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ICMS45">
				<xs:annotation>
					<xs:documentation>ICMS  Isento, não Tributado ou diferido</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>Classificação Tributária do Serviço</xs:documentation>
								<xs:documentation>Preencher com:
								40 - ICMS isenção;
								41 - ICMS não tributada;
								51 - ICMS diferido</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="40"/>
									<xs:enumeration value="41"/>
									<xs:enumeration value="51"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ICMS60">
				<xs:annotation>
					<xs:documentation>Tributação pelo ICMS60 - ICMS cobrado por substituição tributária.Responsabilidade do recolhimento do ICMS atribuído ao tomador ou 3º por ST</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>Classificação Tributária do Serviço</xs:documentation>
								<xs:documentation>60 - ICMS cobrado por substituição tributária</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="60"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="vBCSTRet" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor da BC do ICMS ST retido</xs:documentation>
								<xs:documentation>Valor do frete sobre o qual será calculado o ICMS a ser substituído na Prestação. </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vICMSSTRet" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor do ICMS ST retido</xs:documentation>
								<xs:documentation>Resultado da multiplicação do “vBCSTRet” x “pICMSSTRet” – que será valor do ICMS a ser retido pelo Substituto. Podendo o valor do ICMS a ser retido efetivamente, sofrer ajustes conforme a opção tributaria do transportador substituído. </xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="pICMSSTRet" type="TDec_0302">
							<xs:annotation>
								<xs:documentation>Alíquota do ICMS</xs:documentation>
								<xs:documentation>Percentual de Alíquota incidente na prestação de serviço de transporte.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vCred" type="TDec_1302" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Valor do Crédito outorgado/Presumido</xs:documentation>
								<xs:documentation>Preencher somente quando o transportador substituído, for optante pelo crédito outorgado previsto no Convênio 106/96 e corresponde ao percentual de 20% do valor do ICMS ST retido. </xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ICMS90">
				<xs:annotation>
					<xs:documentation>ICMS Outros</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>Classificação Tributária do Serviço</xs:documentation>
								<xs:documentation> 90 - ICMS outros</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="90"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="pRedBC" type="TDec_0302Opc" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Percentual de redução da BC</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vBC" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor da BC do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="pICMS" type="TDec_0302">
							<xs:annotation>
								<xs:documentation>Alíquota do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vICMS" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vCred" type="TDec_1302" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Valor do Crédito Outorgado/Presumido</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ICMSOutraUF">
				<xs:annotation>
					<xs:documentation>ICMS devido à UF de origem da prestação, quando  diferente da UF do emitente</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>Classificação Tributária do Serviço</xs:documentation>
								<xs:documentation>90 - ICMS Outra UF</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="90"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="pRedBCOutraUF" type="TDec_0302Opc" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Percentual de redução da BC</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vBCOutraUF" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor da BC do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="pICMSOutraUF" type="TDec_0302">
							<xs:annotation>
								<xs:documentation>Alíquota do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vICMSOutraUF" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor do ICMS devido outra UF</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ICMSSN">
				<xs:annotation>
					<xs:documentation>Simples Nacional</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>Classificação Tributária do Serviço</xs:documentation>
								<xs:documentation>90 - ICMS Simples Nacional</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="90"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="indSN">
							<xs:annotation>
								<xs:documentation>Indica se o contribuinte é Simples Nacional			1=Sim</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="1"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="TImpOS">
		<xs:annotation>
			<xs:documentation>Tipo Dados do Imposto para CT-e OS</xs:documentation>
		</xs:annotation>
		<xs:choice>
			<xs:element name="ICMS00">
				<xs:annotation>
					<xs:documentation>Prestação sujeito à tributação normal do ICMS</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>classificação Tributária do Serviço</xs:documentation>
								<xs:documentation>00 - tributação normal ICMS</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="00"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="vBC" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor da BC do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="pICMS" type="TDec_0302">
							<xs:annotation>
								<xs:documentation>Alíquota do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vICMS" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ICMS20">
				<xs:annotation>
					<xs:documentation>Prestação sujeito à tributação com redução de BC do ICMS</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>Classificação Tributária do serviço</xs:documentation>
								<xs:documentation>20 - tributação com BC reduzida do ICMS</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="20"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="pRedBC" type="TDec_0302Opc">
							<xs:annotation>
								<xs:documentation>Percentual de redução da BC</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vBC" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor da BC do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="pICMS" type="TDec_0302">
							<xs:annotation>
								<xs:documentation>Alíquota do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vICMS" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ICMS45">
				<xs:annotation>
					<xs:documentation>ICMS  Isento, não Tributado ou diferido</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>Classificação Tributária do Serviço</xs:documentation>
								<xs:documentation>Preencher com:
								40 - ICMS isenção;
								41 - ICMS não tributada;
								51 - ICMS diferido</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="40"/>
									<xs:enumeration value="41"/>
									<xs:enumeration value="51"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ICMS90">
				<xs:annotation>
					<xs:documentation>ICMS Outros</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>Classificação Tributária do Serviço</xs:documentation>
								<xs:documentation> 90 - Outros</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="90"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="pRedBC" type="TDec_0302Opc" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Percentual de redução da BC</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vBC" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor da BC do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="pICMS" type="TDec_0302">
							<xs:annotation>
								<xs:documentation>Alíquota do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vICMS" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vCred" type="TDec_1302" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Valor do Crédito Outorgado/Presumido</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ICMSOutraUF">
				<xs:annotation>
					<xs:documentation>ICMS devido à UF de origem da prestação, quando  diferente da UF do emitente</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>Classificação Tributária do Serviço</xs:documentation>
								<xs:documentation>90 - ICMS Outra UF</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="90"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="pRedBCOutraUF" type="TDec_0302Opc" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Percentual de redução da BC</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vBCOutraUF" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor da BC do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="pICMSOutraUF" type="TDec_0302">
							<xs:annotation>
								<xs:documentation>Alíquota do ICMS</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="vICMSOutraUF" type="TDec_1302">
							<xs:annotation>
								<xs:documentation>Valor do ICMS devido outra UF</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ICMSSN">
				<xs:annotation>
					<xs:documentation>Simples Nacional</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CST">
							<xs:annotation>
								<xs:documentation>Classificação Tributária do Serviço</xs:documentation>
								<xs:documentation>90 - ICMS Simples Nacional</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="90"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
						<xs:element name="indSN">
							<xs:annotation>
								<xs:documentation>Indica se o contribuinte é Simples Nacional			1=Sim</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:whiteSpace value="preserve"/>
									<xs:enumeration value="1"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="TUnidadeTransp">
		<xs:annotation>
			<xs:documentation>Tipo Dados Unidade de Transporte</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpUnidTransp" type="TtipoUnidTransp">
				<xs:annotation>
					<xs:documentation>Tipo da Unidade de Transporte</xs:documentation>
					<xs:documentation>1 - Rodoviário Tração
2 - Rodoviário Reboque
3 - Navio
4 - Balsa
5 - Aeronave
6 - Vagão
7 - Outros</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="idUnidTransp" type="TContainer">
				<xs:annotation>
					<xs:documentation>Identificação da Unidade de Transporte</xs:documentation>
					<xs:documentation>Informar a identificação conforme o tipo de unidade de transporte.
Por exemplo: para rodoviário tração ou reboque deverá preencher com a placa do veículo.
</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="lacUnidTransp" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Lacres das Unidades de Transporte</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="nLacre">
							<xs:annotation>
								<xs:documentation>Número do lacre</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TString">
									<xs:minLength value="1"/>
									<xs:maxLength value="20"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="infUnidCarga" type="TUnidCarga" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Informações das Unidades de Carga (Containeres/ULD/Outros)</xs:documentation>
					<xs:documentation>Dispositivo de carga utilizada (Unit Load Device - ULD) significa todo tipo de contêiner de carga, vagão, contêiner de avião, palete de aeronave com rede ou palete de aeronave com rede sobre um iglu. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="qtdRat" type="TDec_0302_0303" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Quantidade rateada (Peso,Volume)</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TUnidCarga">
		<xs:annotation>
			<xs:documentation>Tipo Dados Unidade de Carga</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="tpUnidCarga" type="TtipoUnidCarga">
				<xs:annotation>
					<xs:documentation>Tipo da Unidade de Carga</xs:documentation>
					<xs:documentation>1 - Container
2 - ULD
3 - Pallet
4 - Outros</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="idUnidCarga" type="TContainer">
				<xs:annotation>
					<xs:documentation>Identificação da Unidade de Carga</xs:documentation>
					<xs:documentation>Informar a identificação da unidade de carga, por exemplo: número do container.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="lacUnidCarga" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Lacres das Unidades de Carga</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:element name="nLacre">
							<xs:annotation>
								<xs:documentation>Número do lacre</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="TString">
									<xs:minLength value="1"/>
									<xs:maxLength value="20"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="qtdRat" type="TDec_0302_0303" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Quantidade rateada (Peso,Volume)</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TRespTec">
		<xs:annotation>
			<xs:documentation>Tipo Dados da Responsável Técnico</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="CNPJ" type="TCnpj">
				<xs:annotation>
					<xs:documentation>CNPJ da pessoa jurídica responsável técnica pelo sistema utilizado na emissão do documento fiscal eletrônico</xs:documentation>
					<xs:documentation>Informar o CNPJ da pessoa jurídica desenvolvedora do sistema utilizado na emissão do documento fiscal eletrônico.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="xContato">
				<xs:annotation>
					<xs:documentation>Nome da pessoa a ser contatada</xs:documentation>
					<xs:documentation>Informar o nome da pessoa a ser contatada na empresa desenvolvedora do sistema utilizado na emissão do documento fiscal eletrônico. No caso de pessoa física, informar o respectivo nome.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="TString">
						<xs:maxLength value="60"/>
						<xs:minLength value="2"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="email" type="TEmail">
				<xs:annotation>
					<xs:documentation>Email da pessoa jurídica a ser contatada</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="fone">
				<xs:annotation>
					<xs:documentation>Telefone da pessoa jurídica a ser contatada</xs:documentation>
					<xs:documentation>Preencher com o Código DDD + número do telefone.</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:whiteSpace value="preserve"/>
						<xs:pattern value="[0-9]{7,12}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:sequence minOccurs="0">
				<xs:element name="idCSRT">
					<xs:annotation>
						<xs:documentation>Identificador do código de segurança do responsável técnico</xs:documentation>
						<xs:documentation>Identificador do CSRT utilizado para geração do hash</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:pattern value="[0-9]{3}"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="hashCSRT">
					<xs:annotation>
						<xs:documentation>Hash do token do código de segurança do responsável técnico</xs:documentation>
						<xs:documentation>O hashCSRT é o resultado das funções SHA-1 e base64 do token CSRT fornecido pelo fisco + chave de acesso do DF-e. (Implementação em futura NT)

Observação: 28 caracteres são representados no schema como 20 bytes do tipo base64Binary</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:base64Binary">
							<xs:length value="20"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
			</xs:sequence>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="TCfop">
		<xs:annotation>
			<xs:documentation>Tipo CFOP</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[123567][0-9]([0-9][1-9]|[1-9][0-9])"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCListServ">
		<xs:annotation>
			<xs:documentation>Tipo Código da Lista de Serviços LC 116/2003</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="101"/>
			<xs:enumeration value="102"/>
			<xs:enumeration value="103"/>
			<xs:enumeration value="104"/>
			<xs:enumeration value="105"/>
			<xs:enumeration value="106"/>
			<xs:enumeration value="107"/>
			<xs:enumeration value="108"/>
			<xs:enumeration value="201"/>
			<xs:enumeration value="302"/>
			<xs:enumeration value="303"/>
			<xs:enumeration value="304"/>
			<xs:enumeration value="305"/>
			<xs:enumeration value="401"/>
			<xs:enumeration value="402"/>
			<xs:enumeration value="403"/>
			<xs:enumeration value="404"/>
			<xs:enumeration value="405"/>
			<xs:enumeration value="406"/>
			<xs:enumeration value="407"/>
			<xs:enumeration value="408"/>
			<xs:enumeration value="409"/>
			<xs:enumeration value="410"/>
			<xs:enumeration value="411"/>
			<xs:enumeration value="412"/>
			<xs:enumeration value="413"/>
			<xs:enumeration value="414"/>
			<xs:enumeration value="415"/>
			<xs:enumeration value="416"/>
			<xs:enumeration value="417"/>
			<xs:enumeration value="418"/>
			<xs:enumeration value="419"/>
			<xs:enumeration value="420"/>
			<xs:enumeration value="421"/>
			<xs:enumeration value="422"/>
			<xs:enumeration value="423"/>
			<xs:enumeration value="501"/>
			<xs:enumeration value="502"/>
			<xs:enumeration value="503"/>
			<xs:enumeration value="504"/>
			<xs:enumeration value="505"/>
			<xs:enumeration value="506"/>
			<xs:enumeration value="507"/>
			<xs:enumeration value="508"/>
			<xs:enumeration value="509"/>
			<xs:enumeration value="601"/>
			<xs:enumeration value="602"/>
			<xs:enumeration value="603"/>
			<xs:enumeration value="604"/>
			<xs:enumeration value="605"/>
			<xs:enumeration value="701"/>
			<xs:enumeration value="702"/>
			<xs:enumeration value="703"/>
			<xs:enumeration value="704"/>
			<xs:enumeration value="705"/>
			<xs:enumeration value="706"/>
			<xs:enumeration value="707"/>
			<xs:enumeration value="708"/>
			<xs:enumeration value="709"/>
			<xs:enumeration value="710"/>
			<xs:enumeration value="711"/>
			<xs:enumeration value="712"/>
			<xs:enumeration value="713"/>
			<xs:enumeration value="716"/>
			<xs:enumeration value="717"/>
			<xs:enumeration value="718"/>
			<xs:enumeration value="719"/>
			<xs:enumeration value="720"/>
			<xs:enumeration value="721"/>
			<xs:enumeration value="722"/>
			<xs:enumeration value="801"/>
			<xs:enumeration value="802"/>
			<xs:enumeration value="901"/>
			<xs:enumeration value="902"/>
			<xs:enumeration value="903"/>
			<xs:enumeration value="1001"/>
			<xs:enumeration value="1002"/>
			<xs:enumeration value="1003"/>
			<xs:enumeration value="1004"/>
			<xs:enumeration value="1005"/>
			<xs:enumeration value="1006"/>
			<xs:enumeration value="1007"/>
			<xs:enumeration value="1008"/>
			<xs:enumeration value="1009"/>
			<xs:enumeration value="1010"/>
			<xs:enumeration value="1101"/>
			<xs:enumeration value="1102"/>
			<xs:enumeration value="1103"/>
			<xs:enumeration value="1104"/>
			<xs:enumeration value="1201"/>
			<xs:enumeration value="1202"/>
			<xs:enumeration value="1203"/>
			<xs:enumeration value="1204"/>
			<xs:enumeration value="1205"/>
			<xs:enumeration value="1206"/>
			<xs:enumeration value="1207"/>
			<xs:enumeration value="1208"/>
			<xs:enumeration value="1209"/>
			<xs:enumeration value="1210"/>
			<xs:enumeration value="1211"/>
			<xs:enumeration value="1212"/>
			<xs:enumeration value="1213"/>
			<xs:enumeration value="1214"/>
			<xs:enumeration value="1215"/>
			<xs:enumeration value="1216"/>
			<xs:enumeration value="1217"/>
			<xs:enumeration value="1302"/>
			<xs:enumeration value="1303"/>
			<xs:enumeration value="1304"/>
			<xs:enumeration value="1305"/>
			<xs:enumeration value="1401"/>
			<xs:enumeration value="1402"/>
			<xs:enumeration value="1403"/>
			<xs:enumeration value="1404"/>
			<xs:enumeration value="1405"/>
			<xs:enumeration value="1406"/>
			<xs:enumeration value="1407"/>
			<xs:enumeration value="1408"/>
			<xs:enumeration value="1409"/>
			<xs:enumeration value="1410"/>
			<xs:enumeration value="1411"/>
			<xs:enumeration value="1412"/>
			<xs:enumeration value="1413"/>
			<xs:enumeration value="1501"/>
			<xs:enumeration value="1502"/>
			<xs:enumeration value="1503"/>
			<xs:enumeration value="1504"/>
			<xs:enumeration value="1505"/>
			<xs:enumeration value="1506"/>
			<xs:enumeration value="1507"/>
			<xs:enumeration value="1508"/>
			<xs:enumeration value="1509"/>
			<xs:enumeration value="1510"/>
			<xs:enumeration value="1511"/>
			<xs:enumeration value="1512"/>
			<xs:enumeration value="1513"/>
			<xs:enumeration value="1514"/>
			<xs:enumeration value="1515"/>
			<xs:enumeration value="1516"/>
			<xs:enumeration value="1517"/>
			<xs:enumeration value="1518"/>
			<xs:enumeration value="1601"/>
			<xs:enumeration value="1701"/>
			<xs:enumeration value="1702"/>
			<xs:enumeration value="1703"/>
			<xs:enumeration value="1704"/>
			<xs:enumeration value="1705"/>
			<xs:enumeration value="1706"/>
			<xs:enumeration value="1708"/>
			<xs:enumeration value="1709"/>
			<xs:enumeration value="1710"/>
			<xs:enumeration value="1711"/>
			<xs:enumeration value="1712"/>
			<xs:enumeration value="1713"/>
			<xs:enumeration value="1714"/>
			<xs:enumeration value="1715"/>
			<xs:enumeration value="1716"/>
			<xs:enumeration value="1717"/>
			<xs:enumeration value="1718"/>
			<xs:enumeration value="1719"/>
			<xs:enumeration value="1720"/>
			<xs:enumeration value="1721"/>
			<xs:enumeration value="1722"/>
			<xs:enumeration value="1723"/>
			<xs:enumeration value="1724"/>
			<xs:enumeration value="1801"/>
			<xs:enumeration value="1901"/>
			<xs:enumeration value="2001"/>
			<xs:enumeration value="2002"/>
			<xs:enumeration value="2003"/>
			<xs:enumeration value="2101"/>
			<xs:enumeration value="2201"/>
			<xs:enumeration value="2301"/>
			<xs:enumeration value="2401"/>
			<xs:enumeration value="2501"/>
			<xs:enumeration value="2502"/>
			<xs:enumeration value="2503"/>
			<xs:enumeration value="2504"/>
			<xs:enumeration value="2601"/>
			<xs:enumeration value="2701"/>
			<xs:enumeration value="2801"/>
			<xs:enumeration value="2901"/>
			<xs:enumeration value="3001"/>
			<xs:enumeration value="3101"/>
			<xs:enumeration value="3201"/>
			<xs:enumeration value="3301"/>
			<xs:enumeration value="3401"/>
			<xs:enumeration value="3501"/>
			<xs:enumeration value="3601"/>
			<xs:enumeration value="3701"/>
			<xs:enumeration value="3801"/>
			<xs:enumeration value="3901"/>
			<xs:enumeration value="4001"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TContainer">
		<xs:annotation>
			<xs:documentation>Tipo Número do Container</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[A-Z0-9]+"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TDocAssoc">
		<xs:annotation>
			<xs:documentation> Tipo Documento Associado</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="07"/>
			<xs:enumeration value="08"/>
			<xs:enumeration value="09"/>
			<xs:enumeration value="10"/>
			<xs:enumeration value="11"/>
			<xs:enumeration value="12"/>
			<xs:enumeration value="13"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TEmail">
		<xs:annotation>
			<xs:documentation>Tipo Email</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:minLength value="1"/>
			<xs:maxLength value="60"/>
			<xs:pattern value="[^@]+@[^\.]+\..+"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TFinCTe">
		<xs:annotation>
			<xs:documentation>Tipo Finalidade da CT-e</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="0"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="3"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TIdLote">
		<xs:annotation>
			<xs:documentation>Tipo Identificador de controle do envio do lote. Número seqüencial auto-incremental, de controle correspondente ao identificador único do lote enviado. A responsabilidade de gerar e controlar esse número é do próprio contribuinte.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{1,15}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TModDoc">
		<xs:annotation>
			<xs:documentation> Tipo Modelo do Documento</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="01"/>
			<xs:enumeration value="1B"/>
			<xs:enumeration value="02"/>
			<xs:enumeration value="2D"/>
			<xs:enumeration value="2E"/>
			<xs:enumeration value="04"/>
			<xs:enumeration value="06"/>
			<xs:enumeration value="07"/>
			<xs:enumeration value="08"/>
			<xs:enumeration value="8B"/>
			<xs:enumeration value="09"/>
			<xs:enumeration value="10"/>
			<xs:enumeration value="11"/>
			<xs:enumeration value="13"/>
			<xs:enumeration value="14"/>
			<xs:enumeration value="15"/>
			<xs:enumeration value="16"/>
			<xs:enumeration value="17"/>
			<xs:enumeration value="18"/>
			<xs:enumeration value="20"/>
			<xs:enumeration value="21"/>
			<xs:enumeration value="22"/>
			<xs:enumeration value="23"/>
			<xs:enumeration value="24"/>
			<xs:enumeration value="25"/>
			<xs:enumeration value="26"/>
			<xs:enumeration value="27"/>
			<xs:enumeration value="28"/>
			<xs:enumeration value="55"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TModTranspOS">
		<xs:annotation>
			<xs:documentation> Tipo Modal transporte Outros Serviços</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="01"/>
			<xs:enumeration value="02"/>
			<xs:enumeration value="03"/>
			<xs:enumeration value="04"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TModTransp">
		<xs:annotation>
			<xs:documentation> Tipo Modal transporte</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="01"/>
			<xs:enumeration value="02"/>
			<xs:enumeration value="03"/>
			<xs:enumeration value="04"/>
			<xs:enumeration value="05"/>
			<xs:enumeration value="06"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TRNTRC">
		<xs:annotation>
			<xs:documentation>Tipo RNTRC - Registro Nacional Transportadores Rodoviários de Carga</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{8}|ISENTO"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCIOT">
		<xs:annotation>
			<xs:documentation>Tipo CIOT - Código Identificador da Operação de Transporte</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[0-9]{12}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TCRT">
		<xs:annotation>
			<xs:documentation>Tipo Código Regime Tributário</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:minLength value="1"/>
			<xs:maxLength value="1"/>
			<xs:enumeration value="1"/>
			<xs:enumeration value="2"/>
			<xs:enumeration value="3"/>
			<xs:enumeration value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TProcEmi">
		<xs:annotation>
			<xs:documentation>Tipo processo de emissão do CT-e</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:enumeration value="0"/>
			<xs:enumeration value="3"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TTime">
		<xs:annotation>
			<xs:documentation>Tipo hora</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="(([0-1][0-9])|([2][0-3])):([0-5][0-9]):([0-5][0-9])"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TVerCTe">
		<xs:annotation>
			<xs:documentation>Tipo Versão do CT-e - 4.00</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="4\.00"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
