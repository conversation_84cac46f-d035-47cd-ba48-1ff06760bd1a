/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.nfe.deserializers;

import br.com.sasw.satwebservice.nfe.exception.NFeException;
import br.com.sasw.satwebservice.nfe.models.ItensModel;
import br.com.sasw.satwebservice.nfe.models.RejeicaoModel;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import java.lang.reflect.Type;

/**
 *
 * <AUTHOR>
 */
public class ItensDeserializer implements JsonDeserializer<ItensModel> {

    @Override
    public ItensModel deserialize(JsonElement json, Type typeOfT,
            JsonDeserializationContext context) throws NFeException {
        final JsonObject jsonObject = json.getAsJsonObject();

        ItensModel itens = new ItensModel();

        try {
            itens.setNumero(jsonObject.get("Numero").getAsInt());
            if (itens.getNumero() > 999) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.Numero")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.Numero", jsonObject.get("Numero").toString())));
        }

        try {
            itens.setCodigo(jsonObject.get("Codigo").getAsString());
            if (itens.getCodigo().length() > 60) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.Codigo")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.Codigo", jsonObject.get("Codigo").toString())));
        }

        try {
            itens.setDescriçao(jsonObject.get("Descriçao").getAsString());
            if (itens.getDescriçao().length() > 120) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.Descriçao")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.Descriçao", jsonObject.get("Descriçao").toString())));
        }

        try {
            itens.setCFOP(jsonObject.get("CFOP").getAsString());
            if (itens.getCFOP().length() > 4) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.CFOP")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.CFOP", jsonObject.get("CFOP").toString())));
        }

        try {
            itens.setUnCom(jsonObject.get("unCom").getAsString());
            if (itens.getUnCom().length() > 6) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.unCom")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.unCom", jsonObject.get("unCom").toString())));
        }

        try {
            itens.setQtde(jsonObject.get("Qtde").getAsFloat());
            if (itens.getQtde() > 99999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.Qtde")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.Qtde", jsonObject.get("Qtde").toString())));
        }

        try {
            itens.setValorUn(jsonObject.get("valorUn").getAsFloat());
            if (itens.getValorUn() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorUn")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorUn", jsonObject.get("valorUn").toString())));
        }

        try {
            itens.setValor(jsonObject.get("valor").getAsFloat());
            if (itens.getValor()> 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valor")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valor", jsonObject.get("valor").toString())));
        }

        try {
            itens.setValorFrete(jsonObject.get("valorFrete").getAsFloat());
            if (itens.getValorFrete()> 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorFrete")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorFrete", jsonObject.get("valorFrete").toString())));
        }

        try {
            itens.setValorSeg(jsonObject.get("valorSeg").getAsFloat());
            if (itens.getValorSeg()> 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorSeg")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorSeg", jsonObject.get("valorSeg").toString())));
        }

        try {
            itens.setValorDesc(jsonObject.get("valorDesc").getAsFloat());
            if (itens.getValorDesc()> 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorDesc")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorDesc", jsonObject.get("valorDesc").toString())));
        }

        try {
            itens.setValorOutros(jsonObject.get("valorOutros").getAsFloat());
            if (itens.getValorOutros()> 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorOutros")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorOutros", jsonObject.get("valorOutros").toString())));
        }

        try {
            itens.setIndTributo(jsonObject.get("indTributo").getAsInt());
            if (itens.getIndTributo()> 1) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.indTributo")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.indTributo", jsonObject.get("indTributo").toString())));
        }
        
        try {
            itens.setOri(jsonObject.get("Ori").getAsInt());
            if (itens.getOri() > 2) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.Ori")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.Ori", jsonObject.get("Ori").toString())));
        }

        try {
            itens.setCST(jsonObject.get("CST").getAsString());
            if (itens.getCST().length() > 2) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.CST")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.CST", jsonObject.get("CST").toString())));
        }

        try {
            itens.setAliqISS(jsonObject.get("aliqISS").getAsFloat());
            if (itens.getAliqISS() > 99999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.aliqISS")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.aliqISS", jsonObject.get("aliqISS").toString())));
        }

        try {
            itens.setValorISS(jsonObject.get("valorISS").getAsFloat());
            if (itens.getValorISS() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorISS")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorISS", jsonObject.get("valorISS").toString())));
        }

        try {
            itens.setAliqICMS(jsonObject.get("aliqICMS").getAsFloat());
            if (itens.getAliqICMS() > 99999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.aliqICMS")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.aliqICMS", jsonObject.get("aliqICMS").toString())));
        }

        try {
            itens.setValorICMS(jsonObject.get("valorICMS").getAsFloat());
            if (itens.getValorICMS() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorICMS")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorICMS", jsonObject.get("valorICMS").toString())));
        }

        try {
            itens.setValorBC(jsonObject.get("valorBC").getAsFloat());
            if (itens.getValorBC() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorBC")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorBC", jsonObject.get("valorBC").toString())));
        }

        try {
            itens.setAliqPIS(jsonObject.get("aliqPIS").getAsFloat());
            if (itens.getAliqPIS() > 99999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.aliqPIS")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.aliqPIS", jsonObject.get("aliqPIS").toString())));
        }

        try {
            itens.setValorPIS(jsonObject.get("valorPIS").getAsFloat());
            if (itens.getValorPIS() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorPIS")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorPIS", jsonObject.get("valorPIS").toString())));
        }

        try {
            itens.setAliqCOFINS(jsonObject.get("aliqCOFINS").getAsFloat());
            if (itens.getAliqCOFINS() > 99999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.aliqCOFINS")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.aliqCOFINS", jsonObject.get("aliqCOFINS").toString())));
        }

        try {
            itens.setValorCOFINS(jsonObject.get("valorCOFINS").getAsFloat());
            if (itens.getValorCOFINS() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorCOFINS")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorCOFINS", jsonObject.get("valorCOFINS").toString())));
        }

        try {
            itens.setAliqCSL(jsonObject.get("aliqCSL").getAsFloat());
            if (itens.getAliqCSL() > 99999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.aliqCSL")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.aliqCSL", jsonObject.get("aliqCSL").toString())));
        }

        try {
            itens.setValorCSL(jsonObject.get("valorCSL").getAsFloat());
            if (itens.getValorCSL() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorCSL")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorCSL", jsonObject.get("valorCSL").toString())));
        }

        try {
            itens.setAliqIR(jsonObject.get("aliqIR").getAsFloat());
            if (itens.getAliqIR() > 99999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.aliqIR")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.aliqIR", jsonObject.get("aliqIR").toString())));
        }

        try {
            itens.setValorIR(jsonObject.get("valorIR").getAsFloat());
            if (itens.getValorIR() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorIR")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorIR", jsonObject.get("valorIR").toString())));
        }

        try {
            itens.setValorRetISS(jsonObject.get("valorRetISS").getAsFloat());
            if (itens.getValorRetISS() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorRetISS")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorRetISS", jsonObject.get("valorRetISS").toString())));
        }

        try {
            itens.setValorRetICMS(jsonObject.get("valorRetICMS").getAsFloat());
            if (itens.getValorRetICMS() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorRetICMS")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorRetICMS", jsonObject.get("valorRetICMS").toString())));
        }

        try {
            itens.setValorRetPIS(jsonObject.get("valorRetPIS").getAsFloat());
            if (itens.getValorRetPIS() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorRetPIS")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorRetPIS", jsonObject.get("valorRetPIS").toString())));
        }

        try {
            itens.setValorRetCSL(jsonObject.get("valorRetCSL").getAsFloat());
            if (itens.getValorRetCSL() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorRetCSL")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorRetCSL", jsonObject.get("valorRetCSL").toString())));
        }

        try {
            itens.setValorRetIR(jsonObject.get("valorRetIR").getAsFloat());
            if (itens.getValorRetIR() > 999999999999999.99) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (e instanceof NullPointerException) {
                throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorRetIR")));
            }
            throw new NFeException(e.getMessage(), new NFeException.NFeError(jsonObject, new RejeicaoModel("NFE.Itens.valorRetIR", jsonObject.get("valorRetIR").toString())));
        }

        return itens;
    }
}
