/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.importacao.exceptions;

/**
 *
 * <AUTHOR>
 */
public class RotaException extends Exception{
    
    private static final long serialVersionUID = 7718828512143293558L;
    private final RotaErrorCode code;
    
    public RotaException(RotaErrorCode code) {
            super();
            this.code = code;
    }

    public RotaException(String message, Throwable cause, RotaErrorCode code) {
            super(message, cause);
            this.code = code;
    }

    public RotaException(String message, RotaErrorCode code) {
            super(message);
            this.code = code;
    }

    public RotaException(Throwable cause, RotaErrorCode code) {
            super(cause);
            this.code = code;
    }

    public RotaErrorCode getCode() {
            return this.code;
    }
        
    public static class RotaErrorCode implements ErrorCode{

        public RotaErrorCode() {
        }

        public RotaErrorCode(int code) {
            this.code = code;
        }
        
        private int code;
        private String status;

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        @Override
        public String getStatus() {
            switch(this.code){
                case 1:
                    status = "Trajeto inserido com sucesso.";
                    break;
                case 2:
                    status = "Customer successfully deactivated.";
                    break;
                case 3:
                    status = "Request for inclusion of inactive customer. Customer reactivated.";
                    break;
                case 6:
                    status = "Trajeto já inserido. Inserção cancelada.";
                    break;
                case 7:
                    status = "Inactive/Non-existent customer. Deactivation not processed.";
                    break;
                case 9:
                    status = "Insufficient/Incorrect data. Inclusion not processed.";
                    break;
                case 0:
                    status = "Erro na estrutura do JSON.";
                    break;
                default:
                    status = "Unknown error.";
            }
            return status;
        }
    }
}
