<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by <EMAIL> (PROCERGS) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.portalfiscal.inf.br/cte" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://www.portalfiscal.inf.br/cte" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="eventoCTeTiposBasico_v3.00.xsd"/>
	<xs:element name="evPrestDesacordo">
		<xs:annotation>
			<xs:documentation>Schema XML de validação do evento Prestação do Serviço em Desacordo 610110</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="descEvento">
					<xs:annotation>
						<xs:documentation>Descrição do Evento - “Prestação do Serviço em Desacordo”</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:whiteSpace value="preserve"/>
							<xs:enumeration value="Prestação do Serviço em Desacordo"/>
							<xs:enumeration value="Prestacao do Servico em Desacordo"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="indDesacordoOper">
					<xs:annotation>
						<xs:documentation>Indicador de operação em desacordo</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:enumeration value="1"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="xObs">
					<xs:annotation>
						<xs:documentation>Observações do tomador</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:minLength value="15"/>
							<xs:maxLength value="255"/>
							<xs:whiteSpace value="preserve"/>
							<xs:pattern value="[!-ÿ]{1}[ -ÿ]{0,}[!-ÿ]{1}|[!-ÿ]{1}"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
