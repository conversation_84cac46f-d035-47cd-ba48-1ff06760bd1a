/*
 */
package br.com.sasw.satwebservice.itauapi;

import Arquivo.ArquivoLog;
import Dados.Consulta;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasDaos.FiliaisDao;
import br.com.sasw.pacotesuteis.sasbeans.TOKENS;
import br.com.sasw.pacotesuteis.sasdaos.TOKENSDao;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import static br.com.sasw.satwebservice.Utilidades.obterParametros;
import br.com.sasw.satwebservice.executasvc.ValidarToken;
import br.com.sasw.satwebservice.messages.Messages;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.sql.Date;
import java.sql.ResultSet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Map;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import org.json.JSONObject;
import sun.security.util.PropertyExpander;

/**
 *
 * <AUTHOR>
 */
@Path("/itauapi/")
public class IntegracaoItau {

    private static boolean isComparaHora(String vHoraAtual, String vHoraCorte) throws ParseException {
        boolean vEventual = false;
        if (!new SimpleDateFormat("HH:mm").parse(vHoraCorte).before(new SimpleDateFormat("HH:mm").parse(vHoraAtual))) {
            vEventual = true;
        }
        return vEventual;
    }

    private final ArquivoLog logerro;
    private ArquivoLog logexecucao;
    private ArquivoLog arquivohtml;
    private final SasPoolPersistencia pool;
    private String caminho;
    private String caminhoweb;
    private final Messages messages;
    private final FiliaisDao filiaisDao;
    private final TOKENSDao tokenDao;
    private ResultSet vResultadoConsulta;
    private String vRetorno;
    private String vRetornoCST;
    private String vNumPed;
    private String vNumeroControleAnt;
    private String vSeqIntegra;
    private String vDataConsulta;
    private Calendar calendar;

    public IntegracaoItau() {
        logerro = new ArquivoLog();
        pool = new SasPoolPersistencia();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ItauAPI"
                + "\\erro.txt";
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));

        messages = new Messages();

        filiaisDao = new FiliaisDao();
        tokenDao = new TOKENSDao();
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/obterTokenItau")
    public String obterTokenItau(String param) throws UnsupportedEncodingException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vClientID = (String) parametros.getOrDefault("clientid", null);
        String vClientSecret = (String) parametros.getOrDefault("clientsecret", null);
        String vSenhaCertificado = (String) parametros.getOrDefault("senha", null);
//        String vCNPJ_filial = (String) parametros.getOrDefault("cnpj_filial", null);
//        String vCodTransp = (String) parametros.getOrDefault("codtranspitau", null);
//        String vCodfil = (String) parametros.getOrDefault("codfilitau", null);
        String vBDx = (String) parametros.getOrDefault("parametro", null);
        String vData = (String) parametros.getOrDefault("data", null);
        vData = vData.substring(6, 8) + "/" + vData.substring(4, 6) + "/" + vData.substring(0, 4);//"20/04/2023"; 20230401

        // PRESERVE File vCertificado = new File("C:\\Clientes\\Preserve\\ITAUAPI\\Arquivos\\certificadop.crt");
        //File vCertificado = new File("C:\\Clientes\\CORPVS\\ITAUAPI\\Cert\\cert.p12");
        File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "", vSQLF = "", vTokenGerado = "";
        //Corpvs 
        //vSenhaCertificado = "Corp!@#@$#$%@1975BB";
        //vSenhaCertificado = "";
        try {

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENSDao tokensDao = new TOKENSDao();
            TOKENS vGerToken = new TOKENS();
            vGerToken.setBancoDados(vBDx);
            vGerToken.setModulo("SATMOB");
            vGerToken.setChave("WS");
            vGerToken.setData(getDataAtual("SQL"));
            vGerToken.setHora(getDataAtual("HORA"));
            vTokenGerado = tokensDao.gerarToken(vGerToken, dbsatellite);
            TOKENS qToken = this.tokenDao.obterToken(vTokenGerado, dbsatellite);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ItauApi\\"
                        + getDataAtual("SQL") + "\\" + qToken.getBancoDados() + "\\obterTokenItau.txt";

            }

            try {
                GetAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                //vjnRetorno.put("token", vRetorno);                
                String vToken = vRetorno;
                consultaSuprimentosProc(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                //consultaRecolhimentoProc(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
//                conectaitau(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                //consultaTransportadora(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vRetorno);
                vjnRetorno.put("Retorno:", vRetorno);
            } catch (Exception e) {
                throw new Exception("PstServDao.listarPostosPessoa - " + e.getMessage() + "\r\n"
                        + vSQL);
            }
        } finally {
            vRetorno = vjnRetorno.toString();
            return vRetorno;
            /*
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
             */
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/obterTokenItauh")
    public String obterTokenItauh(String param) throws UnsupportedEncodingException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vClientID = (String) parametros.getOrDefault("clientid", null);
        String vClientSecret = (String) parametros.getOrDefault("clientsecret", null);
        String vSenhaCertificado = (String) parametros.getOrDefault("senha", null);
//        String vCNPJ_filial = (String) parametros.getOrDefault("cnpj_filial", null);
//        String vCodTransp = (String) parametros.getOrDefault("codtranspitau", null);
//        String vCodfil = (String) parametros.getOrDefault("codfilitau", null);
        String vBDx = (String) parametros.getOrDefault("parametro", null);
        String vData = (String) parametros.getOrDefault("data", null);
        vData = vData.substring(6, 8) + "/" + vData.substring(4, 6) + "/" + vData.substring(0, 4);//"20/04/2023"; 20230401

        // PRESERVE File vCertificado = new File("C:\\Clientes\\Preserve\\ITAUAPI\\Arquivos\\certificadop.crt");
        //File vCertificado = new File("C:\\Clientes\\CORPVS\\ITAUAPI\\Cert\\cert.p12");
        //File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
        File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "", vSQLF = "", vTokenGerado = "";
        //Corpvs 
        //vSenhaCertificado = "Corp!@#@$#$%@1975BB";
        //vSenhaCertificado = "acesso2023";
        try {

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENSDao tokensDao = new TOKENSDao();
            TOKENS vGerToken = new TOKENS();
            vGerToken.setBancoDados(vBDx);
            vGerToken.setModulo("SATMOB");
            vGerToken.setChave("WS");
            vGerToken.setData(getDataAtual("SQL"));
            vGerToken.setHora(getDataAtual("HORA"));
            vTokenGerado = tokensDao.gerarToken(vGerToken, dbsatellite);
            TOKENS qToken = this.tokenDao.obterToken(vTokenGerado, dbsatellite);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ItauApiH\\"
                        + getDataAtual("SQL") + "\\" + qToken.getBancoDados() + "\\obterTokenItauh.txt";

            }

            try {
                GetAccessTokenh(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                //vjnRetorno.put("token", vRetorno);                
                String vToken = vRetorno;
                //consultaSuprimentosProc(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                this.logerro.Grava("CONSULTA_RECPROC", caminho);
                consultaRecolhimentoProc(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                this.logerro.Grava("CONSULTA_REALIZADOS_INICIO", caminho);
                //Trata data do dia Verifica Feriado e coloca data dia anterior
                calendar = Calendar.getInstance();
                calendar.setTime(Date.valueOf(DataAtual.getDataAtual("SQL-L")));
                SimpleDateFormat formato = new SimpleDateFormat("dd/MM/yyyy");
                boolean vPedEventual;

                calendar.setTime(Date.valueOf(DataAtual.getDataAtual("SQL-L")));
                int vDia = calendar.get(Calendar.DAY_OF_WEEK);
                if (vDia == 2) {
                    calendar.add(Calendar.DATE, -3);
                } else {
                    calendar.add(Calendar.DATE, -1);
                }
                vDataConsulta = formato.format(calendar.getTime());
                GetAccessTokenh(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                //vjnRetorno.put("token", vRetorno);                
                vToken = vRetorno;
                consultaRecolhimentoRealizados(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataConsulta);
                this.logerro.Grava("CONSULTA_FIM_REALIZADA", caminho);
                this.logerro.Grava("CONFIRMA_REALIZA INICIO", caminho);
                //confirmaRecolhimentoRealizados(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                this.logerro.Grava("CONFIRMA_FIM_REALIZADA_FINAL", caminho);
//                conectaitau(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                //consultaTransportadora(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vRetorno);
                vjnRetorno.put("Retorno:", vRetorno);
            } catch (Exception e) {
                throw new Exception("PstServDao.listarPostosPessoa - " + e.getMessage() + "\r\n"
                        + vSQL);
            }
        } finally {
            vRetorno = vjnRetorno.toString();
            return vRetorno;
            /*
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
             */
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/obterTokenItauhrec")
    public String obterTokenItauhrec(String param) throws UnsupportedEncodingException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vClientID = (String) parametros.getOrDefault("clientid", null);
        String vClientSecret = (String) parametros.getOrDefault("clientsecret", null);
        String vSenhaCertificado = (String) parametros.getOrDefault("senha", null);
//        String vCNPJ_filial = (String) parametros.getOrDefault("cnpj_filial", null);
//        String vCodTransp = (String) parametros.getOrDefault("codtranspitau", null);
//        String vCodfil = (String) parametros.getOrDefault("codfilitau", null);
        String vBDx = (String) parametros.getOrDefault("parametro", null);
        String vData = (String) parametros.getOrDefault("data", null);
        vData = vData.substring(6, 8) + "/" + vData.substring(4, 6) + "/" + vData.substring(0, 4);//"20/04/2023"; 20230401

        // PRESERVE File vCertificado = new File("C:\\Clientes\\Preserve\\ITAUAPI\\Arquivos\\certificadop.crt");
        //File vCertificado = new File("C:\\Clientes\\CORPVS\\ITAUAPI\\Cert\\cert.p12");
        //     File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
        File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "", vSQLF = "", vTokenGerado = "";
        //Corpvs 
        //vSenhaCertificado = "Corp!@#@$#$%@1975BB";
        vSenhaCertificado = "acesso2023";
        try {

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENSDao tokensDao = new TOKENSDao();
            TOKENS vGerToken = new TOKENS();
            vGerToken.setBancoDados(vBDx);
            vGerToken.setModulo("SATMOB");
            vGerToken.setChave("WS");
            vGerToken.setData(getDataAtual("SQL"));
            vGerToken.setHora(getDataAtual("HORA"));
            vTokenGerado = tokensDao.gerarToken(vGerToken, dbsatellite);
            TOKENS qToken = this.tokenDao.obterToken(vTokenGerado, dbsatellite);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ItauApiH\\"
                        + getDataAtual("SQL") + "\\" + qToken.getBancoDados() + "\\obterTokenItauhrec.txt";

            }

            try {
                GetCSTTokenh(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                //vjnRetorno.put("token", vRetorno);                
                String vToken = vRetorno;
                //consultaSuprimentosProc(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                this.logerro.Grava("CONSULTA_RECPROC", caminho);
                consultaRecolhimentoProcH(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                this.logerro.Grava("CONSULTA_REALIZADOS_INICIO", caminho);
                //Trata data do dia Verifica Feriado e coloca data dia anterior
                calendar = Calendar.getInstance();
                calendar.setTime(Date.valueOf(DataAtual.getDataAtual("SQL-L")));
                SimpleDateFormat formato = new SimpleDateFormat("dd/MM/yyyy");
                boolean vPedEventual;

                calendar.setTime(Date.valueOf(DataAtual.getDataAtual("SQL-L")));
                int vDia = calendar.get(Calendar.DAY_OF_WEEK);
                if (vDia == 2) {
                    calendar.add(Calendar.DATE, -3);
                } else {
                    calendar.add(Calendar.DATE, -1);
                }
                vDataConsulta = formato.format(calendar.getTime());
//                GetAccessTokenh(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                //vjnRetorno.put("token", vRetorno);                
                //              vToken = vRetorno;
                GetCSTTokenh(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                //vjnRetorno.put("token", vRetorno);                
                vToken = vRetorno;
                consultaRecolhimentoRealizadosH(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataConsulta);
                this.logerro.Grava("CONSULTA_FIM_REALIZADA", caminho);
                this.logerro.Grava("CONFIRMA_REALIZA INICIO", caminho);
                //confirmaRecolhimentoRealizados(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                this.logerro.Grava("CONFIRMA_FIM_REALIZADA_FINAL", caminho);
//                conectaitau(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                //consultaTransportadora(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vRetorno);
                vjnRetorno.put("Retorno:", vRetorno);
            } catch (Exception e) {
                throw new Exception("PstServDao.listarPostosPessoa - " + e.getMessage() + "\r\n"
                        + vSQL);
            }
        } finally {
            vRetorno = vjnRetorno.toString();
            return vRetorno;
            /*
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
             */
        }
    }

    //Especifico Custodia Itau
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/obterCustodiaItauh")
    public String obterCustodiaItauh(String param) throws UnsupportedEncodingException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vClientID = (String) parametros.getOrDefault("clientid", null);
        String vClientSecret = (String) parametros.getOrDefault("clientsecret", null);
        String vSenhaCertificado = (String) parametros.getOrDefault("senha", null);
//        String vCNPJ_filial = (String) parametros.getOrDefault("cnpj_filial", null);
//        String vCodTransp = (String) parametros.getOrDefault("codtranspitau", null);
//        String vCodfil = (String) parametros.getOrDefault("codfilitau", null);
        String vBDx = (String) parametros.getOrDefault("parametro", null);
        String vData = (String) parametros.getOrDefault("data", null);
        String vDataCst = (String) parametros.getOrDefault("datacst", null);
        String vComando = (String) parametros.getOrDefault("comando", null);
        String vChave = (String) parametros.getOrDefault("chave", null);
        String vEndPoint = (String) parametros.getOrDefault("endpoint", null);
        String vStatus = (String) parametros.getOrDefault("status", null);
        String vMensagem = (String) parametros.getOrDefault("mensagem", null);
        vData = vData.substring(6, 8) + "/" + vData.substring(4, 6) + "/" + vData.substring(0, 4);//"20/04/2023"; 20230401
        vDataCst = vDataCst.substring(6, 8) + "/" + vDataCst.substring(4, 6) + "/" + vDataCst.substring(0, 4);//"20/04/2023"; 20230401

        // PRESERVE File vCertificado = new File("C:\\Clientes\\Preserve\\ITAUAPI\\Arquivos\\certificadop.crt");
        //File vCertificado = new File("C:\\Clientes\\CORPVS\\ITAUAPI\\Cert\\cert.p12");
        File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");  //Producao
        //File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "", vSQLF = "", vTokenGerado = "";
        //Corpvs 
        //vSenhaCertificado = "Corp!@#@$#$%@1975BB";
        //vSenhaCertificado = "acesso2023";
        try {

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENSDao tokensDao = new TOKENSDao();
            TOKENS vGerToken = new TOKENS();
            vGerToken.setBancoDados(vBDx);
            vGerToken.setModulo("SATMOB");
            vGerToken.setChave("WS");
            vGerToken.setData(getDataAtual("SQL"));
            vGerToken.setHora(getDataAtual("HORA"));
            vTokenGerado = tokensDao.gerarToken(vGerToken, dbsatellite);
            TOKENS qToken = this.tokenDao.obterToken(vTokenGerado, dbsatellite);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ItauApiH\\"
                        + getDataAtual("SQL") + "\\" + qToken.getBancoDados() + "\\obterCustodiaItauh.txt";

            }

            try {
                GetCSTTokenh(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                //vjnRetorno.put("token", vRetorno);                
                String vToken = vRetorno;
                String vIndiceControle = "";
                vMensagem = vMensagem.replace("|", "=");
                vMensagem = vMensagem.replace("*", "&");
                this.logerro.Grava("Parametros:"
                        + "DataCST:" + vDataCst
                        + "Comando:" + vComando
                        + "Chave:" + vChave
                        + "EnPoint:" + vEndPoint
                        + "Status:" + vStatus
                        + "Mensage:" + vMensagem, caminho);
                vIndiceControle = vChave;
                if (vComando.equals("RECOLHIMENTO")) {
                    vRetornoCST = consultaRecolhimentoRealizadosH(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataConsulta);
                    this.logerro.Grava("CONSULTA_FIM_REALIZADA HOMOLOG", caminho);
                    vRetorno = vRetornoCST;
                } else if (vComando.equals("CONSULTAR")) {
                    //consultaSuprimentosProc(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                    if (vEndPoint.equals("01")) {
                        this.logerro.Grava("custodias_posicoes_parciais_consultar INI", caminho);
                        vRetornoCST = consultaPosicoesCST(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                        this.logerro.Grava("custodias_posicoes_parciais_consultar FIM", caminho);
                        vRetorno = vRetornoCST;
                    } else if (vEndPoint.equals("03")) {
                        this.logerro.Grava("custodias_posicoes_fechamentos_consultar INI", caminho);
                        vRetornoCST = consultaFechamentoCST(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                        this.logerro.Grava("custodias_posicoes_fechamentos_consultar FIM", caminho);
                        vRetorno = vRetornoCST;
                    } else if (vEndPoint.equals("06")) {
                        GetCSTTokenh(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                        //vjnRetorno.put("token", vRetorno);                
                        vToken = vRetorno;
                        this.logerro.Grava("custodias_posicoes_historicos_listas_consultar INI", caminho);
                        consultaListaCST(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataCst);
                        this.logerro.Grava("custodias_posicoes_historicos_listas_consultar Fim", caminho);
                        //Trata data do dia Verifica Feriado e coloca data dia anterior
                        calendar = Calendar.getInstance();
                        calendar.setTime(Date.valueOf(DataAtual.getDataAtual("SQL-L")));
                        SimpleDateFormat formato = new SimpleDateFormat("dd/MM/yyyy");

                        calendar.setTime(Date.valueOf(DataAtual.getDataAtual("SQL-L")));
                        int vDia = calendar.get(Calendar.DAY_OF_WEEK);
                        if (vDia == 2) {
                            calendar.add(Calendar.DATE, -3);
                        } else {
                            calendar.add(Calendar.DATE, -1);
                        }
                        vDataConsulta = formato.format(calendar.getTime());
                    } else if (vEndPoint.equals("07")) {
                        GetCSTTokenh(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                        //vjnRetorno.put("token", vRetorno);                
                        vToken = vRetorno;
                        this.logerro.Grava("custodias_posicoes_historicos_detalhes_consultar INI", caminho);
                        vRetornoCST = consultaListaDetalheCST(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataCst, vChave);
                        this.logerro.Grava("custodias_posicoes_historicos_detalhes_consultar Fim", caminho);
                        vRetorno = vRetornoCST;
                    } else if (vEndPoint.equals("09")) {
                        this.logerro.Grava("custodias_movimentacoes_pendentes_consultar INI", caminho);
                        vRetornoCST = consultaMovimentacaoPendente(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataCst);
                        this.logerro.Grava("custodias_movimentacoes_pendentes_consultar Fim", caminho);
                        vRetorno = vRetornoCST;
                    } else if (vEndPoint.equals("11")) {
                        this.logerro.Grava("custodias_movimentacoes_detalhes_consultar INI Text:" + vMensagem, caminho);
                        vRetornoCST = consultaMovimentacaoDetalhe(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataCst, vMensagem, vIndiceControle);
                        this.logerro.Grava("Param:" + vClientID + " - " + vClientSecret + " - DataCst: " + vDataCst
                                + "Mensagem: " + vMensagem
                                + "Indice_Controle: " + vIndiceControle, caminho);
                        this.logerro.Grava("custodias_movimentacoes_detalhes_consultar Fim", caminho);
                        vRetorno = vRetornoCST;
                    } else if (vEndPoint.equals("12")) {
                        this.logerro.Grava("custodias_movimentacoes_historicos_listas_consultar INI", caminho);
                        consultaMovimentacaoHistorico(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataCst);
                        this.logerro.Grava("custodias_movimentacoes_historicos_listas_consultar Fim", caminho);
                        vRetorno = vRetornoCST;
                    }
                } else if (vComando.equals("CANCELAR")) {
                    GetCSTTokenh(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                    //vjnRetorno.put("token", vRetorno);                
                    vToken = vRetorno;
                    CancelarConsultaCST(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData, vChave);
                } else if (vComando.equals("CONFIRMAR")) {
                    if (vEndPoint.equals("08")) {
                        this.logerro.Grava("custodias_posicoes_confirma_presumida INI", caminho);
                        vRetornoCST = confirmaPosicaoPresumida(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataCst, vStatus, vChave, vMensagem);
                        this.logerro.Grava("custodias_posicoes_confirma_presumida Fim", caminho);
                        vRetorno = vRetornoCST;
                    } else if (vEndPoint.equals("10")) {
                        this.logerro.Grava("custodias_movimentacoes_confirmar INI", caminho);
                        vRetornoCST = confirmaMovimentacoes(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataCst, vStatus, vChave, vMensagem);
                        this.logerro.Grava("custodias_movimentacoes_confirmar Fim", caminho);
                        vRetorno = vRetornoCST;
                    }
                } else if (vComando.equals("INCLUIR")) {
                    if (vEndPoint.equals("02")) {
                        this.logerro.Grava("custodias_posicoes_Incluir_Parcial INI", caminho);
                        if (vMensagem == null) {
                            vMensagem = "";
                        }
                        if (vMensagem.equals("")) {
                            vRetornoCST = incluirPosicaoParcial(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataCst, vStatus, vChave);
                        } else {
                            vRetornoCST = incluirPosicaoParcialjson(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataCst, vStatus, vChave, vMensagem);
                        }
                        this.logerro.Grava("custodias_posicoes_incluir_Parcial Fim", caminho);
                        vRetorno = vRetornoCST;
                    }else if (vEndPoint.equals("04")) {
                        this.logerro.Grava("custodias_posicoes_Incluir_Parcial INI", caminho);
                        if (vMensagem == null) {
                            vMensagem = "";
                        }
                        
                        vRetornoCST = incluirPosicaoFechamentojson(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vDataCst, vStatus, vChave, vMensagem);
                        
                        this.logerro.Grava("custodias_posicoes_incluir_Parcial Fim", caminho);
                        vRetorno = vRetornoCST;
                    }
                }

                vjnRetorno.put("Retorno:", vRetorno);
            } catch (Exception e) {
                throw new Exception("PstServDao.listarPostosPessoa - " + e.getMessage() + "\r\n"
                        + vSQL);
            }
        } finally {
            vRetorno = vjnRetorno.toString();
            return vRetorno;
            /*
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
             */
        }
    }

    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/obterTokenItaup")
    public String obterTokenItaup(String param) throws UnsupportedEncodingException {

        // TODO ler body em json
        Map parametros = obterParametros(URLDecoder.decode(param, Charset.forName("UTF-8").toString()));

        String vClientID = (String) parametros.getOrDefault("clientid", null);
        String vClientSecret = (String) parametros.getOrDefault("clientsecret", null);
        String vSenhaCertificado = (String) parametros.getOrDefault("senha", null);
//        String vCNPJ_filial = (String) parametros.getOrDefault("cnpj_filial", null);
//        String vCodTransp = (String) parametros.getOrDefault("codtranspitau", null);
//        String vCodfil = (String) parametros.getOrDefault("codfilitau", null);
        String vBDx = (String) parametros.getOrDefault("parametro", null);
        String vData = (String) parametros.getOrDefault("data", null);
        vData = vData.substring(6, 8) + "/" + vData.substring(4, 6) + "/" + vData.substring(0, 4);//"20/04/2023"; 20230401

        // PRESERVE File vCertificado = new File("C:\\Clientes\\Preserve\\ITAUAPI\\Arquivos\\certificadop.crt");
        //File vCertificado = new File("C:\\Clientes\\CORPVS\\ITAUAPI\\Cert\\cert.p12");
        File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");

        Persistencia dbpadrao, dbsatellite;

        JSONObject vjnRetorno = new JSONObject();

        String vSQL = "", vSQLF = "", vTokenGerado = "";
        //Corpvs 
        //vSenhaCertificado = "Corp!@#@$#$%@1975BB";
        vSenhaCertificado = "acesso2023";
        try {

            dbsatellite = this.pool.getConexao("SATELLITE");
            TOKENSDao tokensDao = new TOKENSDao();
            TOKENS vGerToken = new TOKENS();
            vGerToken.setBancoDados(vBDx);
            vGerToken.setModulo("SATMOB");
            vGerToken.setChave("WS");
            vGerToken.setData(getDataAtual("SQL"));
            vGerToken.setHora(getDataAtual("HORA"));
            vTokenGerado = tokensDao.gerarToken(vGerToken, dbsatellite);
            TOKENS qToken = this.tokenDao.obterToken(vTokenGerado, dbsatellite);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            dbsatellite.FechaConexao();

            ValidarToken.validarExistencia(qToken);
            ValidarToken.validarValidade(qToken);
            dbpadrao = this.pool.getConexao(qToken.getBancoDados());

            if (dbpadrao != null) { // Atualiza o caminho do log
                this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ItauApiH\\"
                        + getDataAtual("SQL") + "\\" + qToken.getBancoDados() + "\\obterTokenItauh.txt";

            }

            try {
                GetAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                //vjnRetorno.put("token", vRetorno);                
                String vToken = vRetorno;
                //consultaSuprimentosProc(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                this.logerro.Grava("CONSULTA_RECPROC", caminho);
                consultaRecolhimentoProc(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                this.logerro.Grava("CONSULTA_REALIZA", caminho);
                consultaRecolhimentoRealizados(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                this.logerro.Grava("CONSULTA_FIM_REALIZADA", caminho);
//                conectaitau(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vToken, vData);
                //consultaTransportadora(vClientID, vClientSecret, vCertificado, vSenhaCertificado, vRetorno);
                vjnRetorno.put("Retorno:", vRetorno);
            } catch (Exception e) {
                throw new Exception("PstServDao.listarPostosPessoa - " + e.getMessage() + "\r\n"
                        + vSQL);
            }
        } finally {
            vRetorno = vjnRetorno.toString();
            return vRetorno;
            /*
            return Response
                    .status(Response.Status.OK)
                    .type("application/json")
                    .entity(vjnRetorno.toString())
                    .build();
             */
        }
    }

    public String GetAccessToken(String clientId, String clientSecret, File certificado, String senhaCertificado) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();

        try {

            //URL url = new URL("https://sts.rdhi.com.br/api/oauth/token");
            URL url = new URL("https://sts.itau.com.br/api/oauth/token");  //Producao

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);

            // Add certificate
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            String body = "grant_type=client_credentials"
                    + "&client_id=" + clientId
                    + "&client_secret=" + clientSecret;

            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);

            if (!jsonObject.get("access_token").getAsString().equals(null)) {
                connection.disconnect();
                vRetorno = jsonObject.get("access_token").getAsString();
            } else {
                connection.disconnect();
                vRetorno = response.toString();
            }
            //vRetorno =  + bufferedReader.toString();

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String GetAccessTokenh(String clientId, String clientSecret, File certificado, String senhaCertificado) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();

        try {

            //URL url = new URL("https://sts.rdhi.com.br/api/oauth/token");
            URL url = new URL("https://sts.itau.com.br/api/oauth/token");  //Producao

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);

            // Add certificate
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            /*
            String body = "grant_type=client_credentials"
                    + "&client_id=5bb4bb63-48e6-43cf-8478-908d6d363578"
                    + "&client_secret=34c05187-dfc0-4c14-9ecb-e3c5bc89c5fc";
             */
            String body = "grant_type=client_credentials"
                    + "&client_id=" + clientId
                    + "&client_secret=" + clientSecret;

            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);

            if (!jsonObject.get("access_token").getAsString().equals(null)) {
                connection.disconnect();
                vRetorno = jsonObject.get("access_token").getAsString();
            } else {
                connection.disconnect();
                vRetorno = response.toString();
            }
            //vRetorno =  + bufferedReader.toString();

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String GetCSTTokenh(String clientId, String clientSecret, File certificado, String senhaCertificado) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();

        try {

            //URL url = new URL("https://sts.rdhi.com.br/api/oauth/token");
            URL url = new URL("https://sts.itau.com.br/api/oauth/token");  //Producao

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);

            // Add certificate
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            /*
            String body = "grant_type=client_credentials"
                    + "&client_id=5bb4bb63-48e6-43cf-8478-908d6d363578"
                    + "&client_secret=34c05187-dfc0-4c14-9ecb-e3c5bc89c5fc";
             */
            String body = "grant_type=client_credentials"
                    + "&client_id=" + clientId
                    + "&client_secret=" + clientSecret;

            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);

            if (!jsonObject.get("access_token").getAsString().equals(null)) {
                connection.disconnect();
                vRetorno = jsonObject.get("access_token").getAsString();
            } else {
                connection.disconnect();
                vRetorno = response.toString();
            }
            //vRetorno =  + bufferedReader.toString();

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    
        return response.toString();
    }

    public String obterAccessToken(String clientId, String clientSecret, File certificado, String senhaCertificado) throws MalformedURLException, IOException {

        HttpURLConnection conn = null;
        StringBuilder response = new StringBuilder();

        //URL targetUrl = new URL("https://sts.rdhi.com.br/api/oauth/token");
        URL targetUrl = new URL("https://sts.itau.com.br/api/oauth/token");

        HttpURLConnection httpConnection = (HttpURLConnection) targetUrl.openConnection();
        httpConnection.setDoOutput(true);
        httpConnection.setRequestMethod("POST");
        httpConnection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

        String input = "client_id=" + clientId + "&client_secret=" + clientSecret + "&grant_type=client_credentials";

        OutputStream outputStream = httpConnection.getOutputStream();
        outputStream.write(input.getBytes());
        outputStream.flush();
        int vret = httpConnection.getResponseCode();
        if (httpConnection.getResponseCode() != 200) {
            throw new RuntimeException("Failed : HTTP error code : "
                    + httpConnection.getResponseCode());
        }

        BufferedReader br = new BufferedReader(new InputStreamReader((conn.getInputStream())));

        String output;
        System.out.println("Output from Server .... \n");
        while ((output = br.readLine()) != null) {
            vRetorno = output;
        }
        vRetorno = response.toString();
        conn.disconnect();
        return response.toString();

    }

    public String consultaTransportadora(String clientId, String clientSecret, File certificado, String senhaCertificado, String vToken) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/consultachavetransportadoras");
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/consultachavetransportadoras");

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);
            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Authorization", "Bearer " + vToken);
            //connection.addRequestProperty("x-itau-correlationID", "a1e64241-7fdb-4d05-a7f6-c44febcdd8d9");
            //connection.addRequestProperty("x-itau-flowID", "1");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // Add certificate
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            /*
            String body = "cnpj_matriz=8787673000145"
                        + "&cnpj_filial=8787673000145"
                        + "&codigo_transportadora=162"
                        + "&codigo_filial=02";
             */
            String body = "{\"cnpj_matriz\":\"11179264000170\","
                    + "\"cnpj_filial\":\"11179264000170\","
                    + "\"codigo_transportadora\":\"160\","
                    + "\"codigo_filial\":\"02\"}";
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            int vret = connection.getResponseCode();
            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);
            if (!jsonObject.get("chave_unica").getAsString().equals(null)) {
                vRetorno = jsonObject.get("chave_unica").getAsString();
            } else {
                vRetorno = response.toString();
            }
            //vRetorno =  + bufferedReader.toString();

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
//                    " - "+Integer.toString(vret);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();

    }

    public String conectaitau(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {
        StringBuilder response = new StringBuilder();
        try {
            String urlString = ("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_processantes?data_atendimento=" + vData);
            URL url = new URL(urlString);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx"), "acesso2023".toCharArray());

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, "acesso2023".toCharArray());

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(keyManagerFactory.getKeyManagers(), null, null);

            connection.setSSLSocketFactory(sslContext.getSocketFactory());

            connection.setRequestMethod("GET");
            connection.setDoInput(true);

            response.append(connection.getContent().toString());

            InputStream inputStream = connection.getInputStream();
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            int vret = connection.getResponseCode();

            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
            vRetorno = bufferedReader.lines().toString() + "--" + response.toString();
        } catch (Exception e) {
            vRetorno = e.toString();
//                    " - "+Integer.toString(vret);
            e.printStackTrace();
        } finally {
            vRetorno = vRetorno;
        }

// read the response
//        vRetorno ="";
        return null;

    }

    public String consultaSuprimentosProc(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_processantes?data_atendimento=" + vData);
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_processantes?data_atendimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            Consulta SQLPdrLog;
            String vSQLLog;
            if (vResposta == 404) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'suprimentos_processantes', \n"
                        + "'data_atendimento=" + vData + "', \n"
                        + "'', \n"
                        + "'SEM RESPOSTA DE SUPRIMENTO', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

                InputStream vErro = connection.getErrorStream();
                String vErro2;
                BufferedReader br = null;

                /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
                 */
                while ((vErro2 = bufferedReader.readLine()) != null) {
                    response.append(vErro2);
                }

                //String body = "data_atendimento=" + vData;
                //OutputStream outputStream = connection.getOutputStream();
                //outputStream.write(body.toString().getBytes());
                //outputStream.close();            
//            String line = null;
//            while ((line = bufferedReader.readLine()) != null) {
//                response.append(line);
//            }            
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava(response.toString(), caminho);
                String vRespostaServer = response.toString();
                Persistencia dbpadrao, dbsatellite;
                //dbsatellite = this.pool.getConexao("SATELLITE");
                dbpadrao = this.pool.getConexao("SATPRESERVE");
                //dbsatellite.FechaConexao();  

                // Gravar JSON
                String vTotalRegistros = "";
                String vOrgao_centralizador = "";
                String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
                String vCodBanco = "";
                String vTipoEmpresa = "";
                String vCodAgencia = "";
                String vNome_agencia_origem = "";
                String vNumeroSolicitacao = "";
                String vNumeroMalote = "";
                String vNumeroControle = "";
                String vTipoDenominacao = "";
                String vDescricaoDEnominacao = "";
                String vHorarioAtendimento = "";
                String vLocal_Atendimento = "";
                String vStatusSuprimento = "";
                String vDescricaoStatus = "";
                String vIdentificacaoApropricao = "";
                String vDescricaoApropriacao = "";
                String vSQL = "";
                int vPedido = 0;
                int vQtdePed = 0;
                String vIndice_controle_solicitacao = "";

                JsonArray jPedidoA = jsonObject.getAsJsonArray("data");

                if (!jsonObject.get("total_registros").equals(null)) {
                    vOrgao_centralizador = jsonObject.get("orgao_centralizador").toString();
                    //vDataPed = jsonObject.get("data_solicitacao").toString();
                    for (int i = 0; i < jPedidoA.size(); i++) {
                        JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();

                        vCodBanco = jPedido.getAsJsonPrimitive("codigo_banco").getAsString();
                        vTipoEmpresa = jPedido.getAsJsonPrimitive("tipo_empresa").getAsString();
                        vCodAgencia = jPedido.getAsJsonPrimitive("codigo_agencia").getAsString();
                        vNome_agencia_origem = jPedido.getAsJsonPrimitive("nome_agencia_origem").getAsString();
                        vNumeroSolicitacao = jPedido.getAsJsonPrimitive("numero_solicitacao").getAsString();
                        vNumeroMalote = jPedido.getAsJsonPrimitive("numero_malote").getAsString();
                        vNumeroControle = jPedido.getAsJsonPrimitive("numero_controle").getAsString();
                        vTipoDenominacao = jPedido.getAsJsonPrimitive("tipo_denominacao").getAsString();
                        vDescricaoDEnominacao = jPedido.getAsJsonPrimitive("descricao_denominacao").getAsString();
                        vHorarioAtendimento = jPedido.getAsJsonPrimitive("horario_atendimento").getAsString();
                        vLocal_Atendimento = jPedido.getAsJsonPrimitive("local_atendimento").getAsString();
                        if (jsonObject.get("status_suprimento") != null) {
                            vStatusSuprimento = jPedido.getAsJsonPrimitive("status_suprimento").getAsString();
                        } else {
                            vStatusSuprimento = "N";
                        }
                        vDescricaoStatus = jPedido.getAsJsonPrimitive("descricao_status_suprimento").getAsString();
                        vIdentificacaoApropricao = jPedido.getAsJsonPrimitive("identificacao_apropriacao").getAsString();
                        vDescricaoApropriacao = jPedido.getAsJsonPrimitive("descricao_identificacao_apropriacao").getAsString();
                        vIndice_controle_solicitacao = jPedido.getAsJsonPrimitive("indice_controle_solicitacao").getAsString();
                        Consulta qTmpX, qTmpX2, qTmpXPed;

                        //Grava Log JSON
                        vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                                + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                + "'suprimentos_processantes', \n"
                                + "'data_atendimento=" + vData + "', \n"
                                + "'" + vIndice_controle_solicitacao + "', \n"
                                + "'" + vRespostaServer + "', \n"
                                + "'SATSERVER', \n"
                                + "'" + getDataAtual("SQL") + "', \n"
                                + "'" + getDataAtual("HORA") + "')";
                        SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                        SQLPdrLog.insert();

                        vSQL = "Select Max(Numero)+1 Numero from Pedido \n"
                                + " where CodFil = 1 ";

                        try {
                            qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                            qTmpX.select();
                        } catch (Exception e) {
                            logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                            throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                                    + vSQL);
                        }
                        int vConta = 0;
                        Consulta SQLPdr;

                        if (qTmpX.Proximo()) {
                            vConta++;
                            vPedido = qTmpX.getInt("NUMERO");
                            String vSQLPed = "(Select Count(*) from Pedido where Data = '" + vDataPed + "' and Flag_Excl <> '*' and PedidoCliente = '" + vNumeroControle + "')";

                            try {
                                vSQL = "Select Clientes.Codigo, Clientes.Nred, OS_Vig.OS, OS_Vig.CliDst, CliCxf.Nred NRedCxf, CliCxf.Codigo CodCliCxf, " + vSQLPed + " QtdePed from Clientes \n"
                                        + "Left Join OS_Vig on OS_Vig.Cliente = Clientes.Codigo\n"
                                        + "                and OS_Vig.CodFil = Clientes.CodFil\n"
                                        + "                and OS_Vig.Situacao = 'A'      \n"
                                        + "Left Join Clientes CliCxf on CliCXf.Codigo = '9990001' "
                                        + "                         and CliCxf.CodFil = Clientes.CodFil "
                                        + "where Clientes.InterfExt like '%" + vTipoEmpresa + " " + vCodAgencia + " " + vNome_agencia_origem + "%'";
                                qTmpX2 = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                qTmpX2.select();
                                if (!vNumeroSolicitacao.equals("")) {
                                    String vOS = "";
                                    if (qTmpX2.Proximo()) {
                                        vCodCli = qTmpX2.getString("Codigo");
                                        vNomeCli = qTmpX2.getString("Nred");
                                        vOS = qTmpX2.getString("OS");
                                        vQtdePed = qTmpX2.getInt("QtdePed");

                                    }
                                    if (vQtdePed == 0) {
                                        vSQL = "insert into Pedido(Numero, Codfil, Data, Tipo, Codcli1, Nred1, Hora1o, Hora2O, "
                                                + "Codcli2, Nred2, Hora1D, Hora2D, Solicitante, Valor, Obs, OperIncl, ClassifSrv, Dt_Incl, "
                                                + " hr_Incl, OS, PedidoCliente, Situacao,   Operador, Dt_alter, Hr_Alter, Flag_Excl)"
                                                + "Values(\n"
                                                + vPedido + ", \n"
                                                + "1" + ", \n"
                                                + "'" + vDataPed + "', \n"
                                                + "'T', \n"
                                                + "'" + "9990001" + "', \n"
                                                + "'" + "PRESERV CX FORTE" + "', \n"
                                                + "'08:00'" + ", \n"
                                                + "'18:00'" + ", \n"
                                                + "'" + vCodCli + "', \n"
                                                + "'" + vNomeCli + "', \n"
                                                + "'" + vHorarioAtendimento + "', \n"
                                                + "'" + vHorarioAtendimento + "', \n"
                                                + "'WS ITAU'" + ", \n"
                                                + "0" + ", \n"
                                                + "'IMPORTACAO'" + ", \n"
                                                + "'SATSERVER'" + ", \n"
                                                + "'V'" + ", \n"
                                                + "'" + getDataAtual("SQL") + "', \n"
                                                + "'" + getDataAtual("HORA") + "',\n"
                                                + "'" + vOS + "', \n"
                                                + "'" + vNumeroControle + "'" + ", \n"
                                                + "'PD'" + ", \n"
                                                + "'SATSERVER'" + ", \n"
                                                + "'" + getDataAtual("SQL") + "', \n"
                                                + "'" + getDataAtual("HORA") + "',\n"
                                                + "''" + ") \n";
                                        SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                        SQLPdr.insert();
                                    }
                                    //}
                                }
                            } catch (Exception e) {
                                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                throw new Exception("Pedido - " + e.getMessage() + "\r\n"
                                        + vSQL);
                            }

                        }
                        //Gravar Pedido

                        vSQL = "";
                        //Buscar Detalhes
                        if (vQtdePed == 0) {
                            consultaSuprimentosProcDet(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vPedido, vStatusSuprimento);
                        }
                    }
                    vRetorno = jsonObject.get("indice_controle_solicitacao")
                            + "-RespostaCompleta:" + response.toString();
                } else {
                    vRetorno = response.toString();
                }
                bufferedReader.close();
            }
            //vRetorno = response.toString() + bufferedReader.toString();            
        } catch (Exception e) {
            vRetorno = e.toString() + " - Resposta EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaSuprimentosProcDet(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vNumPed, int NumPedSat, String vStatus) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_detalhes?tipo_solicitante=1&indice_controle_solicitacao=" + vNumPed);
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_detalhes?tipo_solicitante=1&indice_controle_solicitacao=" + vNumPed);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            InputStream vErro = connection.getErrorStream();
            String vErro2;
            BufferedReader br = null;
            /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
             */
            Persistencia dbpadrao, dbsatellite;
            //dbsatellite = this.pool.getConexao("SATELLITE");
            dbpadrao = this.pool.getConexao("SATPRESERVE");

            String line = null;

            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }

            // Gravar JSON
            Consulta SQLPdrLog;
            String vSQLLog;
            String vRespostaServer = response.toString();
            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'suprimentos_detalhes', \n"
                    + "'indice_controle_solicitacao=" + vNumPed + "', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + vRespostaServer + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
            SQLPdrLog.insert();
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);

            String vDenominacao = "";
            String vHorario;
            String vDescricaoSuprimento;
            String vValorTotal;
            String vTipo;
            String vQtde;

            vDenominacao = jsonObject.get("tipo_denominacao").getAsString();
            vHorario = jsonObject.get("horario_atendimento").getAsString();
            vDescricaoSuprimento = jsonObject.get("descricao_suprimento").getAsString();
            vValorTotal = jsonObject.get("valor_total_malote_suprimento").getAsString();
            Consulta SQLPdr;
            String vSQL = "";
            if (vStatus == null) {
                vStatus = "S";
            } else {
                vStatus = "S";
            }
            if (jsonObject.get("descricao_suprimento").getAsString().equals("CONFIRMADO")) {
                JsonArray jPedidoA = jsonObject.getAsJsonArray("data");

                for (int i = 0; i < jPedidoA.size(); i++) {
                    vDenominacao = jsonObject.get("tipo_denominacao").getAsString();
                    JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();

                    vTipo = jPedido.getAsJsonPrimitive("valor_denominacao").getAsString();
                    vQtde = jPedido.getAsJsonPrimitive("quantidade_em_milheiros").getAsString();

                    //Gravar Pedido
                    try {
                        //if (!vQtde.equals("0")) {
                        if (vDenominacao.toString().equals("C")) {
                            vSQL = "insert into PedidoDN(Numero, Codfil, Codigo, Docto, Qtde) Values(\n"
                                    + NumPedSat + ", \n"
                                    + "1" + ", \n"
                                    + vTipo + ", \n"
                                    + "'IMP', \n"
                                    + vQtde + "*1000);Update Pedido Set Valor = " + vValorTotal + " where Numero = " + NumPedSat + " and codfil = 1;delete from PedidoDN where Numero = " + NumPedSat + " and codfil = 1 and Qtde = 0;\n";
                        } else {
                            if (vTipo.contains("1.0")) {
                                vTipo = "100";
                            } else {
                                vTipo = vTipo.replace("0.", "");
                            }
                            vSQL = "insert into PedidoMD(Numero, Codfil, Codigo, Docto, Qtde) Values(\n"
                                    + NumPedSat + ", \n"
                                    + "1" + ", \n"
                                    + vTipo + ", \n"
                                    + "'IMP', \n"
                                    + vQtde + "*1000);Update Pedido Set Valor = " + vValorTotal + " where Numero = " + NumPedSat + " and codfil = 1;delete from PedidoMD where Numero = " + NumPedSat + " and codfil = 1 and Qtde = 0;\n";
                        }
                        SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                        SQLPdr.insert();
                        //}
                    } catch (Exception e) {
                        logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                        throw new Exception("PedidoDN - " + e.getMessage() + "\r\n"
                                + vSQL);
                    }

                    //Buscar Detalhes
                }
                vRetorno = jsonObject.get("indice_controle_solicitacao")
                        + "-RespostaCompleta:" + response.toString();
            } else {
                //aqui inicia
                apropriaSuprimentosProc(clientId, clientSecret, certificado, senhaCertificado, Token, vNumPed, NumPedSat, vStatus);
                vSQL = " Update Pedido Set Valor = 0, Obs = 'CANCELADO' where Numero = " + NumPedSat + " and codfil = 1;\n";
                SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                SQLPdr.update();
                vRetorno = response.toString();

            }
            apropriaSuprimentosProc(clientId, clientSecret, certificado, senhaCertificado, Token, vNumPed, NumPedSat, vStatus);
            //vRetorno = response.toString() + bufferedReader.toString();

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String apropriaSuprimentosProc(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vNumPed, int NumPedSat, String vStatus) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            this.logerro.Grava("INICIA APROPRIACAO", caminho);
            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_apropriacoes/" + vNumPed+"?");                               
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_apropriacoes/" + vNumPed + "?");

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            this.logerro.Grava("APROPRIACAO URL:" + url.toString(), caminho);
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("PUT");
            connection.setDoInput(true);
            connection.setDoOutput(true);

            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();
            if (vStatus == null) {
                vStatus = "S";
            }

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            //this.logerro.Grava("RESPOSTA APROPRIACAO: " + Integer.toString(connection.getResponseCode()), caminho);
            /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
             */
            String body = "{\"status_solicitacao\":\"" + vStatus + "\"}";
            this.logerro.Grava("GRAVA BODY: " + body, caminho);

            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            InputStream vErro = connection.getErrorStream();
            String vErro2;
            BufferedReader br = null;

//            this.logerro.Grava("GRAVA RESPOSTA BODY: " + Integer.toString(vResposta), caminho);
            this.logerro.Grava("GRAVA RESPOSTA ERRO: " + vErro, caminho);
            String line = null;

            //          this.logerro.Grava("GRAVA RESPOSTA ERRO: " + vErro, caminho);
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }

            String vRespostaServer;
            vRespostaServer = response.toString();

            int vResposta = 0;
            vResposta = connection.getResponseCode();

            this.logerro.Grava("RESPOSTA_SERVER_APROPRIA: " + vRespostaServer + " - Cod: " + Integer.toString(vResposta), caminho);
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava(response.toString(), caminho);
            //Gravar JSON
            Consulta SQLPdrLog;
            String vSQLLog;

            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'suprimentos_apropriacoes', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + vRespostaServer + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
            SQLPdrLog.insert();

            String vDenominacao = "";
            String vHorario;
            String vDescricaoSuprimento;
            String vValorTotal;
            String vTipo;
            String vQtde;

            vRetorno = response.toString();

            this.logerro.Grava("SERVER_APROPRIACAO: " + vRetorno, caminho);

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
            e.printStackTrace();
            this.logerro.Grava("ERRO_SERVER_APROPRIA: " + vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaSuprimentosTransp(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_transportantes?data_atendimento=" + vData);
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_transportantes?data_atendimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("x-itau-apikey", clientId);
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);

            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            // Add certificate
            File p12 = vCertificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            //String body = "data_atendimento=" + vData;
            OutputStream outputStream = connection.getOutputStream();
            //outputStream.write(body.toString().getBytes());
            outputStream.close();

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
            vRetorno = response.toString() + bufferedReader.toString();

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaRecolhimentoProc(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_eventuais?data_atendimento=" + vData);
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_eventuais?data_atendimento=" + vData);
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_processantes?data_atendimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            //connection.addRequestProperty("x-itau-apikey", "5bb4bb63-48e6-43cf-8478-908d6d363578"); //Homolog
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("x-itau-apikey", clientId); //Homolog
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

//            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
//            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");
            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
//            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
//            senhaCertificado = "acesso2023";
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";

            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            int vResposta = 0;
            vResposta = connection.getResponseCode();

            this.logerro.Grava("RESPOSTA_CONSULTA_REALIZA:" + Integer.toString(vResposta), caminho);

            if (vResposta == 403) {
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                Token = vRetorno;
            }

            Consulta SQLPdrLog;
            String vSQLLog;

            if (vResposta == 404) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_eventuais?data_atendimento" + " ', \n"
                        + "'recolhimentos_eventuais?data_atendimento=" + vData + "', \n"
                        + "'" + "', \n"
                        + "'SEM RESPOSTA RECOLHIMENTO', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else {
                this.logerro.Grava("RESPOSTA_CONSULTA_REALIZA_EXISTE_EVENTUAIS RECEV:" + Integer.toString(vResposta), caminho);
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                InputStream vErro = connection.getErrorStream();
                String vErro2;
                BufferedReader br = null;
                /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                            br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
                 */
                while ((vErro2 = bufferedReader.readLine()) != null) {
                    response.append(vErro2);
                }
                //this.logerro.Grava("Erro2_CONSULTA_REALIZA:" + vErro2, caminho);
                this.logerro.Grava("JSON REALIZADO:" + response.toString(), caminho);
                //String body = "data_atendimento=" + vData;
                //OutputStream outputStream = connection.getOutputStream();
                //outputStream.write(body.toString().getBytes());
                //outputStream.close();            
//            String line = null;
//            while ((line = bufferedReader.readLine()) != null) {
//                response.append(line);
//            }
                String vRespostaServer = response.toString();
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava(response.toString(), caminho);

                Persistencia dbpadrao, dbsatellite;
                //dbsatellite = this.pool.getConexao("SATELLITE");
                dbpadrao = this.pool.getConexao("SATPRESERVE");
                //dbsatellite.FechaConexao();            

                String vTotalRegistros = "";
                String vOrgao_centralizador = "";
                String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
                String vCodBanco = "";
                String vTipoEmpresa = "";
                String vCodAgencia = "";
                String vNome_agencia_origem = "";
                String vNumeroSolicitacao = "";
                String vNumeroMalote = "";
                String vNumeroControle = "";
                String vTipoDenominacao = "";
                String vDescricaoDEnominacao = "";
                String vHorarioAtendimento = "";
                String vLocal_Atendimento = "";
                String vStatusSuprimento = "";
                String vDescricaoStatus = "";
                String vIdentificacaoApropricao = "";
                String vDescricaoApropriacao = "";
                String vIdentificadorCancelamento = "";
                String vSQL = "";
                int vPedido = 0;
                int vQtdePed = 0;
                String vIndice_controle_solicitacao = "";

                JsonArray jPedidoA = jsonObject.getAsJsonArray("data");

                if (!jsonObject.isJsonNull()) {
                    //vDataPed = jsonObject.get("data_solicitacao").toString();                
                    for (int i = 0; i < jPedidoA.size(); i++) {

                        JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();

                        JsonArray jPedidoB = jPedido.getAsJsonArray("registros");
                        if (!jPedidoB.isJsonNull()) {
                            this.logerro.Grava("Json entrou array: " + jPedido.toString(), caminho);
                            for (int z = 0; z < jPedidoB.size(); z++) {
                                JsonObject jPedidoC = jPedidoB.get(z).getAsJsonObject();
                                vCodBanco = jPedidoC.getAsJsonPrimitive("codigo_banco").getAsString();
                                vTipoEmpresa = jPedidoC.getAsJsonPrimitive("tipo_empresa").getAsString();
                                vCodAgencia = jPedidoC.getAsJsonPrimitive("codigo_agencia").getAsString();
                                vNome_agencia_origem = jPedidoC.getAsJsonPrimitive("nome_agencia").getAsString();
                                vOrgao_centralizador = jPedido.get("orgao_centralizador").getAsString();
                                vNumeroSolicitacao = jPedidoC.getAsJsonPrimitive("solicitacao").getAsString();
                                vHorarioAtendimento = jPedidoC.getAsJsonPrimitive("horario").getAsString();
                                vLocal_Atendimento = jPedidoC.getAsJsonPrimitive("local").getAsString();
                                vIdentificacaoApropricao = jPedidoC.getAsJsonPrimitive("identificador_apropriacao").getAsString();
                                vDescricaoApropriacao = jPedidoC.getAsJsonPrimitive("descricao_apropriacao").getAsString();
                                vIdentificadorCancelamento = jPedidoC.getAsJsonPrimitive("identificador_cancelamento").getAsString();
                                vIndice_controle_solicitacao = jPedidoC.getAsJsonPrimitive("indice_controle_solicitacao").getAsString();
                                Consulta qTmpX, qTmpX2, qTmpXPed;
                                this.logerro.Grava("vai gravar integra itau: " + jPedido.toString(), caminho);

                                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                        + "'recolhimentos_eventuais?data_atendimento" + "', \n"
                                        + "'recolhimentos_eventuais?data_atendimento=" + vData + "', \n"
                                        + "'" + vIndice_controle_solicitacao + "', \n"
                                        + "'" + vRespostaServer + "', \n"
                                        + "'SATSERVER', \n"
                                        + "'" + getDataAtual("SQL") + "', \n"
                                        + "'" + getDataAtual("HORA") + "')";
                                this.logerro.Grava("vai gravar SQL: " + vSQLLog, caminho);
                                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                                SQLPdrLog.insert();
                                this.logerro.Grava("vai gravou SQL: " + vSQLLog, caminho);
                                // Log JSON                                                                            
                                //Final log JSON
                                // Apropria Carlos 31/08/2023
                                /*
                                logexecucao.Grava("Situacao Apropria rec: " + vDescricaoApropriacao + " vIndiceControle: " + vIndice_controle_solicitacao, caminho);
                                if ((!vIndice_controle_solicitacao.equals("")) && (!vDescricaoApropriacao.equals("Apropriado"))) {*/
                                recolhimentos_eventuais_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vIdentificadorCancelamento);
                                //}
                                vSQL = "Select Max(Numero)+1 Numero from Pedido \n"
                                        + " where CodFil = 1 ";

                                try {
                                    qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                    qTmpX.select();
                                } catch (Exception e) {
                                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                    throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                                            + vSQL);
                                }
                                int vConta = 0;
                                Consulta SQLPdr;

                                if (qTmpX.Proximo()) {
                                    vConta++;
                                    vPedido = qTmpX.getInt("NUMERO");
                                    String vSQLPed = "(Select Count(*) from Pedido where Data = '" + vDataPed + "' and Solicitante = '" + vIndice_controle_solicitacao + "')";

                                    try {
                                        vSQL = "Select Clientes.Codigo, Clientes.Nred, OS_Vig.OS, OS_Vig.CliDst, CliCxf.Nred NRedCxf, CliCxf.Codigo CodCliCxf, " + vSQLPed + " QtdePed from Clientes \n"
                                                + "Left Join OS_Vig on OS_Vig.Cliente = Clientes.Codigo\n"
                                                + "                and OS_Vig.CodFil = Clientes.CodFil\n"
                                                + "                and OS_Vig.Situacao = 'A'      \n"
                                                + "Left Join Clientes CliCxf on CliCXf.Codigo = '9990001' "
                                                + "                         and CliCxf.CodFil = Clientes.CodFil "
                                                + "where Clientes.InterfExt like '%" + vTipoEmpresa + " " + vCodAgencia + " " + vNome_agencia_origem + "%'";
                                        qTmpX2 = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                        qTmpX2.select();
                                        if (!vNumeroSolicitacao.equals("")) {
                                            String vOS = "";
                                            if (qTmpX2.Proximo()) {
                                                vCodCli = qTmpX2.getString("Codigo");
                                                vNomeCli = qTmpX2.getString("Nred");
                                                vOS = qTmpX2.getString("OS");
                                                vQtdePed = qTmpX2.getInt("QtdePed");

                                            }
                                            if (vQtdePed == 0) {
                                                vSQL = "insert into Pedido(Numero, Codfil, Data, Tipo, Codcli1, Nred1, Hora1o, Hora2O, "
                                                        + "Codcli2, Nred2, Hora1D, Hora2D, Solicitante, Valor, Obs, OperIncl, ClassifSrv, Dt_Incl, "
                                                        + " hr_Incl, OS, PedidoCliente, Situacao,   Operador, Dt_alter, Hr_Alter, Flag_Excl)"
                                                        + "Values(\n"
                                                        + vPedido + ", \n"
                                                        + "1" + ", \n"
                                                        + "'" + vDataPed + "', \n"
                                                        + "'T', \n"
                                                        + "'" + vCodCli + "', \n"
                                                        + "'" + vNomeCli + "', \n"
                                                        + "'" + vHorarioAtendimento + "', \n"
                                                        + "'" + vHorarioAtendimento + "', \n"
                                                        + "'" + "9990001" + "', \n"
                                                        + "'" + "PRESERV CX FORTE" + "', \n"
                                                        + "'08:00'" + ", \n"
                                                        + "'18:00'" + ", \n"
                                                        //+ "'WS ITAU'" + ", \n"
                                                        + "'" + vIndice_controle_solicitacao + "'" + ", \n"
                                                        + "0" + ", \n"
                                                        + "'IMPORTACAO - WS ITAU'" + ", \n"
                                                        + "'SATSERVER'" + ", \n"
                                                        + "'V'" + ", \n"
                                                        + "'" + getDataAtual("SQL") + "', \n"
                                                        + "'" + getDataAtual("HORA") + "',\n"
                                                        + "'" + vOS + "', \n"
                                                        + "'" + vNumeroControle + "'" + ", \n"
                                                        + "'PD'" + ", \n"
                                                        + "'SATSERVER'" + ", \n"
                                                        + "'" + getDataAtual("SQL") + "', \n"
                                                        + "'" + getDataAtual("HORA") + "',\n"
                                                        + "''" + ") \n";
                                                SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                                SQLPdr.insert();
                                                vIndice_controle_solicitacao = "";
                                            }
                                            //}
                                        }
                                    } catch (Exception e) {
                                        logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                        throw new Exception("Pedido - " + e.getMessage() + "\r\n"
                                                + vSQL);
                                    }

                                }
                            }
                        }
                        //Gravar Pedido

                        vSQL = "";
                        //Buscar Apropriar
                        if (vQtdePed == 0) {
                            recolhimentos_eventuais_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vIdentificadorCancelamento);
                            //recolhimentos_eventuais_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao);
                            //consultaSuprimentosProcDet(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vPedido, vStatusSuprimento);
                        }
                    }
                    vRetorno = jsonObject.get("indice_controle_solicitacao")
                            + "-RespostaCompleta:" + response.toString();
                } else {
                    vRetorno = response.toString();
                }

                //vRetorno = response.toString() + bufferedReader.toString();
                bufferedReader.close();
            }

        } catch (Exception e) {
            vRetorno = e.toString() + " - Resposta EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaRecolhimentoProcH(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_eventuais?data_atendimento=" + vData);
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_eventuais?data_atendimento=" + vData);
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/suprimentos_processantes?data_atendimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", "5bb4bb63-48e6-43cf-8478-908d6d363578"); //Homolog
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            //connection.addRequestProperty("x-itau-apikey", clientId); //Homolog
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");
            //InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            //InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            int vResposta = 0;
            vResposta = connection.getResponseCode();

            this.logerro.Grava("RESPOSTA_CONSULTA_REALIZA:" + Integer.toString(vResposta), caminho);

            if (vResposta == 403) {
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                Token = vRetorno;
            }

            Consulta SQLPdrLog;
            String vSQLLog;

            if (vResposta == 404) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_eventuais?data_atendimento" + " ', \n"
                        + "'recolhimentos_eventuais?data_atendimento=" + vData + "', \n"
                        + "'" + "', \n"
                        + "'SEM RESPOSTA RECOLHIMENTO HOMOLOG', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else {
                this.logerro.Grava("RESPOSTA_CONSULTA_REALIZA_EXISTE_EVENTUAIS HOMOLOG RECEV:" + Integer.toString(vResposta), caminho);
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                InputStream vErro = connection.getErrorStream();
                String vErro2;
                BufferedReader br = null;
                /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                            br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
                 */
                while ((vErro2 = bufferedReader.readLine()) != null) {
                    response.append(vErro2);
                }
                //this.logerro.Grava("Erro2_CONSULTA_REALIZA:" + vErro2, caminho);
                this.logerro.Grava("JSON REALIZADO H:" + response.toString(), caminho);
                //String body = "data_atendimento=" + vData;
                //OutputStream outputStream = connection.getOutputStream();
                //outputStream.write(body.toString().getBytes());
                //outputStream.close();            
//            String line = null;
//            while ((line = bufferedReader.readLine()) != null) {
//                response.append(line);
//            }
                String vRespostaServer = response.toString();
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava(response.toString(), caminho);

                Persistencia dbpadrao, dbsatellite;
                //dbsatellite = this.pool.getConexao("SATELLITE");
                dbpadrao = this.pool.getConexao("SATPRESERVE");
                //dbsatellite.FechaConexao();            

                String vTotalRegistros = "";
                String vOrgao_centralizador = "";
                String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
                String vCodBanco = "";
                String vTipoEmpresa = "";
                String vCodAgencia = "";
                String vNome_agencia_origem = "";
                String vNumeroSolicitacao = "";
                String vNumeroMalote = "";
                String vNumeroControle = "";
                String vTipoDenominacao = "";
                String vDescricaoDEnominacao = "";
                String vHorarioAtendimento = "";
                String vLocal_Atendimento = "";
                String vStatusSuprimento = "";
                String vDescricaoStatus = "";
                String vIdentificacaoApropricao = "";
                String vDescricaoApropriacao = "";
                String vIdentificadorCancelamento = "";
                String vSQL = "";
                int vPedido = 0;
                int vQtdePed = 0;
                String vIndice_controle_solicitacao = "";

                JsonArray jPedidoA = jsonObject.getAsJsonArray("data");

                if (!jsonObject.isJsonNull()) {
                    //vDataPed = jsonObject.get("data_solicitacao").toString();                
                    for (int i = 0; i < jPedidoA.size(); i++) {

                        JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();

                        JsonArray jPedidoB = jPedido.getAsJsonArray("registros");
                        if (!jPedidoB.isJsonNull()) {
                            this.logerro.Grava("Json entrou array H: " + jPedido.toString(), caminho);
                            for (int z = 0; z < jPedidoB.size(); z++) {
                                JsonObject jPedidoC = jPedidoB.get(z).getAsJsonObject();
                                vCodBanco = jPedidoC.getAsJsonPrimitive("codigo_banco").getAsString();
                                vTipoEmpresa = jPedidoC.getAsJsonPrimitive("tipo_empresa").getAsString();
                                vCodAgencia = jPedidoC.getAsJsonPrimitive("codigo_agencia").getAsString();
                                vNome_agencia_origem = jPedidoC.getAsJsonPrimitive("nome_agencia").getAsString();
                                vOrgao_centralizador = jPedido.get("orgao_centralizador").getAsString();
                                vNumeroSolicitacao = jPedidoC.getAsJsonPrimitive("solicitacao").getAsString();
                                vHorarioAtendimento = jPedidoC.getAsJsonPrimitive("horario").getAsString();
                                vLocal_Atendimento = jPedidoC.getAsJsonPrimitive("local").getAsString();
                                vIdentificacaoApropricao = jPedidoC.getAsJsonPrimitive("identificador_apropriacao").getAsString();
                                vDescricaoApropriacao = jPedidoC.getAsJsonPrimitive("descricao_apropriacao").getAsString();
                                vIdentificadorCancelamento = jPedidoC.getAsJsonPrimitive("identificador_cancelamento").getAsString();
                                vIndice_controle_solicitacao = jPedidoC.getAsJsonPrimitive("indice_controle_solicitacao").getAsString();
                                Consulta qTmpX, qTmpX2, qTmpXPed;
                                this.logerro.Grava("vai gravar integra itau: " + jPedido.toString(), caminho);

                                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                        + "'recolhimentos_eventuais?data_atendimento H" + "', \n"
                                        + "'recolhimentos_eventuais?data_atendimento=" + vData + "', \n"
                                        + "'" + vIndice_controle_solicitacao + "', \n"
                                        + "'" + vRespostaServer + "', \n"
                                        + "'SATSERVER', \n"
                                        + "'" + getDataAtual("SQL") + "', \n"
                                        + "'" + getDataAtual("HORA") + "')";
                                this.logerro.Grava("vai gravar SQL: " + vSQLLog, caminho);
                                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                                SQLPdrLog.insert();
                                this.logerro.Grava("vai gravou SQL: " + vSQLLog, caminho);
                                // Log JSON                                                                            
                                //Final log JSON
                                // Apropria Carlos 31/08/2023
                                /*
                                logexecucao.Grava("Situacao Apropria rec: " + vDescricaoApropriacao + " vIndiceControle: " + vIndice_controle_solicitacao, caminho);
                                if ((!vIndice_controle_solicitacao.equals("")) && (!vDescricaoApropriacao.equals("Apropriado"))) {*/
                                recolhimentos_eventuais_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vIdentificadorCancelamento);
                                //}
                                vSQL = "Select Max(Numero)+1 Numero from Pedido \n"
                                        + " where CodFil = 1 ";

                                try {
                                    qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                    qTmpX.select();
                                } catch (Exception e) {
                                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                    throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                                            + vSQL);
                                }
                                int vConta = 0;
                                Consulta SQLPdr;

                                if (qTmpX.Proximo()) {
                                    vConta++;
                                    vPedido = qTmpX.getInt("NUMERO");
                                    String vSQLPed = "(Select Count(*) from Pedido where Data = '" + vDataPed + "' and Solicitante = '" + vIndice_controle_solicitacao + "')";

                                    try {
                                        vSQL = "Select Clientes.Codigo, Clientes.Nred, OS_Vig.OS, OS_Vig.CliDst, CliCxf.Nred NRedCxf, CliCxf.Codigo CodCliCxf, " + vSQLPed + " QtdePed from Clientes \n"
                                                + "Left Join OS_Vig on OS_Vig.Cliente = Clientes.Codigo\n"
                                                + "                and OS_Vig.CodFil = Clientes.CodFil\n"
                                                + "                and OS_Vig.Situacao = 'A'      \n"
                                                + "Left Join Clientes CliCxf on CliCXf.Codigo = '9990001' "
                                                + "                         and CliCxf.CodFil = Clientes.CodFil "
                                                + "where Clientes.InterfExt like '%" + vTipoEmpresa + " " + vCodAgencia + " " + vNome_agencia_origem + "%'";
                                        qTmpX2 = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                        qTmpX2.select();
                                        if (!vNumeroSolicitacao.equals("")) {
                                            String vOS = "";
                                            if (qTmpX2.Proximo()) {
                                                vCodCli = qTmpX2.getString("Codigo");
                                                vNomeCli = qTmpX2.getString("Nred");
                                                vOS = qTmpX2.getString("OS");
                                                vQtdePed = qTmpX2.getInt("QtdePed");

                                            }
                                            if (vQtdePed == 0) {
                                                vSQL = "insert into Pedido(Numero, Codfil, Data, Tipo, Codcli1, Nred1, Hora1o, Hora2O, "
                                                        + "Codcli2, Nred2, Hora1D, Hora2D, Solicitante, Valor, Obs, OperIncl, ClassifSrv, Dt_Incl, "
                                                        + " hr_Incl, OS, PedidoCliente, Situacao,   Operador, Dt_alter, Hr_Alter, Flag_Excl)"
                                                        + "Values(\n"
                                                        + vPedido + ", \n"
                                                        + "1" + ", \n"
                                                        + "'" + vDataPed + "', \n"
                                                        + "'T', \n"
                                                        + "'" + vCodCli + "', \n"
                                                        + "'" + vNomeCli + "', \n"
                                                        + "'" + vHorarioAtendimento + "', \n"
                                                        + "'" + vHorarioAtendimento + "', \n"
                                                        + "'" + "9990001" + "', \n"
                                                        + "'" + "PRESERV CX FORTE" + "', \n"
                                                        + "'08:00'" + ", \n"
                                                        + "'18:00'" + ", \n"
                                                        //+ "'WS ITAU'" + ", \n"
                                                        + "'" + vIndice_controle_solicitacao + "'" + ", \n"
                                                        + "0" + ", \n"
                                                        + "'IMPORTACAO-WS ITAU H'" + ", \n"
                                                        + "'SATSERVER'" + ", \n"
                                                        + "'V'" + ", \n"
                                                        + "'" + getDataAtual("SQL") + "', \n"
                                                        + "'" + getDataAtual("HORA") + "',\n"
                                                        + "'" + vOS + "', \n"
                                                        + "'" + vNumeroControle + "'" + ", \n"
                                                        + "'PD'" + ", \n"
                                                        + "'SATSERVER'" + ", \n"
                                                        + "'" + getDataAtual("SQL") + "', \n"
                                                        + "'" + getDataAtual("HORA") + "',\n"
                                                        + "''" + ") \n";
                                                SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                                SQLPdr.insert();
                                                vIndice_controle_solicitacao = "";
                                            }
                                            //}
                                        }
                                    } catch (Exception e) {
                                        logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                        throw new Exception("Pedido - " + e.getMessage() + "\r\n"
                                                + vSQL);
                                    }

                                }
                            }
                        }
                        //Gravar Pedido

                        vSQL = "";
                        //Buscar Apropriar
                        if (vQtdePed == 0) {
                            recolhimentos_eventuais_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vIdentificadorCancelamento);
                            //recolhimentos_eventuais_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao);
                            //consultaSuprimentosProcDet(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vPedido, vStatusSuprimento);
                        }
                    }
                    vRetorno = jsonObject.get("indice_controle_solicitacao")
                            + "-RespostaCompleta H:" + response.toString();
                } else {
                    vRetorno = response.toString();
                }

                //vRetorno = response.toString() + bufferedReader.toString();
                bufferedReader.close();
            }

        } catch (Exception e) {
            vRetorno = e.toString() + " - Resposta EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaRecolhimentoRealizados(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
        logexecucao.Grava("TOKEN INICIO - Recolhimentos Realizados: ", caminho);
        Token = GetAccessTokenh(clientId, clientSecret, certificado, senhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
        logexecucao.Grava("TOKEN FINAL - Recolhimentos Realizados: " + Token, caminho);
        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_realizados?data_atendimento=" + vData);
            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_realizados?data_atendimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            //Itau Key prod 5bb4bb63-48e6-43cf-8478-908d6d363578
            //connection.addRequestProperty("x-itau-apikey", "5bb4bb63-48e6-43cf-8478-908d6d363578");
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("x-itau-apikey", clientId); //Homolog 
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

//            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
//            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");
            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            logexecucao.Grava("URL - Recolhimentos Realizados: " + url.toString(), caminho);
//            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";

//            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String vErro2;
            BufferedReader br = null;
            /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                            br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
             */
            while ((vErro2 = bufferedReader.readLine()) != null) {
                response.append(vErro2);
            }
            //this.logerro.Grava("Erro2_CONSULTA_REALIZA:" + vErro2, caminho);
            this.logerro.Grava("RetornoJSON:" + response.toString(), caminho);
            //String body = "data_atendimento=" + vData;
            //OutputStream outputStream = connection.getOutputStream();
            //outputStream.write(body.toString().getBytes());
            //outputStream.close();            
//            String line = null;
//            while ((line = bufferedReader.readLine()) != null) {
//                response.append(line);
//            }
            String vRespostaServer = response.toString();
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava("RESPOSTA REALIZADO JSON:" + response.toString(), caminho);

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            logexecucao.Grava("Recolhimentos Realizados Resposta: " + Integer.toString(vResposta, vResposta), caminho);

            Consulta SQLPdrLog;
            String vSQLLog;
            if (vResposta == 403) {
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                Token = vRetorno;
            }

            if (vResposta == 404 || vResposta == 400) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_realizados?data_atendimento" + " ', \n"
                        + "'recolhimentos_realizados?data_atendimento=" + vData + "', \n"
                        + "'" + "', \n"
                        + "'SEM RESPOSTA RECOLHIMENTO', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            }

            InputStream vErro = connection.getErrorStream();

            /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                            br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
             */
            while ((vErro2 = bufferedReader.readLine()) != null) {
                response.append(vErro2);
            }

            logexecucao.Grava("Recolhimentos Realizados Resposta Erro: " + response.toString() + "/n"
                    + " Normal:" + vErro2, caminho);
            //String body = "data_atendimento=" + vData;
            //OutputStream outputStream = connection.getOutputStream();
            //outputStream.write(body.toString().getBytes());
            //outputStream.close();            
//            String line = null;
//            while ((line = bufferedReader.readLine()) != null) {
//                response.append(line);
//            }            

            Persistencia dbpadrao, dbsatellite;
            //dbsatellite = this.pool.getConexao("SATELLITE");
            dbpadrao = this.pool.getConexao("SATPRESERVE");
            //dbsatellite.FechaConexao();            

            String vTotalRegistros = "";
            String vOrgao_centralizador = "";
            String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
            String vCodBanco = "";
            String vTipoEmpresa = "";
            String vCodAgencia = "";
            String vNome_agencia_origem = "";
            String vNumeroSolicitacao = "";
            String vNumeroMalote = "";
            String vNumeroControle = "";
            String vTipoDenominacao = "";
            String vDescricaoDEnominacao = "";
            String vHorarioAtendimento = "";
            String vLocal_Atendimento = "";
            String vStatusSuprimento = "";
            String vDescricaoStatus = "";
            String vIdentificacaoApropricao = "";
            String vDescricaoApropriacao = "";
            String vValorRec = "";
            String vDescRec = "";
            String vSQL = "";
            int vPedido = 0;
            int vQtdePed = 0;
            String vIndice_controle_solicitacao = "";

            JsonArray jPedidoA = jsonObject.getAsJsonArray("data");

            if (!jsonObject.isJsonNull()) {
                //vDataPed = jsonObject.get("data_solicitacao").toString();                
                for (int i = 0; i < jPedidoA.size(); i++) {

                    JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();

                    JsonArray jPedidoB = jPedido.getAsJsonArray("registros");
                    if (!jPedidoB.isJsonNull()) {
                        this.logerro.Grava("Json: " + jPedido.toString(), caminho);
                        for (int z = 0; z < jPedidoB.size(); z++) {
                            JsonObject jPedidoC = jPedidoB.get(z).getAsJsonObject();
                            vCodBanco = jPedidoC.getAsJsonPrimitive("codigo_banco").getAsString();
                            vTipoEmpresa = jPedidoC.getAsJsonPrimitive("tipo_empresa").getAsString();
                            vCodAgencia = jPedidoC.getAsJsonPrimitive("codigo_agencia").getAsString();
                            vNome_agencia_origem = jPedidoC.getAsJsonPrimitive("nome_agencia").getAsString();
                            vOrgao_centralizador = "";//jPedido.get("orgao_centralizador").getAsString();
                            vHorarioAtendimento = jPedidoC.getAsJsonPrimitive("horario").getAsString();
                            vIndice_controle_solicitacao = jPedidoC.getAsJsonPrimitive("indice_controle_solicitacao").getAsString();
                            vValorRec = jPedidoC.getAsJsonPrimitive("valor_recolhimento").getAsString();
                            vDescRec = jPedidoC.getAsJsonPrimitive("descricao_situacao_recolhimento").getAsString();
                            Consulta qTmpX, qTmpX2, qTmpXPed;
                            logexecucao.Grava("LOG: " + vSQL + "-" + vDescRec + "-" + vIndice_controle_solicitacao + "-" + vNome_agencia_origem, caminho);
                            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                    + "'recolhimentos_realizados?data_atendimento" + " ', \n"
                                    + "'recolhimentos_realizados?data_atendimento=" + vData + "', \n"
                                    + "'" + vIndice_controle_solicitacao + "', \n"
                                    + "'" + vRespostaServer + "', \n"
                                    + "'SATSERVER', \n"
                                    + "'" + getDataAtual("SQL") + "', \n"
                                    + "'" + getDataAtual("HORA") + "')";
                            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                            SQLPdrLog.insert();
                            recolhimentos_realizados_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, "S");
                            // Log JSON                    
                            //Final log JSON
                            // Apropria 31/08/2023 Carlos

                            vSQL = "Select Max(Numero)+1 Numero from Pedido \n"
                                    + " where CodFil = 1 ";

                            try {
                                qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                qTmpX.select();
                            } catch (Exception e) {
                                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                                        + vSQL);
                            }
                            int vConta = 0;
                            Consulta SQLPdr;

                            if (qTmpX.Proximo()) {
                                vConta++;
                                vPedido = qTmpX.getInt("NUMERO");
                                String vSQLPed = "(Select Count(*) from Pedido where Data = '" + vDataPed + "' and Solicitante = '" + vIndice_controle_solicitacao + "')";
                                String vSQLNumPed = "(Select top 1 Numero from Pedido where Data = '" + vDataPed + "' and Solicitante = '" + vIndice_controle_solicitacao + "')";

                                try {
                                    vSQL = "Select Clientes.Codigo, Clientes.Nred, OS_Vig.OS, OS_Vig.CliDst, CliCxf.Nred NRedCxf, CliCxf.Codigo CodCliCxf, " + vSQLPed + " QtdePed,  " + vSQLNumPed + " NumPed "
                                            + "from Clientes \n"
                                            + "Left Join OS_Vig on OS_Vig.Cliente = Clientes.Codigo\n"
                                            + "                and OS_Vig.CodFil = Clientes.CodFil\n"
                                            + "                and OS_Vig.Situacao = 'A'      \n"
                                            + "Left Join Clientes CliCxf on CliCXf.Codigo = '9990001' "
                                            + "                         and CliCxf.CodFil = Clientes.CodFil "
                                            + "where Clientes.InterfExt like '%" + vTipoEmpresa + " " + vCodAgencia + " " + vNome_agencia_origem + "%'";
                                    logexecucao.Grava("Consulta SQL: " + vSQL + "-" + vSQLNumPed + "-" + vSQLPed + "-" + vIndice_controle_solicitacao, caminho);
                                    qTmpX2 = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                    qTmpX2.select();
                                    if (!vNumeroSolicitacao.equals("")) {
                                        String vOS = "";
                                        if (qTmpX2.Proximo()) {
                                            vCodCli = qTmpX2.getString("Codigo");
                                            vNomeCli = qTmpX2.getString("Nred");
                                            vOS = qTmpX2.getString("OS");
                                            vQtdePed = qTmpX2.getInt("QtdePed");
                                            vPedido = qTmpX2.getInt("NumPed");
                                            logexecucao.Grava("Erro SQL: " + vCodCli + "-" + vNomeCli + "-" + vOS + "-" + vQtdePed + "-" + vIndice_controle_solicitacao, caminho);
                                        }
                                        if (vQtdePed >= 0) {
                                            vSQL = "insert into Pedido(Numero, Codfil, Data, Tipo, Codcli1, Nred1, Hora1o, Hora2O, "
                                                    + "Codcli2, Nred2, Hora1D, Hora2D, Solicitante, Valor, Obs, OperIncl, ClassifSrv, Dt_Incl, "
                                                    + " hr_Incl, OS, PedidoCliente, Situacao,   Operador, Dt_alter, Hr_Alter, Flag_Excl)"
                                                    + "Values(\n"
                                                    + vPedido + ", \n"
                                                    + "1" + ", \n"
                                                    + "'" + vDataPed + "', \n"
                                                    + "'T', \n"
                                                    + "'" + vCodCli + "', \n"
                                                    + "'" + vNomeCli + "', \n"
                                                    + "'" + vHorarioAtendimento + "', \n"
                                                    + "'" + vHorarioAtendimento + "', \n"
                                                    + "'" + "9990001" + "', \n"
                                                    + "'" + "PRESERV CX FORTE" + "', \n"
                                                    + "'08:00'" + ", \n"
                                                    + "'18:00'" + ", \n"
                                                    //+ "'WS ITAU'" + ", \n"
                                                    + "'" + vIndice_controle_solicitacao + "'" + ", \n"
                                                    + vValorRec + ", \n"
                                                    + "'IMPORTACAO - WS ITAU - REALIZADO'" + ", \n"
                                                    + "'SATSERVER'" + ", \n"
                                                    + "'V'" + ", \n"
                                                    + "'" + getDataAtual("SQL") + "', \n"
                                                    + "'" + getDataAtual("HORA") + "',\n"
                                                    + "'" + vOS + "', \n"
                                                    + "'" + vNumeroControle + "'" + ", \n"
                                                    + "'PD'" + ", \n"
                                                    + "'SATSERVER'" + ", \n"
                                                    + "'" + getDataAtual("SQL") + "', \n"
                                                    + "'" + getDataAtual("HORA") + "',\n"
                                                    + "''" + ") \n";
                                            logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                            SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                            SQLPdr.insert();
                                            vIndice_controle_solicitacao = "";
                                        } else {
                                            vSQL = " Update Pedido Set Obs = Substring(Obs+" + vDescRec + ",1,80) , "
                                                    + " Valor = " + vValorRec
                                                    + " where Numero = " + vPedido
                                                    + "   and CodFil = 1 ";
                                            SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                            logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                            SQLPdr.update();
                                        }

                                        //}
                                    } else {
                                        logexecucao.Grava("Erro: NAO PROCESSOU", caminho);
                                    }

                                } catch (Exception e) {
                                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                    throw new Exception("Pedido - " + e.getMessage() + "\r\n"
                                            + vSQL);
                                }

                            }
                        }
                    }
                    //Gravar Pedido

                    vSQL = "";
                    //Buscar Apropriar
                    if (vQtdePed >= 0) {
                        recolhimentos_realizados_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, "S");
                        //consultaSuprimentosProcDet(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vPedido, vStatusSuprimento);
                    }
                }
                vRetorno = jsonObject.get("indice_controle_solicitacao")
                        + "-RespostaCompleta REALIZADO:" + response.toString();
                logexecucao.Grava("Retorno REALIZADO: " + vRetorno, caminho);
            } else {
                vRetorno = response.toString();
                logexecucao.Grava("Retorno ERRO REALIZADO: " + vRetorno, caminho);
            }

            //vRetorno = response.toString() + bufferedReader.toString();
            bufferedReader.close();

        } catch (Exception e) {
            this.logerro.Grava("SE HOUVE RETORNO-" + vRetorno, caminho);
            vRetorno = e.toString() + " - Resposta Exception EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        vRetorno = response.toString();
        return vRetorno;
    }

    public String recolhimentos_eventuais_confirmacao(String clientId, String clientSecret, File certificado, String senhaCertificado, String vToken, String vNumPed, String vIdentificadorCencelamento) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_eventuais_confirmacao");
            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_eventuais_confirmacao");

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);
            connection.addRequestProperty("Content-Type", "application/json");
            //connection.addRequestProperty("x-itau-correlationID", "a1e64241-7fdb-4d05-a7f6-c44febcdd8d9");
            //connection.addRequestProperty("x-itau-flowID", "1");
            //connection.addRequestProperty("x-itau-apikey", "5bb4bb63-48e6-43cf-8478-908d6d363578");
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("x-itau-apikey", clientId); //Homolog
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("Authorization", "Bearer " + vToken);
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // Add certificate
//            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
//            senhaCertificado = "acesso2023";
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";

            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            /*
            String body = "cnpj_matriz=8787673000145"
                        + "&cnpj_filial=8787673000145"
                        + "&codigo_transportadora=162"
                        + "&codigo_filial=02";
             */
            String body = " {\r\n \"indice_controle_solicitacao\": \"" + vNumPed + "\",\r\n \"status_solicitacao\": \"" + vIdentificadorCencelamento + "\"\r\n                }";

            // Guarda no Log
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'recolhimentos_eventuais_confirmacao', \n"
                    + "'', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + body + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
            SQLPdrLog.insert();
            this.logerro.Grava("recolhimentos_eventuais_confirmacao: " + body, caminho);
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();
            int vret = connection.getResponseCode();
            if (vret == 403) {
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                vToken = vRetorno;
            }
            if (vret != 400 && vret != 404) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String line = null;
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                }
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                bufferedReader.close();
            }
            vret = connection.getResponseCode();
            this.logerro.Grava("recolhimentos_eventuais_confirmacao: " + "Ret: " + Integer.toString(vret), caminho);

            vRetorno = response.toString();

            if (vRetorno.contains("sucesso")) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_eventuais_confirmacao', \n"
                        + "'', \n"
                        + "'" + vNumPed + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            }
            //this.logerro.Grava(vRetorno, caminho);

        } catch (Exception e) {
            vRetorno = e.toString();
//                    " - "+Integer.toString(vret);
            this.logerro.Grava(vRetorno, caminho);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();

    }

    public String recolhimentos_processamentos_malotes_inclusao(String clientId, String clientSecret, File certificado, String senhaCertificado, String vToken, String vNumPed, String vIdentificadorCencelamento) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_processamentos_malotes_inclusao");
            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_processamentos_malotes_inclusao");

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);
            connection.addRequestProperty("Content-Type", "application/json");
            //connection.addRequestProperty("x-itau-correlationID", "a1e64241-7fdb-4d05-a7f6-c44febcdd8d9");
            //connection.addRequestProperty("x-itau-flowID", "1");
            //connection.addRequestProperty("x-itau-apikey", "5bb4bb63-48e6-43cf-8478-908d6d363578");
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("x-itau-apikey", clientId); //Homolog
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("Authorization", "Bearer " + vToken);
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // Add certificate
//            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
//            senhaCertificado = "acesso2023";
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";

            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            /*
            String body = "cnpj_matriz=8787673000145"
                        + "&cnpj_filial=8787673000145"
                        + "&codigo_transportadora=162"
                        + "&codigo_filial=02";
             */
            String body = " {\r\n \"indice_controle_solicitacao\": \"" + vNumPed + "\",\r\n \"status_solicitacao\": \"" + vIdentificadorCencelamento + "\"\r\n                }";

            // Guarda no Log
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'recolhimentos_eventuais_confirmacao', \n"
                    + "'', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + body + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
            SQLPdrLog.insert();
            this.logerro.Grava("recolhimentos_eventuais_confirmacao: " + body, caminho);
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();
            int vret = connection.getResponseCode();
            if (vret == 403) {
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                vToken = vRetorno;
            }
            if (vret != 400 && vret != 404) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String line = null;
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                }
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                bufferedReader.close();
            }
            vret = connection.getResponseCode();
            this.logerro.Grava("recolhimentos_eventuais_confirmacao: " + "Ret: " + Integer.toString(vret), caminho);

            vRetorno = response.toString();

            if (vRetorno.contains("sucesso")) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_eventuais_confirmacao', \n"
                        + "'', \n"
                        + "'" + vNumPed + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            }
            //this.logerro.Grava(vRetorno, caminho);

        } catch (Exception e) {
            vRetorno = e.toString();
//                    " - "+Integer.toString(vret);
            this.logerro.Grava(vRetorno, caminho);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();

    }

    public String recolhimentos_realizados_confirmacao(String clientId, String clientSecret, File certificado, String senhaCertificado, String vToken, String vNumPed, String vIdentificadorCancelamento) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_realizados_confirmacao");
            //URL url = new URL("https://hom-secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_realizados_confirmacao");

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);
            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Authorization", "Bearer " + vToken);
            //connection.addRequestProperty("x-itau-correlationID", "a1e64241-7fdb-4d05-a7f6-c44febcdd8d9");
            //connection.addRequestProperty("x-itau-flowID", "1");
            //connection.addRequestProperty("x-itau-apikey", "5bb4bb63-48e6-43cf-8478-908d6d363578");
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("x-itau-apikey", clientId); //Homolog
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // Add certificate
//            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
//            senhaCertificado = "acesso2023";
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";

            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            /*
            String body = "cnpj_matriz=8787673000145"
                        + "&cnpj_filial=8787673000145"
                        + "&codigo_transportadora=162"
                        + "&codigo_filial=02";
             */
            String body = " {\r\n \"identificador_confirmacao\": \"S\",\r\n \"indice_controle_solicitacao\": \"" + vNumPed + "\"\r\n                }";
            // Guarda no Log
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'recolhimentos_realizados_confirmacao', \n"
                    + "'', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + body + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
            SQLPdrLog.insert();

            this.logerro.Grava("recolhimentos_realizados_confirmacao: " + body, caminho);
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();
            int vret = connection.getResponseCode();
            this.logerro.Grava("recolhimentos_realizados_confirmacao: " + "Ret: " + Integer.toString(vret), caminho);
            String line = null;
            if (vret == 403) {
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                vToken = vRetorno;
            }
            if (vret != 400 && vret != 404) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                    JsonParser jsonParser = new JsonParser();
                    JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                }
                bufferedReader.close();
            }
            vret = connection.getResponseCode();
            this.logerro.Grava("recolhimentos_realizados_confirmacao2: " + "Ret: " + Integer.toString(vret), caminho);

            vRetorno = response.toString();
            if (vRetorno.contains("sucesso")) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_realizados_confirmacao', \n"
                        + "'', \n"
                        + "'" + vNumPed + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            }

            this.logerro.Grava("recolhimentos_realizados_confirmacao: " + vRetorno, caminho);

        } catch (Exception e) {
            vRetorno = e.toString();
//                    " - "+Integer.toString(vret);
            this.logerro.Grava(vRetorno, caminho);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();

    }

    public String consultaRecolhimentoRealizadosH(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
        logexecucao.Grava("TOKEN INICIO - Recolhimentos Realizados: ", caminho);
        Token = GetCSTTokenh(clientId, clientSecret, certificado, senhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
        logexecucao.Grava("TOKEN FINAL - Recolhimentos Realizados: " + Token, caminho);
        try {

            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_realizados?data_atendimento=" + vData);
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_realizados?data_atendimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            //Itau Key prod 5bb4bb63-48e6-43cf-8478-908d6d363578
            connection.addRequestProperty("x-itau-apikey", "5bb4bb63-48e6-43cf-8478-908d6d363578");
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            //connection.addRequestProperty("x-itau-apikey", clientId); //Homolog 
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

//            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
//            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");
            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            logexecucao.Grava("URL - Recolhimentos Realizados: " + url.toString(), caminho);

//            senhaCertificado = "acesso2023";
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String vErro2;
            BufferedReader br = null;
            /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                            br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
             */
            while ((vErro2 = bufferedReader.readLine()) != null) {
                response.append(vErro2);
            }
            //this.logerro.Grava("Erro2_CONSULTA_REALIZA:" + vErro2, caminho);
            this.logerro.Grava("RetornoJSON:" + response.toString(), caminho);
            //String body = "data_atendimento=" + vData;
            //OutputStream outputStream = connection.getOutputStream();
            //outputStream.write(body.toString().getBytes());
            //outputStream.close();            
//            String line = null;
//            while ((line = bufferedReader.readLine()) != null) {
//                response.append(line);
//            }
            String vRespostaServer = response.toString();
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava("RESPOSTA REALIZADO JSON:" + response.toString(), caminho);

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            logexecucao.Grava("Recolhimentos Realizados Resposta: " + Integer.toString(vResposta, vResposta), caminho);

            Consulta SQLPdrLog;
            String vSQLLog;
            if (vResposta == 403) {
                //GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                GetCSTTokenh(clientId, clientSecret, vCertificado, senhaCertificado);                //obterAccessToken(vClientID, vClientSecret, vCertificado, vSenhaCertificado);                
                Token = vRetorno;
            }

            if (vResposta == 404 || vResposta == 400) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_realizados?data_atendimento" + " ', \n"
                        + "'recolhimentos_realizados?data_atendimento=" + vData + "', \n"
                        + "'" + "', \n"
                        + "'SEM RESPOSTA RECOLHIMENTO', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            }

            InputStream vErro = connection.getErrorStream();

            /*
            if (100 <= connection.getResponseCode() && connection.getResponseCode() <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                            br = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }
             */
            while ((vErro2 = bufferedReader.readLine()) != null) {
                response.append(vErro2);
            }

            logexecucao.Grava("Recolhimentos Realizados Resposta Erro: " + response.toString() + "/n"
                    + " Normal:" + vErro2, caminho);
            //String body = "data_atendimento=" + vData;
            //OutputStream outputStream = connection.getOutputStream();
            //outputStream.write(body.toString().getBytes());
            //outputStream.close();            
//            String line = null;
//            while ((line = bufferedReader.readLine()) != null) {
//                response.append(line);
//            }            

            Persistencia dbpadrao, dbsatellite;
            //dbsatellite = this.pool.getConexao("SATELLITE");
            dbpadrao = this.pool.getConexao("SATPRESERVE");
            //dbsatellite.FechaConexao();            

            String vTotalRegistros = "";
            String vOrgao_centralizador = "";
            String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
            String vCodBanco = "";
            String vTipoEmpresa = "";
            String vCodAgencia = "";
            String vNome_agencia_origem = "";
            String vNumeroSolicitacao = "";
            String vNumeroMalote = "";
            String vNumeroControle = "";
            String vTipoDenominacao = "";
            String vDescricaoDEnominacao = "";
            String vHorarioAtendimento = "";
            String vLocal_Atendimento = "";
            String vStatusSuprimento = "";
            String vDescricaoStatus = "";
            String vIdentificacaoApropricao = "";
            String vDescricaoApropriacao = "";
            String vValorRec = "";
            String vDescRec = "";
            String vSQL = "";
            int vPedido = 0;
            int vQtdePed = 0;
            String vIndice_controle_solicitacao = "";

            JsonArray jPedidoA = jsonObject.getAsJsonArray("data");

            if (!jsonObject.isJsonNull()) {
                //vDataPed = jsonObject.get("data_solicitacao").toString();                
                for (int i = 0; i < jPedidoA.size(); i++) {

                    JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();

                    JsonArray jPedidoB = jPedido.getAsJsonArray("registros");
                    if (!jPedidoB.isJsonNull()) {
                        this.logerro.Grava("Json: " + jPedido.toString(), caminho);
                        for (int z = 0; z < jPedidoB.size(); z++) {
                            JsonObject jPedidoC = jPedidoB.get(z).getAsJsonObject();
                            vCodBanco = jPedidoC.getAsJsonPrimitive("codigo_banco").getAsString();
                            vTipoEmpresa = jPedidoC.getAsJsonPrimitive("tipo_empresa").getAsString();
                            vCodAgencia = jPedidoC.getAsJsonPrimitive("codigo_agencia").getAsString();
                            vNome_agencia_origem = jPedidoC.getAsJsonPrimitive("nome_agencia").getAsString();
                            vOrgao_centralizador = "";//jPedido.get("orgao_centralizador").getAsString();
                            vHorarioAtendimento = jPedidoC.getAsJsonPrimitive("horario").getAsString();
                            vIndice_controle_solicitacao = jPedidoC.getAsJsonPrimitive("indice_controle_solicitacao").getAsString();
                            vValorRec = jPedidoC.getAsJsonPrimitive("valor_recolhimento").getAsString();
                            vDescRec = jPedidoC.getAsJsonPrimitive("descricao_situacao_recolhimento").getAsString();
                            Consulta qTmpX, qTmpX2, qTmpXPed;
                            logexecucao.Grava("LOG: " + vSQL + "-" + vDescRec + "-" + vIndice_controle_solicitacao + "-" + vNome_agencia_origem, caminho);
                            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                    + "'recolhimentos_realizados?data_atendimento" + " ', \n"
                                    + "'recolhimentos_realizados?data_atendimento=" + vData + "', \n"
                                    + "'" + vIndice_controle_solicitacao + "', \n"
                                    + "'" + vRespostaServer + "', \n"
                                    + "'SATSERVER', \n"
                                    + "'" + getDataAtual("SQL") + "', \n"
                                    + "'" + getDataAtual("HORA") + "')";
                            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                            SQLPdrLog.insert();
                            recolhimentos_realizados_confirmacao(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, "S");
                            // Log JSON                    
                            //Final log JSON
                            // Apropria 31/08/2023 Carlos

                            vSQL = "Select Max(Numero)+1 Numero from Pedido \n"
                                    + " where CodFil = 1 ";

                            try {
                                qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                qTmpX.select();
                            } catch (Exception e) {
                                logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                                        + vSQL);
                            }
                            int vConta = 0;
                            Consulta SQLPdr;

                            if (qTmpX.Proximo()) {
                                vConta++;
                                vPedido = qTmpX.getInt("NUMERO");
                                String vSQLPed = "(Select Count(*) from Pedido where Data = '" + vDataPed + "' and Solicitante = '" + vIndice_controle_solicitacao + "')";
                                String vSQLNumPed = "(Select top 1 Numero from Pedido where Data = '" + vDataPed + "' and Solicitante = '" + vIndice_controle_solicitacao + "')";

                                try {
                                    vSQL = "Select Clientes.Codigo, Clientes.Nred, OS_Vig.OS, OS_Vig.CliDst, CliCxf.Nred NRedCxf, CliCxf.Codigo CodCliCxf, " + vSQLPed + " QtdePed,  " + vSQLNumPed + " NumPed "
                                            + "from Clientes \n"
                                            + "Left Join OS_Vig on OS_Vig.Cliente = Clientes.Codigo\n"
                                            + "                and OS_Vig.CodFil = Clientes.CodFil\n"
                                            + "                and OS_Vig.Situacao = 'A'      \n"
                                            + "Left Join Clientes CliCxf on CliCXf.Codigo = '9990001' "
                                            + "                         and CliCxf.CodFil = Clientes.CodFil "
                                            + "where Clientes.InterfExt like '%" + vTipoEmpresa + " " + vCodAgencia + " " + vNome_agencia_origem + "%'";
                                    logexecucao.Grava("Consulta SQL: " + vSQL + "-" + vSQLNumPed + "-" + vSQLPed + "-" + vIndice_controle_solicitacao, caminho);
                                    qTmpX2 = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                    qTmpX2.select();
                                    if (!vNumeroSolicitacao.equals("")) {
                                        String vOS = "";
                                        if (qTmpX2.Proximo()) {
                                            vCodCli = qTmpX2.getString("Codigo");
                                            vNomeCli = qTmpX2.getString("Nred");
                                            vOS = qTmpX2.getString("OS");
                                            vQtdePed = qTmpX2.getInt("QtdePed");
                                            vPedido = qTmpX2.getInt("NumPed");
                                            logexecucao.Grava("Erro SQL: " + vCodCli + "-" + vNomeCli + "-" + vOS + "-" + vQtdePed + "-" + vIndice_controle_solicitacao, caminho);
                                        }
                                        if (vQtdePed >= 0) {
                                            vSQL = "insert into Pedido(Numero, Codfil, Data, Tipo, Codcli1, Nred1, Hora1o, Hora2O, "
                                                    + "Codcli2, Nred2, Hora1D, Hora2D, Solicitante, Valor, Obs, OperIncl, ClassifSrv, Dt_Incl, "
                                                    + " hr_Incl, OS, PedidoCliente, Situacao,   Operador, Dt_alter, Hr_Alter, Flag_Excl)"
                                                    + "Values(\n"
                                                    + vPedido + ", \n"
                                                    + "1" + ", \n"
                                                    + "'" + vDataPed + "', \n"
                                                    + "'T', \n"
                                                    + "'" + vCodCli + "', \n"
                                                    + "'" + vNomeCli + "', \n"
                                                    + "'" + vHorarioAtendimento + "', \n"
                                                    + "'" + vHorarioAtendimento + "', \n"
                                                    + "'" + "9990001" + "', \n"
                                                    + "'" + "PRESERV CX FORTE" + "', \n"
                                                    + "'08:00'" + ", \n"
                                                    + "'18:00'" + ", \n"
                                                    //+ "'WS ITAU'" + ", \n"
                                                    + "'" + vIndice_controle_solicitacao + "'" + ", \n"
                                                    + vValorRec + ", \n"
                                                    + "'IMPORTACAO - WS ITAU - REALIZADO'" + ", \n"
                                                    + "'SATSERVER'" + ", \n"
                                                    + "'V'" + ", \n"
                                                    + "'" + getDataAtual("SQL") + "', \n"
                                                    + "'" + getDataAtual("HORA") + "',\n"
                                                    + "'" + vOS + "', \n"
                                                    + "'" + vNumeroControle + "'" + ", \n"
                                                    + "'PD'" + ", \n"
                                                    + "'SATSERVER'" + ", \n"
                                                    + "'" + getDataAtual("SQL") + "', \n"
                                                    + "'" + getDataAtual("HORA") + "',\n"
                                                    + "''" + ") \n";
                                            logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                            SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                            SQLPdr.insert();
                                            vIndice_controle_solicitacao = "";
                                        } else {
                                            vSQL = " Update Pedido Set Obs = Substring(Obs+" + vDescRec + ",1,80) , "
                                                    + " Valor = " + vValorRec
                                                    + " where Numero = " + vPedido
                                                    + "   and CodFil = 1 ";
                                            SQLPdr = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                                            logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                            SQLPdr.update();
                                        }

                                        //}
                                    } else {
                                        logexecucao.Grava("Erro: NAO PROCESSOU", caminho);
                                    }

                                } catch (Exception e) {
                                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                                    throw new Exception("Pedido - " + e.getMessage() + "\r\n"
                                            + vSQL);
                                }

                            }
                        }
                    }
                    //Gravar Pedido

                    vSQL = "";
                    //Buscar Apropriar
                    if (vQtdePed >= 0) {
                        recolhimentos_realizados_confirmacaoH(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, "S");
                        //consultaSuprimentosProcDet(clientId, clientSecret, certificado, senhaCertificado, Token, vIndice_controle_solicitacao, vPedido, vStatusSuprimento);
                    }
                }
                vRetorno = jsonObject.get("indice_controle_solicitacao")
                        + "-RespostaCompleta REALIZADO:" + response.toString();
                logexecucao.Grava("Retorno REALIZADO: " + vRetorno, caminho);
            } else {
                vRetorno = response.toString();
                logexecucao.Grava("Retorno ERRO REALIZADO: " + vRetorno, caminho);
            }

            //vRetorno = response.toString() + bufferedReader.toString();
            bufferedReader.close();

        } catch (Exception e) {
            this.logerro.Grava("SE HOUVE RETORNO-" + vRetorno, caminho);
            vRetorno = e.toString() + " - Resposta Exception EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        vRetorno = response.toString();
        return vRetorno;
    }

    public String recolhimentos_realizados_confirmacaoH(String clientId, String clientSecret, File certificado, String senhaCertificado, String vToken, String vNumPed, String vIdentificadorCancelamento) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_realizados_confirmacao");
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_realizados_confirmacao");

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);
            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("Authorization", "Bearer " + vToken);
            //connection.addRequestProperty("x-itau-correlationID", "a1e64241-7fdb-4d05-a7f6-c44febcdd8d9");
            //connection.addRequestProperty("x-itau-flowID", "1");
            connection.addRequestProperty("x-itau-apikey", "5bb4bb63-48e6-43cf-8478-908d6d363578");
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            //connection.addRequestProperty("x-itau-apikey", clientId); //Homolog
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // Add certificate
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            //File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            //senhaCertificado = "acesso2023";

            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            /*
            String body = "cnpj_matriz=8787673000145"
                        + "&cnpj_filial=8787673000145"
                        + "&codigo_transportadora=162"
                        + "&codigo_filial=02";
             */
            String body = " {\r\n \"identificador_confirmacao\": \"S\",\r\n \"indice_controle_solicitacao\": \"" + vNumPed + "\"\r\n                }";
            // Guarda no Log
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'recolhimentos_realizados_confirmacao', \n"
                    + "'', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + body + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
            SQLPdrLog.insert();

            this.logerro.Grava("recolhimentos_realizados_confirmacao: " + body, caminho);
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();
            int vret = connection.getResponseCode();
            this.logerro.Grava("recolhimentos_realizados_confirmacao: " + "Ret: " + Integer.toString(vret), caminho);
            String line = null;
            if (vret == 403) {
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                vToken = vRetorno;
            }
            if (vret != 400 && vret != 404) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                    JsonParser jsonParser = new JsonParser();
                    JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                }
                bufferedReader.close();
            }
            vret = connection.getResponseCode();
            this.logerro.Grava("recolhimentos_realizados_confirmacao2: " + "Ret: " + Integer.toString(vret), caminho);

            vRetorno = response.toString();
            if (vRetorno.contains("sucesso")) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_realizados_confirmacao', \n"
                        + "'', \n"
                        + "'" + vNumPed + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            }

            this.logerro.Grava("recolhimentos_realizados_confirmacao: " + vRetorno, caminho);

        } catch (Exception e) {
            vRetorno = e.toString();
//                    " - "+Integer.toString(vret);
            this.logerro.Grava(vRetorno, caminho);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();

    }

    public String consultaPosicoesCST(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) throws IOException {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        int vResposta = 0;
        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_consultar?data_movimento=" + vData);
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_consultar?data_movimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "hom-secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoInput(true);
            connection.setDoOutput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            this.logerro.Grava("custodias_posicoes_parciais_consultar Resposta Antes if connection: INI", caminho);
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            Consulta SQLPdrLog;
            Consulta SQLPdrLogList;
            Consulta SQLPdrLogAgrup;
            Consulta SQLPdrLogComp;

            vResposta = connection.getResponseCode();
            String vSQLLog;
            this.logerro.Grava("custodias_posicoes_parciais_consultar Resposta Antes Buffered: INI", caminho);
            if (vResposta == 404) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_posicoes_parciais_consultar', \n"
                        + "'data_movimento=" + vData + "', \n"
                        + "'', \n"
                        + "'SEM RESPOSTA DE CUSTODIA - " + vResposta + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else {
                InputStream vErro = connection.getErrorStream();
                BufferedReader bufferedReader;
                if (vResposta == 400) {
                    bufferedReader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                } else {
                    bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                }

                this.logerro.Grava("custodias_posicoes_parciais_consultar Resposta POS Buffered: INI - Passou", caminho);

                String vErro2 = "";
                BufferedReader br = null;
                this.logerro.Grava("custodias_posicoes_parciais_consultar Resposta Antes While: INI", caminho);
                while ((vErro2 = bufferedReader.readLine()) != null) {
                    response.append(vErro2);
                }

                //if (vErro2.equals("")){
                //    vErro2 = response.toString();
                //}
                this.logerro.Grava("custodias_posicoes_parciais_consultar Resposta Pos Buffered: " + response.toString(), caminho);
                //this.logerro.Grava("custodias_posicoes_parciais_consultar Resposta: " + vResposta + " TEXTO:" + response.toString(), caminho);                

                if (vResposta == 400 || vResposta == 200) {
                    vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                            + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                            + "'custodias_posicoes_parciais_consultar', \n"
                            + "'data_movimento=" + vData + "', \n"
                            + "'', \n"
                            + "'" + response.toString() + "', \n"
                            + "'SATSERVER', \n"
                            + "'" + getDataAtual("SQL") + "', \n"
                            + "'" + getDataAtual("HORA") + "')";
                    this.logerro.Grava("custodias_posicoes_parciais_consultar Resposta Buffer Vai Gravar BD:" + vSQLLog, caminho);
                    SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                    SQLPdrLog.insert();
                }
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava(response.toString(), caminho);
                String vRespostaServer = response.toString();
                Persistencia dbpadrao, dbsatellite;
                //dbsatellite = this.pool.getConexao("SATELLITE");
                dbpadrao = this.pool.getConexao("SATPRESERVE");
                //dbsatellite.FechaConexao();  

                // Gravar JSON
                String vTotalRegistros = "";
                String vOrgao_centralizador = "";
                String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
                String vCodBanco = "";
                String vTipoEmpresa = "";
                String vCodAgencia = "";
                String vNome_agencia_origem = "";
                String vNumeroSolicitacao = "";
                String vNumeroMalote = "";
                String vNumeroControle = "";
                String vTipoDenominacao = "";
                String vDescricaoDEnominacao = "";
                String vHorarioAtendimento = "";
                String vLocal_Atendimento = "";
                String vStatusSuprimento = "";
                String vDescricaoStatus = "";
                String vIdentificacaoApropricao = "";
                String vDescricaoApropriacao = "";
                String vPosicao = "";
                String vDescPosicao = "";
                String vSituacao = "";
                String vSaldoAnterior = "";
                String vSaldoAtual = "";
                String vDescSituacao = "";
                String vDt_Situacao = "";
                String vHr_Situacao = "";
                String vSaldoEntrada = "";
                String vSaldoSaida = "";
                String vValorCedulas = "";
                String vValorNaoProcCedulas = "";
                String vValorMoedas = "";
                String vValorNaoProcMoedas = "";
                String vSQL = "";
                int vPedido = 0;
                int vQtdePed = 0;
                String vIndice_controle_solicitacao = "";

                if (vResposta == 200) {
                    JsonArray jPedidoA = jsonObject.getAsJsonArray("data");

                    this.logerro.Grava("ANTES DE VALIDAR:" + jPedidoA.toString(), caminho);
                    if (!jsonObject.get("data").equals(null)) {
                        //vOrgao_centralizador = jsonObject.get("orgao_centralizador").toString();
                        //vDataPed = jsonObject.get("data_solicitacao").toString();
                        for (int i = 0; i < jPedidoA.size(); i++) {
                            JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();

                            vIndice_controle_solicitacao = jPedido.getAsJsonPrimitive("indice_controle_posicao").getAsString();
                            vPosicao = jPedido.getAsJsonPrimitive("codigo_tipo_posicao").getAsString();
                            vDescPosicao = jPedido.getAsJsonPrimitive("descricao_posicao").getAsString();
                            vSituacao = jPedido.getAsJsonPrimitive("codigo_situacao_posicao").getAsString();
                            vDescSituacao = jPedido.getAsJsonPrimitive("descricao_situacao_posicao").getAsString();
                            vDt_Situacao = jPedido.getAsJsonPrimitive("data_movimento").getAsString();
                            vHr_Situacao = jPedido.getAsJsonPrimitive("numero_posicao").getAsString();
                            vSaldoAnterior = jPedido.getAsJsonPrimitive("valor_saldo_anterior").getAsString();
                            vSaldoAtual = jPedido.getAsJsonPrimitive("valor_saldo_atual").getAsString();
                            vSaldoEntrada = jPedido.getAsJsonPrimitive("valor_saldo_entrada").getAsString();
                            vSaldoSaida = jPedido.getAsJsonPrimitive("valor_saldo_saida").getAsString();
                            vValorCedulas = jPedido.getAsJsonPrimitive("valor_total_cedulas").getAsString();
                            vValorNaoProcCedulas = jPedido.getAsJsonPrimitive("valor_nao_processado_cedulas").getAsString();
                            vValorMoedas = jPedido.getAsJsonPrimitive("valor_total_moedas").getAsString();
                            vValorNaoProcMoedas = jPedido.getAsJsonPrimitive("valor_nao_processado_moedas").getAsString();
                            //vCodBanco = jPedido.getAsJsonPrimitive("codigo_banco").getAsString();
                            Consulta qTmpX, qTmpX2, qTmpXPed;

                            //Grava Log JSON 28/11/2023
                            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                    + "'custodias_posicoes_parciais_consultar', \n"
                                    + "'data_atendimento=" + vData + "', \n"
                                    + "'" + vIndice_controle_solicitacao + "', \n"
                                    + "'" + response.toString() + "', \n"
                                    + "'SATSERVER', \n"
                                    + "'" + getDataAtual("SQL") + "', \n"
                                    + "'" + getDataAtual("HORA") + "')" + ";"
                                    + "Insert into ItauIntegraDet(Sequencia, TipoOperacao, Inf_Ctrl_Solic, Posicao, DescPosicao, XMLJSON, SaldoAnterior, SaldoAtual, Situacao, "
                                    + " DescSituacao, Dt_Situacao, Hr_situacao, SaldoEntrada, SaldoSaida, ValorCedulas, ValorNaoProcCedulas, ValorMoedas, ValorNaoProcMoedas, Operador, Dt_Incl, Hr_Incl)Values("
                                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegraDet), \n"
                                    + "'custodias_posicoes_parciais_consultar', \n"
                                    + "'" + vIndice_controle_solicitacao + "', \n"
                                    + "'" + vPosicao + "', \n"
                                    + "'" + vDescPosicao + "', \n"
                                    + "'" + response.toString() + "', \n"
                                    + vSaldoAnterior + ", \n"
                                    + vSaldoAtual + ", \n"
                                    + "'" + vSituacao + "', \n"
                                    + "'" + vDescSituacao + "', \n"
                                    + "'" + vDt_Situacao + "', \n"
                                    + "'" + vHr_Situacao + "', \n"
                                    + vSaldoEntrada + ", \n"
                                    + vSaldoSaida + ", \n"
                                    + vValorCedulas + ", \n"
                                    + vValorNaoProcCedulas + ", \n"
                                    + vValorMoedas + ", \n"
                                    + vValorNaoProcMoedas + ", \n"
                                    + "'SATSERVER', \n"
                                    + "'" + getDataAtual("SQL") + "', \n"
                                    + "'" + getDataAtual("HORA") + "')" + ";";
                            this.logerro.Grava(" SQL: " + vSQLLog, caminho);
                            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                            SQLPdrLog.insert();
                            this.logerro.Grava("Gravou SQL: ", caminho);
                            vSQL = "";
                            vSQLLog = "Insert into ItauIntegraList(Sequencia, TipoOperacao, Inf_Ctrl_Solic, Dt_movimento, Posicao, DescPosicao, XMLJSON, Situacao, DescSituacao, SaldoAnterior, SaldoAtual,  "
                                    + "  SaldoEntrada, SaldoSaida, ValorCedulas, ValorNaoProcCedulas, ValorMoedas, ValorNaoProcMoedas, Status, Operador, Dt_Incl, Hr_Incl)Values("
                                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegraList), \n"
                                    + "'custodias_posicoes_parciais_consultar', \n"
                                    + "'" + vIndice_controle_solicitacao + "', \n"
                                    + "'" + vDt_Situacao + "', \n"
                                    + "'" + vPosicao + "', \n"
                                    + "'" + vDescPosicao + "', \n"
                                    + "'" + response.toString() + "', \n"
                                    + "'" + vSituacao + "', \n"
                                    + "'" + vDescSituacao + "', \n"
                                    + vSaldoAnterior + ", \n"
                                    + vSaldoAtual + ", \n"
                                    + vSaldoEntrada + ", \n"
                                    + vSaldoSaida + ", \n"
                                    + vValorCedulas + ", \n"
                                    + vValorNaoProcCedulas + ", \n"
                                    + vValorMoedas + ", \n"
                                    + vValorNaoProcMoedas + ", \n"
                                    + "'PD', \n"
                                    + "'SATSERVER', \n"
                                    + "'" + getDataAtual("SQL") + "', \n"
                                    + "'" + getDataAtual("HORA") + "')" + ";";
                            this.logerro.Grava(" 2 SQL: " + vSQLLog, caminho);
                            SQLPdrLogList = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                            SQLPdrLogList.insert();
                            //Busca Agrupamentos
                            if (!jPedido.get("agrupamentos").equals(null)) {
                                JsonArray jPedidoB = jPedido.getAsJsonArray("agrupamentos");
                                for (int x = 0; x < jPedidoB.size(); x++) {
                                    JsonObject jPedidoList = jPedidoB.get(x).getAsJsonObject();
                                    String vIdentificador;
                                    String vDescIdentificador;
                                    String vValorEntrada;
                                    String vValorSaida;

                                    vIdentificador = jPedidoList.getAsJsonPrimitive("identificador").getAsString();
                                    vDescIdentificador = jPedidoList.getAsJsonPrimitive("descricao").getAsString();
                                    vValorEntrada = jPedidoList.getAsJsonPrimitive("valor_entrada").getAsString();
                                    vValorSaida = jPedidoList.getAsJsonPrimitive("valor_saida").getAsString();

                                    vSQLLog = "Insert into ItauIntegraAgrup(Sequencia, Inf_Ctrl_Solic, Identificador, TipoOperacao, Desc_Identif, Valor_Entrada, Valor_Saida,  "
                                            + " Status, Operador, Dt_Incl, Hr_Incl)Values("
                                            + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegraAgrup), \n"
                                            + "'" + vIndice_controle_solicitacao + "', \n"
                                            + "'" + vIdentificador + "', \n"
                                            + "'custodias_posicoes_parciais_consultar', \n"
                                            + "'" + vDescIdentificador + "', \n"
                                            + vValorEntrada + ", \n"
                                            + vValorSaida + ", \n"
                                            + "'PD', \n"
                                            + "'SATSERVER', \n"
                                            + "'" + getDataAtual("SQL") + "', \n"
                                            + "'" + getDataAtual("HORA") + "')" + ";";
                                    this.logerro.Grava(" SQL: " + vSQLLog, caminho);
                                    SQLPdrLogAgrup = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                                    SQLPdrLogAgrup.insert();
                                }
                            }
                            if (!jPedido.get("cedulas").equals(null)) {
                                JsonArray jPedidoC = jPedido.getAsJsonArray("cedulas");
                                for (int y = 0; y < jPedidoC.size(); y++) {
                                    JsonObject jPedidoAgrup = jPedidoC.get(y).getAsJsonObject();
                                    String vCod_Especie;
                                    String vTipo_Especie;
                                    String vValorTotal;

                                    String vCodFamilia;
                                    vCod_Especie = jPedidoAgrup.getAsJsonPrimitive("codigo_classificacao_especie").getAsString();
                                    vTipo_Especie = jPedidoAgrup.getAsJsonPrimitive("tipo_especie").getAsString();
                                    vValorTotal = jPedidoAgrup.getAsJsonPrimitive("valor_total_especie").getAsString();

                                    vSQLLog = "Insert into ItauIntegraComp(Sequencia, Inf_Ctrl_Solic, Cod_Especie, Tipo_Especie, TipoOperacao, ValorTotal, "
                                            + " Status, Operador, Dt_Incl, Hr_Incl)Values("
                                            + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegraComp), \n"
                                            + "'" + vIndice_controle_solicitacao + "', \n"
                                            + "'" + vCod_Especie + "', \n"
                                            + "'" + vTipo_Especie + "', \n"
                                            + "'custodias_posicoes_parciais_consultar', \n"
                                            + vValorTotal + ", \n"
                                            + "'PD', \n"
                                            + "'SATSERVER', \n"
                                            + "'" + getDataAtual("SQL") + "', \n"
                                            + "'" + getDataAtual("HORA") + "')" + ";";
                                    this.logerro.Grava(" SQL: " + vSQLLog, caminho);
                                    SQLPdrLogAgrup = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                                    SQLPdrLogAgrup.insert();
                                }
                            }
                            if (!jPedido.get("moedas").equals(null)) {
                                JsonArray jPedidoD = jPedido.getAsJsonArray("moedas");
                                for (int y = 0; y < jPedidoD.size(); y++) {
                                    JsonObject jPedidoAgrup = jPedidoD.get(y).getAsJsonObject();
                                    String vCod_Especie;
                                    String vTipo_Especie;
                                    String vValorTotal;

                                    String vCodFamilia;
                                    vCod_Especie = jPedidoAgrup.getAsJsonPrimitive("codigo_classificacao_especie").getAsString();
                                    vTipo_Especie = jPedidoAgrup.getAsJsonPrimitive("tipo_especie").getAsString();
                                    vValorTotal = jPedidoAgrup.getAsJsonPrimitive("valor_total_especie").getAsString();

                                    vSQLLog = "Insert into ItauIntegraComp(Sequencia, Inf_Ctrl_Solic, Cod_Especie, Tipo_Especie, TipoOperacao, ValorTotal, "
                                            + " Status, Operador, Dt_Incl, Hr_Incl)Values("
                                            + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegraComp), \n"
                                            + "'" + vIndice_controle_solicitacao + "', \n"
                                            + "'" + vCod_Especie + "', \n"
                                            + "'" + vTipo_Especie + "', \n"
                                            + "'custodias_posicoes_parciais_consultar', \n"
                                            + vValorTotal + ", \n"
                                            + "'PD', \n"
                                            + "'SATSERVER', \n"
                                            + "'" + getDataAtual("SQL") + "', \n"
                                            + "'" + getDataAtual("HORA") + "')" + ";";
                                    this.logerro.Grava(" SQL: " + vSQLLog, caminho);
                                    SQLPdrLogAgrup = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                                    SQLPdrLogAgrup.insert();
                                }
                            }
                        }
                        vRetorno = "-RespostaCompleta:" + response.toString();
                    } else {
                        vRetorno = response.toString();
                    }
                }
                bufferedReader.close();
            }
            //vRetorno = response.toString() + bufferedReader.toString();            
        } catch (Exception e) {
            InputStream errorstream = connection.getErrorStream();
            vRetorno = e.toString() + " - Resposta EndPoint:" + response.toString() + " Response- " + connection.getResponseMessage().toString() + " \n"
                    + "Erro input: " + errorstream.toString() + " \n"
                    + e.getMessage().toString() + " \n"
                    + Exception.class.getTypeName().toString();
            this.logerro.Grava("EXCEPTION: " + vRetorno + " Resposta:" + vResposta, caminho);
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaListaCST(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_historicos_listas_consultar?data_movimento=" + vData);
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_consultar?data_movimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "hom-secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoInput(true);
            //connection.setDoOutput(true);                        
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            Consulta SQLPdrLog;
            String vSQLLog;
            this.logerro.Grava("custodias_posicoes_historicos_listas_consultar Resposta1:" + vResposta, caminho);
            if (vResposta == 404) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_posicoes_historicos_listas_consultar', \n"
                        + "'data_movimento=" + vData + "', \n"
                        + "'', \n"
                        + "'SEM RESPOSTA DE CUSTODIA - " + vResposta + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

                InputStream vErro = connection.getErrorStream();
                String vErro2;
                BufferedReader br = null;

                this.logerro.Grava("custodias_posicoes_historicos_listas_consultar Resposta2:" + vResposta, caminho);
                while ((vErro2 = bufferedReader.readLine()) != null) {
                    response.append(vErro2);
                }
                this.logerro.Grava("custodias_posicoes_historicos_listas_consultar Buffer" + response.toString(), caminho);
                if (vResposta == 400 || vResposta == 200) {
                    vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                            + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                            + "'custodias_posicoes_historicos_listas_consultar', \n"
                            + "'data_movimento=" + vData + "', \n"
                            + "'', \n"
                            + "'" + response.toString() + "', \n"
                            + "'SATSERVER', \n"
                            + "'" + getDataAtual("SQL") + "', \n"
                            + "'" + getDataAtual("HORA") + "')";

                    SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                    SQLPdrLog.insert();
                }
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava("JASON_LISTA: " + jsonObject.toString(), caminho);
                String vRespostaServer = response.toString();
                Persistencia dbpadrao, dbsatellite;
                //dbsatellite = this.pool.getConexao("SATELLITE");
                dbpadrao = this.pool.getConexao("SATPRESERVE");
                //dbsatellite.FechaConexao();  

                // Gravar JSON
                String vTotalRegistros = "";
                String vOrgao_centralizador = "";
                String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
                String vCodBanco = "";
                String vTipoEmpresa = "";
                String vCodAgencia = "";
                String vNome_agencia_origem = "";
                String vNumeroSolicitacao = "";
                String vNumeroMalote = "";
                String vNumeroControle = "";
                String vTipoDenominacao = "";
                String vDescricaoDEnominacao = "";
                String vHorarioAtendimento = "";
                String vLocal_Atendimento = "";
                String vStatusSuprimento = "";
                String vDescricaoStatus = "";
                String vIdentificacaoApropricao = "";
                String vDescricaoApropriacao = "";
                String vSQL = "";
                String vPosicao = "";
                String vDescPosicao = "";
                String vSituacao = "";
                String vSaldoAnterior = "";
                String vSaldoAtual = "";
                String vDescSituacao = "";
                String vDt_Situacao = "";
                String vHr_Situacao = "";
                int vPedido = 0;
                int vQtdePed = 0;
                String vIndice_controle_solicitacao = "";

                this.logerro.Grava("CRIA ARRAY: ", caminho);
                JsonArray jPedidoA = jsonObject.getAsJsonArray("data");
                this.logerro.Grava("CRIADO ARRAY: ", caminho);
                if (!jsonObject.getAsJsonArray("data").equals(null)) {
                    this.logerro.Grava("ENTROU ARRAY: ", caminho);
                    //vOrgao_centralizador = jsonObject.get("orgao_centralizador").toString();
                    //vDataPed = jsonObject.get("data_solicitacao").toString();
                    for (int i = 0; i < jPedidoA.size(); i++) {
                        JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();
                        this.logerro.Grava("Json REALIZADO A: " + jPedido.toString(), caminho);
                        JsonArray jPedidoB = jPedido.getAsJsonArray("ocorrencias");
                        this.logerro.Grava("Json REALIZADO B: " + jPedidoB.toString(), caminho);
                        if (!jPedidoB.isJsonNull()) {
                            for (int z = 0; z < jPedidoB.size(); z++) {
                                JsonObject jPedidoC = jPedidoB.get(z).getAsJsonObject();
                                //this.logerro.Grava("Json REALIZADO C: " + jPedidoC.toString(), caminho);
                                //vCodBanco = jPedido.getAsJsonPrimitive("codigo_banco").getAsString();
                                Consulta qTmpX, qTmpX2, qTmpXPed;
                                vIndice_controle_solicitacao = jPedidoC.getAsJsonPrimitive("indice_controle_posicao").getAsString();
                                this.logerro.Grava("Json REALIZADO C1: " + vIndice_controle_solicitacao, caminho);
                                vPosicao = jPedidoC.getAsJsonPrimitive("codigo_tipo_posicao").getAsString();
                                this.logerro.Grava("Json REALIZADO C2: ", caminho);
                                vDescPosicao = jPedidoC.getAsJsonPrimitive("descricao_posicao").getAsString();
                                this.logerro.Grava("Json REALIZADO C3: ", caminho);
                                vSituacao = jPedidoC.getAsJsonPrimitive("codigo_situacao_posicao").getAsString();
                                this.logerro.Grava("Json REALIZADO C4: ", caminho);
                                vDescSituacao = jPedidoC.getAsJsonPrimitive("descricao_situacao_posicao").getAsString();
                                this.logerro.Grava("Json REALIZADO C5: ", caminho);
                                vDt_Situacao = jPedidoC.getAsJsonPrimitive("data_cadastro").getAsString();
                                this.logerro.Grava("Json REALIZADO C6: ", caminho);
                                vHr_Situacao = jPedidoC.getAsJsonPrimitive("hora_cadastro").getAsString();
                                this.logerro.Grava("Json REALIZADO C7: ", caminho);
                                vSaldoAnterior = jPedidoC.getAsJsonPrimitive("valor_saldo_anterior_composicao").getAsString();
                                this.logerro.Grava("Json REALIZADO C8: ", caminho);
                                vSaldoAtual = jPedidoC.getAsJsonPrimitive("valor_saldo_atual_composicao").getAsString();
                                this.logerro.Grava("Json REALIZADO C9: ", caminho);
                                //Grava Log JSON
                                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                        + "'custodias_posicoes_historicos_listas_consultar', \n"
                                        + "'data_atendimento=" + vData + "', \n"
                                        + "'" + vIndice_controle_solicitacao + "', \n"
                                        + "'" + response.toString() + "', \n"
                                        + "'SATSERVER', \n"
                                        + "'" + getDataAtual("SQL") + "', \n"
                                        + "'" + getDataAtual("HORA") + "')" + ";"
                                        + "Insert into ItauIntegraDet(Sequencia, TipoOperacao, Inf_Ctrl_Solic, Posicao, DescPosicao, XMLJSON, SaldoAnterior, SaldoAtual, Situacao, "
                                        + " DescSituacao, Dt_Situacao, Hr_situacao, Status, Operador, Dt_Incl, Hr_Incl)Values("
                                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                        + "'custodias_posicoes_historicos_listas_consultar', \n"
                                        + "'" + vIndice_controle_solicitacao + "', \n"
                                        + "'" + vPosicao + "', \n"
                                        + "'" + vDescPosicao + "', \n"
                                        + "'" + response.toString() + "', \n"
                                        + vSaldoAnterior + ", \n"
                                        + vSaldoAtual + ", \n"
                                        + "'" + vSituacao + "', \n"
                                        + "'" + vDescSituacao + "', \n"
                                        + "'" + vDt_Situacao + "', \n"
                                        + "'" + vHr_Situacao + "', \n"
                                        + "'PD', \n"
                                        + "'SATSERVER', \n"
                                        + "'" + getDataAtual("SQL") + "', \n"
                                        + "'" + getDataAtual("HORA") + "')" + ";";
                                this.logerro.Grava(" SQL: " + vSQLLog, caminho);
                                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                                SQLPdrLog.insert();
                                vSQL = "";
                                vSQLLog = "";
                            }
                        }
                    }
                    vRetorno = "PASSOU-RespostaCompleta:" + response.toString();
                    this.logerro.Grava(vRetorno, caminho);
                } else {
                    vRetorno = "NAO LOCALIZOU JSON-" + response.toString();
                    this.logerro.Grava(vRetorno, caminho);
                }
                bufferedReader.close();
            }
            //vRetorno = response.toString() + bufferedReader.toString();            
        } catch (Exception e) {
            vRetorno = e.toString() + " EXCEPTION CUSTODIA LISTA - Resposta EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String CancelarConsultaCST(String clientId, String clientSecret, File certificado, String senhaCertificado, String Token, String vData, String vChave) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();

        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodia_posicoes_cancelamento");  //Homologacao

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            connection.setRequestMethod("POST");
            connection.setDoOutput(true);

            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            this.logerro.Grava("PEGOU CERTIFICADO", caminho);

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            /*
            String body = "grant_type=client_credentials"
                    + "&client_id=5bb4bb63-48e6-43cf-8478-908d6d363578"
                    + "&client_secret=34c05187-dfc0-4c14-9ecb-e3c5bc89c5fc";
             */
            String body = "{\n"
                    + "  \"indice_controle_posicao\": \"" + vChave + "\",\n"
                    + "  \"codigo_tipo_posicao\": \"P\"\n"
                    + "}";

            this.logerro.Grava("BODY: " + body, caminho);
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();

            this.logerro.Grava("Resposta Connection Response: 1-" + " - Cancela Inicia buffer:" + connection.getResponseMessage() + "\n", caminho);
            this.logerro.Grava("Resposta Connection Response: 1.1-" + " - Cancela Inicia buffer:" + connection.getContent().toString() + "\n", caminho);

            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                response.append(line);
            }
            Consulta SQLPdrLog;
            String vSQLLog;
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
            this.logerro.Grava("RESPOSTA" + response.toString(), caminho);
            /*
            if (!jsonObject.get("data").getAsString().equals(null)) {
                connection.disconnect();
                vRetorno = response.toString();
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodia_posicoes_cancelamento', \n"
                        + "'data_atendimento=" + vData + "', \n"
                        + "'" + vChave + "', \n"
                        + "'" + line + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else {
             */
            connection.disconnect();
            vRetorno = response.toString();
//            }
            //vRetorno =  + bufferedReader.toString();

            bufferedReader.close();

        } catch (Exception e) {
            vRetorno = e.toString();
            this.logerro.Grava("RETORNO" + vRetorno, caminho);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String custodiaPosicoesIncluir(String clientId, String clientSecret, File certificado, String senhaCertificado, String vToken, String vNumPed, String vIdentificadorCencelamento) {

        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/recolhimentos_eventuais_confirmacao");
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_incluir");

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);
            connection.addRequestProperty("Content-Type", "application/json");
            //connection.addRequestProperty("x-itau-correlationID", "a1e64241-7fdb-4d05-a7f6-c44febcdd8d9");
            //connection.addRequestProperty("x-itau-flowID", "1");
            //connection.addRequestProperty("x-itau-apikey", "5bb4bb63-48e6-43cf-8478-908d6d363578");
            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("x-itau-apikey", clientId); //Homolog
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("Authorization", "Bearer " + vToken);
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setDoInput(true);

            // Add certificate
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
//            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
//            senhaCertificado = "acesso2023";

            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            String body = " {\r\n \"indice_controle_posicao\": \"" + vNumPed + "\",\r\n \"valor_nao_processado_cedulas\": \"" + vIdentificadorCencelamento + "\"\r\n                }";

            // Guarda no Log
            Consulta SQLPdrLog;
            String vSQLLog;
            vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'recolhimentos_eventuais_confirmacao', \n"
                    + "'', \n"
                    + "'" + vNumPed + "', \n"
                    + "'" + body + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
            SQLPdrLog.insert();
            this.logerro.Grava("recolhimentos_eventuais_confirmacao: " + body, caminho);
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(body.toString().getBytes());
            outputStream.close();
            int vret = connection.getResponseCode();
            if (vret == 403) {
                GetAccessTokenh(clientId, clientSecret, vCertificado, senhaCertificado);
                vToken = vRetorno;
            }
            if (vret != 400 && vret != 404) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String line = null;
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                }
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                bufferedReader.close();
            }
            vret = connection.getResponseCode();
            this.logerro.Grava("recolhimentos_eventuais_confirmacao: " + "Ret: " + Integer.toString(vret), caminho);

            vRetorno = response.toString();

            if (vRetorno.contains("sucesso")) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'recolhimentos_eventuais_confirmacao', \n"
                        + "'', \n"
                        + "'" + vNumPed + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";

                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            }
            //this.logerro.Grava(vRetorno, caminho);

        } catch (Exception e) {
            vRetorno = e.toString();
//                    " - "+Integer.toString(vret);
            this.logerro.Grava(vRetorno, caminho);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaMovimentacaoHistorico(String vClientID, String vClientSecret, File certificado, String senhaCertificado, String vToken, String vData) {
        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_movimentacoes_historicos_listas_consultar?data_movimentacao=" + vData);
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_consultar?data_movimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", vClientID);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + vToken);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "hom-secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            Consulta SQLPdrLog;
            String vSQLLog;
            this.logerro.Grava("custodias_Movimentacoes_historicos_listas_consultar Resposta1:" + vResposta, caminho);
            if (vResposta == 404) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_Movimentacoes_historicos_listas_consultar', \n"
                        + "'data_movimento=" + vData + "', \n"
                        + "'', \n"
                        + "'SEM RESPOSTA DE CUSTODIA - " + vResposta + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

                InputStream vErro = connection.getErrorStream();
                String vErro2;
                BufferedReader br = null;

                this.logerro.Grava("custodias_Movimentacoes_historicos_listas_consultar Resposta2:" + vResposta, caminho);
                while ((vErro2 = bufferedReader.readLine()) != null) {
                    response.append(vErro2);
                }
                this.logerro.Grava("custodias_Movimentacoes_historicos_listas_consultar Buffer" + response.toString(), caminho);
                if (vResposta == 400 || vResposta == 200) {
                    vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                            + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                            + "'custodias_Movimentacoes_historicos_consultar', \n"
                            + "'data_movimentacao=" + vData + "', \n"
                            + "'', \n"
                            + "'" + response.toString() + "', \n"
                            + "'SATSERVER', \n"
                            + "'" + getDataAtual("SQL") + "', \n"
                            + "'" + getDataAtual("HORA") + "')";

                    SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                    SQLPdrLog.insert();
                }
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava("JASON_LISTA: " + jsonObject.toString(), caminho);
                String vRespostaServer = response.toString();
                Persistencia dbpadrao, dbsatellite;
                //dbsatellite = this.pool.getConexao("SATELLITE");
                dbpadrao = this.pool.getConexao("SATPRESERVE");
                //dbsatellite.FechaConexao();  

                // Gravar JSON
                /*String vTotalRegistros = "";
                String vOrgao_centralizador = "";
                String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
                String vCodBanco = "";
                String vTipoEmpresa = "";
                String vCodAgencia = "";
                String vNome_agencia_origem = "";
                String vNumeroSolicitacao = "";
                String vNumeroMalote = "";
                String vNumeroControle = "";
                String vTipoDenominacao = "";
                String vDescricaoDEnominacao = "";
                String vHorarioAtendimento = "";
                String vLocal_Atendimento = "";
                String vStatusSuprimento = "";
                String vDescricaoStatus = "";
                String vIdentificacaoApropricao = "";
                String vDescricaoApropriacao = "";
                String vSQL = "";
                String vPosicao = "";
                String vDescPosicao = "";
                String vSituacao = "";
                String vSaldoAnterior = "";
                String vSaldoAtual = "";
                String vDescSituacao = "";
                String vDt_Situacao = "";
                String vHr_Situacao = "";
                int vPedido = 0;
                int vQtdePed = 0;
                String vIndice_controle_solicitacao = "";

                this.logerro.Grava("CRIA ARRAY: ", caminho);
                JsonArray jPedidoA = jsonObject.getAsJsonArray("data");
                this.logerro.Grava("CRIADO ARRAY: ", caminho);
                if (!jsonObject.getAsJsonArray("data").equals(null)) {
                    this.logerro.Grava("ENTROU ARRAY: ", caminho);
                    //vOrgao_centralizador = jsonObject.get("orgao_centralizador").toString();
                    //vDataPed = jsonObject.get("data_solicitacao").toString();
                    for (int i = 0; i < jPedidoA.size(); i++) {
                        JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();
                        this.logerro.Grava("Json REALIZADO A: " + jPedido.toString(), caminho);
                        JsonArray jPedidoB = jPedido.getAsJsonArray("ocorrencias");
                        this.logerro.Grava("Json REALIZADO B: " + jPedidoB.toString(), caminho);
                        if (!jPedidoB.isJsonNull()) {
                            for (int z = 0; z < jPedidoB.size(); z++) {
                                JsonObject jPedidoC = jPedidoB.get(z).getAsJsonObject();
                                this.logerro.Grava("Json REALIZADO C: " + jPedidoC.toString(), caminho);
                                //vCodBanco = jPedido.getAsJsonPrimitive("codigo_banco").getAsString();
                                Consulta qTmpX, qTmpX2, qTmpXPed;
                                vIndice_controle_solicitacao = jPedidoC.getAsJsonPrimitive("indice_controle_posicao").getAsString();
                                vPosicao = jPedidoC.getAsJsonPrimitive("codigo_tipo_posicao").getAsString();
                                vDescPosicao = jPedidoC.getAsJsonPrimitive("descricao_posicao").getAsString();
                                vSituacao = jPedidoC.getAsJsonPrimitive("codigo_situacao_posicao").getAsString();
                                vDescSituacao = jPedidoC.getAsJsonPrimitive("descricao_situacao_posicao").getAsString();
                                vDt_Situacao = jPedidoC.getAsJsonPrimitive("data_cadastro").getAsString();
                                vHr_Situacao = jPedidoC.getAsJsonPrimitive("hora_cadastro").getAsString();
                                vSaldoAnterior = jPedidoC.getAsJsonPrimitive("valor_saldo_anterior_composicao").getAsString();
                                vSaldoAtual = jPedidoC.getAsJsonPrimitive("valor_saldo_atual_composicao").getAsString();
                                //Grava Log JSON
                                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                        + "'custodias_posicoes_historicos_listas_consultar', \n"
                                        + "'data_atendimento=" + vData + "', \n"
                                        + "'" + vIndice_controle_solicitacao + "', \n"
                                        + "'" + response.toString() + "', \n"
                                        + "'SATSERVER', \n"
                                        + "'" + getDataAtual("SQL") + "', \n"
                                        + "'" + getDataAtual("HORA") + "')" + ";"
                                        + "Insert into ItauIntegraDet(Sequencia, TipoOperacao, Inf_Ctrl_Solic, Posicao, DescPosicao, XMLJSON, SaldoAnterior, SaldoAtual, Situacao, "
                                        + " DescSituacao, Dt_Situacao, Hr_situacao, Operador, Dt_Incl, Hr_Incl)Values("
                                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                        + "'custodias_posicoes_historicos_listas_consultar', \n"
                                        + "'" + vIndice_controle_solicitacao + "', \n"
                                        + "'" + vPosicao + "', \n"
                                        + "'" + vDescPosicao + "', \n"
                                        + "'" + response.toString() + "', \n"
                                        + vSaldoAnterior + ", \n"
                                        + vSaldoAtual + ", \n"
                                        + "'" + vSituacao + "', \n"
                                        + "'" + vDescSituacao + "', \n"
                                        + "'" + vDt_Situacao + "', \n"
                                        + "'" + vHr_Situacao + "', \n"
                                        + "'SATSERVER', \n"
                                        + "'" + getDataAtual("SQL") + "', \n"
                                        + "'" + getDataAtual("HORA") + "')" + ";";
                                this.logerro.Grava(" SQL: " + vSQLLog, caminho);
                                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                                SQLPdrLog.insert();
                                vSQL = "";
                                vSQLLog = "";
                            }
                        }
                    }
                    vRetorno = "PASSOU-RespostaCompleta:" + response.toString();
                    this.logerro.Grava(vRetorno, caminho);
                } else {
                    vRetorno = "NAO LOCALIZOU JSON-" + response.toString();
                    this.logerro.Grava(vRetorno, caminho);
                }
                 */
                bufferedReader.close();
            }
            //vRetorno = response.toString() + bufferedReader.toString();            
        } catch (Exception e) {
            vRetorno = e.toString() + " EXCEPTION CUSTODIA LISTA - Resposta EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaFechamentoCST(String vClientID, String vClientSecret, File certificado, String senhaCertificado, String vToken, String vData) {
        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_fechamentos_consultar?data_movimento=" + vData);
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_consultar?data_movimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", vClientID);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + vToken);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            //connection.addRequestProperty("Host", "hom-secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            Consulta SQLPdrLog;
            String vSQLLog;
            this.logerro.Grava("custodias_posicoes_fechamentos_consultar Resposta1:" + vResposta, caminho);
            if (vResposta == 404) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_posicoes_fechamentos_consultar', \n"
                        + "'data_movimento=" + vData + "', \n"
                        + "'', \n"
                        + "'SEM RESPOSTA DE CUSTODIA - " + vResposta + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else {

                InputStream vErro = connection.getErrorStream();
                BufferedReader bufferedReader;
                if (vResposta == 400) {
                    bufferedReader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                } else {
                    bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                }

                this.logerro.Grava("custodias_posicoes_fechamentos_consultar Resposta POS Buffered: INI - Passou", caminho);

                String vErro2 = "";
                BufferedReader br = null;
                this.logerro.Grava("custodias_posicoes_fechamentos_consultar Resposta Antes While: INI", caminho);
                while ((vErro2 = bufferedReader.readLine()) != null) {
                    response.append(vErro2);
                }

                //if (vErro2.equals("")){
                //    vErro2 = response.toString();
                //}
                this.logerro.Grava("custodias_posicoes_fechamentos_consultar Resposta Pos Buffered: " + response.toString(), caminho);
                //this.logerro.Grava("custodias_posicoes_parciais_consultar Resposta: " + vResposta + " TEXTO:" + response.toString(), caminho);                

                if (vResposta == 400 || vResposta == 200) {
                    vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                            + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                            + "'custodias_posicoes_fechamentos_consultar', \n"
                            + "'data_movimento=" + vData + "', \n"
                            + "'', \n"
                            + "'" + response.toString() + "', \n"
                            + "'SATSERVER', \n"
                            + "'" + getDataAtual("SQL") + "', \n"
                            + "'" + getDataAtual("HORA") + "')";
                    this.logerro.Grava("custodias_posicoes_fechamentos_consultar Resposta Buffer Vai Gravar BD:" + vSQLLog, caminho);
                    SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                    SQLPdrLog.insert();
                }

                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava("JASON_LISTA: " + jsonObject.toString(), caminho);
                String vRespostaServer = response.toString();
                Persistencia dbpadrao, dbsatellite;
                //dbsatellite = this.pool.getConexao("SATELLITE");
                dbpadrao = this.pool.getConexao("SATPRESERVE");
                //dbsatellite.FechaConexao();  

                // Gravar JSON
                String vTotalRegistros = "";
                String vOrgao_centralizador = "";
                String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
                String vCodBanco = "";
                String vTipoEmpresa = "";
                String vCodAgencia = "";
                String vNome_agencia_origem = "";
                String vNumeroSolicitacao = "";
                String vNumeroMalote = "";
                String vNumeroControle = "";
                String vTipoDenominacao = "";
                String vDescricaoDEnominacao = "";
                String vHorarioAtendimento = "";
                String vLocal_Atendimento = "";
                String vStatusSuprimento = "";
                String vDescricaoStatus = "";
                String vIdentificacaoApropricao = "";
                String vDescricaoApropriacao = "";
                String vSQL = "";
                String vPosicao = "";
                String vDescPosicao = "";
                String vSituacao = "";
                String vSaldoAnterior = "";
                String vSaldoAtual = "";
                String vDescSituacao = "";
                String vDt_Situacao = "";
                String vHr_Situacao = "";
                int vPedido = 0;
                int vQtdePed = 0;
                String vIndice_controle_solicitacao = "";

                bufferedReader.close();
            }
            //vRetorno = response.toString() + bufferedReader.toString();            
        } catch (Exception e) {
            vRetorno = e.toString() + "custodias_posicoes_fechamentos_consultar EXCEPTION CUSTODIA LISTA - Resposta EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaListaDetalheCST(String vClientID, String vClientSecret, File certificado, String senhaCertificado, String vToken, String vData, String vChave) {
        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_historicos_detalhes_consultar/?indice_controle_posicao=" + vChave);
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_consultar?data_movimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", vClientID);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + vToken);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "hom-secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            Consulta SQLPdrLog;
            Consulta SQLPdrLogList;
            Consulta SQLPdrLogAgrup;
            Consulta SQLPdrLogComp;

            String vSQLLog;
            this.logerro.Grava("custodias_posicoes_historicos_detalhes_consultar Resposta1:" + vResposta, caminho);
            if (vResposta == 404) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_posicoes_historicos_detalhes_consultar', \n"
                        + "'data_movimento=" + vData + "', \n"
                        + "'', \n"
                        + "'SEM RESPOSTA DE CUSTODIA - " + vResposta + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

                InputStream vErro = connection.getErrorStream();
                String vErro2;
                BufferedReader br = null;

                this.logerro.Grava("custodias_posicoes_historicos_detalhes_consultar Resposta2:" + vResposta, caminho);
                while ((vErro2 = bufferedReader.readLine()) != null) {
                    response.append(vErro2);
                }
                this.logerro.Grava("custodias_posicoes_historicos_detalhes_consultar Buffer" + response.toString(), caminho);
                if (vResposta == 400 || vResposta == 200) {
                    vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                            + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                            + "'custodias_posicoes_historicos_detalhes_consultar', \n"
                            + "'data_movimento=" + vData + "', \n"
                            + "'', \n"
                            + "'" + response.toString() + "', \n"
                            + "'SATSERVER', \n"
                            + "'" + getDataAtual("SQL") + "', \n"
                            + "'" + getDataAtual("HORA") + "')";

                    SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                    SQLPdrLog.insert();
                }
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava("JASON_LISTA: " + jsonObject.toString(), caminho);
                String vRespostaServer = response.toString();
                Persistencia dbpadrao, dbsatellite;
                //dbsatellite = this.pool.getConexao("SATELLITE");
                dbpadrao = this.pool.getConexao("SATPRESERVE");
                //dbsatellite.FechaConexao();  

                // Gravar JSON
                String vTotalRegistros = "";
                String vOrgao_centralizador = "";
                String vDataPed = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);
                String vCodBanco = "";
                String vTipoEmpresa = "";
                String vCodAgencia = "";
                String vNome_agencia_origem = "";
                String vNumeroSolicitacao = "";
                String vNumeroMalote = "";
                String vNumeroControle = "";
                String vTipoDenominacao = "";
                String vDescricaoDEnominacao = "";
                String vHorarioAtendimento = "";
                String vLocal_Atendimento = "";
                String vStatusSuprimento = "";
                String vDescricaoStatus = "";
                String vIdentificacaoApropricao = "";
                String vDescricaoApropriacao = "";
                String vSQL = "";
                String vPosicao = "";
                String vDescPosicao = "";
                String vSituacao = "";
                String vSaldoAnterior = "";
                String vSaldoAtual = "";
                String vDescSituacao = "";
                String vDt_Situacao = "";
                String vHr_Situacao = "";
                String vSaldoEntrada = "";
                String vSaldoSaida = "";
                String vValorCedulas = "";
                String vValorNaoProcCedulas = "";
                String vValorMoedas = "";
                String vValorNaoProcMoedas = "";
                String vDt_Confirma = "";
                String vHr_Confirma = "";
                String vJustificaFechamento = "";
                String vJustificaDivergencia = "";
                String vSaldoDivergencia = "";
                String vSaldoEntradaDivergencia = "";
                String vSaldoSaidaDivergencia = "";
                String vSaldoTotalDivergencia = "";
                String vSaldoAnteriorPadrao = "";
                String vSaldoAtualPadrao = "";
                String vSaldoDivergenciaPadrao = "";
                String vJustificaDivergenciaPadrao = "";
                String vValorCedulasDivergencia = "";
                String vValorNaoProcCedulasDivergencia = "";
                String vValorMoedasDivergencia = "";
                String vValorNaoProcMoedasDivergencia = "";
                int vPedido = 0;
                int vQtdePed = 0;
                String vIndice_controle_solicitacao = "";

                this.logerro.Grava("CRIA ARRAY: ", caminho);
                this.logerro.Grava("CRIA ARRAY: ", caminho);
                JsonArray jPedidoA = jsonObject.getAsJsonArray("data");
                if (!jsonObject.get("data").equals(null)) {
                    //vOrgao_centralizador = jsonObject.get("orgao_centralizador").toString();
                    //vDataPed = jsonObject.get("data_solicitacao").toString();
                    for (int i = 0; i < jPedidoA.size(); i++) {
                        JsonObject jPedido = jPedidoA.get(i).getAsJsonObject();

                        vIndice_controle_solicitacao = vChave;
                        vPosicao = jPedido.getAsJsonPrimitive("CodigoTipoPosicao").getAsString();
                        vDescPosicao = jPedido.getAsJsonPrimitive("descricao_posicao").getAsString();
                        vSituacao = jPedido.getAsJsonPrimitive("codigo_situacao_posicao").getAsString();
                        vDescSituacao = jPedido.getAsJsonPrimitive("descricao_situacao_posicao").getAsString();
                        vDt_Situacao = jPedido.getAsJsonPrimitive("data_cadastramento").getAsString();
                        vHr_Situacao = jPedido.getAsJsonPrimitive("horario_cadastramento").getAsString();
                        vDt_Confirma = jPedido.getAsJsonPrimitive("data_confirmacao").getAsString();
                        vHr_Confirma = jPedido.getAsJsonPrimitive("horario_confirmacao").getAsString();
                        vJustificaFechamento = jPedido.getAsJsonPrimitive("justificativa_fechamento").getAsString();
                        vSaldoAnterior = jPedido.getAsJsonPrimitive("valor_saldo_anterior_agrupamento").getAsString();
                        vSaldoAtual = jPedido.getAsJsonPrimitive("valor_saldo_atual_agrupamento").getAsString();
                        vSaldoDivergencia = jPedido.getAsJsonPrimitive("valor_divergencia_agrupamento").getAsString();
                        vJustificaDivergencia = jPedido.getAsJsonPrimitive("justificativa_divergencia_agrupamento").getAsString();
                        vSaldoEntrada = jPedido.getAsJsonPrimitive("valor_entrada_agrupamento").getAsString();
                        vSaldoEntradaDivergencia = jPedido.getAsJsonPrimitive("valor_divergencia_entrada_agrupamento").getAsString();
                        vSaldoSaida = jPedido.getAsJsonPrimitive("valor_saida_agrupamento").getAsString();
                        vSaldoSaidaDivergencia = jPedido.getAsJsonPrimitive("valor_divergencia_saida_agrupamento").getAsString();
                        vSaldoAnteriorPadrao = jPedido.getAsJsonPrimitive("valor_saldo_anterior_padrao").getAsString();
                        vSaldoAtualPadrao = jPedido.getAsJsonPrimitive("valor_saldo_atual_padrao").getAsString();
                        vSaldoDivergenciaPadrao = jPedido.getAsJsonPrimitive("valor_divergencia_padrao").getAsString();
                        vJustificaDivergenciaPadrao = jPedido.getAsJsonPrimitive("justificativa_divergencia_padrao").getAsString();
                        vValorCedulas = jPedido.getAsJsonPrimitive("valor_total_cedulas").getAsString();
                        vValorCedulasDivergencia = jPedido.getAsJsonPrimitive("valor_divergencia_cedulas").getAsString();
                        vValorNaoProcCedulas = jPedido.getAsJsonPrimitive("valor_nao_processado_cedulas").getAsString();
                        vValorNaoProcCedulasDivergencia = jPedido.getAsJsonPrimitive("valor_divergencia_nao_processado_cedulas").getAsString();
                        vValorMoedas = jPedido.getAsJsonPrimitive("valor_total_moedas").getAsString();
                        vValorMoedasDivergencia = jPedido.getAsJsonPrimitive("valor_divergencia_moedas").getAsString();
                        vValorNaoProcMoedas = jPedido.getAsJsonPrimitive("valor_nao_processado_moedas").getAsString();
                        vValorNaoProcMoedasDivergencia = jPedido.getAsJsonPrimitive("valor_divergencia_nao_processado_moedas").getAsString();
                        //vCodBanco = jPedido.getAsJsonPrimitive("codigo_banco").getAsString();
                        Consulta qTmpX, qTmpX2, qTmpXPed;
                        //Grava Log JSON 28/11/2023
                        vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                                + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                                + "'custodias_posicoes_historicos_detalhes_consultar', \n"
                                + "'data_atendimento=" + vData + "', \n"
                                + "'" + vIndice_controle_solicitacao + "', \n"
                                + "'" + response.toString() + "', \n"
                                + "'SATSERVER', \n"
                                + "'" + getDataAtual("SQL") + "', \n"
                                + "'" + getDataAtual("HORA") + "')" + ";"
                                + "Insert into ItauIntegraDet(Sequencia, TipoOperacao, Inf_Ctrl_Solic, Posicao, DescPosicao, XMLJSON, SaldoAnterior, \n"
                                + " SaldoAtual, SaldoDivergencia, Justifica_DivSaldo, Situacao, "
                                + " DescSituacao, Dt_Situacao, Hr_situacao, Dt_Confirmacao, Hr_Confirmacao, Justifica_Fecha,  \n"
                                + "SaldoEntrada, SaldoEntradaDiv,  SaldoSaida, SaldoSaidaDiv, SaldoAntPadrao, SaldoAtualPadrao, \n"
                                + "SaldoPadraoDiv, Justifica_DivPadrao,  \n"
                                + " ValorCedulas, ValorCedulasDiv, ValorNaoProcCedulas, ValorNaoProcCedDiv, ValorMoedas, ValorMoedasDiv, ValorNaoProcMoedas, ValorNaoProcMoeDiv, Status,  Operador, Dt_Incl, Hr_Incl)Values("
                                + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegraDet), \n"
                                + "'custodias_posicoes_historicos_detalhes_consultar', \n"
                                + "'" + vIndice_controle_solicitacao + "', \n"
                                + "'" + vPosicao + "', \n"
                                + "'" + vDescPosicao + "', \n"
                                + "'" + response.toString() + "', \n"
                                + vSaldoAnterior + ", \n"
                                + vSaldoAtual + ", \n"
                                + vSaldoDivergencia + ", \n"
                                + "'" + vJustificaDivergencia + "', \n"
                                + "'" + vSituacao + "', \n"
                                + "'" + vDescSituacao + "', \n"
                                + "'" + vDt_Situacao + "', \n"
                                + "'" + vHr_Situacao + "', \n"
                                + "'" + vDt_Confirma + "', \n"
                                + "'" + vHr_Confirma + "', \n"
                                + "'" + vJustificaFechamento + "', \n"
                                + vSaldoEntrada + ", \n"
                                + vSaldoEntradaDivergencia + ", \n"
                                + vSaldoSaida + ", \n"
                                + vSaldoSaidaDivergencia + ", \n"
                                + vSaldoAnteriorPadrao + ", \n"
                                + vSaldoAtualPadrao + ", \n"
                                + vSaldoDivergenciaPadrao + ", \n"
                                + "'" + vJustificaDivergenciaPadrao + "', \n"
                                + vValorCedulas + ", \n"
                                + vValorCedulasDivergencia + ", \n"
                                + vValorNaoProcCedulas + ", \n"
                                + vValorNaoProcCedulasDivergencia + ", \n"
                                + vValorMoedas + ", \n"
                                + vValorMoedasDivergencia + ", \n"
                                + vValorNaoProcMoedas + ", \n"
                                + vValorNaoProcMoedasDivergencia + ", \n"
                                + "'OK', \n"
                                + "'SATSERVER', \n"
                                + "'" + getDataAtual("SQL") + "', \n"
                                + "'" + getDataAtual("HORA") + "')" + ";";
                        this.logerro.Grava(" SQL: " + vSQLLog, caminho);
                        SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                        SQLPdrLog.insert();
                        this.logerro.Grava("Gravou SQL: ", caminho);
                        vSQL = "";
                        vSQLLog = "Insert into ItauIntegraList(Sequencia, TipoOperacao, Inf_Ctrl_Solic, Dt_movimento, Posicao, DescPosicao, XMLJSON, Situacao, DescSituacao, SaldoAnterior, SaldoAtual,  "
                                + "  SaldoEntrada, SaldoSaida, ValorCedulas, ValorNaoProcCedulas, ValorMoedas, ValorNaoProcMoedas, Status, Operador, Dt_Incl, Hr_Incl)Values("
                                + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegraList), \n"
                                + "'custodias_posicoes_historicos_detalhes_consultar', \n"
                                + "'" + vIndice_controle_solicitacao + "', \n"
                                + "'" + vDt_Situacao + "', \n"
                                + "'" + vPosicao + "', \n"
                                + "'" + vDescPosicao + "', \n"
                                + "'" + response.toString() + "', \n"
                                + "'" + vSituacao + "', \n"
                                + "'" + vDescSituacao + "', \n"
                                + vSaldoAnterior + ", \n"
                                + vSaldoAtual + ", \n"
                                + vSaldoEntrada + ", \n"
                                + vSaldoSaida + ", \n"
                                + vValorCedulas + ", \n"
                                + vValorNaoProcCedulas + ", \n"
                                + vValorMoedas + ", \n"
                                + vValorNaoProcMoedas + ", \n"
                                + "'PD', \n"
                                + "'SATSERVER', \n"
                                + "'" + getDataAtual("SQL") + "', \n"
                                + "'" + getDataAtual("HORA") + "')" + ";";
                        this.logerro.Grava(" 2 SQL: " + vSQLLog, caminho);
                        SQLPdrLogList = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                        SQLPdrLogList.insert();
                        //Busca Agrupamentos
                        if (!jPedido.get("agrupamento_ocorrencia_lista").equals(null)) {
                            JsonArray jPedidoB = jPedido.getAsJsonArray("agrupamento_ocorrencia_lista");
                            for (int x = 0; x < jPedidoB.size(); x++) {
                                JsonObject jPedidoList = jPedidoB.get(x).getAsJsonObject();
                                String vIdentificador;
                                String vDescIdentificador;
                                String vValorEntrada;
                                String vValorEntradaDiv;
                                String vValorSaida;
                                String vValorSaidaDiv;

                                vIdentificador = jPedidoList.getAsJsonPrimitive("identificador_item_agrupamento").getAsString();
                                vDescIdentificador = jPedidoList.getAsJsonPrimitive("descricao_item_agrupamento").getAsString();
                                vValorEntrada = jPedidoList.getAsJsonPrimitive("valor_entrada_item_agrupamento").getAsString();
                                vValorEntradaDiv = jPedidoList.getAsJsonPrimitive("valor_divergencia_entrada_item_agrupamento").getAsString();
                                vValorSaida = jPedidoList.getAsJsonPrimitive("valor_saida_item_agrupamento").getAsString();
                                vValorSaidaDiv = jPedidoList.getAsJsonPrimitive("valor_divergencia_saida_item_agrupamento").getAsString();

                                vSQLLog = "Insert into ItauIntegraAgrup(Sequencia, Inf_Ctrl_Solic, Identificador, TipoOperacao, Desc_Identif, Valor_Entrada,Valor_EntradaDiv, Valor_Saida,  "
                                        + " Valor_SaidaDiv, Status, Operador, Dt_Incl, Hr_Incl)Values("
                                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegraAgrup), \n"
                                        + "'" + vIndice_controle_solicitacao + "', \n"
                                        + "'" + vIdentificador + "', \n"
                                        + "'custodias_posicoes_historicos_detalhes_consultar', \n"
                                        + "'" + vDescIdentificador + "', \n"
                                        + vValorEntrada + ", \n"
                                        + vValorEntradaDiv + ", \n"
                                        + vValorSaida + ", \n"
                                        + vValorSaidaDiv + ", \n"
                                        + "'PD', \n"
                                        + "'SATSERVER', \n"
                                        + "'" + getDataAtual("SQL") + "', \n"
                                        + "'" + getDataAtual("HORA") + "')" + ";";
                                this.logerro.Grava(" SQL: " + vSQLLog, caminho);
                                SQLPdrLogAgrup = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                                SQLPdrLogAgrup.insert();
                            }
                        }
                        if (!jPedido.get("cedulas_ocorrencia_lista").equals(null)) {
                            JsonArray jPedidoC = jPedido.getAsJsonArray("cedulas_ocorrencia_lista");
                            for (int y = 0; y < jPedidoC.size(); y++) {
                                JsonObject jPedidoAgrup = jPedidoC.get(y).getAsJsonObject();
                                String vCod_Familia;
                                String vCod_Especie;
                                String vTipo_Especie;
                                String vValorTotal;
                                String vValorDivergencia;

                                vCod_Familia = jPedidoAgrup.getAsJsonPrimitive("codigo_familia_cedula").getAsString();
                                vCod_Especie = jPedidoAgrup.getAsJsonPrimitive("codigo_classificacao_cedula").getAsString();
                                vTipo_Especie = jPedidoAgrup.getAsJsonPrimitive("tipo_especie").getAsString();
                                vValorTotal = jPedidoAgrup.getAsJsonPrimitive("valor_especie").getAsString();
                                vValorDivergencia = jPedidoAgrup.getAsJsonPrimitive("valor_divergencia_item_cedula").getAsString();

                                vSQLLog = "Insert into ItauIntegraComp(Sequencia, Inf_Ctrl_Solic, Cod_Familia,  Cod_Especie, Tipo_Especie, TipoOperacao, ValorTotal, "
                                        + " ValorDivergencia, Status, Operador, Dt_Incl, Hr_Incl)Values("
                                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegraComp), \n"
                                        + "'" + vIndice_controle_solicitacao + "', \n"
                                        + "'" + vCod_Familia + "', \n"
                                        + "'" + vCod_Especie + "', \n"
                                        + "'" + vTipo_Especie + "', \n"
                                        + "'custodias_posicoes_historicos_detalhes_consultar', \n"
                                        + vValorTotal + ", \n"
                                        + vValorDivergencia + ", \n"
                                        + "'PD', \n"
                                        + "'SATSERVER', \n"
                                        + "'" + getDataAtual("SQL") + "', \n"
                                        + "'" + getDataAtual("HORA") + "')" + ";";
                                this.logerro.Grava(" SQL: " + vSQLLog, caminho);
                                SQLPdrLogAgrup = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                                SQLPdrLogAgrup.insert();
                            }
                        }
                        if (!jPedido.get("moedas_ocorrencia_lista").equals(null)) {
                            JsonArray jPedidoD = jPedido.getAsJsonArray("moedas_ocorrencia_lista");
                            for (int y = 0; y < jPedidoD.size(); y++) {
                                JsonObject jPedidoAgrup = jPedidoD.get(y).getAsJsonObject();
                                String vCod_Familia;
                                String vCod_Especie;
                                String vTipo_Especie;
                                String vValorTotal;
                                String vValorDivergencia;

                                vCod_Familia = jPedidoAgrup.getAsJsonPrimitive("codigo_familia_moeda").getAsString();
                                vCod_Especie = jPedidoAgrup.getAsJsonPrimitive("codigo_classificacao_moeda").getAsString();
                                vTipo_Especie = jPedidoAgrup.getAsJsonPrimitive("tipo_especie").getAsString();
                                vValorTotal = jPedidoAgrup.getAsJsonPrimitive("valor_especie").getAsString();
                                vValorDivergencia = jPedidoAgrup.getAsJsonPrimitive("valor_divergencia_item_moeda").getAsString();

                                vSQLLog = "Insert into ItauIntegraComp(Sequencia, Inf_Ctrl_Solic, Cod_Familia,  Cod_Especie, Tipo_Especie, TipoOperacao, ValorTotal, "
                                        + " ValorDivergencia, Status, Operador, Dt_Incl, Hr_Incl)Values("
                                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegraComp), \n"
                                        + "'" + vIndice_controle_solicitacao + "', \n"
                                        + "'" + vCod_Familia + "', \n"
                                        + "'" + vCod_Especie + "', \n"
                                        + "'" + vTipo_Especie + "', \n"
                                        + "'custodias_posicoes_historicos_detalhes_consultar', \n"
                                        + vValorTotal + ", \n"
                                        + vValorDivergencia + ", \n"
                                        + "'PD', \n"
                                        + "'SATSERVER', \n"
                                        + "'" + getDataAtual("SQL") + "', \n"
                                        + "'" + getDataAtual("HORA") + "')" + ";";
                                this.logerro.Grava(" SQL: " + vSQLLog, caminho);
                                SQLPdrLogAgrup = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                                SQLPdrLogAgrup.insert();
                            }
                        }
                    }
                    vRetorno = "-RespostaCompleta:" + response.toString();
                } else {
                    vRetorno = response.toString();
                }
                bufferedReader.close();
            }
            //vRetorno = response.toString() + bufferedReader.toString();            
        } catch (Exception e) {
            vRetorno = e.toString() + " EXCEPTION CUSTODIA LISTA - Resposta EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String confirmaPosicaoPresumida(String clientId, String vClientSecret, File certificado, String senhaCertificado, String Token, String vData, String vStatus, String vChave, String vMensagem) {
        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {
            this.logerro.Grava("Ëntrou Confirma:", caminho);
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_presumida_confirmar");
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_consultar?data_movimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            connection.setRequestMethod("POST");
            connection.setDoOutput(true);

            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            String vRespostaServer = response.toString();
            Persistencia dbpadrao, dbsatellite;
            dbpadrao = this.pool.getConexao("SATPRESERVE");
            Consulta qTmpX;
            String vSQL;
            int vConta = 0;
            String vIndiceSolicitacao = "";
            if (!vChave.equals("")) {
                vIndiceSolicitacao = vChave;
            } else {
                this.logerro.Grava("Presumida - Nao tem Chave", caminho);
                vSQL = "Select Top 1 * from ItauIntegraDet\n"
                        + "where TipoOperacao like '%CUSTO%'\n"
                        + " and Posicao = 'C'\n"
                        + " and Status = 'PD'\n"
                        + "order by Sequencia desc";
                try {
                    qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                            + vSQL);
                }
                if (qTmpX.Proximo()) {
                    vConta++;
                    vIndiceSolicitacao = qTmpX.getString("inf_Ctrl_Solic");
                }
            }
            Consulta SQLPdrLog;
            String vSQLLog;
            String body = "";
            if (!vIndiceSolicitacao.equals("")) {
                this.logerro.Grava("Presumida - tem Chave: " + vIndiceSolicitacao, caminho);
                if (vStatus.equals("S")) {
                    body = "{\n"
                            + " \"indice_controle_posicao\": \"" + vIndiceSolicitacao + "\",\n"
                            + " \"codigo_concordancia_presumida\": \"" + vStatus + "\"\n"
                            + "}";
                } else {
                    body = "{\n"
                            + " \"indice_controle_posicao\": \"" + vIndiceSolicitacao + "\",\n"
                            + " \"codigo_concordancia_presumida\": \"" + vStatus + "\",\n"
                            + " \"justificativa_presumida\": \"" + vMensagem + "\"\n"
                            + "}";
                }
                //Insere envio Solicitacao
                this.logerro.Grava("Presumida - Body: " + body, caminho);
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_posicoes_presumida_confirmar', \n"
                        + "'SOLICITA', \n"
                        + "'" + vIndiceSolicitacao + "', \n"
                        + "'" + body + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
                this.logerro.Grava("SQL: " + vSQLLog, caminho);
                OutputStream outputStream = connection.getOutputStream();
                outputStream.write(body.toString().getBytes());
                outputStream.close();

                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

                String line = null;
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                }
                //JsonParser jsonParser = new JsonParser();
                //JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava("RESPOSTA" + response.toString(), caminho);

                connection.disconnect();
                vRetorno = response.toString();
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_posicoes_presumida_confirmar', \n"
                        + "'data_atendimento=" + vData + "', \n"
                        + "'" + vIndiceSolicitacao + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
                //vRetorno =  + bufferedReader.toString();

                bufferedReader.close();
            }
        } catch (Exception e) {
            vRetorno = e.toString();
            this.logerro.Grava("RETORNO" + vRetorno, caminho);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return vRetorno;
    }

    public String incluirPosicaoParcialjson(String clientId, String vClientSecret, File certificado, String senhaCertificado, String Token, String vData, String vStatus, String vChave, String vJson) throws Exception {
        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {
            this.logerro.Grava("Ëntrou Inclusao Parcial:", caminho);

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_incluir");

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            connection.setRequestMethod("POST");
            connection.setDoOutput(true);

            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            String vRespostaServer = response.toString();
            Persistencia dbpadrao, dbsatellite;
            dbpadrao = this.pool.getConexao("SATPRESERVE");
            Consulta qTmpX;
            String vSQL;
            int vConta = 0;
            String vIndiceSolicitacao = "";
            if (!vJson.equals("")) {
            } else {
                if (!vChave.equals("")) {
                    vIndiceSolicitacao = vChave;
                } else {
                    this.logerro.Grava("Presumida - Nao tem Chave", caminho);
                    vSQL = "Select Top 1 * from ItauIntegraDet\n"
                            + "where TipoOperacao like '%CUSTO%'\n"
                            + " and Posicao = 'C'\n"
                            + " and Status = 'PD'\n"
                            + "order by Sequencia desc";
                    try {
                        qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                        qTmpX.select();
                    } catch (Exception e) {
                        logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                        throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                                + vSQL);
                    }
                    if (qTmpX.Proximo()) {
                        vConta++;
                        vIndiceSolicitacao = qTmpX.getString("inf_Ctrl_Solic");
                    }
                }
            }
            Consulta SQLPdrLog;
            String vSQLLog;
            String body = "";
            if (!vJson.equals("")) {
                this.logerro.Grava("Inclusao PArcial: - tem Json: " + vJson, caminho);
                body = vJson;

                //Insere envio Solicitacao
                this.logerro.Grava("Incluir_Parcial - Body: " + body, caminho);
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_posicoes_parciais_incluir', \n"
                        + "'SOLICITA', \n"
                        + "'" + vIndiceSolicitacao + "', \n"
                        + "'" + body + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
                this.logerro.Grava("SQL: " + vSQLLog, caminho);
                OutputStream outputStream = connection.getOutputStream();
                outputStream.write(body.toString().getBytes());
                outputStream.close();

                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

                String line = null;
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                }
                //JsonParser jsonParser = new JsonParser();
                //JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava("RESPOSTA" + response.toString(), caminho);

                connection.disconnect();
                vRetorno = response.toString();
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_posicoes_parciais_incluir', \n"
                        + "'data_atendimento=" + vData + "', \n"
                        + "'" + vIndiceSolicitacao + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
                //vRetorno =  + bufferedReader.toString();

                bufferedReader.close();
            }
        } catch (Exception e) {
            vRetorno = e.toString();
            this.logerro.Grava("RETORNO" + vRetorno, caminho);
            String vSQLLogErr = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'custodias_posicoes_parciais_incluir', \n"
                    + "'data_atendimento=" + vData + "', \n"
                    + "'" + "ERRO" + "', \n"
                    + "'" + vRetorno + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            Consulta SQLPdrLogErr = new Consulta(vSQLLogErr, this.pool.getConexao("SATPRESERVE"));

            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return vRetorno;
    }

    public String incluirPosicaoFechamentojson(String clientId, String vClientSecret, File certificado, String senhaCertificado, String Token, String vData, String vStatus, String vChave, String vJson) throws Exception {
        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {
            this.logerro.Grava("Ëntrou Inclusao Parcial:", caminho);

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_fechamento_incluir");

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            connection.setRequestMethod("POST");
            connection.setDoOutput(true);

            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            String vRespostaServer = response.toString();
            Persistencia dbpadrao, dbsatellite;
            dbpadrao = this.pool.getConexao("SATPRESERVE");
            Consulta qTmpX;
            String vSQL;
            int vConta = 0;
            String vIndiceSolicitacao = "";
            if (!vJson.equals("")) {
            } else {
                if (!vChave.equals("")) {
                    vIndiceSolicitacao = vChave;
                } else {
                    this.logerro.Grava("Presumida - Nao tem Chave", caminho);
                    vSQL = "Select Top 1 * from ItauIntegraDet\n"
                            + "where TipoOperacao like '%CUSTO%'\n"
                            + " and Posicao = 'C'\n"
                            + " and Status = 'PD'\n"
                            + "order by Sequencia desc";
                    try {
                        qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                        qTmpX.select();
                    } catch (Exception e) {
                        logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                        throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                                + vSQL);
                    }
                    if (qTmpX.Proximo()) {
                        vConta++;
                        vIndiceSolicitacao = qTmpX.getString("inf_Ctrl_Solic");
                    }
                }
            }
            Consulta SQLPdrLog;
            String vSQLLog;
            String body = "";
            if (!vJson.equals("")) {
                this.logerro.Grava("Inclusao PArcial: - tem Json: " + vJson, caminho);
                body = vJson;

                //Insere envio Solicitacao
                this.logerro.Grava("Incluir_Parcial - Body: " + body, caminho);
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_posicoes_parciais_incluir', \n"
                        + "'SOLICITA', \n"
                        + "'" + vIndiceSolicitacao + "', \n"
                        + "'" + body + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
                this.logerro.Grava("SQL: " + vSQLLog, caminho);
                OutputStream outputStream = connection.getOutputStream();
                outputStream.write(body.toString().getBytes());
                outputStream.close();

                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

                String line = null;
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                }
                //JsonParser jsonParser = new JsonParser();
                //JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava("RESPOSTA" + response.toString(), caminho);

                connection.disconnect();
                vRetorno = response.toString();
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_posicoes_parciais_incluir', \n"
                        + "'data_atendimento=" + vData + "', \n"
                        + "'" + vIndiceSolicitacao + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
                //vRetorno =  + bufferedReader.toString();

                bufferedReader.close();
            }
        } catch (Exception e) {
            vRetorno = e.toString();
            this.logerro.Grava("RETORNO" + vRetorno, caminho);
            String vSQLLogErr = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                    + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                    + "'custodias_posicoes_parciais_incluir', \n"
                    + "'data_atendimento=" + vData + "', \n"
                    + "'" + "ERRO" + "', \n"
                    + "'" + vRetorno + "', \n"
                    + "'SATSERVER', \n"
                    + "'" + getDataAtual("SQL") + "', \n"
                    + "'" + getDataAtual("HORA") + "')";

            Consulta SQLPdrLogErr = new Consulta(vSQLLogErr, this.pool.getConexao("SATPRESERVE"));

            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return vRetorno;
    }

    
    public String incluirPosicaoParcial(String clientId, String vClientSecret, File certificado, String senhaCertificado, String Token, String vData, String vStatus, String vChave) {
        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {
            this.logerro.Grava("Entrou Confirma Parcial:", caminho);
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_incluir");

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            connection.setRequestMethod("POST");
            connection.setDoOutput(true);

            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            // String vRespostaServer = response.toString();
            Persistencia dbpadrao, dbsatellite;
            dbpadrao = this.pool.getConexao("SATPRESERVE");
            Consulta qTmpX;
            String vSQL;
            int vConta = 0;
            logexecucao.Grava("Passou conexao: ", caminho);
            String vIndiceSolicitacao = "";
            if (!vChave.equals("")) {
                vIndiceSolicitacao = vChave;
            } else {
                this.logerro.Grava("Inclusao Parcial - Nao tem Chave", caminho);
                vSQL = "Select Top 1 * from ItauIntegraDet\n"
                        + "where TipoOperacao like '%CUSTO%'\n"
                        + " and Posicao = 'P'\n"
                        + " and Status = 'PD'\n"
                        + "order by Sequencia desc";
                try {
                    qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                            + vSQL);
                }
                if (qTmpX.Proximo()) {
                    vConta++;
                    vIndiceSolicitacao = qTmpX.getString("inf_Ctrl_Solic");
                }
            }
            String vCodFamilia = "";
            String vValorNaoProcMoe = "";
            String vValorNaoProcCed = "";
            String vQtdeContas = "";
            String vQtdeOcorCed = "";
            String vQtdeOcorMoe = "";
            String vValorEntrada = "";
            String vCodConta = "";
            String vCodMensage = "";
            String vCodMoeda = "";
            String vTipoMoeda = "";
            String vCed001 = "";
            String vCed002 = "";
            String vCed005 = "";
            String vCed010 = "";
            String vCed020 = "";
            String vCed050 = "";
            String vCed100 = "";
            String vCed200 = "";
            String vMoe001 = "";
            String vMoe005 = "";
            String vMoe010 = "";
            String vMoe025 = "";
            String vMoe050 = "";
            String vMoe100 = "";
            String vCodJustEntrada = "";
            String vMsgJustEntrada = "";
            String vValorSaida = "";
            String vCodJustSaida = "";
            String vMsgJustSaida = "";
            Consulta SQLPdrLog;
            String vSQLLog;
            if (!vIndiceSolicitacao.equals("")) {
                this.logerro.Grava("Inclusao Parcial - tem Chave: " + vIndiceSolicitacao, caminho);

                String body = "{\n"
                        + "\n"
                        + "	\"indice_controle_posicao\": \"" + vIndiceSolicitacao + "\",\n"
                        + "\n"
                        + "	\"valor_nao_processado_cedulas\": 14000000.00,\n"
                        + "\n"
                        + "	\"valor_nao_processado_moedas\": 0.00,\n"
                        + "\n"
                        + "	\"quantidade_agrupamentos_valores\": 1,\n"
                        + "\n"
                        + "	\"quantidade_ocorrencias_cedulas\": 1,\n"
                        + "\n"
                        + "	\"quantidade_ocorrencias_moedas\": 1,\n"
                        + "\n"
                        + "	\"agrupamentos\": [\n"
                        + "\n"
                        + "		{\n"
                        + "\n"
                        + "			\"identificador\": \"004\",\n"
                        + "\n"
                        + "			\"valor_entrada\": 203000.00,\n"
                        + "\n"
                        + "			\"codigo_mensagem_justificativa_entrada\": \"026\",\n"
                        + "\n"
                        + "			\"mensagem_justificativa_entrada\": \"NAO PROCESSADO VALIDACAO\",\n"
                        + "\n"
                        + "			\"valor_saida\": 0.00,\n"
                        + "\n"
                        + "			\"codigo_mensagem_justificativa_saida\": \"\",\n"
                        + "\n"
                        + "			\"mensagem_justificativa_saida\": \"\"\n"
                        + "\n"
                        + "		}\n"
                        + "\n"
                        + "	],\n"
                        + "\n"
                        + "	\"cedulas\": [\n"
                        + "\n"
                        + "		{\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0005\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"C\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"100,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 2500.00\n"
                        + "\n"
                        + "		}\n"
                        + "\n"
                        + "	],\n"
                        + "\n"
                        + "	\"moedas\": [\n"
                        + "\n"
                        + "		{\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0001\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"M\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"1,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 5000.00\n"
                        + "\n"
                        + "		}\n"
                        + "\n"
                        + "	],\n"
                        + "\n"
                        + "	\"mensagem_justificativa_padrao\": \"NAO PROCESSADO\"\n"
                        + "\n"
                        + "}";
                /*
                String body = "{\n"
                        + "\n"
                        + "	\"indice_controle_posicao\": \"" + vIndiceSolicitacao + "\",\n"
                        + "\n"
                        + "	\"valor_nao_processado_cedulas\": 20917963.18,\n"
                        + "\n"
                        + "	\"valor_nao_processado_moedas\": 0.18,\n"
                        + "\n"
                        + "	\"quantidade_agrupamentos_valores\": 1,\n"
                        + "\n"
                        + "	\"quantidade_ocorrencias_cedulas\": 11,\n"
                        + "\n"
                        + "	\"quantidade_ocorrencias_moedas\": 1,\n"
                        + "\n"
                        + "	\"agrupamentos\": [\n"
                        + "\n"
                        + "		{\n"
                        + "\n";
                String bodyAgrup = "			\"identificador\": \"006\",\n"
                        + "\n"
                        + "			\"valor_entrada\": 50000.00,\n"
                        + "\n"
                        + "			\"codigo_mensagem_justificativa_entrada\": \"026\",\n"
                        + "\n"
                        + "			\"mensagem_justificativa_entrada\": \"VALOR PRODUCAO\",\n"
                        + "\n"
                        + "			\"valor_saida\": 0.00,\n"
                        + "\n"
                        + "			\"codigo_mensagem_justificativa_saida\": \"\",\n"
                        + "\n"
                        + "			\"mensagem_justificativa_saida\": \"\"\n"
                        + "\n"
                        + "		}\n"
                        + "\n";
                     vQtdeAgrup > 1
                        + "	],\n"
                        + "\n"
                    String bodyCedulas = "	\"cedulas\": [\n"
                        + "\n"
                        + "		{\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0005\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"C\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"200,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 1443800.00\n"
                        + "\n";
                    vQtdeCed > 1
                        + "		},\n"
                     
                        + "        {\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0005\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"C\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"100,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 4052000.00\n"
                        + "\n"
                        + "		},\n"
                        + "        {\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0005\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"C\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"50,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 3118250.00\n"
                        + "\n"
                        + "		},\n"
                        + "        {\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0005\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"C\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"20,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 592660.00\n"
                        + "\n"
                        + "		},\n"
                        + "        {\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0005\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"C\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"10,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 109540.00\n"
                        + "\n"
                        + "		},\n"
                        + "        {\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0005\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"C\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"5,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 329285.00\n"
                        + "\n"
                        + "		},\n"
                        + "        {\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0005\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"C\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"2,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 99622.00\n"
                        + "\n"
                        + "		},\n"
                        + "        {\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0003\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"C\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"100,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 10371200.00\n"
                        + "\n"
                        + "		},\n"
                        + "        {\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0003\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"C\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"20,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 673880.00\n"
                        + "\n"
                        + "		},\n"
                        + "        {\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0003\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"C\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"10,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 776880.00\n"
                        + "\n"
                        + "		},\n"
                        + "        {\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0003\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"C\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"1,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 4.00\n"
                        + "\n"
                        + "		}\n"
                        + "	],\n"
                        + "\n"
                        + "	\"moedas\": [\n"
                        + "\n"
                        + "		{\n"
                        + "\n"
                        + "			\"codigo_familia_especie\": \"0001\",\n"
                        + "\n"
                        + "			\"codigo_classificacao_especie\": \"M\",\n"
                        + "\n"
                        + "			\"tipo_especie\": \"1,00\",\n"
                        + "\n"
                        + "			\"valor_total_especie\": 5000.00\n"
                        + "\n"
                        + "		}\n"
                        + "\n"
                        + "	],\n"
                        + "\n"
                        + "	\"mensagem_justificativa_padrao\": \"NAO PROCESSADO\"\n"
                        + "\n"
                        + "}";
                 */
                //Insere envio Solicitacao
                this.logerro.Grava("Presumida - Body: " + body, caminho);
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_posicoes_presumida_confirmar', \n"
                        + "'SOLICITA', \n"
                        + "'" + vIndiceSolicitacao + "', \n"
                        + "'" + body + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
                this.logerro.Grava("SQL: " + vSQLLog, caminho);
                OutputStream outputStream = connection.getOutputStream();
                outputStream.write(body.toString().getBytes());
                outputStream.close();

                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

                String line = null;
                while ((line = bufferedReader.readLine()) != null) {
                    response.append(line);
                }
                //JsonParser jsonParser = new JsonParser();
                //JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava("RESPOSTA" + response.toString(), caminho);

                connection.disconnect();
                vRetorno = response.toString();
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_posicoes_presumida_confirmar', \n"
                        + "'data_atendimento=" + vData + "', \n"
                        + "'" + vIndiceSolicitacao + "', \n"
                        + "'" + vRetorno + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
                //vRetorno =  + bufferedReader.toString();

                bufferedReader.close();
            }
        } catch (Exception e) {
            vRetorno = vRetorno + "-" + e.toString();
            this.logerro.Grava("RETORNO" + vRetorno, caminho);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return vRetorno;
    }

    public String consultaMovimentacaoPendente(String vClientID, String vClientSecret, File certificado, String senhaCertificado, String vToken, String vData) {
        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_movimentacoes_pendentes_consultar?data_movimentacao=" + vData);
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_consultar?data_movimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", vClientID);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + vToken);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "hom-secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            Consulta SQLPdrLog;
            Consulta SQLPdrLogList;
            Consulta SQLPdrLogAgrup;
            Consulta SQLPdrLogComp;

            String vSQLLog;
            this.logerro.Grava("custodias_movimentacoes_pendentes_consultar Resposta Server XXX :" + vResposta, caminho);
            if (vResposta == 404) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_movimentacoes_pendentes_consultar', \n"
                        + "'data_movimentacao=" + vData + "', \n"
                        + "'', \n"
                        + "'SEM RESPOSTA DE CUSTODIA MOVIMENTACAO- " + vResposta + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else {

                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

                //InputStream vErro = connection.getErrorStream();
                String vErro2;
                BufferedReader br = null;

                this.logerro.Grava("custodias_movimentacoes_pendentes_consultar Resposta1 Else:" + vResposta, caminho);
                while ((vErro2 = bufferedReader.readLine()) != null) {
                    response.append(vErro2);
                }
                this.logerro.Grava("custodias_movimentacoes_pendentes_consultar Buffer" + response.toString(), caminho);
                if (vResposta == 400 || vResposta == 200) {
                    vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                            + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                            + "'custodias_movimentacoes_pendentes_consultar', \n"
                            + "'data_movimentacao=" + vData + "', \n"
                            + "'', \n"
                            + "'" + response.toString() + "', \n"
                            + "'SATSERVER', \n"
                            + "'" + getDataAtual("SQL") + "', \n"
                            + "'" + getDataAtual("HORA") + "')";

                    SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                    SQLPdrLog.insert();
                }
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava("JASON_LISTA: " + jsonObject.toString(), caminho);
                String vRespostaServer = response.toString();
                Persistencia dbpadrao, dbsatellite;
                //dbsatellite = this.pool.getConexao("SATELLITE");
                dbpadrao = this.pool.getConexao("SATPRESERVE");
                //dbsatellite.FechaConexao();  

                // Gravar JSON                                
                String vData_movimentacao = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);

                //bufferedReader.close();
            }
            //vRetorno = response.toString() + bufferedReader.toString();            
        } catch (Exception e) {
            vRetorno = e.toString() + " EXCEPTION CUSTODIA Mov - Resposta EndPoint:" + response.toString();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String consultaMovimentacaoDetalhe(String vClientID, String vClientSecret, File certificado, String senhaCertificado, String vToken, String vData, String vChave, String vIndiceControle) {
        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        this.logerro.Grava("Mov:" + vChave + " - " + vIndiceControle, caminho);
        try {

            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_movimentacoes_detalhes_consultar?" + vChave);
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_consultar?data_movimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(25000);
            connection.setReadTimeout(25000);

            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", vClientID);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + vToken);

            InputStream cert = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\certificadop.crt");
            InputStream key = new FileInputStream("C:\\Clientes\\ChaveP_Privada\\EXT-LN-PRESERVE-SIN_CSR_CRT_PROD.key");

            //connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");
            connection.addRequestProperty("User-Agent", "Satellite Server");
            connection.addRequestProperty("Accept", "*/*");
            connection.addRequestProperty("Host", "hom-secure.api.cloud.itau.com.br");
            connection.addRequestProperty("Connection", "keep-alive");
            //connection.addRequestProperty("data_atendimento", vData);

            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            //keyInput.close();

            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            //KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            String vCodCli = "";
            String vNomeCli = "";
            String vHora1D = "", vHora2D = "";

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();

            this.logerro.Grava("custodias_posicoes_movimentacoes_detalhes_consultar Request Connection:" + connection.toString(), caminho);

            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }

            int vResposta = 0;
            vResposta = connection.getResponseCode();
            Consulta SQLPdrLog;
            Consulta SQLPdrLogList;
            Consulta SQLPdrLogAgrup;
            Consulta SQLPdrLogComp;

            String vSQLLog;
            ;
            this.logerro.Grava("custodias_posicoes_historicos_detalhes_consultar Resposta XXX:" + vResposta, caminho);
            if (vResposta == 404) {
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_movimentacoes_detalhes_consultar', \n"
                        + "'data_movimentacao=" + vData + "', \n"
                        + "'', \n"
                        + "'SEM RESPOSTA DE CUSTODIA MOVIMENTACAO-DETALHES " + vResposta + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else if (vResposta == 400) {
                this.logerro.Grava("custodias_posicoes_historicos_detalhes_consultar Resposta 400 OK:" + connection.getResponseMessage(), caminho);
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_movimentacoes_detalhes_consultar', \n"
                        + "'" + vChave ///+ "', \n"
                        + "'', \n"
                        + "'SEM RESPOSTA DE CUSTODIA MOVIMENTACAO-DETALHES " + vResposta + "+" + connection.getResponseMessage() + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
            } else {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));

                //InputStream vErro = connection.getErrorStream();
                String vErro2;
                BufferedReader br = null;

                this.logerro.Grava("custodias_movimentacoes_Detalhes_consultar Resposta1 Else:" + vResposta, caminho);
                while ((vErro2 = bufferedReader.readLine()) != null) {
                    response.append(vErro2);
                }

                this.logerro.Grava("custodias_movimentacoes_pendentes_consultar Buffer " + vIndiceControle + " Resp: " + response.toString(), caminho);

                if (vResposta == 400 || vResposta == 200) {
                    vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                            + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                            + "'custodias_movimentacoes_Detalhes_consultar', \n"
                            + "'" + "&" + vChave + "', \n"
                            + "'" + vIndiceControle + "', \n"
                            + "'" + response.toString() + "', \n"
                            + "'SATSERVER', \n"
                            + "'" + getDataAtual("SQL") + "', \n"
                            + "'" + getDataAtual("HORA") + "')";

                    this.logerro.Grava("custodias_movimentacoes_Detalhes_consultar LOG :" + vSQLLog, caminho);
                    SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));

                    SQLPdrLog.insert();
                }
                JsonParser jsonParser = new JsonParser();
                JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                this.logerro.Grava("JASON_LISTA Detalhes: " + jsonObject.toString(), caminho);
                String vRespostaServer = response.toString();
                Persistencia dbpadrao, dbsatellite;
                //dbsatellite = this.pool.getConexao("SATELLITE");
                dbpadrao = this.pool.getConexao("SATPRESERVE");
                //dbsatellite.FechaConexao();  

                // Gravar JSON                                
                String vData_movimentacao = vData.substring(3, 5) + "/" + vData.substring(0, 2) + "/" + vData.substring(6, 10);

                //bufferedReader.close();
            }
            //vRetorno = response.toString() + bufferedReader.toString();            
        } catch (Exception e) {
            vRetorno = e.toString() + " EXCEPTION CUSTODIA LISTA - Resposta EndPoint:" + response.toString() + " " + e.getMessage();
            this.logerro.Grava(vRetorno, caminho);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return response.toString();
    }

    public String confirmaMovimentacoes(String clientId, String vClientSecret, File certificado, String senhaCertificado, String Token, String vData, String vStatus, String vChave, String vMensagem) {
        HttpURLConnection connection = null;
        StringBuilder response = new StringBuilder();
        try {
            this.logerro.Grava("Ëntrou Confirma:", caminho);
            URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_movimentacoes_confirmar");
            //URL url = new URL("https://secure.api.cloud.itau.com.br/canaisassistidos-moedanacional/v1/custodias_posicoes_parciais_consultar?data_movimento=" + vData);

            connection = (HttpURLConnection) url.openConnection();
            connection.addRequestProperty("Content-Type", "application/json");
            connection.addRequestProperty("x-itau-apikey", clientId);
            connection.addRequestProperty("chave_unica", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbGllbnRfaWQiOiI1YmI0YmI2My00OGU2LTQzY2YtODQ3OC05MDhkNmQzNjM1NzgiLCJjbnBqX21hdHJpeiI6IjExMTc5MjY0MDAwMTcwIiwiY25wal9maWxpYWwiOiIxMTE3OTI2NDAwMDE3MCIsImNvZGlnb190cmFuc3BvcnRhZG9yYSI6IjE2MCIsImNvZGlnb19maWxpYWwiOiIwMiJ9.HyOO4FKAfLUI7jhumsupcRQy2ISoIqysgrWpsyjAyp4");

            connection.addRequestProperty("Authorization", "Bearer " + Token);

            connection.setRequestMethod("POST");
            connection.setDoOutput(true);

            File vCertificado = new File("C:\\Clientes\\ChaveP_Privada\\certificadop.pfx");
            senhaCertificado = "acesso2023";
            File p12 = certificado;
            String p12password = senhaCertificado;

            InputStream keyInput = new FileInputStream(p12);

            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            keyStore.load(keyInput, p12password.toCharArray());
            keyInput.close();

            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance("SunX509");
            keyManagerFactory.init(keyStore, p12password.toCharArray());

            SSLContext context = SSLContext.getInstance("TLS");
            context.init(keyManagerFactory.getKeyManagers(), null, new SecureRandom());

            SSLSocketFactory socketFactory = context.getSocketFactory();
            if (connection instanceof HttpsURLConnection) {
                ((HttpsURLConnection) connection).setSSLSocketFactory(socketFactory);
            }
            String vRespostaServer = response.toString();
            Persistencia dbpadrao, dbsatellite;
            dbpadrao = this.pool.getConexao("SATPRESERVE");
            Consulta qTmpX;
            String vSQL;
            int vConta = 0;
            String vIndiceSolicitacao = "";
            if (!vChave.equals("")) {
                vIndiceSolicitacao = vChave;
            } else {
                this.logerro.Grava("MOvimentacoes - Nao tem Chave", caminho);
                vSQL = "Select Top 1 * from ItauIntegraDet\n"
                        + "where TipoOperacao like '%CUSTO%'\n"
                        + " and Posicao = 'C'\n"
                        + " and Status = 'PD'\n"
                        + "order by Sequencia desc";
                try {
                    qTmpX = new Consulta(vSQL, this.pool.getConexao("SATPRESERVE"));
                    qTmpX.select();
                } catch (Exception e) {
                    logexecucao.Grava("Erro SQL: " + vSQL, caminho);
                    throw new Exception("Consulta Pedido - " + e.getMessage() + "\r\n"
                            + vSQL);
                }
                if (qTmpX.Proximo()) {
                    vConta++;
                    vIndiceSolicitacao = qTmpX.getString("inf_Ctrl_Solic");
                }
            }
            Consulta SQLPdrLog;
            String vSQLLog;
            String body = "";
            if (!vIndiceSolicitacao.equals("")) {
                this.logerro.Grava("Movimentacoes - tem Chave: " + vIndiceSolicitacao, caminho);
                body = vMensagem;
                //Insere envio Solicitacao
                this.logerro.Grava("Movimentacoes - Body: " + body, caminho);
                vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                        + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                        + "'custodias_movimentacoes_confirmar', \n"
                        + "'SOLICITA', \n"
                        + "'" + vIndiceSolicitacao + "', \n"
                        + "'" + body + "', \n"
                        + "'SATSERVER', \n"
                        + "'" + getDataAtual("SQL") + "', \n"
                        + "'" + getDataAtual("HORA") + "')";
                SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                SQLPdrLog.insert();
                this.logerro.Grava("SQL 1: " + vSQLLog, caminho);
                OutputStream outputStream = connection.getOutputStream();
                outputStream.write(body.toString().getBytes());
                outputStream.close();

                vRespostaServer = Integer.toString(connection.getResponseCode());

                this.logerro.Grava("Resposta Connection Response: 1-" + " - Inicia buffer:" + connection.getResponseMessage() + "\n", caminho);
                this.logerro.Grava("Resposta Connection Response: 1.1-" + " - Inicia buffer:" + connection.getContent().toString() + "\n", caminho);

                //this.logerro.Grava("Resposta Connection: 3-" + connection.getContent().toString(), caminho);
                if (!vRespostaServer.equals("400")) {

                    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    this.logerro.Grava("Resposta Connection: 2-" + bufferedReader.toString(), caminho);

                    String line = null;
                    while ((line = bufferedReader.readLine()) != null) {
                        response.append(line);
                    }
                    this.logerro.Grava("Passou Response: 1-" + response.toString() + " - " + connection.getResponseMessage().toString(), caminho);
                    //JsonParser jsonParser = new JsonParser();
                    //JsonObject jsonObject = jsonParser.parse(response.toString()).getAsJsonObject();
                    this.logerro.Grava("RESPOSTA" + response.toString(), caminho);

                    vRetorno = response.toString();
                    vSQLLog = "Insert into ItauIntegra(Sequencia, TipoOperacao, ParamOperacao, Inf_Ctrl_Solic, XMLJSON, Operador, Dt_Incl, Hr_Incl)Values("
                            + "(Select isnull(Max(Sequencia),0)+1 from ItauIntegra), \n"
                            + "'custodias_movimentacoes_confirmar', \n"
                            + "'data_atendimento=" + vData + "', \n"
                            + "'" + vIndiceSolicitacao + "', \n"
                            + "'" + vRetorno + " - " + vMensagem + "', \n"
                            + "'SATSERVER', \n"
                            + "'" + getDataAtual("SQL") + "', \n"
                            + "'" + getDataAtual("HORA") + "')";
                    SQLPdrLog = new Consulta(vSQLLog, this.pool.getConexao("SATPRESERVE"));
                    SQLPdrLog.insert();
                }
                //vRetorno =  + bufferedReader.toString();

                //bufferedReader.close();
            }
        } catch (Exception e) {
            vRetorno = e.toString();
            this.logerro.Grava("RETORNO" + vRetorno, caminho);
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return vRetorno;
    }
}
