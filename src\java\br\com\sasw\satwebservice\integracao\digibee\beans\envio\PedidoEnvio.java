    /*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.integracao.digibee.beans.envio;

import br.com.sasw.satwebservice.integracao.digibee.beans.Pedido;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PedidoEnvio extends Pedido{
    
    private int acao;
    private String tipo;
    private String classificacao;
    private String data;
    private String idpontoservicoorigem;
    private String hora1O;
    private String hora2O;
    private String idpontoservicodestino;
    private String hora1D;
    private String hora2D;
    private float valor;
    private String moeda;
    private String observacao;
    private List<Composicao> composicoes;

    public int getAcao() {
        return acao;
    }

    public void setAcao(int acao) {
        this.acao = acao;
    }

    public String getTipo() {
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }

    public String getClassificacao() {
        return classificacao;
    }

    public void setClassificacao(String classificacao) {
        this.classificacao = classificacao;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getIdpontoservicoorigem() {
        return idpontoservicoorigem;
    }

    public void setIdpontoservicoorigem(String idpontoservicoorigem) {
        this.idpontoservicoorigem = idpontoservicoorigem;
    }

    public String getHora1O() {
        return hora1O;
    }

    public void setHora1O(String hora1O) {
        this.hora1O = hora1O;
    }

    public String getHora2O() {
        return hora2O;
    }

    public void setHora2O(String hora2O) {
        this.hora2O = hora2O;
    }

    public String getIdpontoservicodestino() {
        return idpontoservicodestino;
    }

    public void setIdpontoservicodestino(String idpontoservicodestino) {
        this.idpontoservicodestino = idpontoservicodestino;
    }

    public String getHora1D() {
        return hora1D;
    }

    public void setHora1D(String hora1D) {
        this.hora1D = hora1D;
    }

    public String getHora2D() {
        return hora2D;
    }

    public void setHora2D(String hora2D) {
        this.hora2D = hora2D;
    }

    public float getValor() {
        return valor;
    }

    public void setValor(float valor) {
        this.valor = valor;
    }

    public String getMoeda() {
        return moeda;
    }

    public void setMoeda(String moeda) {
        this.moeda = moeda;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
    
    public List<Composicao> getComposicoes() {
        return composicoes;
    }

    public void setComposicoes(List<Composicao> composicoes) {
        this.composicoes = composicoes;
    }
}
