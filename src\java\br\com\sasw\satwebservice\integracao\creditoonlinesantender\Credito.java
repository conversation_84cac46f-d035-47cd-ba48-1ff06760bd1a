/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.integracao.creditoonlinesantender;

import Arquivo.ArquivoLog;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import br.com.sasw.satwebservice.integracao.creditoonlinesantender.comunicacao.Comunicacao;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.UriInfo;

/**
 * REST Web Service
 *
 * <AUTHOR>
 */
@Path("/ws-creditoonlinesantender")
public class Credito {
    
    private final ArquivoLog logerro;
    private String caminho;

    @Context
    private UriInfo context;
    
    /** 
     * Creates a new instance
     */
    public Credito() {
        logerro = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\ERRO\\"
                + "\\creditoonlinesantender\\" + DataAtual.getDataAtual("SQL") + "\\log.txt";
    }

    @POST
    @Path("/{empresa}/credito")
    public String consultar(@PathParam("empresa") String empresa, String corpo) {
        String resp;
        try {
            Comunicacao comunicacao = new Comunicacao(empresa, corpo);
            
            caminho = comunicacao.getCaminho();
            logerro.Grava(corpo, caminho);
            
            comunicacao.autenticar();
            comunicacao.trocarChaves();
            
            resp = comunicacao.obterCredito();
//            resp = comunicacao.realizarCredito();
        } catch (Exception e) {
            resp = e.getMessage();
        }
        return resp;
    }

    @POST
    @Path("/{empresa}/creditar")
    public String creditar(@PathParam("empresa") String empresa, String corpo) {
        String resp;
        try {
            Comunicacao comunicacao = new Comunicacao(empresa, corpo);
            
            caminho = comunicacao.getCaminho();
            logerro.Grava(corpo, caminho);
            
            comunicacao.autenticar();
            comunicacao.trocarChaves();
            
//            resp = comunicacao.obterCredito();
            resp = comunicacao.realizarCredito();
        } catch (Exception e) {
            resp = e.getMessage();
        }
        return resp;
    }
}
