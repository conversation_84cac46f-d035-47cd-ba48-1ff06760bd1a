/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
/* CSS Document */

body {
	margin:0;
}
.form_sun4 {
	margin:0;
}
.hidden_sun4 {
	display:none;
}

.clear_sun4 {
	clear:both;
}
.float_sun4 {
	float:left;
}

.ConMgn_sun4 {
	margin:0px 10px;
}
/*---*/
/* SKIP NAVIGATION LINK */
.SkpWht_sun4, .SkpMedGry1_sun4 {
	height:0px;
}
/*---*/
/* ADDREMOVE [originalName: ADD-REMOVE-IDIOM]*/
.AddRmvBtnTbl_sun4 .Btn1_sun4, .AddRmvBtnTbl_sun4 .Btn1Hov_sun4, .AddRmvBtnTbl_sun4 .Btn1Dis_sun4,
.AddRmvBtnTbl_sun4 .Btn2_sun4, .AddRmvBtnTbl_sun4 .Btn2Hov_sun4, .AddRmvBtnTbl_sun4 .Btn2Dis_sun4 {
	width:100%;
	margin:0px;
}
.AddRmvVrtDiv_sun4 .Btn1_sun4, .AddRmvVrtDiv_sun4 .Btn1Hov_sun4, .AddRmvVrtDiv_sun4 .Btn1Dis_sun4,
.AddRmvVrtDiv_sun4 .Btn2_sun4, .AddRmvVrtDiv_sun4 .Btn2Hov_sun4, .AddRmvVrtDiv_sun4 .Btn2Dis_sun4 {
	width:9em;
	margin:0px;
}
.AddRmvBtnTbl_sun4 {
	margin-top:1.6em;
}
.AddRmvVrtFst_sun4 {
	margin:5px 0px 10px 5px;
	float:left;
}
.AddRmvVrtWin_sun4 {
	margin:5px 0px 10px 5px;
	float:left;
}
.AddRmvVrtBwn_sun4 {
	margin:5px 0px 10px 10px;
	float:left;
}
.AddRmvHrzWin_sun4 {
	margin-top:3px;
	min-width:100px;
}
.AddRmvHrzBwn_sun4 {
	margin-top:8px;
	min-width:100px;
}
.AddRmvHrzDiv_sun4 {
	margin:5px 7px 10px 0px;
}
.AddRmvVrtDiv_sun4 {
	clear:left;
}
.AddRmvHrzLst_sun4 {
	clear:both;
}
.AddRmvVrtBtn_sun4 {
	width:120px;
}
.AddRmvLbl_sun4 {
	margin-left: 0px;
}
.AddRmvLbl2_sun4 {
	padding-left: 5px;
	display: block;
}
.AddRmvLbl2ReadOnly_sun4 {
	padding-left: 0px;
}
.AddRmvLbl2_sun4, .AddRmvLbl2ReadOnly_sun4 {
	height: 1.6em;
}
/*---*/

/* BREADCRUMBS */
.BcmWhtDiv_sun4 {
	margin:10px 10px 0px 10px;
        padding: 0px;  /* must nullify BcmGryDiv.padding */
}
.BcmGryDiv_sun4 {
	padding:13px 10px 10px 10px;
        margin: 0px;  /* must nullify BcmWhtDiv.margin */
}
.BcmSep_sun4 {
	margin:0px 5px;
}
/*---*/

/* CHECKBOXES AND RADIO BUTTONS */
.RbSpn_sun4 input, .RbSpnDis_sun4 input {
	vertical-align: -1px;
}
.CbSpn_sun4 input, .CbSpnDis_sun4 input {
	vertical-align: -1px;
}

.CbReadOnly_sun4, .RbReadOnly_sun4 {
        opacity:.45;  -moz-opacity: 0.45;
}

.CbDis_sun4, .RbDis_sun4 {
        opacity:.40;  -moz-opacity: 0.4;    
}
.RbLbl_sun4, .RbLblDis_sun4 {
	vertical-align: 1px;
}
.CbLbl_sun4, .CbLblDis_sun4 {
	vertical-align: 4px;
}
.RbImg_sun4, .RbImgDis_sun4 {
	vertical-align: -5px
}
.CbImg_sun4, .CbImgDis_sun4 {
	vertical-align: -3px
}
.CbGrp_sun4 td, .RbGrp_sun4 td {
	text-align: left;
}
.CbGrp_sun4 label, .RbGrp_sun4 label {
	vertical-align: 6px;
}

/* DnD styles */

.dojoDndItem { 
	padding:3px; 
}

.dndOuterSpan_sun4 {
    position: relative;
}

.dndContainer_sun4 {
    position:relative; 
    top:0px; 
    right:0px; 
    bottom:0px; 
    left:0px;
}
/* HORIZONTAL AND VERTICAL CHECKBOX GROUP AND RADIO BUTTON GROUP */
.RBGRPVert_sun4, .RBGRPHoriz_sun4, .CBGRPVert_sun4, .CBGRPHoriz_sun4 {
    padding:0;
}
.RBGRPVert_sun4 ul, .RBGRPHoriz_sun4 ul, .CBGRPVert_sun4 ul, .CBGRPHoriz_sun4 ul {
    clear:left;
    float:left;
    list-style:none;
    margin:0;
    padding:0;
    text-align:left;
}
.RBGRPClear_sun4, .CBGRPClear_sun4  {
    clear:both;
}
.RBGRPCaption_sun4, .CBGRPCaption_sun4 {
    vertical-align:top;
}
.RBGRPHoriz_sun4 ul li, .CBGRPHoriz_sun4 ul li {
    float:left;
    margin:0 10px 0 0;
}

/*---*/

/* EDITABLE LIST */
/* [check overwrite classes on ie.css] */
.EdtLstTbl_sun4 .EdtLstAddLblTd_sun4 {
	padding:2px 10px 0px 0px;
}
.EdtLstTbl_sun4 .EdtLstRmvLblTd_sun4 {
	padding:6px 10px 0px 0px;
	vertical-align:top;
}
.EdtLstTbl_sun4 .EdtLstAddTxtTd_sun4 {
	padding:2px 14px 0px 0px;
	vertical-align:top;
	margin-top: 3px;
}
.EdtLstTbl_sun4 .EdtLstRmvLstTd_sun4 {
	padding:2px 10px 1px 0px;
	vertical-align:top;
}
.EdtLstTbl_sun4 .EdtLstRmvLstTd_sun4 .Lst_sun4,
.EdtLstTbl_sun4 .EdtLstRmvLstTd_sun4 .LstDis_sun4 {
	margin-bottom: 3px;
}
.EdtLstTbl_sun4 .EdtLstAddTxtTd_sun4 .TxtFld_sun4,
.EdtLstTbl_sun4 .EdtLstAddTxtTd_sun4 .TxtFldDis_sun4 {
	height: 1.3em;
}
.EdtLstTbl_sun4 .EdtLstAddBtnTd_sun4 {
	padding:2px 10px 0px 0px;
	vertical-align:top;
	width:100px;
}
.EdtLstTbl_sun4 .EdtLstRmvBtnTd_sun4 {
	padding:2px 10px 1px 0px;
	vertical-align:top;
	width:100px;
}
.EdtLstTbl_sun4 .EdtLstBtnWin_sun4 {
	margin-top:3px;
}
.EdtLstTbl_sun4 .EdtLstBtnBwn_sun4 {
	margin-top:9px;
}
.EdtLstTbl_sun4 .TxtFld_sun4, .EdtLstTbl_sun4 .TxtFldDis_sun4,
.EdtLstTbl_sun4 .Btn1_sun4, .EdtLstTbl_sun4 .Btn1Hov_sun4, .EdtLstTbl_sun4 .Btn1Dis_sun4,
.EdtLstTbl_sun4 .Btn2_sun4, .EdtLstTbl_sun4 .Btn2Hov_sun4, .EdtLstTbl_sun4 .Btn2Dis_sun4 {
	width:100%;
	margin:0px;
}
/*---*/

/* BUTTONS */
.Btn1_sun4, .Btn1Hov_sun4, .Btn1Mni_sun4, .Btn1MniHov_sun4, .Btn2_sun4, .Btn2Hov_sun4, .Btn2Mni_sun4, .Btn2MniHov_sun4, .Btn1Dis_sun4, .Btn2Dis_sun4, .Btn1MniDis_sun4, .Btn2MniDis_sun4 {
	border-style:solid;
	border-width:1px;
}
.Btn1_sun4, .Btn1Hov_sun4, .Btn1Mni_sun4, .Btn1MniHov_sun4, .Btn2Mni_sun4, .Btn2MniHov_sun4, .Btn2_sun4, .Btn2Hov_sun4 {
	background-repeat:repeat-x;
	background-position:center center;
}
.Btn1_sun4, .Btn1Hov_sun4, .Btn1Dis_sun4, .Btn1Mni_sun4, .Btn1MniHov_sun4, .Btn1MniDis_sun4, .Btn2_sun4, .Btn2Hov_sun4, .Btn2Dis_sun4, .Btn2Mni_sun4, .Btn2MniHov_sun4, .Btn2MniDis_sun4 {
	padding:0 5px 1px 5px;
	margin:0 2px 0 1px;
}

/* for arrays where buttons need to be of equal width */
.BtnTbl_sun4 .Btn1_sun4, .BtnTbl_sun4 .Btn1Hov_sun4, .BtnTbl_sun4 .Btn1Dis_sun4, .BtnTbl_sun4 .Btn2_sun4, .BtnTbl_sun4 .Btn2Hov_sun4, .BtnTbl_sun4 .Btn2Dis_sun4 {
	width:100%;
	margin:0px
}
.BtnAryDiv_sun4 {
	margin:5px 8px;
}
/*revised borders for masthead  */
.MstDiv_sun4 .Btn1_sun4, .MstDiv_sun4 .Btn1Mni_sun4, .MstDiv_sun4 .Btn1Hov_sun4, .MstDiv_sun4 .Btn1MniHov_sun4, .MstDiv_sun4 .Btn2_sun4, .MstDiv_sun4 .Btn2Mni_sun4, .MstDiv_sun4 .Btn2Hov_sun4, .MstDiv_sun4 .Btn2MniHov_sun4 {
	border-width:0;
	vertical-align:middle;
	margin:0;
	padding-bottom:1px;
}
.mastheadButton_4_sun4 a:link, .mastheadButton_4_sun4 a:visited, .mastheadButton_4_sun4 a:hover {
	padding:1px 7px 1px 9px;
}
.mastheadButton_4_sun4 {
	display:inline;
	border-style:solid;
	border-top-width:1px;
	border-right-width:0;
	border-bottom-width:1px;
	border-left-width:0;
	margin:0;
	padding:0 1px 2px 1px;
	text-align:center;
}
/*---*/

/* FILE CHOOSER */
/* [check overwrite classes on ie.css] */
.ChoNavBtnGrpDiv_sun4 {
	float: left;
	margin-top: 5px;
	margin-bottom: 5px;
}
.ChoImgBtn_sun4 {
	padding-right: 6px;
}
.ChoSortByDiv_sun4 {
	margin-top: 5px;
	margin-bottom: 11px;
	float: right;
}
.ChoFltHlpDiv_sun4 {
	float: left;
	margin-bottom: 5px;
	margin-left: 7em;
}
.ChoSortByDiv_sun4 .LblLev2Txt_sun4 {
	padding-right: 35px;
}
.ChoMultiHlpDiv_sun4 {
	float: left;
	margin-top: 0px;
	margin-bottom: 0px;
}
.ChoLev2Div_sun4 {
	float: left;
	width: 7em;
}
.ChoLookinDiv_sun4 {
	float: left;
	margin-top: 2px;
	margin-bottom: 2px;
}
.ChoFltDiv_sun4 {
	float: left;
	margin-top: 2px;
	margin-bottom: 2px;
}
.ChoSelFileDiv_sun4 {
	float: left;
	margin-top: 8px;
	margin-bottom: 7px;
}
.ChoSelFileLev2Div_sun4 {
	float: left;
	width: 8.7em;
}
.ChoSrvDiv_sun4 {
	float: left;
	margin-top: 10px;
	margin-bottom: 2px;
}
.ChoFltDiv_sun4 .TxtFld_sun4, .ChoLookinDiv_sun4 .TxtFld_sun4, .ChoSelFileDiv_sun4 .TxtFld_sun4, .ChoSrvDiv_sun4 .ChoSrvTxt_sun4, .ChoFltHlpDiv_sun4 .inlineFieldHelp_sun4 {
	margin-left: 10px;
}
.ChoLookinDiv_sun4 .TxtFld_sun4, .ChoSelFileDiv_sun4 .TxtFld_sun4 {
	width: 32em;
}
.ChoFltHlpDiv_sun4 .inlineFieldHelp_sun4 {
	padding-top: 4px;
}
.ChoLstHdr_sun4 {
	margin-bottom: 0px;
	height: 1.6em;
	line-height: 1.6em;
}
.ChoLstHdr_sun4 .ChoNameHdr_sun4 {
	height: 1.6em;
	padding-left: 5px;
	width: 56%;
	float: left;
}
.ChoLstHdr_sun4 .ChoSizeHdr_sun4 {
	height: 1.6em;
	padding-left: 5px;
	width: 16%;
	float: left;
}
.ChoLstHdr_sun4 .ChoDateTimeHdr_sun4 {
	height: 1.6em;
	padding-left: 5px;
	float: left;
}
.ChoLstDiv_sun4 .LstMno_sun4 {
	margin-bottom: 0;
}
/*---*/

/* HELP WINDOW */
.HlpStpTab_sun4 {
	margin:0px 0px 5px 0px;
}
.HlpTtlDiv_sun4 {
	margin:15px 0px 0px 10px;
}
.HlpSchDiv_sun4, .HlpIdxDiv_sun4 {
	margin:5px 5px 5px 10px;
}
.HlpRltDiv_sun4 {
	margin-top:6px;
}
body.HlpBdy_sun4 {
	border-left-style:solid;
	border-left-width:1px;
	padding:10px;
}
body.HlpBdy_sun4 h1 {
	margin-bottom:-3px;
}
body.HlpBdy_sun4 h2, body.HlpBdy_sun4 h3, body.HlpBdy_sun4 h4, body.HlpBdy_sun4 h5, H6 {
	margin-bottom:-5px;
}
.HlpBtnDiv_sun4 {
	padding:7px 10px 1px 0;
	vertical-align:middle;
}
/*---*/

/* PAGEALERT [originalName: FULL ALERTS]*/
.FulAlrtHdrDiv_sun4 {
	margin:7px 10px 5px 37px;
}
.FulAlrtMsgDiv_sun4 {
	margin:5px 10px 0px 37px;
}
.FulAlrtFrmDiv_sun4 {
	margin:10px 10px 5px 37px;
}


/*---*/
/* INLINE ALERT */
.inlineAlert_4_sun4 {
	margin:0 20px 0 20px;
	text-align:center;
}
.inlineAlert_4_sun4 table {
	display: inline;	
}
.inlineAlert_4_sun4 .topLeftCorner_sun4, .inlineAlert_4_sun4 .topRightCorner_sun4, .inlineAlert_4_sun4 .bottomLeftCorner_sun4, .inlineAlert_4_sun4 .bottomRightCorner_sun4 {
	width:8px;
	height:8px;
}
.inlineAlert_4_sun4 .topMiddle_sun4 {
	height:8px;
}
.inlineAlert_4_sun4 .leftMiddle_sun4 {
	width:7px;
	vertical-align:top;
	border-left-style:solid;
	border-left-width:1px;
} 
.inlineAlert_4_sun4 .middle_sun4 {
	padding:0 13px;
}
.inlineAlert_4_sun4 .middle_sun4 .header_sun4{
	vertical-align:middle;
	text-align:center;
}
.inlineAlert_4_sun4 .middle_sun4 .header_sun4 img{
	vertical-align:middle;
	padding:0 4px 0 0;
}
.inlineAlert_4_sun4 .middle_sun4  .header_sun4 .label_sun4{
	vertical-align:middle;
}
.inlineAlert_4_sun4 .middle_sun4 .details_sun4 {
	margin:7px 0 0 0;
	vertical-align:middle;
	text-align:center;
}
.inlineAlert_4_sun4 .middle_sun4 .details_sun4 img{
	padding:0 1px 0 10px;
}
.inlineAlert_4_sun4 .rightMiddle_sun4 {
	width:7px;
	vertical-align:top;
	border-right-style:solid;
	border-right-width:1px;
}
.inlineAlert_4_sun4 .bottomMiddle_sun4 {
	height:7px;
	border-bottom-style:solid;
	border-bottom-width:1px;
}
/*---*/
/* LEFT PANE HELP */
.LftHlpHlp_sun4 {
	position:absolute;
	top:0px;
	right:70%;
	bottom:34px;
	left:0px;
	overflow:auto;
}
.LftHlpBdy_sun4 {
	position:absolute;
	top:0px;
	bottom:34px;
	left:30%;
	right:0px;
	overflow:auto;
	border-left:1px solid;
}
.LftHlpDiv_sun4 {
	margin:15px 10px 5px;
}
.LftHlpBtm_sun4 {
	position:absolute;
	bottom:0px;
	border-top:1px solid;
	padding:0 13px 0 30%;
	height:33px;
}
.LftHlpBtnBtm_sun4 {
	float:right;
	margin: 7px 0 8px;
	padding: 0 0 0 13px;
	text-align:right;
}
/*---*/
/* LISTS */
.Lst_sun4, .LstDis_sun4, .LstMno_sun4, .LstMnoDis_sun4 {
	border-width:1px;
	border-style:solid;
}
.LstAln_sun4 {
	vertical-align:top;
	padding-right:5px;
} 
/*---*/
/* HELPINLINE [originalName: INLINE HELP]*/
.inlineFieldHelp_sun4 {
	padding:2px 0 0 0;
}
/*---*/

/* PROPERTY SHEET */
.LblRqdDiv_sun4 {
	line-height:1.0em;
}
.ConFldSetDiv_sun4 {
	margin:7px 10px 0px;
}
.ConFldSetLgdDiv_sun4 {
	margin-bottom:10px;
	line-height:1.1em;
}
.ConSubSecDiv_sun4 {
	padding:0px 10px 5px 0px;
}
.ConTblCl1Div_sun4 {
	margin:8px 6px 0px 15px;
}
.ConTblCl2Div_sun4 {
	margin:8px 6px 0px 0px;
	padding:1px 0 0 0;
}
.ConEmbTblCl1Div_sun4 {
	margin:6px 10px 0px 30px;
}
.ConEmbTblCl2Div_sun4 {
	margin:3px 10px 0px 0px;
}
.ConJmpScnDiv_sun4 {
	margin:17px 10px 0px 0;
}
.ConJmpLnkDiv_sun4 {
	margin:0 25px 0 10px;
}
.ConRqdDiv_sun4 {
	text-align:right;
	margin:5px 10px 5px 0px;
}
.ConJmpTopDiv_sun4 {
	line-height:.7em;
	margin:15px 10px 10px 10px;
}
.ConEmbTblCl1Div_sun4 input[type=checkbox], .ConEmbTblCl1Div_sun4 input[type=radio] {
	margin-left:-5px;
	vertical-align:middle;
}
div.ConTblCl2Div_sun4 input, div.ConTblCl2Div_sun4 select {
	vertical-align:middle;
} 
div.ConTblCl2Div_sun4 .CbSpn_sun4 input[type=checkbox] {
	 margin-top:-8px;
}
.ConFldSetLgdDiv_sun4 {
	margin-bottom:10px;
}
.ConSubSecTtlTxt_sun4 {
	margin:15px 0px 0px 15px;
}
/*---*/
/* CONTENTPAGETITLE [originalName: PAGE TITLE]*/
.TtlTxtDiv_sun4 {
	margin:12px 0px 0px 10px;
} 
.TtlTxtDiv_sun4 img {
	vertical-align:text-bottom;
	margin-right:5px;
} 
.TtlTxt_sun4 {
	margin:0px;
}

.TtlActDiv_sun4, .TtlVewDiv_sun4  {
	margin:8px 10px 0px 10px;
}
.TtlHlpDiv_sun4 {
	margin:5px 10px 0px 10px;
}
.TtlBtnDiv_sun4 {
	margin:0px 8px 0px 10px;
}
.TtlBtnBtmDiv_sun4 {
	padding:10px 8px 0px 10px;
}
/*---*/

/* SCHEDULER [originalName: DATE AND TIME]*/
.DatCalDiv_sun4 {
	border-right-width:1px;
	border-right-style:solid;
	border-bottom-width:1px;
	border-bottom-style:solid;
	border-left-width:1px;
	border-left-style:solid;
	text-align:center;
	padding:2px 0 0 0;
	clear:both;
}
.DatCalDiv_sun4 .DatSelDiv_sun4 {
	float:left;
	clear:right;
}
.DatCalDiv_sun4 .DatCalLeft_sun4 {
	float:left;
	clear:none;
}

.DatCalDiv_sun4 .DatCalLeft_sun4 img {
	vertical-align:middle;
}
.DatCalDiv_sun4 .DatCalLeft_sun4 select {
	margin:2px 1px 2px 2px;
	vertical-align:top;
}

.DatCalDiv_sun4 .DatCalRight_sun4 {
	float:right;
	clear:right;
}
.DatCalDiv_sun4 .DatCalRight_sun4 select {
	margin:2px 1px 2px 0;
	vertical-align:top;
}

.DatCalDiv_sun4 .DatCalDiv_sun4 {
	border:0;
	padding:0;
	margin:0 2px 2px 3px;
}
.DatCalDiv_sun4 .DatCalDiv_sun4 table, .DatCalTbl_sun4 {
	border-collapse:collapse;
}
a.DatLnk_sun4:link, a.DatLnk_sun4:visited, a.DatLnk_sun4:hover, a.DatBldLnk_sun4:link, a.DatBldLnk_sun4:visited, a.DatCurLnk_sun4:link, a.DatCurLnk_sun4:visited,
a.DatCurLnk_sun4:hover, a.DatOthLnk_sun4:link, a.DatOthLnk_sun4:visited, a.DatOthLnk_sun4:hover, a.DatOthBldLnk_sun4:link, a.DatOthBldLnk_sun4:visited,
.DatCalTbl_sun4 td, .DatCalTbl_sun4 th {
	border-style:solid;
	border-width:1px;
}
a.DatLnk_sun4:link, a.DatLnk_sun4:visited {
	display:block;
	padding:5px 0px 3px;
}
a.DatBldLnk_sun4:link, a.DatBldLnk_sun4:visited {
	display:block;
	padding:5px 0px 3px;
}
a.DatCurLnk_sun4:link, a.DatCurLnk_sun4:visited {
	display:block;
	padding:5px 0px 3px;
}
a.DatOthLnk_sun4:link, a.DatOthLnk_sun4:visited {
	display:block;
	padding:5px 0px 3px;
}
a.DatOthBldLnk_sun4:link, a.DatOthBldLnk_sun4:visited {
	display:block;
	padding:5px 0px 3px;
}
.DateSelContainer_sun4 {
	border-top-style:solid;
	border-left-style:solid;
	border-right-style:solid;
	border-top-width:1px;
	border-left-width:1px;
	border-right-width:1px;
}
.DatSelTopMiddle_sun4 {
	border-top-width:1px;
	border-top-style:solid;
	height:4px;
}
.DatSelContent_sun4 {
	border-right-width:1px;
	border-right-style:solid;
	border-left-width:1px;
	border-left-style:solid;
	padding:0 0 6px 8px;
	line-height:.9em;
}
.DatSelDiv_sun4 {
	text-align:left;
	padding:0 1px
}
.DatSelDiv_sun4 span {
	/* no attributes specified */
}
.DatSelDiv_sun4 input {
	padding-top:1px;
}
.DatDayHdrTxt_sun4 {
	display:block;
	padding:2px 0px;
}
.DatCalTbl_sun4 td, .DatCalTbl_sun4 th {
	width:29px;
}
.DatFieldTable_sun4 td {
	padding:1px 0;
}
/*---*/
/*CALENDAR */
.CalPopDiv_sun4 {
	padding:0 4px 0 0;
	display:block;
	position:relative;
	float:left;
	top:0;
	left:0;
}
.CalPopDiv_sun4 .DatCalDiv_sun4 {
	position:relative;
	top: -3px;	
	padding:0 0 0 0;
	margin-bottom:0;
}
.CalPopShdDiv_sun4 {
	display:none;
	position:absolute;
	z-index:1000;
	margin:-9px 0 0 -22px;
	width:20em;
        left:5px;
        top:24px;
}
.CalPopShd2Div_sun4 {
}
.CalPopDiv_sun4 .DatSelContent_sun4 {
	border-right-width:1px;
	border-right-style:solid;
	border-left-width:1px;
	border-left-style:solid;
	position:relative;
	float:left;
	top: -3px;
	left:0;
	padding:0 0 3px 0;
}
.CalPopDiv_sun4 .DatCalTbl_sun4 {
	border-left-style:solid;
	border-left-width:1px;
	border-right-style:solid;
	border-right-width:1px;
}
.CalPopDiv_sun4 .DatCalTbl_sun4 th {
	border-bottom-style:solid;
	border-bottom-width:1px;
	border-left:none;
	border-right:none;
	border-top-style:solid;
	border-top-width:1px;
}
.CalPopDiv_sun4 .DatCalTbl_sun4 td.CalPopFtr_sun4, .CalPopDiv_sun4 .CalPopFtr_sun4Div_sun4 {
	border:0;
}
.CalPopDiv_sun4  a.DatCurLnk_sun4:link, .CalPopDiv_sun4 a.DatCurLnk_sun4:visited,.CalPopDiv_sun4  a.DatLnk_sun4:link,.CalPopDiv_sun4 a.DatLnk_sun4:visited,.CalPopDiv_sun4 a.DatBldLnk_sun4:link,.CalPopDiv_sun4 a.DatBldLnk_sun4:visited,
.CalPopDiv_sun4 a.DatOthLnk_sun4:link,.CalPopDiv_sun4 a.DatOthLnk_sun4:visited,.CalPopDiv_sun4 a.DatOthBldLnk_sun4:link,.CalPopDiv_sun4 a.DatOthBldLnk_sun4:visited {
	display:block;
	padding:2px 0px 2px;
}
.CalPopDiv_sun4 .DatSelDiv_sun4 {
	margin:0 0 5px 0;
	text-align:left;
	padding: 2px 1px 0 0;
}
.CalPopDiv_sun4 .DatDayHdrTxt_sun4 {
	display:block;
	padding:2px 0px;
}
.CalPopDiv_sun4 .DatCalTbl_sun4 {
	border-collapse:collapse;
}
.CalPopDiv_sun4 .DatCalTbl_sun4 td, .CalPopDiv_sun4 .DatCalTbl_sun4 th {
	width:25px;
}
.CalPopDiv_sun4 .DatCalTbl_sun4 th {
	padding-top:2px;
}
.CalPopDiv_sun4 .DatSelContent_sun4 .closeBtn_sun4 {
	display:block;
	float:right;
	margin: 0 3px 0 0;
}
.CalPopDiv_sun4 .DatSelContent_sun4 .DatSelDate_sun4 {
	float:left;
	line-height:.9em;
	padding:3px 0 3px 8px;
}
.CalPopDiv_sun4 .DatCalTbl_sun4 td.CalPopFtr_sun4 {
	width:auto;
}
.CalPopDiv_sun4 .CalPopFtrDiv_sun4 {
	float:left;
	width:100%;
}
.CalPopDiv_sun4 .CurDayTxt_sun4 {
	float:left;
	clear:right;
	padding:4px 0px 4px 4px;
}
.CalPopFldLbl_sun4 {
	display:block;
	margin-right:8px;
	padding-top:3px;
}
.CalPopFldImg_sun4 img {
	margin-left:5px;
}
/* Override pluto portal server rule */
.CalRootTbl_sun4 {
	width: auto;
 }
.CalPopBtnDiv_sun4 {
        position: relative;
}
.CalPopTxt_sun4 {
        width:12em;
        padding-left:4px;
}
/*---*/


/* TEXT, TEXTAREAS AND PASSWORD FIELDS */
.TxtFld_sun4, .TxtFldDis_sun4, .TxtAra_sun4, .TxtAraDis_sun4 {
	border-style:solid;
	border-width:1px;
	padding:1px 0 0 2px;
}
.TxtFld_ReadOnly_sun4, .TxtAra_ReadOnly_sun4{
	border: none;
	border-width:0px;
}
.TxtAra_sun4, .TxtAraDis_sun4 {
	padding-left:2px;
}


.TxtAraLabel_sun4 span , .TxtAraLabel_sun4 span > img { vertical-align: top; }



/*---*/

/* EDITABLE FIELDS */
.EdtFld_Edt_sun4 {
	border: none;
	border-width:0px;
	padding:1px 0 0 2px;
        background-color: #CCCCEE;        
}
.EdtFldDis_sun4 {
	border: none;
	border-width:0px;
	padding:1px 0 0 2px;
        background-color: #CCCCEE;        
}
.EdtFld_ReadOnly_sun4 {
	border: none;
	border-width:0px;
	padding:1px 0 0 2px;
        background-color: #DDDDDD;        
}

/*---*/


/* MENUS */
.MnuJmpOpt_sun4:hover {text-decoration:underline;}
.MnuJmp_sun4, .MnuStd_sun4, .MnuJmp_sun4Dis, .MnuStdDis_sun4 {

	border-width:1px;

	border-style:solid;
}
.MnuJmpOptSel_sun4 {font-weight:normal;}
.MnuJmpOptGrp_sun4, .MnuStdOptGrp_sun4 {font-weight:normal;font-style:normal;}
/*---*/

/* MASTHEAD */
a.MstLnk_sun4:link, a.MstLnk_sun4:visited, a.MstLnkRt_sun4:link, a.MstLnkRt_sun4:visited {
	border-width:1px;
	border-style:solid;
}
a.MstLnkLft_sun4:link, a.MstLnkLft_sun4:visited, a.MstLnkCen_sun4:link, a.MstLnkCen_sun4:visited {
	border-top-width:1px;
	border-top-style:solid;
	border-bottom-width:1px;
	border-bottom-style:solid;
	border-left-width:1px;
	border-left-style:solid;
}
td.MstTdTtl_sun4 {
	padding:0px 10px 0px 10px;
	vertical-align:top;
	white-space:nowrap;
}
td.MstTdAlm_sun4 {
	vertical-align:top;
}
td.MstTdLogo_sun4 {
	vertical-align:top;
	padding:6px 13px 8px 20px;
}
div.MstDivTtl_sun4 {
	padding-top:4px;
}
.MstFooter_sun4 {
	padding:3px 10px 3px 0;
	border-style:solid;
	border-width:1px;
	min-width:590px;
}
.MstSec_sun4 {
	height:54px;
	border-style:solid;
	border-width:1px;
	min-width:600px;
}
.MstSec_sun4 div {
	padding:15px 0 16px 0;
	vertical-align:middle;
}
.MstSec_sun4 td {
	padding:0 10px;
}
div.MstDivUsr_sun4 {
	padding:4px 0 0 0;
}
.MstLbl_sun4, .MstTxt_sun4, 
.MstUsrLnk_sun4, .MstAlmLnk_sun4, 
span.MstAlmDwnTxt_sun4, span.MstAlmCrtTxt_sun4,
span.MstAlmMajTxt_sun4, span.MstAlmMinTxt_sun4 {
	vertical-align:top;
	line-height:1.0em;
}
a.MstLnk_sun4:link, a.MstLnk_sun4:visited {
	background-repeat:repeat-x;
	background-position:center center;
	padding:2px 10px 1px;
	vertical-align:middle
}
a.MstLnk_sun4:hover,a.MstLnkLft_sun4:hover, a.MstLnkRt_sun4:hover, a.MstLnkCen_sun4:hover {
	background-repeat:repeat-x;
	background-position:center center;
}
.MstSpcImg_sun4 {
	display:none;
}
.MstBdy_sun4 {
	margin:0px;
}
.MstDiv_sun4 {
	border-style:solid;
	border-width:1px;
	min-width:600px;
}
.MstTblTop_sun4 td {
	padding:6px 10px 4px;
}
.MstTblTop_sun4 .TxtFld_sun4, .MstTblTop_sun4 .MnuStd_sun4 {
	margin:0 6px 0 0;
	vertical-align:middle;
}
.MstTblTop_sun4 img {
	vertical-align:middle;
}
.MstTblEnd_sun4 {
	background-repeat:repeat-x;
	background-position:left top;
}
.MstTblEnd_sun4  td {
	padding:1px 0 2px 0;
}
td.MstTblEnd_sun4 {
	padding-left:10px;
}
.MstTblBot_sun4 .hrule_sun4 {
	margin:0 0 1px 0;
	border-top-style:solid;
	border-top-width:1px;
	height:1px;
}	
.MstStatDiv_sun4 a, .MstTmeDiv_sun4 span, .MstAlmDiv_sun4 a {
	vertical-align:top;
	line-height:1.0em;
}
a.MstLnkLft_sun4:link, a.MstLnkLft_sun4:visited, a.MstLnkRt_sun4:link, a.MstLnkRt_sun4:visited, a.MstLnkCen_sun4:link, a.MstLnkCen_sun4:visited {
	background-repeat:repeat-x;
	background-position:center center;
	padding:2px 10px 1px;
	vertical-align:middle;
	white-space:nowrap;
}

/*---*/

/* TABSET [originalName: LEVEL TABS]*/
.Tab1Div_sun4 td {
	border-style:solid;
	border-width:1px;
}
.Tab1Div_sun4 {
	padding:6px 10px 0px;
} 
a.Tab1Lnk_sun4:link, a.Tab1Lnk_sun4:visited  {
	display:block;
	padding:8px 15px 7px;
	text-align:center;
}
.Tab1Div_sun4 table {
	border-collapse:collapse;
}
.Tab1Div_sun4 td.Tab1TblSpcTd_sun4 {
	border:none;
}
.Tab1Div_sun4 td.Tab1TblSelTd_sun4 {
	background-repeat:repeat-x;
	background-position:left top;
	border-bottom:none;
}
.Tab1Div_sun4 .Tab1SelTxtLeft_sun4 {
	position: absolute;
	width: 2px;
	height: 2.8em;
	background-position: top left;
	background-repeat: no-repeat;
}
.Tab1Div_sun4 .Tab1SelTxtNew_sun4 {
	display:block;
	padding:0px 13px 2px 13px;
	text-align:center;
	background-position: top right;
	background-repeat: no-repeat;
}
.Tab1Div_sun4 td a.TabPad_sun4 {
	padding:8px 20px 7px;
}
.Tab1Div_sun4 td.Tab1TblSelTd_sun4 div.TabPad_sun4 {
	padding:8px 20px 7px;
}
/* LEVEL 2 TABS */
.Tab2Div_sun4 td {
	border-style:solid;
	border-width:1px;
}
table.Tab2TblNew_sun4 td.Tab2TblSelTd_sun4 {
	border-top-width:1px;
	border-top-style:solid;
	border-right-width:1px;
	border-right-style:solid;
	border-left-width:1px;
	border-left-style:solid;
}
table.Tab2Tbl3New_sun4 td.Tab2TblSelTd_sun4 {
	border-top-width:1px;
	border-top-style:solid;
	border-right-width:1px;
	border-right-style:solid;
	border-left-width:1px;
	border-left-style:solid;
}
.Tab2Div_sun4 {
	padding:6px 0px 0px 10px;
}
a.Tab2Lnk_sun4:link, a.Tab2Lnk_sun4:visited {
	display:block;
	padding:5px 15px 4px;
	text-align:center;
}
.Tab2Div_sun4 table {
	border-collapse:collapse;
}
.Tab2Div_sun4 .Tab2SelTxtLeft_sun4 {
	position: absolute;
	width: 2px;
	height: 2.1em;
	background-position: top left;
	background-repeat: no-repeat;
}
.Tab2Div_sun4 .Tab2SelTxt_sun4 {
	display:block;
	padding:5px 15px 4px;
	text-align:center;
	background-position: top right;
	background-repeat: no-repeat;
}
.Tab2Div_sun4 td.Tab2TblSelTd_sun4 {
	border-bottom:none;
}
.Tab2Div_sun4 td a.TabPad_sun4, .Tab2Div_sun4 td.Tab2TblSelTd_sun4 div.TabPad_sun4 {
	padding:5px 20px 4px;
}
/* LEVEL 3 TABS*/
.Tab3Div_sun4 {
	padding:6px 0px 0px 10px;
}
.Tab3Div_sun4 td {
	border-style:solid;
	border-width:1px;
}
a.Tab3Lnk_sun4:link,a.Tab3Lnk_sun4:visited {
	display:block;
	padding:4px 15px 3px;
	text-align:center;
}
table.Tab3TblNew_sun4 td {
	border-bottom-style:solid;
	border-bottom-width:1px;
}
table.Tab3TblNew_sun4 div.Tab3SelTxt_sun4 {
	padding:4px 15px 3px;
	text-align:center;
}
table.Tab3TblNew_sun4 td.Tab3TblSelTd_sun4 {
	border-top-width:1px;
	border-top-style:solid;
	border-right-width:1px;
	border-right-style:solid;
	border-left-width:1px;
	border-left-style:solid;
	border-bottom:none;
}
.Tab3Div_sun4 td a.TabPad_sun4, .Tab3Div_sun4 td.Tab3TblSelTd_sun4 div.TabPad_sun4 {
	padding:5px 20px 4px;
}
/* Hide Styles */
.Tab1Div_sun4 td.hidden_sun4, .Tab2Div_sun4 td.hidden_sun4, .Tab3Div_sun4 td.hidden_sun4 {
	display:none;
}
/* MINI-TABS */
.MniTabDiv_sun4 {
	padding:7px 0px 0px 10px;
}
.MniTabDiv_sun4 td.hidden_sun4 {
	display:none;
}
table.MniTabTbl_sun4 {
	border-collapse:collapse;
}
table.MniTabTbl_sun4 td {
	border-style:solid;
	border-width:1px;
}
a.MniTabLnk_sun4:link,a.MniTabLnk_sun4:visited {
	display:block;
	padding:5px 15px 4px;
}
table.MniTabTbl_sun4 td.MniTabTblSelTd_sun4 {
	border-top-width:1px;
	border-top-style:solid;
	border-right-width:1px;
	border-right-style:solid;
	border-left-width:1px;
	border-left-style:solid;
	border-bottom:none;
}
.MniTabSelTxt_sun4 {
	display:block;
	padding:5px 15px 4px;
}
/* MINI-TABS - LIGHTWEIGHT */
.TabGrp_sun4 .TabGrpBox_sun4 {
	border-right-width:1px;
	border-right-style:solid;
	border-bottom-width:1px;
	border-bottom-style:solid;
	border-left-width:1px;
	border-left-style:solid;
	border-top:none;
	padding:10px;
}
.TabGrp_sun4 a.MniTabLnk_sun4:link, .TabGrp_sun4 a.MniTabLnk_sun4:visited, .TabGrp_sun4 .MniTabSelTxt_sun4 {
	padding:4px 10px 3px;
}
.TabGrp_sun4 .MniTabDiv_sun4 td.hidden_sun4 {
	display:none;
}

/*---*/
/* TABLE [originalName: ACTION TABLE] */
table.Tbl_sun4 {
	border-style:solid;
	border-width:1px;
	padding:6px;
	width:100%;
	empty-cells:show;
}
table.Tbl_sun4 td, table.Tbl_sun4 th {
	border-right:none;
	border-top:none;
	padding:3px 5px 1px 5px;
	border-left-style:solid;
	border-left-width:1px;
	border-bottom-style:solid;
	border-bottom-width:1px; 
	margin:0;
}
/* Table Caption/Title */
table.Tbl_sun4 caption.TblTtlTxt_sun4 {
	text-align:left;
	background-position:3px 3px;
	-moz-border-radius-topleft:5px;
	-moz-border-radius-topright:5px;
	padding:3px 10px 2px 10px;
}
.TblTtlTxtSpn_sun4 {
	padding:0;
	float:left;
}
.TblTtlMsgSpn_sun4 {
	padding:0 0 0 3px;
	float:right;
}
/* Action Bar */
table.Tbl_sun4 td.TblActTdLst_sun4 {
	border-top-style:solid; 
	border-top-width:1px;
}
table.Tbl_sun4 td.TblActTd_sun4 {
	border-left:none;
	border-bottom:none;
	padding:0px 0px 6px 0px;
	vertical-align:middle;
}
table.Tbl_sun4 td.TblActTdLst_sun4 {
	border-left:none;
	border-bottom:none;
	padding:6px 0px 2px 0px;
	vertical-align:middle;
}
.TblPgnTxtBld_sun4 {
	margin:0px 5px 0px 10px;
}
.TblPgnTxt_sun4 {
	margin:0px 3px 0px 3px;
}
.TblPgnLftBtn_sun4 {
	margin:0px;
}
.TblPgnRtBtn_sun4 {
	margin-right:10px;
}
.TblPgnGoBtn_sun4 {
	margin-right:8px;
}
/* Selection Column - Headers */
table.Tbl_sun4 th.TblColHdrSel_sun4 {
	border-top-width:1px; 
	border-top-style:solid;
	border-left-width:1px; 
	border-left-style:solid; 
	border-bottom-width:1px; 
	border-bottom-style:solid;
	vertical-align:bottom;
	padding:0;
}
table.Tbl_sun4 th.TblColHdrSel_sun4 a.TblHdrLnk_sun4:link {
	display:block;
	padding:3px 0px;
}
table.Tbl_sun4 th.TblColHdrSel_sun4 a.TblHdrLnk_sun4:visited {
	display:block;
	padding:3px 0px;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 {
	border-top-width:1px; 
	border-top-style:solid;
	border-left-width:1px; 
	border-left-style:solid; 
	border-bottom-width:1px; 
	border-bottom-style:solid; 
	text-align:center;
	border-right:none;
	padding:0px;
	vertical-align:bottom;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:link, 
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:visited {
	border-left-width:1px; 
	border-left-style:solid;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:link, 
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:visited {
	display:block;
	text-align:left;
	padding-bottom:1px;
	width:21px;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:hover {
	width:21px;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrLnk_sun4:link,
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrLnk_sun4:visited {
	display:block;
	padding:3px 0px 2px;
}
/* Selection Column - Cells */
table.Tbl_sun4 td.TblTdSel_sun4, 
table.Tbl_sun4 td.TblTdSrtSel_sun4 {
	text-align:center;
	vertical-align:middle;
	padding:0px 3px;
}
/* Regular Column Headers */
table.Tbl_sun4 th.TblColHdr_sun4 {
	border-top-width:1px; 
	border-top-style:solid;
	border-left-width:1px; 
	border-left-style:solid; 
	border-bottom-width:1px; 
	border-bottom-style:solid; 
	vertical-align:bottom;
	padding:0;
}
table.Tbl_sun4 a.TblHdrImgLnk_sun4:link, 
table.Tbl_sun4 a.TblHdrImgLnk_sun4:visited {
	border-left-width:1px; 
	border-left-style:solid;
}
.TblHdrTxt_sun4 {
	display:block;
	padding:4px 5px 3px;
	min-height:11px;
}
table.TblHdrTbl_sun4 {
	width:100%;
	border:0;
	height:1.7em;
}
table.TblHdrTbl_sun4 td, 
table.TblHdrTbl_sun4 th {
	border:0;
	padding:0;
	vertical-align:bottom;
	width:100%;
}
table.TblHdrTbl_sun4 img {
	margin:0;
	padding-right:5px;
	padding-left:5px;
}
table.Tbl_sun4 a.TblHdrLnk_sun4:link, 
table.Tbl_sun4 a.TblHdrLnk_sun4:visited {
	display:block;
	padding:3px 0 3px 5px;
}
table.Tbl_sun4 a.TblHdrImgLnk_sun4:link, 
table.Tbl_sun4 a.TblHdrImgLnk_sun4:visited {
	display:block;
	white-space:nowrap;
	padding:4px 2px 2px 0px;
	width:21px;
}
table.Tbl_sun4 a.TblHdrImgLnk_sun4:hover {
	white-space:nowrap;
	width:21px;
}
table.Tbl_sun4 a.TblHdrImgLnk_sun4 img {
	padding-right:2px;
}
/* Current Sort Column */
table.Tbl_sun4 th.TblColHdrSrt_sun4 {
	border-top-width:1px; 
	border-top-style:solid;
	border-left-width:1px; 
	border-left-style:solid; 
	border-bottom-width:1px; 
	border-bottom-style:solid;
}
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:link,
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:visited {
	border-left-width:1px; 
	border-left-style:solid;
}
table.Tbl_sun4 span.TblColHdrSrtDis_sun4 {
	border-left-width:1px; 
	border-left-style:solid;
}
table.Tbl_sun4 span.TblColHdrSelDis_sun4 {
	border-left-width:1px; 
	border-left-style:solid;
}
table.Tbl_sun4 th.TblColHdrSrt_sun4 {
	vertical-align:bottom;
	padding:0px;
}
table.Tbl_sun4 th.TblColHdrSrt_sun4 table.TblHdrTbl_sun4 {
	width:100%;
	border:none;
}
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrLnk_sun4:link, 
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrLnk_sun4:visited {
	display:block;
	padding:4px 5px 3px 5px;
}
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:link,
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:visited {
	display:block;
	border-left-style:solid;
	border-left-width:1px;
	padding-bottom:1px;
	width:21px;
}
table.Tbl_sun4 .TblHdrSrtNum_sun4 {
	margin-left:-1px;
	width:21px;
}
table.Tbl_sun4 span.TblColHdrSrtDis_sun4 {
	display:block;
	text-align:left;
	padding:0px 2px 0px 0px;
}
table.Tbl_sun4 span.TblColHdrSrtDis_sun4 img, 
span.TblColHdrSelDis_sun4 img {
	padding-right:2px;
}
table.Tbl_sun4 span.TblColHdrSelDis_sun4 {
	display:block;
	white-space:nowrap;
	padding:4px 2px 2px 0px;
	width:21px;
}
/* Multi-Column Headers */ 
table.Tbl_sun4 th.TblMultColHdr_sun4 {
	border-top:none;
	border-left-style:solid;
	border-left-width:1px;
	border-bottom-style:solid; 
	border-bottom-width:1px;
	padding:0px;
	margin:0px;
	vertical-align:bottom;
}
table.Tbl_sun4 th.TblMultHdr_sun4 {
	border-bottom:none;
	border-top-width:1px;
	border-top-style:solid;
	border-left-width:1px;
	border-left-style:solid;
	text-align:center;
	padding:0 5px;
}
table.Tbl_sun4 th.TblMultColHdr_sun4 a.TblHdrLnk_sun4:link, 
table.Tbl_sun4 th.TblMultColHdr_sun4 a.TblHdrLnk_sun4:visited {
	border-top:none;
}
table.Tbl_sun4 th.TblMultColHdr_sun4 a.TblHdrLnk_sun4:link img, 
table.Tbl_sun4 th.TblMultColHdr_sun4 a.TblHdrLnk_sun4:visited img, 
table.Tbl_sun4 th.TblMultColHdr_sun4 .TblHdrTxt_sun4 img, 
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrLnk_sun4:link img, 
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrLnk_sun4:visited img {
	margin:1px 0px;
}
table.Tbl_sun4 th.TblMultColHdr_sun4 a.TblHdrLnk_sun4:hover {
	border-top:none;
}
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 {
	border-top:none;
	border-left-width:1px; 
	border-left-style:solid; 
	border-bottom-width:1px; 
	border-bottom-style:solid;
	padding:0px;
	margin:0px;
	vertical-align:bottom;
}
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrImgLnk_sun4:link, 
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrImgLnk_sun4:visited {
	border-left-width:1px; 
	border-left-style:solid;
}
/* Sorted Cells */
table.Tbl_sun4 .TblColFtrSpc_sun4 {
	border-left:none;
	border-bottom-style:solid; 
	border-bottom-width:1px;
	border-top-width:2px; 
	border-top-style:solid;
}
table.Tbl_sun4 .TblColFtrSpc_sun4 {
	padding:4px 5px 1px 5px;
}
/* Spacer Colums */
table.Tbl_sun4 th.TblTdSpc_sun4 {
	border-top-width:1px; 
	border-top-style:solid;
	border-left:none; 
	border-bottom-width:1px; 
	border-bottom-style:solid;
	vertical-align:bottom;
	padding:0;
}
table.Tbl_sun4 td.TblTdSpc_sun4 { 
	border-left:none;
}
/*Table Sub-Grouping */
table.Tbl_sun4 .TblGrpRow_sun4 {
	border-left-style:solid;
	border-left-width:1px;
	border-bottom-style:solid; 
	border-bottom-width:1px;
	border-top-width:0px;
	border-top-style:solid;
}
table.Tbl_sun4 .TblGrpRow_sun4 {
	vertical-align:bottom;
}
table.Tbl_sun4 .TblGrpLft_sun4 input {
	margin:2px 1px 1px 2px;
}
table.Tbl_sun4 .TblGrpLft_sun4 {
	float:left;
}
table.Tbl_sun4 .TblGrpCbImg_sun4 img {
	margin-left:-4px;
	margin-bottom:-3px;
}
table.Tbl_sun4 .TblGrpRt_sun4 {
	float:right;
}
table.Tbl_sun4 .TblGrpRow_sun4 {
	vertical-align:bottom;
	min-height:12px;
	padding:4px 5px 3px;
}
/* Table Footers */
/* Table Column-level Footer */
table.Tbl_sun4 .TblColFtr_sun4 {
	border-bottom-style:solid;
	border-bottom-width:1px;
	border-top-width:2px;
	border-top-style:solid;
}
table.Tbl_sun4 .TblColFtrSrt_sun4 {
	border-bottom-style:solid;
	border-bottom-width:1px;
	border-top-width:2px;
	border-top-style:solid;
}
/*Table Group Column-Level Footer */
table.Tbl_sun4 .TblGrpColFtr_sun4 {
	border-bottom-style:solid;
	border-bottom-width:1px;
}
table.Tbl_sun4 .TblGrpColFtrSrt_sun4 {
	border-bottom-style:solid;
	border-bottom-width:1px;}
table.Tbl_sun4 .TblColFtr_sun4 {
	padding:4px 5px 1px 5px;
}
/* Embedded Table Panels */
.TblPnlLytDiv_sun4 {
	border-top-width:1px; 
	border-top-style:solid;
}
.TblPnlShd1Div_sun4 {}
.TblPnlDiv_sun4 {}
.TblMgn_sun4 {
	margin:0px 10px;
}
/*Other Table Content Styles*/
table.Tbl_sun4 .TblTdLyt_sun4 img, 
table.Tbl_sun4 .TblTdSrt_sun4 img, 
table.Tbl_sun4 .TblTdAlm_sun4 img,  
table.Tbl_sun4 .TblTdSrtAlm_sun4 img {
	vertical-align:middle;
	margin-bottom:2px;
}
table.Tbl_sun4 .TblTdSel_sun4 img, 
table.Tbl_sun4 .TblTdSrtSel_sun4 img {
	vertical-align:middle;
	margin:3px 0px 0px 8px;
}
.TblTdLyt_sun4 {} 
/* Mouseover and Row Selection Styles */
table.Tbl_sun4 tr.TblHovRow_sun4 td {}
table.Tbl_sun4 tr.TblHovRow_sun4 th {}
/* Table Overall Footer */ 
table.Tbl_sun4 td.TblFtrRow_sun4 {
	padding:6px 3px 3px 0px;
	border-left:none;
	border-bottom:none;
	vertical-align:middle;
	text-align:center;
}
table.Tbl_sun4 .TblFtrLft_sun4 {
	float:left;
}
table.Tbl_sun4 .TblFtrMsgSpn_sun4 {
	float:right;
}
/* Table Group Overall Footer */
table.Tbl_sun4 .TblGrpFtrRow_sun4 {
	padding:4px 5px 3px;
}
/* Embedded Table Panels */
table.Tbl_sun4 td.TblPnlTd_sun4 {
	border:none;
	padding:0px;
	vertical-align:middle;
}
.TblPnlLytDiv_sun4 {
	display:none;
	padding:4px 0px;
	margin-left:-3px;
}
.TblPnlShd3Div_sun4 {}
.TblPnlShd2Div_sun4 {}
.TblPnlShd1Div_sun4 {
	border-width:1px;
	border-style:solid;
	margin: 0 0 10px 0;
}
.TblPnlDiv_sun4 {
	border-top-width:1px;
	border-top-style:solid;
	padding:1px 15px 1px 15px;
}
table.Tbl_sun4 div.TblPnlDiv_sun4 td {
	border:none;
	padding-left:0px;
}
.TblPnlTtl_sun4 {
	margin:8px 0 0 0;
	padding:0 0px 5px;
}
.TblPnlBtnDiv_sun4 {
	text-align:left;
	margin:10px 0;
	padding-top:8px;
	margin-right:3px;
	border-top-width:1px;
       	border-top-style:solid;
}
.TblPnlHlpTxt_sun4 {
	margin:10px 0;
	padding:7px 12px;
        border-style:solid;
   	border-width:1px;
	white-space:normal;
   	-moz-border-radius-topleft:5px;
   	-moz-border-radius-topright:5px;
   	-moz-border-radius-bottomleft:5px;
   	-moz-border-radius-bottomright:5px; 
}
.TblPnlSrtTbl_sun4 td {
	padding:3px;
}
/* For when appearing in titledbox*/
.TtldBoxInrDiv_sun4 .TblMgn_sun4 {
	margin:0px;
}
/*---*/

/* LIGHTWEIGHT TABLE DESIGN */
/* Table Caption/Title 
table.Tbl_sun4 .TblLt_sun4 .TblTtlTxt_sun4 {
	padding:5px 5px 5px 0px;
}
.TblLt_sun4 .TblTtlTxt_sun4 .TblTtlMsgSpn_sun4 {
	margin-top:1px;
}*/
/*---*/

/* LIGHTWEIGHT TABLE DESIGN */
table.TblLt_sun4 {
	width:100%;
	padding:0px;
	border-bottom:none;
	empty-cells:show;
	border-collapse:collapse;
}
/* Table Caption/Title */
table.TblLt_sun4 caption.TblTtlTxt_sun4 {
	text-align:left;
	padding:5px 5px 5px 0px;
	border:none;
}
table.TblLt_sun4 caption.TblTtlTxt_sun4 span.TblTtlTxtSpn_sun4 {
	float:left;
}
table.TblLt_sun4 caption.TblTtlTxt_sun4 span.TblTtlMsgSpn_sun4 {
	float:right;
	margin-top:1px;
}
/* Action Bar */
table.TblLt_sun4 td.TblActTd_sun4 {
	padding:8px 5px;
	vertical-align:middle;
	border-style:solid;
	border-width:1px;
}
table.TblLt_sun4 td.TblActTdLst_sun4 {
	padding:5px 5px;
	vertical-align:middle;
	border-style:solid;
	border-width:1px;
}
/* Selection Column - Headers */
table.TblLt_sun4 th.TblColHdrSel_sun4 {
	border-top-width:1px; 
	border-top-style:solid;
	border-left-width:1px; 
	border-left-style:solid;
	border-bottom-width:1px; 
	border-bottom-style:solid;
}
table.TblLt_sun4 th.TblColHdrSrtSel_sun4 {
	border-top-width:1px; 
	border-top-style:solid;
	border-left-width:1px; 
	border-left-style:solid;
	border-bottom-width:1px; 
	border-bottom-style:solid;
}
/* Regular Column Headers */
table.TblLt_sun4 th.TblColHdr_sun4 {
	border-top-width:1px; 
	border-top-style:solid;
	border-left-width:1px; 
	border-left-style:solid;
	border-bottom-width:1px; 
	border-bottom-style:solid;
}
table.TblLt_sun4 table.TblHdrTbl_sun4 td {
	border:none;
	padding:0px;
	margin:0px;
	vertical-align:
	bottom;width:100%;
}
table.TblLt_sun4 a.TblHdrLnk_sun4:link, 
table.TblLt_sun4 a.TblHdrLnk_sun4:visited {
	padding:5px 5px 3px;
	min-height:12px;
}
table.TblLt_sun4 a.TblHdrImgLnk_sun4:link, 
table.TblLt_sun4 a.TblHdrImgLnk_sun4:visited {
	border-left-width:1px; 
	border-left-style:solid;
}
table.TblLt_sun4 span.TblColHdrSelDis_sun4 {
	border-left-width:1px; 
	border-left-style:solid;
}
/* Current Sort Column */
table.TblLt_sun4 th.TblColHdrSrt_sun4 {
	border-top-width:1px; 
	border-top-style:solid;
	border-left-width:1px; 
	border-left-style:solid;
	border-bottom-width:1px; 
	border-bottom-style:solid;
}
table.TblLt_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:link, 
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:visited {
	border-left-width:1px; 
	border-left-style:solid;
}
/* Multi-Column Headers */
table.TblLt_sun4 th.TblMultColHdr_sun4 {
	border-top:none;
	border-left-width:1px; 
	border-left-style:solid;
	border-bottom-width:1px; 
	border-bottom-style:solid;
	padding:0px;margin:0px;
	vertical-align:bottom;
}
table.TblLt_sun4 th.TblMultHdr_sun4 {
	border-bottom:none;
	border-top-width:1px; 
	border-top-style:solid;
	border-left-width:1px; 
	border-left-style:solid;
	text-align:center;
	padding:4px 5px;
}
/*Table Sub-Grouping */
table.TblLt_sun4 .TblGrpRow_sun4 {
	border-left-width:1px; 
	border-left-style:solid; 
	border-top-width:3px; 
	border-top-style:double;
	padding:4px 5px 3px;
}
/* Table Footers */ 
table.TblLt_sun4 td.TblFtrRow_sun4 {
	padding:6px 5px 3px;
	border:none;
	vertical-align:middle;
	text-align:center;
	border-left:none;
	border-right:none;
}
table.TblLt_sun4 .TblColFtr_sun4 {
	border-bottom-width:1px; 
	border-bottom-style:solid;
	border-top-width:3px; 
	border-top-style:double;
}
table.TblLt_sun4 .TblColFtrSrt_sun4 {
	border-bottom-width:1px; 
	border-bottom-style:solid;
	border-top-width:3px; 
	border-top-style:double;
}
table.TblLt_sun4 .TblColFtrSpc_sun4 {
	border-left:none;
	border-bottom-width:1px; 
	border-bottom-style:solid; 
	border-top-width:3px; 
	border-top-style:double;
}
/* Embedded Table Panels */
table.TblLt_sun4 td.TblPnlTd_sun4 {
	padding-left:10px;
	border-top:none;
	border-bottom:none;
}
/*---*/

/* TREE */
.TreBdy_sun4 {	
	margin:0px;
}

.Tree_sun4 {
	/*	width:500px;*/
}

.TreeImg_sun4 {	
	padding-top:2px;
	padding-bottom:1px;
}

.TreeImgHeight_sun4 {
	display:inline;	
	line-height:22px;
}

.TreeContent_sun4 {
	display:inline;	
	vertical-align:middle;	
	padding-left:5px;
	white-space:nowrap;
}

.TreeLinkSpace_sun4 {
	margin-left:3px;
}

.TreeRootRow_sun4, .TreeRootSelRow_sun4 {
	height:28px;
}

.TreeRootRowHeader_sun4 {
	height:9px;}

.TreeRow_sun4 {
	white-space:nowrap;
	clear:both;
}

.TreeRow_sun4 .float_sun4 {
	padding-left:5px;
}


.TreeRootRow_sun4 .float_sun4, .TreeRootSelRow_sun4 .float_sun4 {	padding-left:7px;

}

.TreeSelRow_sun4 {
	white-space:nowrap;
}

.TreeSelRow_sun4 .float_sun4 {
	padding-left:5px;
}

/*---*/
/* VERSION DIALOG */
.VrsMstBdy_sun4 {
	float:left;
	clear:right;
	width:100%;
	clear:both;
	height:109px;
}
.VrsBdy_sun4 {
	position:absolute;
	top:0px;
	bottom:0px;
        width:100%;
}
.VrsMgn_sun4 {
	clear:both;
	width:100%;
	overflow:auto;
}
.VrsTxt_sun4 {
	margin:0px 25px 10px;
}
.VrsBtnAryDiv_sun4 {
	text-align:right;
	padding:10px 15px;
	width:auto;
	height:auto;
}
.VrsPrdDiv_sun4 {
	padding:30px 0px 0px 195px;
}
.VrsLgoDiv_sun4 {
	padding:5px 0 0 0;
}
.VrsPrdTd_sun4 {
	float:left;
	clear:none;
	vertical-align:top;
	padding:10px 0px 0px 5px;
}
.VrsLgoTd_sun4 {
	float:right;
	clear:right;
	text-align:right;
	vertical-align:top;
	padding:12px 10px 0px 10px;
}
.VrsHdrTxt_sun4 {
	margin:20px 25px 7px;
}
/*---*/
/* WIZARD */
.WizBar_sun4 {
	height:30px;
	border-bottom-style:solid;
	border-bottom-width:1px;
}
.WizStpTab_sun4 {
	position:relative;
}
.WizTtlBar_sun4 {
	position:relative;
	height:41px;
	border-bottom-style:solid;
	border-bottom-width:1px;
	line-height:41px;
	padding:0 0 0 15px;
}
.WizStp_sun4 {
	position:absolute;
	top:72px;
	right:70%;
	bottom:34px;
	left:0px;
	overflow:auto;
	padding:18px 10px 0 4px;
}
.WizHlpDiv_sun4 {
	position:absolute;
	top:73px;
	right:70%;
	bottom:34px;
	left:0px;
	overflow:auto;
	padding:15px 10px 0 10px;
}
.WizBdy_sun4 {
	position:absolute;
	top:72px;
	bottom:34px;
	left:30%;
	right:0px;
	overflow:auto;
	border-left-style:solid;
	border-left-width:1px;
	padding:18px 10px 0 15px;
}
.WizBtm_sun4 {
	position:absolute;
	bottom:0px;
	border-top-style:solid;
	border-top-width:1px;
	padding:0 13px 0 30%;
	height:33px;
}
.WizBtnBtm_sun4 {
	float:right;
	margin: 7px 0 8px;
	padding: 0 0 0 13px;
	text-align:right;
}
.WizBtnBtm_sun4 .left_sun4 {
	float:left;
	clear:none;
}
.WizBtnBtm_sun4 .right_sun4 {
	float:right;
	clear:none;
}
.WizStpNumDiv_sun4 {
	margin:0 5px 20px 15px;
}
.WizStpArwDiv_sun4 {
	margin:0 5px 20px 0;
}
.WizStpArwDiv_sun4 span {
	vertical-align:middle;
}
.WizStpArwDiv_sun4 img {
	vertical-align:middle;
	padding:0 1px 0 0;
}
.WizStpTxtDiv_sun4 {
	margin:0 0 20px 4px;
}
.WizStpCurTxt_sun4 {
	vertical-align:top;
}
.WizSubTtlDiv_sun4 {
	margin:0 0 20px 0;
}
.WizStpTitle_sun4 {
	padding:4px 0 21px 10px;
}
.WizSubStpTtlDiv_sun4 {
	padding:2px 0 0 10px;
}
.WizCntHlpTxt_sun4 {
	margin:0 0 20px 0;
}
.WizTtl_sun4 .TtlTxtDiv_sun4 {
	margin:10px 0 3px 10px;
} 
.WizStpsPnTtlDiv_sun4 {
	padding:7px 0 7px 10px;
}
/*---*/

/* BUBBLE */
.BubbleDiv_sun4 {
    position:absolute;
    width:20em;
    z-index:99;    
}

.BubbleShadow_sun4 {
    display: block;
}

.Bubble_sun4 {
    position: relative; 
    padding: 0px;
    left: -5px;
    top: -5px;
}

.Bubble_sun4 .topLeftArrow_sun4, 
.Bubble_sun4 .topRightArrow_sun4 {
    margin: 0px;
    width: 44px;
    height: 15px;
    position: relative;
    display: none;
}

.Bubble_sun4 .bottomLeftArrow_sun4,
.Bubble_sun4 .bottomRightArrow_sun4 {
    margin: 0px;
    width: 53px;
    height: 20px;
    position: relative;
    display: none;
}

.Bubble_sun4 .bottomLeftArrow_sun4 {
    top:0px;
    left:5px;
    float:left;
}

.Bubble_sun4 .bottomRightArrow_sun4 {
    top:0px;
    left:-5px;
    float:right;
}

.Bubble_sun4 .topLeftArrow_sun4 {
    left:5px;
    float:left;
}

.Bubble_sun4 .topRightArrow_sun4 {
    left:-5px;
    float:right;
}

.BubbleHeader_sun4 {  /* Note override for IE6 in ie.css */
    overflow: auto;
    margin-right: 1px;
}

.BubbleTitle_sun4 {
    float: left;
    width: 75%;
    padding: 0;
    margin-top: 4px;
    margin-bottom: 4px;
    margin-left: 10px;
    margin-right: 10px;
}

.BubbleCloseBtn_sun4, .NoBubbleCloseBtn_sun4 {
    float: right;
    margin-top: 4px;
    margin-bottom: 4px;
    margin-right: 4px;
    width: 15px;
    height: 14px;
}

.BubbleContent_sun4 {
    margin:10px;
}

.BubbleContent_sun4 {
    clear: both;
}
/*---*/

/* ACCORDION */
.Accordion_sun4 {
    padding: 0;
    width: 100%;  /* default, can be overridden by supplying a style attribute */
    overflow: hidden;
}

.AccdHeader_sun4 {
    border-style: none solid solid solid;
    border-width: 1px;
    overflow: hidden;
    height: 25px;
}

.AccdHeader_sun4 .AccdRefreshBtn_sun4 img, .AccdHeader_sun4 .AccdOpenAllBtn_sun4 img, .AccdHeader_sun4 .AccdCloseAllBtn_sun4 img {
    float:right;
    margin-top: 2px;
    margin-right: 2px;
    margin-left: 2px;
}

.AccdDivider_sun4 {
    float: right;
    height:15px;
    width:1px;
    margin-top: 4px;
    margin-right: 2px;
    margin-left: 2px;
}

.AccdTabExpanded_sun4, .AccdTabCollapsed_sun4 {
    border-style: none solid solid solid;
    border-width: 1px;
}

.AccdTabExpanded_sun4 a, .AccdTabCollapsed_sun4 a {
    display: block;
    padding: 3px 6px 3px 6px;
}

.AccdDownTurner_sun4, .AccdRightTurner_sun4 {
    display: block;
    margin-top:3px;
    margin-left:1px;
    margin-right:1px;
    float: left;
    width: 16px;
    height: 15px;
}

.AccdTabMenuCue_sun4 {
    margin-top: 0px;
    margin-right: 2px;
    float: right;
    height: 16px;
    width: 16px;
}

.AccdTabContent_sun4 {
    border-style: solid solid solid solid;
    border-width: 1px;
    padding: 5px;
    overflow: auto;
    height: 100px;   /* default height, but can be overridden */
 }
/*---*/

/* PROGRESS BAR */
.progressBar_4_sun4 {
	margin:12px 11px;
}
.progressBar_4_sun4 .operationLabel_sun4 {
	margin:0 0 9px 1px;
	line-height:1.0;
}
.progressBar_4_sun4 .barContainer_sun4 {
	display:block;
	float:left;
	position:relative;
	margin-bottom:6px;
	padding:.09em;
	border-style:solid;
	border-width:1px;
	width:16.9em;
}
.progressBar_4_sun4 .barDeterminate_sun4, .progressBar_4_sun4 .barIndeterminate_sun4, .progressBar_4_sun4 .barIndeterminatePaused_sun4 {
	width:0%;
	height:.8em;
}
.progressBar_4_sun4 .failure_sun4{
	
	float:left;
	margin-bottom:7px;
	padding:1px;
	width:17.1em;
}
.progressBar_4_sun4 .failureLabel_sun4 {
	padding:0 0 0 1.3em;
	line-height:1.0;
}
.progressBar_4_sun4 .barIndeterminate_sun4, .progressBar_4_sun4 .barIndeterminatePaused_sun4{
	width:100%
}
.progressBar_4_sun4 .barLabel_sun4 {
	position:absolute;
	line-height:.9;
	top:.09em;
	left:0;
	width:16.9em;
	height:.9em;
	text-align:right;
}
.progressBar_4_sun4 .statusLabel_sun4 {
	padding:0 0 0 1px ;
	line-height:1.0;
	width:16.9em;
	margin-bottom:20px;
}
.progressBar_4_sun4 .buttonsBottom_sun4 {
	margin: 11px 0 20px -1px;
}
.progressBar_4_sun4 .buttonsRight_sun4 {
	float:left;
	clear:right;
	margin: -2px 0 0 0;
	padding-left:.9em;
}
.progressBar_4_sun4 .log_sun4 {
	margin:20px 0;
}
.progressBar_4_sun4 .busy_sun4 {
	margin:10px;
}
/*---*/

/* MENU */
.MenuDiv_sun4 {
    position: absolute;
    z-index: 99;
}

.MenuShadow_sun4 {
    display:block;
}

.Menu_sun4 {
    position: relative; 
    padding: 1px 0px;

    /* If you change these, then you MUST change the defaults in the javascript
       to be the absolute values of these */
    left: -5px;  /* horizontal offset from shadow */
    top: -5px;   /* vertical offset from shadow */
}

ul.MenuItems_sun4, ul.MenuItems_sun4 ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

ul.MenuItems_sun4 {
    position: relative;
    top: 0px;
    left: 0px;
}

ul.MenuItems_sun4 li .MenuItemSeparator_sun4 {
    margin:0 0.5em;
}

ul.MenuItems_sun4 ul li {
    padding:0;
}

ul.MenuItems_sun4 li .MenuItem_sun4,          /* 1st-level menu item */ 
ul.MenuItems_sun4 li .MenuItemDisabled_sun4,
ul.MenuItems_sun4 ul li .MenuItem_sun4,       /* 2nd-level menu item in 1st-level optgroup */
ul.MenuItems_sun4 ul li .MenuItemDisabled_sun4,
ul.MenuItems_sun4 li .MenuOptGroupHeader_sun4 {  /* 1st-level optgroup header */
    margin: 0.3em 0;
    height: 1.3em;
}

ul.MenuItems_sun4 li .MenuItem_sun4,             /* 1st-level menu item */
ul.MenuItems_sun4 li .MenuItemDisabled_sun4,
ul.MenuItems_sun4 li .MenuOptGroupHeader_sun4 {  /* 1st-level optgroup header */
    padding: 0px 5px;
}

.MenuItemWidget_sun4, .NoMenuItemWidget_sun4 {
    margin-top: 0.15em;  /* depends on final image height */
    width: 12px;
    float: left;
}

.MenuItemWidget_sun4 {
    height: 14px;
}

.NoMenuItemWidget_sun4 {
    height: 1px;
}

.MenuItemLabel_sun4 {
    display: block;
    margin-left: 5px;
    float: left;
}

.MenuItemAccel_sun4 {  /* Accelerators not currently supported */
    float: right;
}

.MenuItemSubmenu_sun4, .NoMenuItemSubmenu_sun4 {
    margin-top: .3em;
    margin-left: 5px;
    width: 7px;
    float: right;
}

.MenuItemSubmenu_sun4 {
    height: 7px;
}

.NoMenuItemSubmenu_sun4 {
    height: 1px;
}

ul.MenuItems_sun4 ul li .MenuItem_sun4 .MenuItemLabel_sun4,  /* 2nd-level menu item label under 1st-level optgroup header */
ul.MenuItems_sun4 ul li .MenuItemDisabled_sun4 .MenuItemLabel_sun4 {
    margin: 0 1.0em;
}

/* COMMON TASKS */
.commonTaskSection_4_sun4 {
	width: 100%;
}
.commonTaskSection_4_sun4 .TskScnTpBx_sun4 .header_sun4 {
	margin: 17px 0px 0px 20px;
}
.commonTaskSection_4_sun4 .TskScnTpBx_sun4 .help_sun4 {
	margin: 3px 0 23px 21px;
}
.commonTaskGroup_4_sun4 {
	margin:0 0 20px 20px;
}
.commonTaskSection_4_sun4 .header_sun4 {
	padding-bottom: 5px;
	display: block;
}
.commonTask_4_sun4 {
	margin:0 22px 20px;
}
.commonTaskGroup_4_sun4 .commonTask_4_sun4 {
	margin:0 0 2px 0;
}
.commonTask_4_sun4 .left_sun4 {
	border-top-style:solid;
	border-top-width:1px;
	border-bottom-style:solid;
	border-bottom-width:1px;
	border-left-style:solid;
	border-left-width:1px;
}
.commonTask_4_sun4 .center_sun4 {
	border-top-style:solid;
	border-top-width:1px;
	border-bottom-style:solid;
	border-bottom-width:1px;
}
.commonTask_4_sun4 .right_sun4 {
	border-top-style:solid;
	border-top-width:1px;
	border-bottom-style:solid;
	border-bottom-width:1px;
	border-right-style:solid;
	border-right-width:1px;
}
.commonTask_4_sun4 a.TskScnTxtBg_sun4,.commonTask_4_sun4  a.TskScnTxtBgOvr_sun4 {
	display: block;
	position: relative;
	width: 100%;
	min-height:21px;
}
.commonTask_4_sun4 .infoPanel_sun4 {
	border-style:solid;
	border-width:1px;
	position:absolute;
	height: 20em;
	width: 10em;
	z-index:2;
}
.commonTask_4_sun4 .infoPanel_sun4 .closeButton_sun4 {
	float: right;
}
.commonTask_4_sun4 .infoPanel_sun4 .info_sun4 {
	padding: 0 18px 0 0;
	margin: 21px 3px 15px 12px;
	height:14.2em;
	overflow:auto;
}
.commonTask_4_sun4 .infoPanel_sun4 .more_sun4 {
	height:1.5em;
	margin:5px 5px 0 5px;
	border-top-style:solid;
	border-top-width:1px;
	padding:2px 0 0 5px;
	overflow:hidden;
}
.commonTask_4_sun4 .bullet_sun4 {
	margin-right:.4em;
}
.commonTask_4_sun4 a.TskScnTxtBg_sun4, .commonTask_4_sun4 a.TskScnTxtBgOvr_sun4 {
	border-left-style:solid;
	border-left-width:1px;
}
.commonTask_4_sun4 .TskScnTskPdng_sun4 {
	padding: 0px 7px;
	display: block;
	padding-top: 4px;
	padding-bottom: 3px;
}
.commonTask_4_sun4 .TskScnTskLftBtm_sun4, .commonTask_4_sun4 .TskScnTskLftTp_sun4, .commonTask_4_sun4 .TskScnTskRghtBtm_sun4, .commonTask_4_sun4 .TskScnTskRghtTp_sun4 {
	position: absolute;
	width: 8px;
	height: 11px;
	cursor: pointer;
	background-position: top left;
	background-repeat: no-repeat;
}
.commonTask_4_sun4 .TskScnTskLftBtm_sun4 {
	bottom: -1px;
	left: -1px;
}     
.commonTask_4_sun4 .TskScnTskLftTp_sun4 {
	top: -1px;
	left: -1px;
}             
.commonTask_4_sun4 .TskScnTskRghtBtm_sun4 {
	bottom: -1px;
	right: 0px;
	z-index:1;
}     
.commonTask_4_sun4 .TskScnTskRghtTp_sun4 {
	position: absolute;
	top: -1px;
	right: 0px;
	z-index:1;
} 
.commonTask_4_sun4 .TskScnTskRghtBrdr_sun4 {
	display: block;
	position: absolute;
	height: 100%;
	right: 0px;
	width: 1px;
	cursor: pointer;
}
/*---*/

/* TABLE2 */
.table2_sun4 .center_sun4 {
	text-align:center !important;
}
.table2_sun4 .clear_sun4 {
	clear:both;
	height:0;
}
.table2_sun4 .clear_sun4.spacer_sun4 {
	clear:both;
	height:6px;
}
form {
	margin:0;
}
.table2_sun4 input {
	margin:0;
	padding:0;
}
.table2_sun4 {
	empty-cells:show;
	-moz-border-radius-topleft:5px;
	-moz-border-radius-topright:5px;
	padding:3px 0 0 0;
	position:relative;
	text-align:left;
        clear:both;
        float:left;
        width:100%;
}
.table2_sun4 .header_sun4, .table2_sun4 .footer_sun4 {
	clear:both;
	float:left;
	position:absolute;
	border-left-style:solid;
	border-left-width:1px;
	margin:0 0 0 -1px;
}
.table2_sun4 .header_sun4 th, .table2_sun4 .footer_sun4 th {
	border-bottom-style:solid;
	border-bottom-width:1px;
	padding:0;
}
.table2_sun4 .header_sun4 th, .table2_sun4 .footer_sun4 th {
	border-bottom-style:solid;
	border-bottom-width:1px;
	padding:0;
}
.table2_sun4 .header_sun4 th span, .table2_sun4 .footer_sun4 th span {
	display:block;
	padding:3px 5px 1px 5px;
}
.table2_sun4 .header_sun4 th a.hyperlink_sun4:link,
.table2_sun4 .header_sun4 th a.hyperlink_sun4:hover,
.table2_sun4 .header_sun4 th a.hyperlink_sun4:visited {
	display:block;
	padding:3px 5px 1px 5px;
}
.table2_sun4 .header_sun4 th a span {
	display:block;
	padding:0;
}
.table2_sun4 .bg_sun4 {
	border-width:1px;
	border-style:solid;
	border-top:none;
	float:left;
	padding:6px 5px 6px;
        height:100%;
}
.table2_sun4 .title_sun4 {
	padding:2px 10px 2px 10px;
}
.table2_sun4 .actionBar_sun4 {
	border-left:none;
	border-bottom:none;
	padding:0 0 6px 0;
	vertical-align:middle;
}
.table2_sun4 .actionBar_sun4 button, .table2_sun4 .actionBar_sun4 select {
	margin:0 5px;
}
.table2_sun4 .actionBar_sun4 .actionList_sun4 {
	float:left;
	margin:0;
	padding:0;
}
.table2_sun4 .actionBar_sun4 .controlsList_sun4 {
	float:right;
	margin:0;
	padding:0;
	text-align:right;
}
.table2_sun4 .group_sun4 {
	clear:left;
}
.table2_sun4 .tableScroller_sun4 {
	border-left-style:solid;
	border-left-width:1px;
	border-bottom-width:1px;
	border-bottom-style:solid;
	border-top-width:1px;
	border-top-style:solid;
	float:left;
	height:100px;
	overflow:auto;
	overflow-x: hidden;
	width:100%;
}
.table2_sun4 .tableScroller_sun4 table {
	clear:left;
	float:left;
	width:100%;
}
.table2_sun4 tr {
	border-right-style:solid;
	border-right-width:1px;
}
.table2_sun4 th a.hyperlink_sun4 {
	display:block;
	padding:3px 0 2px;
}
.table2_sun4 td, .table2_sun4 th  {
	border-right-style:solid;
	border-right-width:1px;
	padding:3px 5px 1px 5px;
	text-align:left;
}
.table2_sun4 td {
	border-bottom-style:solid;
	border-bottom-width:1px;
}
.table2_sun4 .groupHeader_sun4 {
	border-style:solid;
	border-width:1px;
	margin:0;
}
.table2_sun4 .groupHeader_sun4 button {
	vertical-align:middle;
}
.table2_sun4 .groupHeader_sun4 input {
	vertical-align:middle;
}
.table2_sun4 .groupHeader_sun4 th {
	padding:6px 5px 5px 5px;
	border:none;
}
.table2_sun4 .groupHeader_sun4 .groupControls_sun4 {
	float:left;
}
.table2_sun4 .groupHeader_sun4 .groupLabel_sun4 {
	margin:0 0 0 .5em;
	vertical-align:middle;
}
.table2_sun4 .groupHeader_sun4 .paginationControls_sun4 {
	float:right;
}
.table2_sun4 .groupHeader_sun4 .rowSelectionCount_sun4 {
	vertical-align:middle;
}
.table2_sun4 .groupHeader_sun4 .paginationButtons_sun4 {
	margin:0 0 0 .5em;
}
.table2_sun4 .groupHeader_sun4 table {
	width:100%;
}
/*---*/
/* CSS Document */

body {
	font-family:Arial, Helvetica, sans-serif;
	font-size:70%;
}
TABLE, TH, TD, P, DIV, SPAN, INPUT, BUTTON, SELECT, TEXTAREA, FORM, B, STRONG, LABEL, I, U, H1, H2, H3, H4, H5, H6, DL, DD, DT, UL, LI, OL, OPTION, OPTGROUP, A {
	font-size:100%;
}
H1, H2, H3, H4, H5, H6 {
	font-weight:bold;
}
H1 {
	font-size:1.7em;
}
H2 {
	font-size:1.4em;
}
H3 {
	font-size:1.3em;
}
H4 {
	font-size:1.2em;
}
H5, H6 {
	font-size:1.0em;
}
input, select {
	font-size:1.0em;
}
strong {
	font-size:1.0em;
	font-weight:bold;
}

/* ADDREMOVE [originalName: ADD-REMOVE-IDIOM]*/
.AddRmvLbl_sun4, .AddRmvLbl2_sun4 {
	font-size:1.0em;
	font-weight:bold;
}
/*---*/

/* BREADCRUMBS */
a.BcmLnk_sun4 {
	font-size:1.0em;
}
/*---*/

/* BUTTONS */
.Btn1_sun4, .Btn1Hov_sun4, .Btn1Dis_sun4, .Btn2_sun4, .Btn2Hov_sun4, .Btn2Dis_sun4, .Btn1Mni_sun4, .Btn1MniHov_sun4, .Btn1MniDis_sun4, .Btn2Mni_sun4, .Btn2MniHov_sun4, .Btn2MniDis_sun4 {
	font-size:1.0em;
}
/*---*/

/* FILE CHOOSER */
.ChoLstHdr_sun4 {
	font-size:1.0em;
	font-weight: bold;
}
.ChoSrvTxt_sun4 {
	font-size:1.0em;
}
/*---*/

/* PAGEALERT [originalName: FULL ALERTS]*/
.FulAlrtHdrTxt_sun4, .FulAlrtMsgTxt_sun4 {
	font-size:1.0em;
}
.FulAlrtHdrTxt_sun4 {
	font-weight:bold;
} 
/*---*/

/* INLINE ALERT */
.inlineAlert_4_sun4 .middle_sun4 .details_sun4 {
	font-size:1.0em;
}
.inlineAlert_4_sun4 .middle_sun4 .header_sun4 .label_sun4 {
	font-size:1.2em;
	font-weight:bold;
}
/*---*/

/* HELP WINDOW */
body.HlpBdy_sun4 h1, body.HlpBdy_sun4 h2, body.HlpBdy_sun4 h3, body.HlpBdy_sun4 h4, body.HlpBdy_sun4 h5, body.HlpBdy_sun4 h6 {
	font-family:sans-serif;
	font-weight:bold;
}
body.HlpBdy_sun4 h1 {
	font-size:1.5em;
}
body.HlpBdy_sun4 h2 {
	font-size:1.3em;
}
body.HlpBdy_sun4 h3 {
	font-size:1.2em;
}
body.HlpBdy_sun4 h4 {
	font-size:1.1em;
}
body.HlpBdy_sun4 h5, H6 {
	font-size:1.0em;
}
/*---*/


/** DnD avatar class */
.dojoDndAvatar {
	font-size: 75%;
}


/* HELPINLINE [originalName: INLINE HELP] */
.inlineFieldHelp_sun4, a.HlpFldLnk_sun4 {
	font-size:1.0em;
}
/*---*/

/* EDITABLE LIST */
table.EdtLstTbl_sun4 td.EdtLstRmvLblTd_sun4 span.LblLev2Txt_sun4,
table.EdtLstTbl_sun4 td.EdtLstAddLblTd_sun4 span.LblLev2Txt_sun4 {
	font-weight: bold;
	font-size:1.0em;
}
/*---*/
/* LABEL [originalName: GENERIC FIELD LABELS]*/

.LblLev2Txt_sun4, .LblLev2smTxt_sun4, .LblRqdDiv_sun4, .LblLev3Txt_sun4, .LblLev3TxtDis_sun4 {
	font-size:1.0em;
}
.LblLev1Txt_sun4 {
	font-size:1.3em;
}
.LblLev1Txt_sun4, .LblLev2Txt_sun4, .LblLev2smTxt_sun4,
.LblLev1TxtDis_sun4, .LblLev2TxtDis_sun4, .LblLev2smTxtDis_sun4, .LblLev3Txt_sun4, .LblLev3TxtDis_sun4 {
	font-weight:bold;
}
/*---*/

/* LEFT PANE HELP */
.LftHlpDiv_sun4 {
	font-size:1.0em;
}
/*---*/

/* LISTS */
.LstOptSel_sun4, 
.LstOptSep_sun4, 
.LstOptGrp_sun4, 
.LstDis_sun4 .LstOptGrp_sun4 {
	font-size:1.0em;
}

/* Use fixed font size in listboxes with mono-spaced fonts */
.LstMno_sun4 .LstOptGrp_sun4, 
.LstMnoDis_sun4 .LstOptGrp_sun4, 
.LstMno_sun4, 
.LstMno_sun4 option, 
.LstMno_sun4 .LstOptGrp_sun4, 
.LstMnoDis_sun4, 
.LstMnoDis_sun4 option, 
.LstMnoDis_sun4 .LstOptGrp_sun4  {
	font-size:11px;
}

.LstOptGrp_sun4, .LstDis_sun4 .LstOptGrp_sun4, .LstMno_sun4 .LstOptGrp_sun4, .LstMnoDis_sun4 .LstOptGrp_sun4 {
	font-weight:normal;
	font-style:normal;
}
.LstMno_sun4, .LstMno_sun4 option, .LstMno_sun4 .LstOptGrp_sun4, .LstMnoDis_sun4, .LstMnoDis_sun4 option, .LstMnoDis_sun4 .LstOptGrp_sun4  {
	font-family:monospace;
}
/*---*/

/* PROPERTY SHEET */
.ConFldSetLgdDiv_sun4 {
	font-size:1.1em;
}
.ConSubSecTtlTxt_sun4 {
	font-size:1.5em;
	font-weight:bold;
}
.ConFldSetLgdDiv_sun4, .ConErrLblTxt_sun4, .ConWrnLblTxt_sun4 {
	font-weight:bold;
}
.ConJmpTopDiv_sun4, a.JmpLnk_sun4 {
	font-size:1.0em;
}
/*---*/

/* CONTENTPAGETITLE [originalName: PAGE TITLE]*/
.TtlVewLbl_sun4 {
	font-weight:bold;
}
.TtlTxt_sun4 {
	font-size:1.5em;
	font-weight:bold;
}
/*---*/

/* TEXTAREA and TEXTFIELDS*/
.TxtAra_sun4, .TxtAraDis_sun4, .TxtAra_ReadOnly_sun4, 
.TxtFld_sun4, .TxtFldDis_sun4, .TxtFld_ReadOnly_sun4, 
.EdtFld_Edt_sun4, .EdtFldDis_sun4, .EdtFld_ReadOnly_sun4{
	font-family:Arial, Helvetica, sans-serif;
	font-size:1.0em;
}
/*---*/

/* TABSET [originalName: LEVEL TABS]*/
.Tab1Div_sun4, .Tab2Div_sun4, .Tab3Div_sun4, .MniTabDiv_sun4, .TabGrpBox_sun4 {
	font-size:1.0em;
}
.Tab1Div_sun4, .Tab2Div_sun4, .Tab3Div_sun4, .MniTabDiv_sun4 {
	font-weight:bold;
}
/*---*/

/* SCHEDULER/POPUP CALENDAR*/
a.DatLnk_sun4:hover, a.DatOthLnk_sun4:hover, .DatLblTxt_sun4, a.DatBldLnk_sun4:link, a.DatBldLnk_sun4:visited, a.DatCurLnk_sun4:link,
 a.DatCurLnk_sun4:visited, a.DatCurLnk_sun4:hover, a.DatOthBldLnk_sun4:link, a.DatOthBldLnk_sun4:visited, .DatSelContent_sun4 {
	font-weight:bold;
}
.DatDayHdrTxt_sun4 {
	font-weight:normal;
}
a.DatLnk_sun4, a.DatBldLnk_sun4, a.DatOthLnk_sun4, a.DatOthBldLnk_sun4, a.DatCurLnk_sun4, .DatDayHdrTxt_sun4, .DatSelContent_sun4 {
	font-size:1.1em;
}
.DatZonTxt_sun4 {
	font-size:1.0em;
}
.CalPopDiv_sun4 a.DatLnk_sun4, .CalPopDiv_sun4 a.DatBldLnk_sun4, .CalPopDiv_sun4 a.DatOthLnk_sun4, .CalPopDiv_sun4 a.DatOthBldLnk_sun4,
.CalPopDiv_sun4 a.DatCurLnk_sun4, .CalPopDiv_sun4 .DatDayHdrTxt_sun4, .CalPopDiv_sun4 .DatSelContent_sun4 {
	font-size:1.0em;
}
.CalPopDiv_sun4  a.DatCurLnk_sun4:link,.CalPopDiv_sun4 a.DatCurLnk_sun4:visited, .CalPopDiv_sun4 a.DatBldLnk_sun4:link,.CalPopDiv_sun4 a.DatBldLnk_sun4:visited,
.CalPopDiv_sun4 a.DatOthBldLnk_sun4:link,.CalPopDiv_sun4 a.DatOthBldLnk_sun4:visited, .CalPopDiv_sun4 .DatDayHdrTxt_sun4 {
	font-weight:bold;
}
/*---*/

/* MASTHEAD */
span.MstLbl_sun4, span.MstUsrRole_sun4 {
	font-weight:bold;
	font-size:1.0em;
}
span.MstTxt_sun4 {
	font-weight:normal;
	font-size:1.0em;
}
a.MstPrgLnk_sun4, a.MstAlmLnk_sun4 {
	font-size:1.0em;
	font-weight:bold;
}
/*---*/

/* TABLE [originalName: ACTION TABLE] */
.TblTtlTxt_sun4 {
	font-size:1.0em;
	font-weight:bold;
}
.TblTtlMsgSpn_sun4 {
	font-weight:normal;
}
.TblHdrTxt_sun4, .TblMultHdrTxt_sun4, .TblGrpTxt_sun4 {
	font-weight:bold;
}
table.Tbl_sun4 th {font-weight:normal}
table.Tbl_sun4 th.TblMultColHdr_sun4 a.TblHdrLnk_sun4:link, table.Tbl_sun4 th.TblMultColHdr_sun4 a.TblHdrLnk_sun4:visited {font-weight:normal;}
table.Tbl_sun4 th.TblMultColHdr_sun4 .TblHdrTxt_sun4 {font-weight:normal;}
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrLnk_sun4:link, table.Tbl_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrLnk_sun4:visited {font-weight:normal;}
.TblActLbl_sun4 {font-weight:bold;}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:link, th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:visited {font-weight:bold;}
table.Tbl_sun4 a.TblHdrLnk_sun4:link, table.Tbl_sun4 a.TblHdrLnk_sun4:visited {font-weight:bold;}
table.Tbl_sun4 a.TblHdrLnk_sun4:hover {font-weight:bold;}
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrLnk_sun4:link, table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrLnk_sun4:visited {font-weight:bold;}
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrLnk_sun4:hover {font-weight:bold;}
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:link, table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:visited {font-weight:bold;}
table.Tbl_sun4 .TblHdrSrtNum_sun4 {font-size:.9em;font-weight:normal;}
.TblGrpTxt_sun4 {font-weight:bold;}
.TblColFtrTxt_sun4 {font-weight:bold;}
table.Tbl_sun4 .TblFtrRowTxt_sun4 {font-weight:bold;}
table.Tbl_sun4 .TblFtrLft_sun4 {font-weight:bold;}
.TblGrpColFtrTxt_sun4 {font-weight:bold;}
.TblGrpFtrRowTxt_sun4 {font-weight:bold;}
.TblPnlTtl_sun4 {
	font-size:1.2em;
	font-weight:bold;
}
.TblPnlHlpTxt_sun4 {font-size:1.0em;}
/*---*/

/* LIGHTWEIGHT TABLE DESIGN */
/* Table Caption/Title */
table.TblLt_sun4 caption.TblTtlTxt_sun4 {font-weight:bold;font-size:1.0em;}
table.TblLt_sun4 caption.TblTtlTxt_sun4 span.TblTtlTxtSpn_sun4 {font-weight:bold;font-size:1.0em;}
table.TblLt_sun4 caption.TblTtlTxt_sun4 span.TblTtlMsgSpn_sun4 {font-weight:normal;font-size:1.0em;}
/* Table Footers */ 
table.TblLt_sun4 .TblFtrRowTxt_sun4 {font-weight:normal;}
/*---*/

/* TREE */
.Tree_sun4 {
	font-size:1.0em;
}
.TreTtl_sun4 {
	font-weight:bold;
}
a.TreeParentLink_sun4:link, a.TreeParentLink_sun4:visited {
	font-weight:bold;
}
a.TreeParentLink_sun4:hover {
	font-weight:bold;
}
a.TreeSelLink_sun4:link, a.TreeSelLink_sun4:visited {
	font-weight:bold;
}
a.TreeSelLink_sun4:hover {
	font-weight:bold;
}
.TreeSelText_sun4 {
	font-weight:bold;
}
/*---*/

/* VERSION DIALOG */
.VrsHdrTxt_sun4 {
	font-size:1.3em;
	font-weight:bold;
} 
.VrsTxt_sun4 {
	font-size:1.1em;
} 
/*---*/

/* WIZARD */
.WizStpTitle_sun4, .WizStpTxt_sun4, .WizHlpTxt_sun4, .WizStpCurTxt_sun4, a.WizStpLnk_sun4, .WizSubStpTtlTxt_sun4, .WizCntHlpTxt_sun4 {
	font-size:1.0em;
}
.WizTtlBar_sun4 {
	font-size:1.3em;
	font-weight:bold;
}
.WizStpTxt_sun4 {
	font-weight:normal;
}
.WizStpTitle_sun4 {
        font-weight:bold;
}
.WizSubStpTtlTxt_sun4, .WizStpCurTxt_sun4 {
	font-weight:bold;
}
.WizSubTtlTxt_sun4 {
	font-weight:bold;
	font-size:1.4em;
}
/*---*/

/* BUBBLE */
.Bubble_sun4 {
    font-weight: normal;
}
.BubbleTitle_sun4 {
    font-size: 1.0em;
    font-weight: bold;
}
/*---*/

/* ACCORDION */
.AccdTabTitle_sun4 {
    font-weight: bold;
}
/*---*/

/* PROGRESS BAR */
.progressBar_4_sun4 {
	font-size:1.0em;
}
.progressBar_4_sun4 .operationLabel_sun4, .progressBar_4_sun4 .failureLabel_sun4 {
	font-weight:bold;
}
/*---*/
/* MENU */
.Menu_sun4 {
    font-weight: normal;
}

ul.MenuItems_sun4 li div.MenuItem_sun4:hover{    /* standard hover */
    font-weight: bold;
}
/*---*/
/* COMMON TASKS */
.commonTaskSection_4_sun4 .header_sun4 {
	font-size:1.8em;
}
.commonTaskGroup_4_sun4 .header_sun4 {
	font-size: 1.3em;
}
.commonTask_4_sun4 .infoPanel_sun4 .header_sun4, .commonTaskSection_4_sun4 .help_sun4 {
	font-size:1.2em;
}
.commonTask_4_sun4 .TskScnTskPdng_sun4, .commonTask_4_sun4 .infoPanel_sun4 .content_sun4,.commonTask_4_sun4 .infoPanel_sun4 .more_sun4 a:link,
.commonTask_4_sun4 .infoPanel_sun4 .more_sun4 a:visited, .commonTask_4_sun4 .infoPanel_sun4 .more_sun4 a:hover {
	font-size: 1.1em;
} 
.commonTaskSection_4_sun4 .header_sun4, .commonTaskGroup_4_sun4 .header_sun4, .commonTask_4_sun4 .infoPanel_sun4 .header_sun4 {
	font-weight: bold;
}
/*---*/

/* TABLE2 */
.baseFont_sun4 {
	font-family:Arial, Helvetica, sans-serif;
	font-size:70%;
}
.boldface_sun4 {
	font-weight:bold;
}
.actionBar_sun4 button, .actionBar_sun4 select {
	font-size:.95em;
}
td, th  {
	font-weight:normal;
}
/*---*/
/* CSS Document */

body {
	color:#000;
}

a.Hyp_sun4:link, a.Hyp_sun4:visited, a.Anc_sun4:link, a.Anc_sun4:visited {
	color:#003399;
        text-decoration:none;
}
a.Hyp_sun4:hover, a.Anc_sun4:hover {
	color:#003399;
	text-decoration:underline;
}

a.HypDis_sun4:link, a.HypDis_sun4:visited, a.AncDis_sun4:link,a.AncDis_sun4:visited {
    color:#000000;
    text-decoration:none;
    cursor:default;
}

a.HypDis_sun4:hover, a.AncDis_sun4:hover {
   color:#000000;
   text-decoration:none;
}
/* SKIP NAVIGATION LINK */
.SkpWht_sun4 {
	background-color:#FFFFFF;
}
.SkpMedGry1_sun4 {
	background-color:#E5E9ED
}
/*---*/

/* ADDREMOVE [originalName: ADD-REMOVE-IDIOM]*/

.AddRmvLbl_sun4 {
	color:#000;
}

.AddRmvLbl2_sun4 {
	background: #f7f8fb url(images/addremove/add-rem_header.gif) repeat-x top;
	border-top: 1px solid #a4aaaf;
	border-right: 1px solid #a4aaaf;
	border-bottom: 1px solid #686b6e;
	border-left: 1px solid #a4aaaf;
}

.AddRmvLbl2_sun4, .AddRmvLbl2ReadOnly_sun4 {
	color:#000;
}

/*---*/
/* BREADCRUMBS */
.BcmWhtDiv_sun4 {
	background-color:#FFF;
}
.BcmGryDiv_sun4 {
	background-color:#E5E9ED;
}
.BcmGryBackColor_sun4 {
	background-color:#E5E9ED;  /* MUST match BcmGryDiv.background-color */
}
a.BcmLnk_sun4:link, a.BcmLnk_sun4:visited, a.BcmLnk_sun4:hover, .BcmSep_sun4 {
	color:#003399;
}
a.BcmLnk_sun4:link, a.BcmLnk_sun4:visited {
	text-decoration:none;
}
a.BcmLnk_sun4:hover {
	text-decoration:underline;
}
/*---*/

/* BUTTONS */

.Btn1_sun4, .Btn1Mni_sun4 {
	background:#ABD2EB url(images/primary-enabled.gif) repeat-x top;
	border-top-color:#6C8197;
	border-right-color:#4E647A;
	border-bottom-color:#263E5A;
	border-left-color:#4E647A;
}

.Btn1Hov_sun4, .Btn1MniHov_sun4 {
	background:#E2F1FF url(images/primary-roll.gif) repeat-x top;
	border-top-color:#6D8197;
	border-right-color:#475D75;
	border-bottom-color:#273E5A;
	border-left-color:#475D75;
}

.Btn2_sun4, .Btn2Mni_sun4, .Btn2Hov_sun4, .Btn2MniHov_sun4 {
	border-top-color:#83858C;
	border-right-color:#62656B;
	border-bottom-color:#44464C;
	border-left-color:#62656B;
}

.Btn2_sun4, .Btn2Mni_sun4 {
	background:#E0E3EB url(images/secondary-enabled.gif) repeat-x top;
}

.Btn2Hov_sun4, .Btn2MniHov_sun4 {
	background: #F4F7FE url(images/secondary-roll.gif) repeat-x top;
}

.Btn1Dis_sun4, .Btn1MniDis_sun4 {
	background:#BAC4D1 url(images/primary-disabled.gif) repeat-x top;
	color:#777E86;
	border-top-color:#B5BDC6;
	border-right-color:#A7AFB8;
	border-bottom-color:#99A0A8;
	border-left-color:#A7AFB8;
}

.Btn2Dis_sun4, .Btn2MniDis_sun4 {
	background:#D1D4DB url(images/secondary-disabled.gif) repeat-x top;
	color:#7C7D82;
	border-top-color:#CCCDD0;
	border-right-color:#BEBFC2;
	border-bottom-color:#AEAFB2;
	border-left-color:#BEBFC2;
}

.Btn1Hov_sun4, .Btn1MniHov_sun4, .Btn2Hov_sun4, .Btn2MniHov_sun4 {
	cursor:pointer;
}



/* DnD styles -----------*/

/* style for dragItem - item that is activated for dragging 
    Note that due to dojo implementation, this style will stay with the element
    even after it is dropped into Target that is not a Source. In this case
    item is not armed to be dragged, but style misleadingly remains there.
 */
.dojoDndItem { 
	cursor:pointer; 
}


/* CONTAINER styles ---------------------------------------------
    apply to source/target containers
 */

/* container ( source or target) takes on this style when dragItem is dragged
  over it 
 */
.dojoDndContainerOver {
	 border:2px dotted #aaa; 
}

/* horizontal container is marked with .dojoDndHorizontal 
 * although it is anticipated to be used alone but only as part of descendant selector ( see below)
 
 */
.dojoDndHorizontal {
	
}


/* SELECTOR styles ----------------------------------------------
    applied to individual items within containers( selectors)
*/

/* this style is added to dojoDndItem when cursor is over the potential selectable item */
.dojoDndItemOver {
	background: #99ccff; 
}
/* item(s) selected within a source(container) are marked with this style.
   Drag operation will drag all of the selected items
   */
.dojoDndItemSelected {
	background: #90C0FD;         
}
/*  item is selected and an anchor for a "shift" selection, usually  
    presented along with dojoDndItemSelected
*/
.dojoDndItemAnchor {
	background: #9ACAFF; color: black;
}

/* TARGET styles -------------------------------------------------*/
/** 
 This following styles are used to indicate an insertion point in the target. Insertion point is 
 'anchored' to existing nodes ( i.e. if no nodes are present in the list, no insertion point 
 will be displayed ). Thus the element in the target container will be assigned
 both dojoDndItemOver AND dojoDndItemBefore-or-dojoDndItemAfter.
 
 dojoDndItemBefore - The drop insertion point is located just above of the target item that is 
 currently under the cursor. 

 dojoDndItemAfter - The drop insertion point is located just under of the target item that is 
 currently under the cursor. 
 */
.dojoDndItemBefore {
	border-top: 4px dotted #369;
}

.dojoDndItemAfter {
	border-bottom:  4px dotted #369;
}
/* children of horizontal container present border differently */
.dojoDndHorizontal > .dojoDndItemBefore {
	border-left: 2px solid #369;
	border-top: none;
	border-bottom: none;
}
.dojoDndHorizontal > .dojoDndItemAfter {
	border-right: 2px solid #369;
	border-top: none;
	border-bottom: none;
}

/* AVATAR styles ------------------------------------------------
    these styles are hardcoded into default avatar implementation.
    if implementation is changed, they may be irrelevant.
 */
 

/* avatar header */	
.dojoDndAvatarHeader {
	background: #aaa;
}

/* avatar item */
.dojoDndAvatarItem {
	background: #fff;
	border-bottom:1px solid #666;
}
/* avatar is ready to be dropped */
.dojoDndAvatarCanDrop {
	background: #aaf;
	
}

.dojoDndMove .dojoDndAvatarHeader	{background-image: url(images/dnd/dndNoMove.png); background-repeat: no-repeat;}
.dojoDndCopy .dojoDndAvatarHeader	{background-image: url(images/dnd/dndNoCopy.png); background-repeat: no-repeat;}
.dojoDndMove .dojoDndAvatarCanDrop .dojoDndAvatarHeader	{background-image: url(images/dnd/dndMove.png); background-repeat: no-repeat;}
.dojoDndCopy .dojoDndAvatarCanDrop .dojoDndAvatarHeader	{background-image: url(images/dnd/dndCopy.png); background-repeat: no-repeat;}

.dojoDndWebuiItemDragged {
    opacity:.50; -moz-opacity: 0.5; 
}


.MstDiv_sun4 .Btn1_sun4, .MstDiv_sun4 .Btn1Mni_sun4, .MstDiv_sun4 .Btn2_sun4, .MstDiv_sun4 .Btn2Mni_sun4, .MstDiv_sun4 .Btn1Hov_sun4, .MstDiv_sun4 .Btn1MniHov_sun4, .MstDiv_sun4 .Btn2Hov_sun4, .MstDiv_sun4 .Btn2MniHov_sun4,
.mastheadButton_4_sun4 a:link, .mastheadButton_4_sun4 a:visited, .mastheadButton_4_sun4 a:hover {
	color:#FFF;
	vertical-align:middle;
}.MstDiv_sun4 .Btn1_sun4, .MstDiv_sun4 .Btn1Mni_sun4, .MstDiv_sun4 .Btn2_sun4, .MstDiv_sun4 .Btn2Mni_sun4,
.mastheadButton_4_sun4 a:link, .mastheadButton_4_sun4 a:visited {
	background:#7595AB url(images/masthead/masthead_button.gif) repeat-x bottom;
}.mastheadButton_4_sun4 a:link, .mastheadButton_4_sun4 a:visited, .mastheadButton_4_sun4 a:hover {
	text-decoration:none;
}
.mastheadButton_4_sun4 a:hover,
.MstDiv_sun4 .Btn1Hov_sun4, .MstDiv_sun4 .Btn1MniHov_sun4, .MstDiv_sun4 .Btn2Hov_sun4, .MstDiv_sun4 .Btn2MniHov_sun4 {
	background:#99B0C4 url(images/masthead/masthead_button_over.gif) repeat-x bottom;
}

.mastheadButton_4_sun4 {
	background-color:#374154;
	border-top-color:#4a5467;
	border-right-color:#374154;
	border-left-color:#374154;
	border-bottom-color:#7ea0b8;
}
/*---*/
/* SCHEDULER [originalName: DATE AND TIME]*/
.DatCalDiv_sun4 {
	border-left-color: #5F6466;
	border-right-color:#5F6466;
	border-bottom-color:#5F6466;
}
.DatSelTopMiddle_sun4 {
	background-color:#3D6079;
	border-top-color:#5F6466;
}

.DatSelContent_sun4 {
	border-left-color: #5F6466;
	border-right-color:#5F6466;
	background:#3D6079 url(images/scheduler/header-short.gif) bottom left repeat-x;
	color:#FFFFFF;
}

.DatCalDiv_sun4 {
	border-left-color: #5F6466;
	border-right-color:#5F6466;
	border-bottom-color:#5F6466;
}

.DatCalDiv_sun4, .DatCalTbl_sun4 th {
	background-color:#E3E7EA;
}

.DatCalTbl_sun4 th {
	border-color:#E3E7EA;
}

.DatCalTbl_sun4 td {
	border-color:#E1E5E8;
}

.DatDayHdrTxt_sun4, .DatZonTxt_sun4 {
	color:#000000;
}
.DatZonTxt_sun4 {
	color:#707277;
}
.DatLblTxt_sun4 {
	color:#666;
}

a.DatLnk_sun4:link, a.DatLnk_sun4:visited {
	color:#000000;
	background-color:#fff;
	text-decoration:none;
	border-color:#fff;
}

a.DatLnk_sun4:hover {
	text-decoration:underline;
	border-color:#fff;
}

a.DatBldLnk_sun4:link, a.DatBldLnk_sun4:visited {
	color:#000000;
	background-color:#90B7D0;
	border-top-color:#242E34;
	border-right-color:#90B7D0;
	border-bottom-color:#90B7D0;
	border-left-color:#242E34;
	text-decoration:none;
}

a.DatBldLnk_sun4:hover {
	text-decoration:underline;
}

a.DatCurLnk_sun4:link, a.DatCurLnk_sun4:visited {
	color:#000;
	background-color:#fff;
	text-decoration:none;
	border-color:#fff;
}

a.DatCurLnk_sun4:hover {
	text-decoration:underline;
	border-color:#fff;
}

a.DatOthLnk_sun4 {
	color:#000;
	background-color:#ECF0F3;
	text-decoration:none;
	border-color:#ECF0F3;
}

a.DatOthLnk_sun4:hover {
	text-decoration:underline;
}

a.DatOthBldLnk_sun4:link, a.DatOthBldLnk_sun4:visited {
	color:#000000;
	background-color:#90B7D0;
	border-top-color:#242E34;
	border-right-color:#90B7D0;
	border-bottom-color:#90B7D0;
	border-left-color:#242E34;
	text-decoration:none;
}

a.DatOthBldLnk_sun4:hover {
	text-decoration:underline;
}
/*---*/

/*CALENDAR */
.CalPopDiv_sun4 {
	background:transparent url(images/calendar/calpop_dropshadow.png) bottom right no-repeat;
}
.CalPopDiv_sun4  a.DatCurLnk_sun4:hover, .CalPopDiv_sun4  a.DatLnk_sun4:hover, .CalPopDiv_sun4 a.DatBldLnk_sun4:hover, .CalPopDiv_sun4 a.DatOthBldLnk_sun4:hover {
	text-decoration:underline;
}
.CalPopDiv_sun4 .DatCalDiv_sun4 {
	background-color:#E5E9ED;
}
.CalPopDiv_sun4 a.DatOthLnk_sun4 {
	background-color:#E5E9ED;
	border-color:#E5E9ED;
}
.CalPopDiv_sun4 a.DatOthLnk_sun4:hover {
	text-decoration:underline;
}
.DatCalDiv_sun4, .DatCalTbl_sun4 th {
	background-color:#E3E7EA;
}
.CalPopDiv_sun4 .DatCalTbl_sun4 {
	border-color:#D6DCE1;
}
.CalPopDiv_sun4 .DatCalTbl_sun4 td {
	border-color:#D6DCE1;
}
.CalPopDiv_sun4 .DatCalTbl_sun4 th {
	border-color:#D6DCE1;
	background-color:#E5E9ED;
}
.CalPopDiv_sun4 a.CalPopClsLnk_sun4:link, .CalPopDiv_sun4 a.CalPopClsLnk_sun4:active {
	color:#333;
	border-color:#A8B8C3;
	text-decoration:none;
}
.CalPopDiv_sun4 a.CalPopClsLnk_sun4:hover {
	text-decoration:underline;
}
.CalPopDiv_sun4 .CalPopFtrDiv_sun4 {
	background:#EEF1F4 url(images/calendar/calpop_footer_grad.gif) bottom left repeat-x;
}
.CalPopDiv_sun4 .CurDayTxt_sun4 {
	color:#333;
}
/*---*/

/* FILE CHOOSER */
.ChoLblTxt_sun4 {
	color:#5F6466;
}
.ChoSrvTxt_sun4 {
	color:#333;
}
.ChoHr_sun4 {
	background-color: #98a0a5;
}
.ChoLstHdr_sun4 {
	background: #f7f8fb url(images/file-chooser/column_hdr_gradient.gif) repeat-x top;
	border-top: 1px solid #9ca4aa;
	border-bottom: 1px none #686b6e;
	border-left: 1px solid #9ca4aa;
	border-right: 1px solid #9ca4aa;
}
.ChoLstHdr_sun4 .ChoSizeHdr_sun4 {
	border-right: 1px solid #ced0d3;
	border-left: 1px solid #ced0d3;
}

/*---*/
/* PAGEALERT [originalName: FULL ALERTS]*/
.FulAlrtHdrTxt_sun4, .FulAlrtMsgTxt_sun4 {
	color:#000;
}


/* INLINE ALERT */
.inlineAlert_4_sun4 .topLeftCorner_sun4 {
	background:transparent url(images/inlineAlert/alertbackground_top_left.gif) no-repeat;
}
.inlineAlert_4_sun4 .topMiddle_sun4 {
	background:transparent url(images/inlineAlert/alertbackground_top.gif) top repeat-x;
}
.inlineAlert_4_sun4 .topRightCorner_sun4 {
	background:transparent url(images/inlineAlert/alertbackground_top_right.gif) no-repeat;
}
.inlineAlert_4_sun4 .middleRow_sun4 {
	background-color:#FFF7C8;
}
.inlineAlert_4_sun4 .leftMiddle_sun4 {
	background:#FFF7C8 url(images/inlineAlert/alertbackground_middle.gif) top left repeat-x;
	border-left-color:#EDE18D;
} 
.inlineAlert_4_sun4 .middle_sun4 {
	background:#FFF7C8 url(images/inlineAlert/alertbackground_middle.gif) top left repeat-x;
} 
.inlineAlert_4_sun4 .middle_sun4 .details_sun4 a {
	color:#003399;
	text-decoration:none;
}
.inlineAlert_4_sun4 .middle_sun4 .details_sun4 a:hover {
	text-decoration:underline;
}
.inlineAlert_4_sun4 .rightMiddle_sun4 {
	background:#FFF7C8 url(images/inlineAlert/alertbackground_middle.gif) top left repeat-x;
	border-right-color:#EDE18D;
}
.inlineAlert_4_sun4 .bottomLeftCorner_sun4 {
	background:transparent url(images/inlineAlert/alertbackground_bottom_left.gif) no-repeat;
}
.inlineAlert_4_sun4 .bottomMiddle_sun4 {
	background-color:#FFF7C8;
	border-bottom-color:#E6DDA2;
}
.inlineAlert_4_sun4 .bottomRightCorner_sun4 {
	background:transparent url(images/inlineAlert/alertbackground_bottom_right.gif) no-repeat;}
/*---*/

/* HELP WINDOW */
body.HlpBdy_sun4 {
	background-color:#FFF;
	color:#000000;
	border-left-color:#A8B0B5;
}
.HlpMstTtlBdy_sun4 {
	background-color:#E5E9ED;
}
.HlpBtnDiv_sun4 {
	background:#E5E9ED url(images/tabs/background_border_bottom.gif) bottom left repeat-x;
}
/*---*/

/* HELPINLINE [originalName: INLINE HELP]*/
.inlinePageHelp_sun4, .inlineFieldHelp_sun4 {
	color:#707277;
}
 .inlinePageHelp_sun4 a, a.HlpPgeLnk_sun4:link, a.HlpPgeLnk_sun4:visited, a.HlpFldLnk_sun4:link, a.HlpFldLnk_sun4:visited {
	color:#003399;
	text-decoration:none;
}
.inlinePageHelp_sun4 a:hover, a.HlpPgeLnk_sun4:hover, a.HlpFldLnk_sun4:hover {
	color:#003399;
	text-decoration:underline;
}
/*---*/

/* CHECKBOXES AND RADIO BUTTONS */
.CbDis_sun4, .RbDis_sun4 {
	color:#848687;
}
/*---*/

/* Editable Field*/
    
.EdtInvld_sun4 {
    background-color: #FFF7C8 !important;    
} 

.EdtVld_sun4  {
}

/*---*/

/* LABEL [originalName: GENERIC FIELD LABELS]*/

.LblLev1Txt_sun4, .LblLev2Txt_sun4, .LblLev2smTxt_sun4, .LblLev3Txt_sun4 {
	color:#000000;
}
.LblLev1TxtDis_sun4, .LblLev2TxtDis_sun4, .LblLev2smTxtDis_sun4, .LblLev3TxtDis_sun4 {
	color:#7E7E7E;
}
.LblRqdDiv_sun4 {
	color:#707277;
}

/*---*/
/* LEFT PANE HELP */
.LftHlpMst_sun4 {
	background:#E5E9ED;
}
.LftHlpHlp_sun4 {
	background:#E5E9ED url(images/leftpane/left-pane-background.gif) left top repeat-x;
}
.LftHlpBdy_sun4 {
	background-color:#FFFFFF;
}
.LftHlpBtm_sun4, .LftHlpBtnBtm_sun4 {
	background-color:#E5E9ED;
}
.LftHlpTxt_sun4, .LftHlpHlp_sun4, .LftHlpBdy_sun4 {
	color:#000000;
}
.LftHlpBdy_sun4, .LftHlpBtm_sun4, .LftHlpBtnBtm_sun4, .LftHlpMst_sun4 {
	border-color:#A8B0B5;
}
/*---*/
/* LISTS */
/* Regular List */
.Lst_sun4, .LstMno_sun4 {
	background:#FFFFFF url(images/bg_gradient.gif) repeat-x top;
}
.LstDis_sun4, .LstMnoDis_sun4 {
	background:#EAEDF0 url(images/bg_gradient_disabled.gif) repeat-x top;
}
.Lst_sun4, .LstDis_sun4, .LstMno_sun4, .LstMnoDis_sun4 {
	border-top-color:#686B6E;
	border-right-color:#CDCDCF;
	border-bottom-color:#CDCDCF;
	border-left-color:#CDCDCF;
}
.Lst_sun4, .LstOpt_sun4, .LstOptSel_sun4, .LstOptGrp_sun4, .LstMno_sun4, .LstMno_sun4 .LstOptGrp_sun4 {
	color:#333;
}
.LstOptSep_sun4, .LstOptDis_sun4 {
	color:#848687;
}
.LstDis_sun4, .LstDis_sun4 option, .LstDis_sun4 .LstOptGrp_sun4, .LstMnoDis_sun4, .LstMnoDis_sun4 option, .LstMnoDis_sun4 .LstOptGrp_sun4 {
	background-color:#EAEDF0;
	color:#848687;
}
/*---*/

/* MESSAGE AND MESSAGE GROUPS */
.MsgFldSumTxt_sun4 {
    color:#C00;
}
.MsgFldTxt_sun4 {
    color:#C00;
}
.MsgGrpTbl_sun4 {
    border:solid 1px #C00;
    margin-top:10px;
}
.MsgGrpTbl_sun4 td {
    background-color:#FAEDED;
}
.MsgGrpTblTtl_sun4 {
    color:#000;
    background-color:#FAEDED;
}
.MsgGrpDiv_sun4 ul {
    color:#C00;
    list-style-type:square;
}
.MsgGrpSumTxt_sun4 {
    color:#000;
}
.MsgGrpTxt_sun4 {
    color:#333;
}
/*---*/

/* PROPERTY SHEET */
/* [check overwrite classes on css_ie55up.css] */
.ConLin_sun4 {
	margin:10px 0 10px 10px;
	background-color:#92A2AA;
	width:100%;
}
.ConFldSetLgdDiv_sun4, .ConSubSecTtlTxt_sun4, .ConFldSetLgd_sun4 {
	color:#000000;
}
.ConDefTxt_sun4, .ConRqdTxt_sun4 {
	color:#707277;
}
.ConErrLblTxt_sun4 {
	color:#C00;
}
.ConWrnLblTxt_sun4 {
	color:#C90;
}
a.JmpLnk_sun4, a.JmpTopLnk_sun4 {
	color:#003399;
}
a.JmpLnk_sun4:link, a.JmpLnk_sun4:visited {
	text-decoration:none;
	vertical-align:top;
}
a.JmpLnk_sun4:hover {
	text-decoration:underline;
	vertical-align:top;
}
a.JmpTopLnk_sun4:link, a.JmpTopLnk_sun4:visited {
	text-decoration:none;
}
a.JmpTopLnk_sun4:hover {
	text-decoration:underline;
}
/*---*/
/* CONTENTPAGETITLE [originalName: PAGE TITLE]*/
.TtlLin_sun4 {
	background-color:#81939B;
}
span.TtlTxt_sun4, h1.TtlTxt_sun4 {
	color:#000;
}
.TtlVewLbl_sun4 {
	color:#5F6466;
}
/*---*/
/* TABSET [originalName: LEVEL TABS]*/
.Tab1Div_sun4, .Tab1Div_sun4 .Tab1TblSpcTd_sun4 {
	background:#FFFFFF url(images/background_border_bottom.gif) left bottom repeat-x;
} 
a.Tab1Lnk_sun4:link, a.Tab1Lnk_sun4:visited  {
	color:#000;
	text-decoration:none;
}
a.Tab1Lnk_sun4:hover {
	text-decoration:underline;
}
.Tab1Div_sun4 td {
	border-color: #80929B;
	background:#FCFFFF url(images/level1_deselect.jpg) left top repeat-x;
}.Tab1Tbl2New_sun4 .Tab1SelTxtLeft_sun4 {
	background-image:url(images/level1_selected-left.jpg);
}.Tab1Tbl2New_sun4 .Tab1SelTxtNew_sun4{
	background-image:url(images/level1_selected-right.jpg);
}
.Tab1TblNew_sun4 .Tab1TblSelTd_sun4 {
	background-color:#FFFFFF ;
	background-image:url(images/level1_selected-1lvl.jpg);
}
.Tab1Tbl2New_sun4 .Tab1TblSelTd_sun4, .Tab1Tbl3New_sun4 .Tab1TblSelTd_sun4 {
	background-color:#EEF0F4;
	background-image:url(images/level1_selected-middle.jpg);
}
/* LEVEL 2 TABS */
.Tab2Div_sun4 {
	background:#F1F3F6 url(images/background_border_bottom.gif) left bottom repeat-x;
}
a.Tab2Lnk_sun4:link, a.Tab2Lnk_sun4:visited {
	color:#000;
	text-decoration:none;
}
a.Tab2Lnk_sun4:hover {
	text-decoration:underline;
}
.Tab2Div_sun4 td {
	border-color:#80929B;
	background:#FCFFFF url(images/level2_deselect.jpg) left top repeat-x;
}.Tab2Tbl3New_sun4 .Tab2SelTxtLeft_sun4 {
	background-image:url(images/level2_selected-left.jpg);
}.Tab2Tbl3New_sun4 .Tab2SelTxt_sun4 {
	background-image:url(images/level2_selected-right.jpg);
}.Tab2TblNew_sun4 .Tab2TblSelTd_sun4 {
	border-color:#80929B;	background-image:url(images/level2_selected.gif);
}.Tab2Tbl3New_sun4 .Tab2TblSelTd_sun4 {
	border-color:#80929B;
	background:#F8F9FB url(images/level2_selected-middle.jpg) left top repeat-x;
}
.Tab2Div_sun4 {
	padding:6px 0px 0px 10px;
}
.Tab2Div_sun4 table {
	border-collapse:collapse;
}
.Tab2Div_sun4 td.Tab2TblSelTd_sun4 {
	border-bottom:none;
}
/* LEVEL 3 TABS*/
.Tab3Div_sun4 {
	background:#F8F9FA url(images/tabs/background_border_bottom.gif) right bottom repeat-x;
}
.Tab3Div_sun4 td {
	border-color:#80929B;
}
.Tab3Div_sun4 table {
	border-collapse:collapse;
}
a.Tab3Lnk_sun4:link,a.Tab3Lnk_sun4:visited {
	color:#000;
	text-decoration:none;
}
a.Tab3Lnk_sun4:hover {
	text-decoration:underline;
}
table.Tab3TblNew_sun4 {
	background-color:#E2E7EA;
}
table.Tab3TblNew_sun4 td {
	border-bottom-color:#80929B;
	background:#FCFFFF url(images/tabs/level3_deselect.jpg) left top repeat-x;
}.Tab3TblNew_sun4 td.Tab3TblSelTd_sun4 {
	border-color:#80929B;
	background:#FFFFFF url(images/tabs/level3_selected.jpg) left top repeat-x;
}
/* MINI-TABS */
.MniTabDiv_sun4 {
	background:#E5E9ED url(images/tabs/background_border_bottom.gif) bottom left repeat-x;
}
table.MniTabTbl_sun4 td {
	border-color:#80929B;
	background:#FCFFFF url(images/tabs/minitab_deselect.jpg) left top repeat-x;
}
a.MniTabLnk_sun4:link,a.MniTabLnk_sun4:visited {
	color:#000;
	text-decoration:none;
}
a.MniTabLnk_sun4:hover {
	text-decoration:underline;
}
.MniTabSelTxt_sun4 {
	color:#000;
	text-decoration:none;
}
table.MniTabTbl_sun4 td.MniTabTblSelTd_sun4 {
	background:#FFFFFF url(images/tabs/minitab_selected.jpg) left top repeat-x;
	border-color:#80929B;
}
/* MINI-TABS - LIGHTWEIGHT */
.TabGrp_sun4 .TabGrpBox_sun4 {
	border-color:#80929B;
}
.TabGrp_sun4 .MniTabDiv_sun4 {
	background-color:transparent;
	background-image:url(images/tabs/background_border_bottom.gif);
}
/*---*/
/* TEXT AND PASSWORD FIELDS */

.TxtFldDis_sun4, .TxtAraDis_sun4 {
	background:#EAEDF0 url(images/bg_gradient_disabled.gif) repeat-x top;
	color:#7E7E7E;
}

.TxtFld_sun4Sel {
	background:#A0BBD8 url(images/bg_gradient_selected.gif) repeat-x top;
}

.TxtFld_sun4, .TxtFldDis_sun4, .TxtAra_sun4, .TxtAraDis_sun4 {
	border-top-color:#9D9FA1;
	border-right-color:#AEAFB0;
	border-bottom-color:#B9B9BA;
	border-left-color:#AEAFB0;
}
.TxtFldInvld_sun4 , .TxtAraInvld_sun4 {
    background-color: #FFF7C8 !important;    
} 

.TxtFldVld_sun4 , .TxtAraVld_sun4 {
	background:#FFFFFF url(images/bg_gradient.gif) repeat-x top;
}

/*---*/

/* MENUS */

.MnuJmpOptGrp_sun4, .MnuStdOptGrp_sun4 {color:#000;}

.MnuStdOpt_sun4, .MnuStdOptSel_sun4, .MnuStd_sun4 {color:#333;}

.MnuJmpOpt_sun4, .MnuJmpOpt_sun4:hover, .MnuJmp_sun4 {color:#0A3A9C;}

.MnuJmpOptSep_sun4, .MnuJmpOptDis_sun4, .MnuStdOptSep_sun4, .MnuStdOptDis_sun4, .MnuJmpDis_sun4, .MnuStdDis_sun4 {
	color:#848687;
	border-top-color:#9D9FA1;
	border-right-color:#AEAFB0;
	border-bottom-color:#B9B9BA;
	border-left-color:#AEAFB0;
}

.MnuJmpOpt_sun4:hover {text-decoration:underline;}

.MnuJmp_sun4, .MnuStd_sun4 {
	background:#FFFFFF url(images/bg_gradient.gif) repeat-x top;
	border-top-color:#9D9FA1;
	border-right-color:#AEAFB0;
	border-bottom-color:#B9B9BA;
	border-left-color:#AEAFB0;
}

.MnuJmpOptSel_sun4 {font-weight:normal;}

.MnuJmpOptGrp_sun4, .MnuStdOptGrp_sun4 {font-weight:normal;font-style:normal;}

/*---*/

/* MASTHEAD */

span.MstLbl_sun4, span.MstTxt_sun4, span.MstUsrRole_sun4, span.MstAlmDwnTxt_sun4, span.MstAlmCrtTxt_sun4, span.MstAlmMajTxt_sun4, span.MstAlmMinTxt_sun4,
a.MstUsrLnk_sun4:link, a.MstUsrLnk_sun4:visited, a.MstAlmLnk_sun4:link, a.MstAlmLnk_sun4:visited, a.MstPrgLnk_sun4:link, a.MstPrgLnk_sun4:visited {
	color:#FFFFFF;
}

a.MstUsrLnk_sun4:hover, a.MstAlmLnk_sun4:hover, a.MstPrgLnk_sun4:hover,
a.MstUsrLnk_sun4:link, a.MstUsrLnk_sun4:visited, a.MstAlmLnk_sun4:link, a.MstAlmLnk_sun4:visited, a.MstPrgLnk_sun4:link, a.MstPrgLnk_sun4:visited {
	text-decoration:underline;
}

.MstBdy_sun4 {
	background-color:#5B87A5;
}

.MstTblEnd_sun4 {
	background-color:#5B87A5;
}
.MstTblBot_sun4 .hrule_sun4{
	border-top-color:#3E637E;
	background-color:#7196B0;
}	
/*
.MstDiv_sun4 {
	background:#5B87A5 url(images/masthead/masthead-background.jpg) top left repeat-x;
	border-color:#000000;
}
.MstSec_sun4 {
	background:#5B87A5 url(images/masthead/sec-masthead-background.jpg) top left repeat-x;
	border-color:#000000;
}
*/
.MstFooter_sun4 {
	background-color:#5B87A5;
	border-color:#000000;
}

a.MstLnk_sun4:hover, a.MstLnkLft_sun4:hover, a.MstLnkRt_sun4:hover, a.MstLnkCen_sun4:hover {
	background-color:#EAF9FF;
	background-image:url(images/masthead_link_roll.gif);
}

a.MstLnk_sun4:link, a.MstLnk_sun4:visited, a.MstLnkLft_sun4:link, a.MstLnkLft_sun4:visited, a.MstLnkRt_sun4:link, a.MstLnkRt_sun4:visited, a.MstLnkCen_sun4:link, a.MstLnkCen_sun4:visited {
	background-color:#D9E6EC;
	background-image:url(images/masthead_link_enabled.gif);
	color:#000;
	text-decoration:none;
	border-top-color:#2D3C46;
	border-bottom-color:#0E1418;
	border-left-color:#1C262D;
}

a.MstLnk_sun4:link, a.MstLnk_sun4:visited, a.MstLnkRt_sun4:link, a.MstLnkRt_sun4:visited {
	border-right-color:#1C262D;
}
/*---*/


/* TABLE [originalName: ACTION TABLE] */
.Tbl_sun4 {
	border-color:#8C8F91;
	border-top-color:#35556A;
	background-color:#E5E9ED;
	color:#000;
}
table.Tbl_sun4 td, 
table.Tbl_sun4 th {
	border-color:#CAD0D2;
	background-color:#fff;
}
/* Table Caption/Title */
table.Tbl_sun4 caption.TblTtlTxt_sun4 {
	color:#fff;
	background:#36586F url(images/table/table_titlebar_gradient.gif) bottom left repeat-x;
}
caption.TblTtlTxt_sun4 span.TblTtlMsgSpn_sun4 {
	color:#fff;
}
/* Action Bar */
table.Tbl_sun4 td.TblActTdLst_sun4 {
	border-top-color:#e5e5e5;
	background-color:#E5E9ED;
}
table.Tbl_sun4 td.TblActTd_sun4 {
	background-color:#E5E9ED;}
 
/* Selection Column - Headers */
table.Tbl_sun4 th.TblColHdrSel_sun4 {
	border-top-color:#8C8F91;
	border-left-color:#8C8F91;
	border-bottom-color:#8C8F91;
	background-color:#f8f8f9;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 {
	background:#E1E5E8  url(images/table2/column_gradient.png) center top repeat-x;
	border-top-color:#959aa5;
	border-left-color:#959aa5;
	border-bottom-color:#828da4;
}
table.Tbl_sun4 th.TblColHdrSel_sun4 a.TblHdrLnk_sun4:link, 
table.Tbl_sun4 th.TblColHdrSel_sun4 a.TblHdrLnk_sun4:visited, 
table.Tbl_sun4 th.TblColHdrSel_sun4 a.TblHdrLnk_sun4:hover {
	background-color:#fff;
	text-decoration:none;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:link,
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:visited {
	border-left-color:#C7C8CA;
	color:#336699;text-decoration:none;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:hover {
	color:#003399;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrLnk_sun4:link,
table.Tbl_sun4  th.TblColHdrSrtSel_sun4 a.TblHdrLnk_sun4:visited {
	color:#000000;text-decoration:none;
}
table.Tbl_sun4 th.TblColHdrSel_sun4 a.TblHdrLnk_sun4:link,
table.Tbl_sun4 th.TblColHdrSel_sun4 a.TblHdrLnk_sun4:visited {
	background:#E1E5E8 url(images/table2/column_gradient.png) repeat-x center top;
}
table.Tbl_sun4 th.TblColHdrSel_sun4 a.TblHdrLnk_sun4:hover {
	background:#f1f3f6 url(images/table2/column_gradient.png) repeat-x center top;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:hover {
	background-color:#E1E5E8;
	background-image:url(images/table2/column_gradient.png);
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:hover {
	background-repeat:repeat-x;
	background-position:center top;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrLnk_sun4:hover {
	background-color:#E1E5E8; 
	background-image:url(images/table2/column_gradient.png);
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrLnk_sun4:hover {
	background-repeat:repeat-x;
	background-position:center top;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:link,
th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:visited {
	background:none;
}
table.Tbl_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrLnk_sun4:link, 
th.TblColHdrSrtSel_sun4 a.TblHdrLnk_sun4:visited {
	background:none;
}
/* Selection Column - Cells */
table.Tbl_sun4 td.TblTdSel_sun4 {
	background-color:#FFFFFF;
}
table.Tbl_sun4 td.TblTdSrtSel_sun4 {
	background-color:#e5e9ed;
}
/* Regular Column Headers */
table.Tbl_sun4 th.TblColHdr_sun4 {
	border-color:#8C8F91;
	background-color:#FFFFFF;
}
table.Tbl_sun4 .TblHdrTxt_sun4 {
	color:#000000;
	text-decoration:none;
	background:#E1E5E8 url(images/table2/column_gradient.png) repeat-x center top;
}
table.Tbl_sun4 a.TblHdrLnk_sun4:link, 
table.Tbl_sun4 a.TblHdrLnk_sun4:visited {
	background-color:#E1E5E8;
	background-position: center top;
	color:#000000;
	text-decoration:none; 
	background-image:url(images/table2/column_gradient.png);
}
table.Tbl_sun4 a.TblHdrLnk_sun4:link, 
table.Tbl_sun4 a.TblHdrLnk_sun4:visited {
	background-repeat:repeat-x;
	background-position:center top;
}
table.Tbl_sun4 a.TblHdrLnk_sun4:hover {
	color:#000000;
	text-decoration:underline;
	background-color:#f1f3f6; 
	background-image:url(images/table2/column_gradient.png);
}
table.Tbl_sun4 a.TblHdrLnk_sun4:hover {
	background-repeat:repeat-x;
	background-position:center top;
}
table.Tbl_sun4 a.TblHdrImgLnk_sun4:link, 
table.Tbl_sun4 a.TblHdrImgLnk_sun4:visited {
	background-color:#E1E5E8;
	color:#336699;
	text-decoration:none;
	border-left-color:#CAD0D2; 
	background-image:url(images/table2/column_gradient.png);
}
table.Tbl_sun4 a.TblHdrImgLnk_sun4:link, 
table.Tbl_sun4 a.TblHdrImgLnk_sun4:visited {
	background-repeat:repeat-x;
	background-position:center top;
}
table.Tbl_sun4 a.TblHdrImgLnk_sun4:hover {
	color:#336699; 
	background-image:url(images/table2/column_gradient.png);
	background-color:#f1f3f6;
}
table.Tbl_sun4 a.TblHdrImgLnk_sun4:hover {
	background-repeat:repeat-x;
	background-position:center top;
}
table.TblHdrTbl_sun4 {
	background:none;
}
table.TblHdrTbl_sun4 td {
	background:none;
}
/* Current Sort Column */
table.Tbl_sun4 th.TblColHdrSrt_sun4 {
	border-color:#81939B;
	background:#CAD0D2  url(images/table2/column_gradient.png) center top repeat-x; 	
}
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrLnk_sun4:link, 
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrLnk_sun4:visited {
	color:#000000;
	text-decoration:none;
}
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:link, 
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:visited {
	color:#336699;
	border-left-color:#C7C8CA;
	text-decoration:none;
}
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrLnk_sun4:hover {
	color:#000000;
	text-decoration:underline;
	background-color:#E1E5E8;
	background-image:url(images/table2/column_gradient.png);
	background-position:center top;
	background-repeat:repeat-x;
}
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:hover {
	color:#003399;
	background-color:#E1E5E8;
	background-image:url(images/table2/column_gradient.png);
	background-repeat:repeat-x;
	background-position:center top;
}

table.Tbl_sun4 span.TblColHdrSrtDis_sun4 {
	color:#000000;
	text-decoration:none;
	border-left-color:#C7C8CA;
	background:none;
}
table.Tbl_sun4 span.TblColHdrSelDis_sun4 {
	background-color:#E1E5E8;
	color:#000000;
	text-decoration:none;
	border-left-color:#DBE0E3;
	background-image:url(images/table2/column_gradient.png);
	background-repeat:repeat-x;
	background-position:center top;
}
table.Tbl_sun4 th.TblColHdrSrt_sun4 table.TblHdrTbl_sun4 {
	background:none;
}
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrLnk_sun4:link, 
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrLnk_sun4:visited {
	background:none;
}
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:link, 
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:visited {
	background:none;
	border-left-color:#C7C8CA;
}
/* Multi-Column Headers */ 
table.Tbl_sun4 th.TblMultColHdr_sun4 {
	border-left-color:#81939B;
	border-bottom-color:#81939B;
	background-color:#fff;
}
.TblMultHdrTxt_sun4 {
	color:#000000;
}
table.Tbl_sun4 th.TblMultHdr_sun4 {
	background-color:#fff;
	border-top-color:#81939B;
	border-left-color:#81939B;
}
table.Tbl_sun4 th.TblMultColHdr_sun4 a.TblHdrLnk_sun4:link, 
table.Tbl_sun4 th.TblMultColHdr_sun4 a.TblHdrLnk_sun4:visited {
	color:#000000;
	text-decoration:none;
}
table.Tbl_sun4 th.TblMultColHdr_sun4 a.TblHdrLnk_sun4:hover {
	text-decoration:underline;
}
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 {
	border-left-color:#CAD0D2;
	border-bottom-color:#81939B;
	background-color:#E2E4E9;
	background-image:url(images/table2/column_gradient.png);
	background-repeat:repeat-x;
}
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrLnk_sun4:link,
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrLnk_sun4:visited {
	background:none;
	color:#000000;
	text-decoration:none;
}
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrLnk_sun4:hover {
	background-color:#E1E5E8; 
	color:#000000;
	text-decoration:underline; 
	background-image:url(images/table2/column_gradient.png);
}
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrImgLnk_sun4:link, 
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrImgLnk_sun4:visited {
	background:none;
	color:#336699;
	text-decoration:none;
	border-left-color:#C7C8CA;
}
table.Tbl_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrImgLnk_sun4:hover {
	background-color:#E1E5E8;
	color:#003399; 
	background-image:url(images/table2/column_gradient.png);
}
/* Sorted Cells */
table.Tbl_sun4 .TblTdSrt_sun4 {
	background-color:#e5e9ed;
}
table.Tbl_sun4 .TblColFtrSpc_sun4 {
	border-bottom-color:#CAD0D2;
	border-top-color:#E5E9ED;
	background-color:#fff;}
 
/* Spacer Colums */
table.Tbl_sun4 th.TblTdSpc_sun4 {
	border-color:#81939B;
	background-color:#fff;
}
/* Alarm Cells */
table.Tbl_sun4 .TblTdAlm_sun4, 
table.Tbl_sun4 .TblTdSrtAlm_sun4 {
	background-color:#FFFEE7;
}
/*Other Table Content Styles*/
table.Tbl_sun4 a:link, table.Tbl_sun4 a:visited {
	color:#003399;
	text-decoration:none;
}
table.Tbl_sun4 a:hover {
	color:#003399;
	text-decoration:underline;
}
.TblMsgTxt_sun4 {
	color:#000000;
}
/* Mouseover and Row Selection Styles */
table.Tbl_sun4 tr.TblSelRow_sun4 td, 
table.Tbl_sun4 tr.TblSelRow_sun4 th {
	background-color:#90B7D0;
}
/*Table Sub-Grouping */
table.Tbl_sun4 .TblGrpRow_sun4 {
	border-left-color:#98A0A5;
   	border-bottom-color:#98A0A5;
	border-top-color:#98A0A5;
	background:#E1E5E8 url(images/table2/column_gradient.png) center top repeat-x; 
}	
.TblGrpTxt_sun4 {
	color:#000000;
	text-decoration:none;
}
.TblGrpTxt_sun4 {
	color:#000000;
	margin:0px 3px;
}
table.Tbl_sun4 .TblGrpMsgTxt_sun4 {
	color:#000000;
}
/* Table Footers */
/* Table Column-level Footer */
table.Tbl_sun4 .TblColFtr_sun4 {
	border-bottom-color:#CAD0D2;
	border-top-color:#E5E9ED;
	background-color:#fff;}
 
table.Tbl_sun4 .TblColFtrSrt_sun4 {
	background-color:#e5e9ed;
	border-bottom-color:#CAD0D2;
	border-top-color:#E5E9ED;
}
.TblColFtrTxt_sun4 {
	color:#000000;
}
/* Table Overall Footer */ 
table.Tbl_sun4 td.TblFtrRow_sun4 {
	background-color:#E5E9ED;
}
table.Tbl_sun4 .TblFtrRowTxt_sun4 {
	color:#000000;
}
table.Tbl_sun4 .TblFtrLft_sun4 {
	color:#000000;
}
/*Table Group Column-Level Footer */
table.Tbl_sun4 .TblGrpColFtr_sun4 {
	border-bottom-color:#CAD0D2;
	background-color:#fff;
}
table.Tbl_sun4 .TblGrpColFtrSrt_sun4 {
	border-bottom-color:#CAD0D2;
	background-color:#e5e9ed;
}
.TblGrpFtrRowTxt_sun4 {
	color:#000000;
}
/* Embedded Table Panels */
table.Tbl_sun4 td.TblPnlTd_sun4 {
	background-color:#E5E9ED;
}
.TblPnlLytDiv_sun4 {
	border-top-color:#A3AAAC;
}
.TblPnlShd3Div_sun4 {}
.TblPnlShd2Div_sun4 {}
.TblPnlShd1Div_sun4 {
	border-top-color:#4c4f53 ;
	border-right-color:#76797C;
	border-bottom-color:#8C8F91;
	border-left-color:#76797C;
}
.TblPnlDiv_sun4 {
	background-color:#FFFFFF;
	border-top:1px solid #CBCCCD;
}
.TblPnlBtnDiv_sun4 {
   border-top-color:#81939B;
} 
.TblPnlTtl_sun4 {
	color:#000000;
}
.TblCstFltMnu_sun4 {
	background-color:#90B7D0;
	color:#003399;
	border-right-color:#AEAFB0;
	border-bottom-color:#B9B9BA;
}
table.Tbl_sun4 div.TblPnlDiv_sun4 td {
	background:transparent;
}
.TblPnlCnt_sun4 {
	background:none;
}
.TblPnlHlpTxt_sun4 {
	background:#DBE5ED url(images/table2/column_gradient.png) top left repeat-x;
	border-top-color:#758B9B;
	border-right-color:#9CB6C7;
	border-bottom-color:#CADDED;
	border-left-color:#9CB6C7;
} 
/*---*/

/* LIGHTWEIGHT TABLE DESIGN */
table.TblLt_sun4 {
	background-color:#fff;
	color:#000000;
}
/* Table Caption/Title */
table.TblLt_sun4 caption.TblTtlTxt_sun4 {
	color:#000000;
	background:none;
}
table.TblLt_sun4 caption.TblTtlTxt_sun4 span.TblTtlMsgSpn {
	color:#000000;
}
/* Action Bar */
table.TblLt_sun4 td.TblActTd_sun4 {
	background-color:#E5E9ED;
	border-color:#e5e5e5;
}
table.TblLt_sun4 td.TblActTdLst_sun4 {
	background-color:#E5E9ED;
	border-color:#e5e5e5;
}
/* Selection Column - Headers */
table.TblLt_sun4 th.TblColHdrSel_sun4 {
	border-top-color:#8C8F91; 
	border-left-color:#8C8F91; 
	border-bottom-color:#8C8F91; 
	background-color:#FFFFFF;
}
table.TblLt_sun4 th.TblColHdrSrtSel_sun4 {
	background-color:#E1E5E8;
	border-top-color:#959aa5;
	border-left-color:#959aa5;
	border-bottom-color:#828da4; 
	background-image:url(images/table2/column_gradient.png);
}
table.TblLt_sun4 th.TblColHdrSel_sun4 a.TblHdrLnk_sun4:link {
	background-color:#E1E5E8;
	background-image:url(images/table2/column_gradient.png);
	background-position:center top;
}
table.TblLt_sun4 th.TblColHdrSel_sun4 a.TblHdrLnk_sun4:visited {
	background-color:#E1E5E8;
	background-image:url(images/table2/column_gradient.png);
	background-position:center top;
}
table.TblLt_sun4 th.TblColHdrSel_sun4 a.TblHdrLnk_sun4:hover {
	background-color:#f1f3f6; 
	background-image:url(images/table2/column_gradient.png);
	background-position:center top;
}
table.TblLt_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrImgLnk_sun4:hover {
	background-color:#f1f3f7; 
	background-image:url(images/table2/column_gradient.png);
	background-position:center top;
}
table.TblLt_sun4 th.TblColHdrSrtSel_sun4 a.TblHdrLnk_sun4:hover {
	background-color:#f1f3f7; 
	background-image:url(images/table2/column_gradient.png);
}
/* Regular Column Headers */
table.TblLt_sun4 th.TblColHdr_sun4 {
	border-top-color:#81939B;
	border-left-color:#81939B;
	border-bottom-color:#81939B;
	background-color:#E6EBEF;
}
table.TblLt_sun4 table.TblHdrTbl_sun4 td {
	background:none;
}
table.TblLt_sun4 a.TblHdrLnk_sun4:link, 
table.TblLt_sun4 a.TblHdrLnk_sun4:visited {
	background-color:#E1E5E8;
	background-position:center top;
	color:#000000;
	text-decoration:none; 
	background-image:url(images/table2/column_gradient.png);
}
table.TblLt_sun4 a.TblHdrLnk_sun4:hover {
	color:#000000;
	text-decoration:underline;
	background-color:#f1f3f6; 
	background-image:url(images/table2/column_gradient.png);
	background-position:center top;
}
table.TblLt_sun4 a.TblHdrImgLnk_sun4:link, 
table.TblLt_sun4 a.TblHdrImgLnk_sun4:visited {
	background-color:#E1E5E8;
	border-left-color:#CAD0D2;
	color:#336699;
	text-decoration:none; 
	background-image:url(images/table2/column_gradient.png);
	background-position:center top;
}
table.TblLt_sun4 a.TblHdrImgLnk_sun4:hover {
	background-color:#f1f3f6; 
	color:#336699; 
	background-image:url(images/table2/column_gradient.png);
	background-position:center top;
}
table.TblLt_sun4 span.TblColHdrSelDis_sun4 {
	border-left-color:#DBE0E3;
	background-color:#E1E5E8;
	color:#000000;
	text-decoration:none; 
	background-image:url(images/table2/column_gradient.png);
	background-position:center top;
}
table.TblLt_sun4 .TblHdrTxt_sun4 {
	background-color:#E1E5E8;
	color:#000000;
	text-decoration:none;
	background-image:url(images/table2/column_gradient.png);
	background-position:center top;
}
/* Current Sort Column */
table.TblLt_sun4 th.TblColHdrSrt_sun4 {
	border-top-color:#81939B;
	border-left-color:#81939B;
	border-bottom-color:#81939B;
	background-color:#E1E5E8; 
	background-image:url(images/table2/column_gradient.png);
}
table.TblLt_sun4 th.TblColHdrSrt_sun4 a.TblHdrLnk_sun4:hover {
	background-color:#f1f3f7; 
	color:#000000;
	text-decoration:underline; 
	background-image:url(images/table2/column_gradient.png);
}
table.TblLt_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:link, 
table.Tbl_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:visited {
	border-left-color:#C7C8CA; 
	color:#336699; 
	text-decoration:none;
}
table.TblLt_sun4 th.TblColHdrSrt_sun4 a.TblHdrImgLnk_sun4:hover {
	background-color:#f1f3f7; 
	color:#003399; 
	background-image:url(images/table2/column_gradient.png);
}
/* Multi-Column Headers */
table.TblLt_sun4 th.TblMultColHdr_sun4 {
	border-left-color:#81939B;
	border-bottom-color:#81939B;
	background-color:#E6EBEF;
}
table.TblLt_sun4 th.TblMultHdr_sun4 {
	border-top-color:#81939B;
	border-left-color:#81939B;
	background-color:#E6EBEF;
}
table.TblLt_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrLnk_sun4:hover {
	background-color:#f1f3f7;
	color:#000000;
	text-decoration:underline; 
	background-image:url(images/table2/column_gradient.png);
}
table.TblLt_sun4 th.TblMultColHdrSrt_sun4 a.TblHdrImgLnk_sun4:hover {
	background-color:#f1f3f7;
	color:#003399; 
	background-image:url(images/table2/column_gradient.png);
}
/*Table Sub-Grouping */
table.TblLt_sun4 .TblGrpRow_sun4 {
	background-color:#FFFFFF;
	border-left-color:#CAD0D2;
   	border-bottom-color:#CAD0D2;
   	border-top-color:#E5E9ED;
}
/* Table Footers */ 
table.TblLt_sun4 td.TblFtrRow_sun4 {
	background-color:#fff;
}
table.TblLt_sun4 .TblFtrRowTxt_sun4 {
	color:#5F6466;
}
table.TblLt_sun4 .TblColFtr_sun4 {
	border-bottom-color:#a8b2b6;
	border-top-color:#A9ADB5;
	background-color:#fff;}
 
table.TblLt_sun4 .TblColFtrSrt_sun4 {
	background-color:#e5e9ed;
	border-bottom-color:#a8b2b6;
	border-top-color:#A9ADB5;
}
table.TblLt_sun4 .TblColFtrSpc_sun4 {
	border-bottom-color:#a8b2b6; 
	border-top-color:#A9ADB5; 
	background-color:#fff;
}
/*---*/

/* TREE */
.TreTtl_sun4 {
	color:#000;
}
.Tree_sun4 {
	background-color:#fff;
}
a.TreeLink_sun4:link, a.TreeLink_sun4:visited {
	color:#003399;
	text-decoration:none;
}
a.TreeLink_sun4:hover {
	color:#003399;
	text-decoration:underline;
}
a.TreeParentLink_sun4:link, a.TreeParentLink_sun4:visited {
	color:#003399;
	text-decoration:none;
}
a.TreeParentLink_sun4:hover {
	color:#003399;
	text-decoration:underline;
}
a.TreeSelLink_sun4:link, a.TreeSelLink_sun4:visited {
	color:#000;
	text-decoration:none;
}
a.TreeSelLink_sun4:hover {
	color:#000;
	text-decoration:underline;
}
.TreeRootRow_sun4, .TreeRootRowHeader_sun4 {
	background-color:#e5e9ed;
}
.TreeSelRow_sun4, .TreeRootSelRow  {
	background-color:#90b7d0;
}
.TreeSelText_sun4 {
	color:#000;
}
.TreeContent_sun4 a:link, .TreeContent_sun4 a:visited {
	color:#000;
	text-decoration:none;
}
/*---*/

/* VERSION DIALOG */
.VrsBdy_sun4, .VrsMgn_sun4, .VrsBtnBdy_sun4 {
	background-color:#D2DBE4;
}
.VrsHdrTxt_sun4, .VrsTxt_sun4 {
	color:#000000;
}
.VrsMstBdy_sun4 {
	background:#FFFFFF url(images/version/version_brand.jpg) no-repeat;
}
/*---*/
/* WIZARD */
.WizTtlBar_sun4 {
	background:#5B87A5 url(images/masthead/sec-masthead-background.jpg) top left repeat-x;
	border-color:#000000;
	color:#FFFFFF;
}
.WizBar_sun4 {
        background-color:#E5E9ED;
        border-bottom-color:#80929B;
}
.WizBdy_sun4 {
	border-left-color:#98A0A5;
}
.WizBtm_sun4 {
	background-color:#E5E9ED;
	border-top-color:#98A0A5;
}.WizHlpDiv_sun4 {
	background:#E5E9ED url(images/wizard/left-pane-background.gif) left top repeat-x;
}
.WizSubTtlDiv_sun4, .WizStpsPnTtlDiv_sun4, .WizTtl_sun4 .TtlLin_sun4 {}
a.WizStpLnk_sun4 {
	color:#003399;
	text-decoration:none;
}
a.WizStpLnk_sun4:hover {
	text-decoration:underline;
}
/*---*/

/* BUBBLE */
.BubbleShadow_sun4 {
    background: #BBBBBB;
    border: 1px solid #BBBBBB;
}

.Bubble_sun4 {
    background: #ffffff; 
    border: 1px solid #8c8f91; 
}

.BubbleShadow_sun4, .Bubble_sun4 {
    -moz-border-radius: 5px;
}

.Bubble_sun4 .bottomLeftArrow_sun4 {
    background-image: url(images/bubble/bottomLeftArrow.png);
}

.Bubble_sun4 .bottomRightArrow_sun4 {
    background-image: url(images/bubble/bottomRightArrow.png);
}

.Bubble_sun4 .topLeftArrow_sun4 {
    background-image: url(images/bubble/topLeftArrow.png);
}

.Bubble_sun4 .topRightArrow_sun4 {
    background-image: url(images/bubble/topRightArrow.png);
}

.BubbleHeader_sun4 {
    background-color: #e1e5e8;
    border-bottom: 1px solid #8c8f91;
    -moz-border-radius-topleft: 5px;
    -moz-border-radius-topright: 5px;
}

.BubbleTitle_sun4 {
    color: #36586f;
}

.BubbleCloseBtn_sun4 {
    background-image: url(images/bubble/dismiss_icon.png);
}

.NoBubbleCloseBtn_sun4 {
    background-image: url(images/other/dot.gif);
}
/*---*/

/* ACCORDION */
.Accordion_sun4 {
    border-top: thin solid #8c8f91;
}

.AccdHeader_sun4 {
    border-color: #8c8f91;
    background-image: url(images/accordion/tab_collapsed_gradient.png);
    background-repeat: repeat-x;
    background-color: #e1e5e8;
    background-position: top;
}

.AccdRefreshBtn_sun4, .AccdOpenAllBtn_sun4, .AccdCloseAllBtn_sun4 {
    cursor: pointer;
}

.AccdDivider_sun4 {
    background-color:#C7C8CA;
}

.AccdTabExpanded_sun4, .AccdTabCollapsed_sun4 {
    border-color: #8c8f91;
    cursor: pointer;
}

.AccdTabExpanded_sun4 {
    background: #46708e url(images/accordion/tab_expanded_gradient.png) repeat-x top;
    color: #FFFFFF;
 }
.AccdTabCollapsed_sun4 {
    background: #e1e5e8 url(images/accordion/tab_collapsed_gradient.png) repeat-x top;
 }

.AccdTabExpanded_sun4 a:link,
.AccdTabExpanded_sun4 a:visited,
.AccdTabExpanded_sun4 a:active {
    color: #FFFFFF;
}

.AccdTabCollapsed_sun4 a:link,
.AccdTabCollapsed_sun4 a:visited,
.AccdTabCollapsed_sun4 a:active {
    color: #333333; 
}

.AccdTabExpanded_sun4 a:hover {
    background: #5b87a5 url(images/accordion/tab_expanded_gradient.png) repeat-x top;
}

.AccdTabCollapsed_sun4 a:hover {
    background: #f1f3f6 url(images/accordion/tab_collapsed_gradient.png) repeat-x top;
}

.AccdTabTitle_sun4 a:link,
.AccdTabTitle_sun4 a:visited,
.AccdTabTitle_sun4 a:hover,
.AccdTabTitle_sun4 a:active {
    text-decoration: none;
}

.AccdTabExpanded_sun4 .AccdDownTurner_sun4 {
    background: transparent url(images/accordion/tab_expanded_turner.png);
    cursor: pointer;
}

.AccdTabCollapsed_sun4 .AccdRightTurner_sun4 {
    background: transparent url(images/accordion/tab_collapsed_turner.png);
    cursor: pointer;
}

.AccdTabMenuCue_sun4 {
    cursor: pointer;
}

.AccdTabExpanded_sun4 .AccdTabMenuCue_sun4 {
    background-image: url(images/accordion/tab_expanded_droparrow.png);
}

.AccdTabCollapsed_sun4 .AccdTabMenuCue_sun4 {
    background-image: url(images/accordion/tab_collapsed_droparrow.png);
}

.AccdTabContent_sun4 {
    background-color: #FFFFFF;
    border-color: #8c8f91;
 }
/*---*/

/* PROGRESS BAR */
.progressBar_4_sun4 {
	border-color:#F1F1F1;
}
.progressBar_4_sun4 .barContainer_sun4 {
	background-color:#F2F3F4;
	border-color:#666664;
}
.progressBar_4_sun4 .barIndeterminatePaused_sun4 {
	background: #A0CCE7 url(images/progressBar/still-indeterminate.gif) repeat-x top;
}
.progressBar_4_sun4 .barIndeterminate_sun4 {
	background: #A0CCE7 url(images/progressBar/indeterminate.gif) repeat-x top;
}
.progressBar_4_sun4 .barDeterminate_sun4 {
	background: #A0CCE7 url(images/progressBar/reg-slice.gif) repeat-x top;
}
.progressBar_4_sun4 .failure_sun4 {
	background-image:url(images/alerts/failed_small.gif);
	background-position:top left;
	background-repeat:no-repeat;
}
.progressBar_4_sun4 .failureLabel_sun4 {
	color:#CC0000;
}
.progressBar_4_sun4 .checkboxContainer_sun4 label {
	cursor:pointer;
}

/* MENU */
.MenuShadow_sun4 {
    background:#bbb; 
    border:1px solid #ddd;
}

.Menu_sun4 {
    background: #fff; 
    border: 1px solid #8c8f91; 
}

ul.MenuItems_sun4 {
    background-color: #FFFFFF;
}

ul.MenuItems_sun4 li .MenuItemSeparator_sun4 {
    border-top: 1px solid #cad0d2;
}

ul.MenuItems_sun4 li .MenuItem_sun4,       /* 1st-level menu item */
ul.MenuItems_sun4 ul li .MenuItem_sun4 {   /* 2nd-level menu item in 1st-level optgroup */
    color: #003399;
}

ul.MenuItems_sun4 li .MenuOptGroupHeader_sun4 {  /* 1st-level optgroup header */
    color: #000;
}

ul.MenuItems_sun4 li .MenuItemDisabled_sun4,       /* 1st-level menu item */
ul.MenuItems_sun4 ul li .MenuItemDisabled_sun4 {   /* 2nd-level menu item in 1st-level optgroup */
    color: #7a7277;
}

ul.MenuItems_sun4 li .MenuItem_sun4:hover,     /* standard hover */
ul.MenuItems_sun4 li .MenuItem_sun4.MenuFocus_sun4{  
    background-color: #90b7d0;
    color: #ffffff;
}

ul.MenuItems_sun4 li .MenuItem_sun4:hover{     /* standard hover */
    cursor: pointer;
}

.NoMenuItemWidget_sun4 {
    background-image: url(images/other/dot.gif);
}

.MenuItemSubmenu_sun4 {
    background-color: transparent;
}

.NoMenuItemSubmenu_sun4 {
    background-image: url(images/other/dot.gif);
}

/*---*/

/* COMMON TASKS */
.commonTaskbgColor_sun4 {
	background:#B6C6D6
}
.commonTaskSection_4_sun4 {
	background:#B6C6D6 url(images/commontaskssection/bkgrnd.gif) right top repeat-x;
}
.commonTaskSection_4_sun4 .TskScnTpBx_sun4 {
	background:url(images/commontaskssection/s-curve.gif) bottom left repeat-x;
}
.commonTaskSection_4_sun4 .header_sun4, .commonTaskSection_4_sun4 .help_sun4, .commonTaskGroup_4_sun4 .header_sun4 {
	color: #4B5E6A;
}
.commonTask_4_sun4 .left_sun4 {
	border-top-color:#AEB3B8;
	border-bottom-color:#AEB3B8;
	border-left-color:#AEB3B8;
}
.commonTask_4_sun4 .center_sun4 {
	border-top-color:#AABCC8;
	border-bottom-color:#AABCC8;
}
.commonTask_4_sun4 .right_sun4 {
	background-color: #99B3C0;
	border-top-color:#AABCC8;
	border-bottom-color:#AABCC8;
	border-right-color:#AABCC8;
}
.commonTask_4_sun4 .background_sun4 {
	background: #9AB2BF url(images/commontaskssection/gradation-grey.gif) right top repeat-x;
}
.commonTask_4_sun4 a.TskScnTxtBg_sun4 {
	border-left-color:#AABCC8;
}
.commonTask_4_sun4 a.TskScnTxtBg_sun4:link,.commonTask_4_sun4  a.TskScnTxtBg_sun4:visited {
	background: #E2E7EA url(images/commontaskssection/grad1.gif) right top repeat-x;
	color:#333333;
}
.commonTask_4_sun4 a.TskScnTxtBg_sun4:hover {
	background:#4581B3 url(images/commontaskssection/grad2.gif) right top repeat-x;
	color:#FFF;
}
.commonTask_4_sun4 .infoPanel_sun4 {
	border-color: #AABCC8;
	background-color: #F4F6F7;
}
.commonTask_4_sun4 .infoPanel_sun4 .more_sun4 {
	border-top-color:#CFD7D9;
}
.commonTask_4_sun4 .infoPanel_sun4 .more_sun4 a:link, .commonTask_4_sun4 .infoPanel_sun4 .more_sun4 a:visited, .commonTask_4_sun4 .infoPanel_sun4 .more_sun4 a:hover {
	color:#003399;
}
.commonTask_4_sun4 .infoPanel_sun4 .more_sun4 a:link, .commonTask_4_sun4 .infoPanel_sun4 .more_sun4 a:visited, .commonTask_4_sun4 a.TskScnTxtBg_sun4, .commonTask_4_sun4 a.TskScnTxtBgOvr_sun4 {
	text-decoration:none;
}
.commonTask_4_sun4 .infoPanel_sun4 .more_sun4 a:hover {
	text-decoration:underline;
}
.commonTask_4_sun4 .infoPanel_sun4 .header_sun4 {
	color:#666666;
}
.commonTask_4_sun4 .TskScnTskLftBtm_sun4 {
	background-image: url(images/commontaskssection/leftBottom.gif);
}     
.commonTask_4_sun4 .TskScnTskLftTp_sun4 {
	background-image: url(images/commontaskssection/leftTop.gif);
}             
.commonTask_4_sun4 .TskScnTskRghtBtm_sun4 {
	background-image: url(images/commontaskssection/rightBottom.gif);
}     
.commonTask_4_sun4 .TskScnTskRghtTp_sun4 {
	background-image: url(images/commontaskssection/rightTop.gif);
} 
.commonTask_4_sun4 .TskScnTskRghtBrdr_sun4 {
	background-color: #AABCC8;
}
/*---*/
.hyperlink_sun4 {
	color:#003399;
	text-decoration:none;
}

/* Table2 */
.table2_sun4 {
	background-color:#213D51;
	border-top:1px solid #374154;	
	color:#000000;
}
.table2_sun4 .bg_sun4 {
	background-color:#E5E9ED;
	border-color:#8C8F91;
}
.table2_sun4 .title_sun4 {
	color:#FFFFFF;
	background:#386787 url(images/table2/tab_expanded_gradient.png) top left repeat-x;
}
.table2_sun4 .header_sun4, .table2_sun4 .footer_sun4 {
	border-left-color:#98A0A5;
}
.table2_sun4 .header_sun4 th, .table2_sun4 .footer_sun4 th {
	border-bottom-color:#98A0A5;
}
.table2_sun4 .tableScroller_sun4 {
	background-color:#FFFFFF;
	border-top-color:#CAD0D2;
	border-bottom-color:#CAD0D2;
	border-left-color:#98A0A5;
}
.table2_sun4 .tableScroller_sun4 tr.selected_sun4 td {
	background-color:#90B7D0;
}
.table2_sun4 tr {
	border-right-color:#CAD0D2;
}
.table2_sun4 th {
	border-color:#CAD0D2;
	background:#E1E5E8 url(images/table2/column_gradient.png) top left repeat-x;
	width:100%;
	text-align:left;
}
.table2_sun4 td {
	border-bottom-color:#CAD0D2;
	border-right-color:#CAD0D2;
	background-color:#FFFFFF;
}
.table2_sun4 th a.hyperlink_sun4 {
	color:#000000;
}
.table2_sun4 .header_sun4 th a.hyperlink_sun4:hover {
	background:#CAD0D2 url(images/table2/column_gradient_selected.png) top left repeat-x;
}
.table2_sun4 .header_sun4 th a span {
	background-image:url(images/table2/down_arrow.png);
	background-position:right;
	background-repeat:no-repeat;
}
.table2_sun4 .groupHeader_sun4 {
	border-color:#98A0A5;
	border-top-color:#8C8F91;
	background:#E1E5E8 url(images/table2/column_gradient.png) top left repeat-x;
}
/*---*/
