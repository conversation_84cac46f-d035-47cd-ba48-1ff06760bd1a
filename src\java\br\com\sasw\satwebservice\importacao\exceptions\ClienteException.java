/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.sasw.satwebservice.importacao.exceptions;

/**
 *
 * <AUTHOR>
 */
public class ClienteException extends Exception{
    
    private static final long serialVersionUID = 7718828512143293558L;
    private final ClienteErrorCode code;
    
    public ClienteException(ClienteErrorCode code) {
            super();
            this.code = code;
    }

    public ClienteException(String message, Throwable cause, ClienteErrorCode code) {
            super(message, cause);
            this.code = code;
    }

    public ClienteException(String message, ClienteErrorCode code) {
            super(message);
            this.code = code;
    }

    public ClienteException(Throwable cause, ClienteErrorCode code) {
            super(cause);
            this.code = code;
    }

    public ClienteErrorCode getCode() {
            return this.code;
    }
        
    public static class ClienteErrorCode implements ErrorCode{

        public ClienteErrorCode() {
        }

        public ClienteErrorCode(int code) {
            this.code = code;
        }
        
        private int code;
        private String status;

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        @Override
        public String getStatus() {
            switch(this.code){
                case 1:
                    status = "Customer successfully added.";
                    break;
                case 2:
                    status = "Customer successfully deactivated.";
                    break;
                case 3:
                    status = "Request for inclusion of inactive customer. Customer reactivated.";
                    break;
                case 6:
                    status = "Customer already registered. Inclusion not processed.";
                    break;
                case 7:
                    status = "Inactive/Non-existent customer. Deactivation not processed.";
                    break;
                case 9:
                    status = "Insufficient/Incorrect data. Inclusion not processed.";
                    break;
                default:
                    status = "Unknown error.";
            }
            return status;
        }
    }
}
