/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.service;

import br.com.sasw.satwebservice.bradesconovo.dao.BradescoCertificateDao;
import br.com.sasw.satwebservice.bradesconovo.dao.BradescoIntegraDao;
import br.com.sasw.satwebservice.bradesconovo.dao.entity.BradescoCertificateEntity;
import br.com.sasw.satwebservice.bradesconovo.dto.request.ConsultaMovimentacaoEntrebasesRequest;
import br.com.sasw.satwebservice.bradesconovo.dto.request.ConsultaMovimentacaoInterBancarioRequest;
import static br.com.sasw.satwebservice.bradesconovo.utils.BradescoConstants.*;
import br.com.sasw.satwebservice.bradesconovo.utils.BradescoUtils;
import com.google.gson.Gson;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustAllStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

/**
 *
 * <AUTHOR> Silva
 */
public class ConsultaMovimentacaoService {

    final private BradescoTokenService bradescoTokenService = new BradescoTokenService();
    final private BradescoCertificateDao bradescoCertificateDao = new BradescoCertificateDao();
    final private BradescoIntegraDao bradescoIntegraDao = new BradescoIntegraDao();
    private final Gson gson = new Gson();

    private final static String CONSULTA_MOVIMENTACAO_INTERBANCARIOS_TOMADOS = "/conciliacao-integrada/consulta-movimentacao/interbancarios/tomados";
    private final static String CONSULTA_MOVIMENTACAO_INTERBANCARIOS_CEDIDOS = "/conciliacao-integrada/consulta-movimentacao/interbancarios/cedidos";
    private final static String CONSULTA_MOVIMENTACAO_ENTREBASES = "/conciliacao-integrada/consulta-movimentacao/entrebases";
    private final static String CONSULTA_MOVIMENTACAO_SUPRIMENTOS_MASSIFICADOS = "/conciliacao-integrada/consulta-movimentacao/clientes/suprimentos/massificados";
    private final static String CONSULTA_MOVIMENTACAO_SUPRIMENTOS_PERSONALIZADOS = "/conciliacao-integrada/consulta-movimentacao/clientes/suprimentos/personalizados";
    private final static String CONSULTA_MOVIMENTACAO_CUSTODIANTES = "/conciliacao-integrada/consulta-movimentacao/custodiantes";
    private final static String CONSULTA_MOVIMENTACAO_AGENCIAS = "/conciliacao-integrada/consulta-movimentacao/agencias";

    public String getConsultaMovintacaoInterbancariosTomados(String empresa, 
            String cnpj, String token, String situacaoPedido, 
            String dataAtendimentoInicio, String dataAtendimentoFim, String page, 
            String size, String sort, String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    CONSULTA_MOVIMENTACAO_INTERBANCARIOS_TOMADOS, page, size, sort);
            HttpPost httpPost = new HttpPost(uri);

            String rawRequest = buildInterbancarioRequest(
                    bradescoCertificateEntity.getChaveCoin(ambiente), cnpj, null,
                    dataAtendimentoInicio, dataAtendimentoFim, situacaoPedido);
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        CONSULTA_MOVIMENTACAO_INTERBANCARIOS_TOMADOS, query, 
                        bradescoCertificateEntity.getBancoDeDados(), "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String getConsultaMovintacaoInterbancariosCedidos(String empresa, 
            String cnpj, String token, String situacaoPedido, 
            String dataAtendimentoInicio, String dataAtendimentoFim, String page, 
            String size, String sort, String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    CONSULTA_MOVIMENTACAO_INTERBANCARIOS_CEDIDOS, page, size, sort);
            HttpPost httpPost = new HttpPost(uri);
            String rawRequest = buildInterbancarioRequest(
                    bradescoCertificateEntity.getChaveCoin(ambiente), cnpj, null,
                    dataAtendimentoInicio, dataAtendimentoFim, situacaoPedido);
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        CONSULTA_MOVIMENTACAO_INTERBANCARIOS_CEDIDOS, query, 
                        bradescoCertificateEntity.getBancoDeDados(), "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String getConsultaMovintacaoEntrebases(String empresa, String cnpj, 
            String token, String situacaoPedido, String dataAtendimentoInicio, 
            String dataAtendimentoFim, String page, String size, String sort, 
            String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    CONSULTA_MOVIMENTACAO_ENTREBASES, page, size, sort);
            HttpPost httpPost = new HttpPost(uri);
            String rawRequest = buildEntrebasesRequest(
                    bradescoCertificateEntity.getChaveCoin(ambiente), cnpj, null,
                    dataAtendimentoInicio, dataAtendimentoFim, situacaoPedido);
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        CONSULTA_MOVIMENTACAO_ENTREBASES, query, 
                        bradescoCertificateEntity.getBancoDeDados(), "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String getConsultaMovimentacaoClientesSuprimentosMassificados(
            String empresa, String cnpj, String token, String situacaoPedido, 
            String dataAtendimentoInicio, String dataAtendimentoFim, String page, 
            String size, String sort, String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    CONSULTA_MOVIMENTACAO_SUPRIMENTOS_MASSIFICADOS, page, size, sort);
            HttpPost httpPost = new HttpPost(uri);
            String rawRequest = buildEntrebasesRequest(
                    bradescoCertificateEntity.getChaveCoin(ambiente), cnpj, null,
                    dataAtendimentoInicio, dataAtendimentoFim, situacaoPedido);
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        CONSULTA_MOVIMENTACAO_SUPRIMENTOS_MASSIFICADOS, query, 
                        bradescoCertificateEntity.getBancoDeDados(), "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String getConsultaMovimentacaoClientesSuprimentosPersonalizados(
            String empresa, String cnpj, String token, String situacaoPedido, 
            String dataAtendimentoInicio, String dataAtendimentoFim, String page, 
            String size, String sort, String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    CONSULTA_MOVIMENTACAO_SUPRIMENTOS_PERSONALIZADOS, page, size, sort);
            HttpPost httpPost = new HttpPost(uri);
            String rawRequest = buildEntrebasesRequest(
                    bradescoCertificateEntity.getChaveCoin(ambiente), cnpj, null,
                    dataAtendimentoInicio, dataAtendimentoFim, situacaoPedido);
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        CONSULTA_MOVIMENTACAO_SUPRIMENTOS_PERSONALIZADOS, query, 
                        bradescoCertificateEntity.getBancoDeDados(), "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String getConsultaMovimentacaoCustodiantes(String empresa, 
            String cnpj, String token, String situacaoPedido, 
            String dataAtendimentoInicio, String dataAtendimentoFim, String page, 
            String size, String sort, String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    CONSULTA_MOVIMENTACAO_CUSTODIANTES, page, size, sort);
            HttpPost httpPost = new HttpPost(uri);
            String rawRequest = buildEntrebasesRequest(
                    bradescoCertificateEntity.getChaveCoin(ambiente), cnpj, null,
                    dataAtendimentoInicio, dataAtendimentoFim, situacaoPedido);
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        CONSULTA_MOVIMENTACAO_CUSTODIANTES, query, 
                        bradescoCertificateEntity.getBancoDeDados(), "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    public String getConsultaMovimentacaoAgencias(String empresa, String cnpj, 
            String token, String situacaoPedido, String dataAtendimentoInicio, 
            String dataAtendimentoFim, String page, String size, String sort, 
            String query, String ambiente) {
        try {
            query = BradescoUtils.removeTokenFromQuery(query, token);
            BradescoCertificateEntity bradescoCertificateEntity = 
                    bradescoCertificateDao.getCertificateByEmpresa(empresa);
            String privateKey = BradescoUtils.loadPrivateKeyFromFile(
                    bradescoCertificateEntity.getPathCertificado(ambiente));
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setSSLContext(new SSLContextBuilder().loadTrustMaterial(null, 
                            TrustAllStrategy.INSTANCE).build())
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();

            URI uri = BradescoUtils.buildUri(getAPI_URL(ambiente) + 
                    CONSULTA_MOVIMENTACAO_AGENCIAS, page, size, sort);
            HttpPost httpPost = new HttpPost(uri);
            String rawRequest = buildEntrebasesRequest(
                    bradescoCertificateEntity.getChaveCoin(ambiente), cnpj, null,
                    dataAtendimentoInicio, dataAtendimentoFim, situacaoPedido);
            httpPost.setEntity(new StringEntity(rawRequest));

            BradescoUtils.setDefaultBradescoHeaders(httpPost, token, privateKey, rawRequest, uri, ambiente);
            try ( CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                bradescoIntegraDao.save(responseBody, 
                        CONSULTA_MOVIMENTACAO_AGENCIAS, query, 
                        bradescoCertificateEntity.getBancoDeDados(), "POST");
                return responseBody;
            } catch (Exception ex) {
                Logger.getLogger(BradescoTokenService.class.getName()).log(Level.INFO, "ERRO:", ex);
            }

            return null;
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        } catch (Exception ex) {
            Logger.getLogger(ListaPontoAtendimentoService.class.getName()).log(Level.SEVERE, "ERRO", ex);
        }
        return null;
    }

    private String buildInterbancarioRequest(String senhaConexao, String cnpj, String tipoAtendimento, String dataAtendimentoInicio,
            String dataAtendimentoFim, String codigoSituacaoPedido) {

        ConsultaMovimentacaoInterBancarioRequest consultaMovimentacaoRequest = new ConsultaMovimentacaoInterBancarioRequest();
        consultaMovimentacaoRequest.setCnpjEmpresa(cnpj);
        consultaMovimentacaoRequest.setSenhaConexao(senhaConexao);
        consultaMovimentacaoRequest.setSituacaoPedido(codigoSituacaoPedido);
        consultaMovimentacaoRequest.setDataAtendimentoInicio(dataAtendimentoInicio);
        consultaMovimentacaoRequest.setDataAtendimentoFim(dataAtendimentoFim);
        return gson.toJson(consultaMovimentacaoRequest);
    }

    private String buildEntrebasesRequest(String senhaConexao, String cnpj, String tipoAtendimento, String dataAtendimentoInicio,
            String dataAtendimentoFim, String codigoSituacaoPedido) {

        ConsultaMovimentacaoEntrebasesRequest consultaMovimentacaoEntrebasesRequest = new ConsultaMovimentacaoEntrebasesRequest();
        consultaMovimentacaoEntrebasesRequest.setCnpjEmpresa(cnpj);
        consultaMovimentacaoEntrebasesRequest.setCodigoConexaoEmpresa(senhaConexao);
        consultaMovimentacaoEntrebasesRequest.setSituacaoPedido(codigoSituacaoPedido);
        consultaMovimentacaoEntrebasesRequest.setDataAtendimentoInicio(dataAtendimentoInicio);
        consultaMovimentacaoEntrebasesRequest.setDataAtendimentoFim(dataAtendimentoFim);
        return gson.toJson(consultaMovimentacaoEntrebasesRequest);
    }

}
