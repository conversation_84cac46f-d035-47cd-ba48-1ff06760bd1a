/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.nfe.exception;

import br.com.sasw.satwebservice.nfe.models.RejeicaoModel;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;

/**
 *
 * <AUTHOR>
 */
public class NFeException extends JsonParseException {

    private static final long serialVersionUID = 7718828512143293558L;
    private final NFeError nfeError;

    public NFeException(String message, Throwable cause, NFeError nfeError) {
        super(message, cause);
        this.nfeError = nfeError;
    }

    public NFeException(String message, NFeError nfeError) {
        super(message);
        this.nfeError = nfeError;
    }

    public NFeException(NFeError nfeError) {
        super("");
        this.nfeError = nfeError;
    }

    public NFeException(Throwable cause, NFeError nfeError) {
        super(cause);
        this.nfeError = nfeError;
    }

    public NFeError getCode() {
        return this.nfeError;
    }

    public NFeError getNfeError() {
        return nfeError;
    }

    public static class NFeError {
        
        private RejeicaoModel rejeicao;
        private JsonObject nfe;

        public NFeError() {
        }

        public NFeError(JsonObject nfe) {
            this.nfe = nfe;
        }

        public NFeError(JsonObject nfe, RejeicaoModel rejeicao) {
            this.nfe = nfe;
            this.rejeicao = rejeicao;
        }

        public JsonObject getNfe() {
            return nfe;
        }

        public void setNfe(JsonObject nfe) {
            this.nfe = nfe;
        }

        public RejeicaoModel getRejeicao() {
            return rejeicao;
        }

        public void setRejeicao(RejeicaoModel rejeicao) {
            this.rejeicao = rejeicao;
        }
    }
}
