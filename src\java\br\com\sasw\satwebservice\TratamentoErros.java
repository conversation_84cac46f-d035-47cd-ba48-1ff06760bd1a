/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice;

import Arquivo.ArquivoLog;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import javax.ws.rs.ext.Provider;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
@Provider
public class TratamentoErros implements ExceptionMapper<Exception> {

    @Override
    public Response toResponse(Exception ex) {

        JSONObject resposta;
        String caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Erros\\"
                + DataAtual.getDataAtual("SQL") + "\\log.txt";
        ArquivoLog logerro = new ArquivoLog();
        logerro.Grava(ex.getMessage()+"\r\n"+ex.getLocalizedMessage()+"\r\n"+ex.getCause().toString(), caminho);

        if (ex instanceof com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException) {
            com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException unrecognizedPropertyException
                    = (com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException) ex;
            
            resposta = new JSONObject();
            resposta.put("exception", "UnrecognizedProperty");
            resposta.put("description", unrecognizedPropertyException.getOriginalMessage());
            resposta.put("field", unrecognizedPropertyException.getPath().get(unrecognizedPropertyException.getPath().size() -1).getFieldName());
//            resposta.put("value", invalidFormatException.getValue());
            
            return Response
                    .status(Response.Status.BAD_REQUEST)
                    .type("application/json")
                    .entity(resposta.toString()).build();
        } else if (ex instanceof com.fasterxml.jackson.databind.exc.InvalidFormatException) {
            com.fasterxml.jackson.databind.exc.InvalidFormatException invalidFormatException = (com.fasterxml.jackson.databind.exc.InvalidFormatException) ex;
            
            resposta = new JSONObject();
            resposta.put("exception", "InvalidFormat");
            resposta.put("description", invalidFormatException.getOriginalMessage());
            resposta.put("field", invalidFormatException.getPath().get(invalidFormatException.getPath().size() -1).getFieldName());
            resposta.put("value", invalidFormatException.getValue());
            
            return Response
                    .status(Response.Status.BAD_REQUEST)
                    .type("application/json")
                    .entity(resposta.toString()).build();
        }

        return Response
                .status(Response.Status.BAD_REQUEST)
                .build();
    }
}
