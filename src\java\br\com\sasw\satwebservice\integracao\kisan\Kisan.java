/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.integracao.kisan;

import Arquivo.ArquivoLog;
import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Clientes;
import SasBeans.TesCofresMov;
import SasDaos.ClientesDao;
import SasDaos.SemaforoDao;
import SasDaos.TesCofresMovDao;
import Xml.Xmls;
import br.com.sasw.pacotesuteis.utilidades.DataAtual;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.UriInfo;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;

/**
 *
 * <AUTHOR>
 */
@Path("ws-kisan")
public class Kisan {

    private final ArquivoLog logerro;
    private String caminho;
    private boolean corpvs;
    private final SasPoolPersistencia pool;
    private final SemaforoDao semaforoDao;
    private final ClientesDao clientesDao;
    private final TesCofresMovDao tesCofresMovDao;

    @Context
    private UriInfo context;

    public Kisan() {
        //localhost:8080/SatWebService/api/ws-cata?username=corpvs.integration&password=Cata1234&machineId=CCO000532&data=2018-11-01
        logerro = new ArquivoLog();
        caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Integracao"
                + "\\Kisan\\" + DataAtual.getDataAtual("SQL") + ".txt";
        pool = new SasPoolPersistencia();
        pool.setCaminho(this.getClass().getResource("/br/com/sasw/satwebservice/mapconect.txt").getPath().replace("%20", " "));
        clientesDao = new ClientesDao();
        semaforoDao = new SemaforoDao();
        tesCofresMovDao = new TesCofresMovDao();
    }

    /**
     * POST
     *
     * @param content representation for the resource
     * @return
     */
    @POST
    @Path("/{empresa}/")
    public String movimentacoesIntegradorPost(@PathParam("empresa") String empresa, String content) {
        String resp = "";
        Persistencia persistencia = null;
        Persistencia CORPVSRECIFE = null;
        
        Clientes cliente = null;
        Clientes clienteCorpvsPE = null;
        boolean temMachineID = false;

        try {
            // Validando empresa
            if (empresa == null || empresa.equals("")) {
                throw new Exception("Empresa inválida");
            }

            // Cria a persistencia
            persistencia = this.pool.getConexao(empresa);
            CORPVSRECIFE = null;

            // Validando persistencia
            if (null == persistencia) {
                throw new Exception("Empresa inválida: " + empresa);
            } else if (empresa.equals("SATCORPVS")) {
                this.corpvs = true;
                CORPVSRECIFE = this.pool.getConexao("SATCORPVSPE");
            }

            TesCofresMov tesCofresMov;
            JSONObject jSONObject = XML.toJSONObject(content);
            JSONObject KMessageJObject = (JSONObject) jSONObject.get("KMessage");
            String dateTime = KMessageJObject.getString("DateTime");

            JSONObject infoJObject = (JSONObject) KMessageJObject.get("Info");
            String machineID = infoJObject.getString("MachineID");
            String userID = infoJObject.get("UserID").toString();
            String transactionNo = infoJObject.get("TransactionNo").toString();

            temMachineID = true;
            this.caminho = "C:\\glassfish4\\glassfish\\domains\\domain1\\applications\\SatWebServiceTracers\\Integracao\\Kisan\\"
                    + empresa + "\\" + getDataAtual("SQL") + "\\" + machineID + ".txt";
            this.logerro.Grava("Conteúdo\r\n" + content, this.caminho);
  
            cliente = this.clientesDao.buscarCofre(machineID, persistencia);
            if(this.corpvs){
                clienteCorpvsPE = this.clientesDao.buscarCofre(machineID, CORPVSRECIFE);
            }
            
            if(cliente == null){
                throw new Exception("Erro cadastro cliente");
            }
            
            JSONObject depositsJObject = (JSONObject) KMessageJObject.get("Deposits");
            Object depositObject = depositsJObject.get("Deposit");

            if (depositObject instanceof JSONObject) {
                JSONObject depositJObject = (JSONObject) depositObject;
                tesCofresMov = new TesCofresMov();
                //private BigDecimal id; // Gerado automático
                tesCofresMov.setCodCofre(new BigDecimal(cliente.getCodCofre()));
                tesCofresMov.setCodCliente(cliente.getCodigo());
                tesCofresMov.setData(LocalDate.parse(dateTime.split("T")[0], DateTimeFormatter.ISO_DATE));
                tesCofresMov.setHora(dateTime.split("T")[1].split("\\.")[0]);
                tesCofresMov.setIdUsuario(userID);
                tesCofresMov.setTipoMoeda(depositJObject.getString("Currency"));
                tesCofresMov.setTipoDeposito("DINHEIRO");
                tesCofresMov.setCodigoBarras(transactionNo);

                tesCofresMov = obterMovimentacao(tesCofresMov, depositJObject);

                this.tesCofresMovDao.inserirMovimentacaoKisan(tesCofresMov, machineID, persistencia);
                try{
                    this.tesCofresMovDao.inserirResumo(cliente.getCodCofre(), persistencia);
                } catch (Exception eM){
                    this.logerro.Grava(eM.getMessage(), this.caminho);
                }

                if (this.corpvs) {
                    
                    tesCofresMov.setCodCofre(new BigDecimal(clienteCorpvsPE.getCodCofre()));
                    tesCofresMov.setCodCliente(cliente.getCodigo());
                    
                    this.tesCofresMovDao.inserirMovimentacaoKisan(tesCofresMov, machineID, CORPVSRECIFE);
                    try{
                        this.tesCofresMovDao.inserirResumo(clienteCorpvsPE.getCodCofre(), CORPVSRECIFE);
                    } catch (Exception eM){
                        this.logerro.Grava(eM.getMessage(), this.caminho);
                    }
                }

                resp += Xmls.tag("transactionNo", transactionNo);

            } else if (depositObject instanceof JSONArray) {
                JSONArray depositJArray = (JSONArray) depositObject;
                JSONObject depositJObject;
                for (Object jSONObject1 : depositJArray) {
                    depositJObject = (JSONObject) jSONObject1;
                    tesCofresMov = new TesCofresMov();
                    //private BigDecimal id; // Gerado automático
                    //private BigDecimal CodCofre; // Busca da tabela clientes
                    //private BigDecimal CodCliente; // Busca da tabela clientes
                    tesCofresMov.setData(LocalDate.parse(dateTime.split("T")[0], DateTimeFormatter.ISO_DATE));
                    tesCofresMov.setHora(dateTime.split("T")[1].split("\\.")[0]);
                    tesCofresMov.setIdUsuario(userID);
                    tesCofresMov.setTipoMoeda(depositJObject.getString("Currency"));
                    tesCofresMov.setTipoDeposito("DINHEIRO");
                    tesCofresMov.setCodigoBarras(transactionNo);

                    tesCofresMov = obterMovimentacao(tesCofresMov, depositJObject);
                    
                    this.tesCofresMovDao.inserirMovimentacaoKisan(tesCofresMov, machineID, persistencia);
                    try{
                        this.tesCofresMovDao.inserirResumo(cliente.getCodCofre(), persistencia);
                    } catch (Exception eM){
                        this.logerro.Grava(eM.getMessage(), this.caminho);
                    }

                    if (this.corpvs) {

                        tesCofresMov.setCodCofre(new BigDecimal(clienteCorpvsPE.getCodCofre()));
                        tesCofresMov.setCodCliente(cliente.getCodigo());

                        this.tesCofresMovDao.inserirMovimentacaoKisan(tesCofresMov, machineID, CORPVSRECIFE);
                        try{
                            this.tesCofresMovDao.inserirResumo(clienteCorpvsPE.getCodCofre(), CORPVSRECIFE);
                        } catch (Exception eM){
                            this.logerro.Grava(eM.getMessage(), this.caminho);
                        }
                    }

                    resp += Xmls.tag("transactionNo", transactionNo);
                }
            }

            this.logerro.Grava("Resposta\r\n" + resp, this.caminho);
        } catch (Exception e) {
            if (!temMachineID) {
                // Grava o conteúdo enviado caso não a rotina acima não tenha identificado o machineID
                this.logerro.Grava("Conteúdo\r\n" + content, this.caminho);
            }
            this.logerro.Grava("Falha\r\n" + e.getMessage(), this.caminho);
            resp = Xmls.tag("resp", e.getMessage());
        } finally {
            if (persistencia != null) {
                try {
                    persistencia.FechaConexao();
                } catch (Exception ex) {
                    this.logerro.Grava("Falha\r\n" + ex.getMessage(), this.caminho);
                }
            }
            if (CORPVSRECIFE != null) {
                try {
                    CORPVSRECIFE.FechaConexao();
                } catch (Exception ex) {
                    this.logerro.Grava("Falha\r\nCORPVSRECIFE: " + ex.getMessage(), this.caminho);
                }
            }
        }
        return resp;
    }

    public static TesCofresMov obterMovimentacao(TesCofresMov tesCofresMov, JSONObject depositJObject) {
        tesCofresMov.setValorDeposito(BigDecimal.ZERO);
        Object banknoteObject = depositJObject.get("Banknote");
        if (banknoteObject instanceof JSONObject) {
            JSONObject banknoteJObject = (JSONObject) banknoteObject;
            try {
                tesCofresMov.setValorDeposito(tesCofresMov.getValorDeposito()
                        .add((new BigDecimal(banknoteJObject.get("Denom").toString())
                                .multiply(new BigDecimal(banknoteJObject.get("Qty").toString())))
                        ));
            } catch (Exception e) {

            }
        } else if (banknoteObject instanceof JSONArray) {
            JSONArray banknoteJArray = (JSONArray) banknoteObject;
            JSONObject banknoteJObject;
            for (Object object : banknoteJArray) {
                banknoteJObject = (JSONObject) object;
                try {
                    tesCofresMov.setValorDeposito(tesCofresMov.getValorDeposito()
                            .add((new BigDecimal(banknoteJObject.get("Denom").toString())
                                    .multiply(new BigDecimal(banknoteJObject.get("Qty").toString())))
                            ));
                } catch (Exception e) {

                }
            }
        }
        tesCofresMov.setNomeUsuario("IMP-KISAN");
        tesCofresMov.setStatus("NORMAL");
        tesCofresMov.setOperador("IMP-KISAN");
        tesCofresMov.setDt_Incl(LocalDate.now());
        tesCofresMov.setHr_Incl(getDataAtual("HORA"));
        return tesCofresMov;
    }
}
