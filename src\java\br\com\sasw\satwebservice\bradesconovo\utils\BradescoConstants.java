/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package br.com.sasw.satwebservice.bradesconovo.utils;

/**
 *
 * <AUTHOR>
 */
public class BradescoConstants {

    // Produção
    public final static String API_URL_PRODUCAO
            = "https://openapi.bradesco.com.br/v1";
    public final static String API_AUTENTICACAO_URL_PRODUCAO
            = "https://openapi.bradesco.com.br/auth/server/v1.1/token";

    // Hologação
    public final static String API_URL_HOMOLOGACAO
            = "https://proxy.api.prebanco.com.br/v1";
    public final static String API_AUTENTICACAO_URL_HOMOLOGACAO
            = "https://proxy.api.prebanco.com.br/auth/server/v1.1/token";
 
    public final static String AMBIENTE_PRODUCAO = "1";

    public static String getAPI_URL(String ambiente) {
        if (AMBIENTE_PRODUCAO.equals(ambiente)) {
            return API_URL_PRODUCAO;
        } else {
            return API_URL_HOMOLOGACAO;
        }
    }

    public static String getAPI_AUTENTICACAO_URL(String ambiente) {
        if (AMBIENTE_PRODUCAO.equals(ambiente)) {
            return API_AUTENTICACAO_URL_PRODUCAO;
        } else {
            return API_AUTENTICACAO_URL_HOMOLOGACAO;
        }
    }

}
