/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.satwebservice.nfe;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 *
 * <AUTHOR>
 */
@Path("/ws/NFE")
public class NFe {
    
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/envioNFE")
    public Response envio(String input) {
        NFEEnvio nfeEvnio = new NFEEnvio();
        return nfeEvnio.envio(input);
    }
    
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/consultaNFE")
    public Response consulta(String input) {
        NFEConsulta nfeConsulta = new NFEConsulta();
        return nfeConsulta.consulta(input);
    }
    
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/autenticacao")
    public Response autenticacao(String input) {
        NFEAutenticacao nfeAutenticacao = new NFEAutenticacao();
        return nfeAutenticacao.autenticacao(input);
    }
    
    @POST
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/impressao/")
    public Response impressao(@QueryParam("chave") String chave) {
        NFEImpressao nfeImpressao = new NFEImpressao();
        return nfeImpressao.impressao(chave);
    }
   
}
