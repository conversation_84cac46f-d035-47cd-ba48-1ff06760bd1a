package br.com.sasw.satwebservice.integracao.creditoonlinesantender.seguranca;

import br.com.sasw.satwebservice.integracao.creditoonlinesantender.excecoes.ApplicationException;
import com.altec.bsbr.app.dl.ecc.DLCrypto;

public class Cripto {

    private static Cripto cripto = null;
    private static DLCrypto dlCrypto;
    private static String clientPublicKey;

    public static synchronized Cripto getInstance() {
        if (cripto == null) {
            cripto = new Cripto();
            dlCrypto = new DLCrypto();
        }
        return cripto;
    }
    
    public String getClientPublicKey() {
        return clientPublicKey;
    }

    public static Cripto inicializar() {
        cripto = new Cripto();
        dlCrypto = new DLCrypto();
        dlCrypto.setClient(true);
        try {
            clientPublicKey = dlCrypto.keyAgreementInitializePhase1();
        }  catch (NoClassDefFoundError e) {
            e.printStackTrace();
        } catch (Exception e) {
            throw new ApplicationException(e);
        }
        return cripto;
    }
    
    public void setServerPublicKey(String serverPublicKey) {
        try {
            dlCrypto.keyAgreementInitializePhase2(serverPublicKey);
        } catch (Exception e) {
            throw new ApplicationException(e);
        }
    }

    public String encript(String value) {
        try {
            return dlCrypto.encrypt(value);
        } catch (Exception e) {
            throw new ApplicationException(e);
        }
    }

    public String descript(String value) {
        try {
            return dlCrypto.decrypt(value);
        } catch (Exception e) {
            throw new ApplicationException(e);
        }
    }

}
